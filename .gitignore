# General
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db
Desktop.ini
*.log
*.swp
*~

# Frontend
/frontend/node_modules/
/frontend/dist/
/frontend/build/
/frontend/.env
/frontend/.env.local
/frontend/.env.development.local
/frontend/.env.test.local
/frontend/.env.production.local
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*
/frontend/coverage/
/frontend/.vscode/
/frontend/.idea/

# Backend
/backend/venv/
/backend/__pycache__/
/backend/*.py[cod]
/backend/*$py.class
/backend/.env
/backend/*.db
/backend/*.sqlite3
/backend/alembic/versions/__pycache__/
/backend/app/__pycache__/
/backend/app/*/__pycache__/
/backend/logs/
/backend/.vscode/
/backend/.idea/

# IDE specific files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.vs/
*.code-workspace

# Temporary and cache files
.pytest_cache/
__pycache__/
.coverage
htmlcov/
.cache/ 