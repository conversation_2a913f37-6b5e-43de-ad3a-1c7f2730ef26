name: Build, Push to ECR, and Update ECS

on: 
  workflow_dispatch: # Manual trigger configuration
    inputs:
      version: 
        description: 'Version' # User selects version during trigger
        required: true
      branch:
        description: 'Branch Name (must be staging)'
        required: true
        default: 'staging'

permissions:
  id-token: write # Required for requesting the JWT
  contents: read  # Required for actions/checkout

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.branch == 'staging' }} # Ensures the workflow runs only if 'staging' branch is selected
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Validate branch selection
        run: |
          if [ "${{ github.event.inputs.branch }}" != "staging" ]; then
            echo "This workflow can only run for the staging branch."
            exit 1
          fi

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_FOR_OIDC }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Extract branch name
        id: extract_branch
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY }}
          VERSION: ${{ github.event.inputs.version }}
        run: |
          VERSION=$(echo "${{ github.event.inputs.version }}" | sed 's/\//-/g')
          BRANCH=${{ github.event.inputs.branch }}
          TAG="${VERSION}+${BRANCH}:latest"
          ECR_URI=$ECR_REGISTRY/$ECR_REPOSITORY
          echo $VERSION
          echo $ECR_REGISTRY
          echo $ECR_REPOSITORY
          echo $BRANCH
          echo $TAG
          echo "ECR_URI=$ECR_URI" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          docker build -t $ECR_URI:latest .
          docker push $ECR_URI:latest
          docker tag $ECR_URI:latest $ECR_URI:$VERSION
          docker push $ECR_URI:$VERSION

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Update ECS task definition
        id: update_task
        run: |
          echo "Current task definition:"
          aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME }} | jq
          TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME }})
          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "${{ env.ECR_URI }}:${{ env.VERSION }}" '.taskDefinition | .containerDefinitions[0].image=$IMAGE | .networkMode="awsvpc" | del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)')
          echo "New task definition with updated image and network mode:"
          echo "$NEW_TASK_DEFINITION" | jq
          echo "$NEW_TASK_DEFINITION" > new-task-def.json
          aws ecs register-task-definition --cli-input-json file://new-task-def.json

      - name: Capture new task revision
        id: capture_revision
        run: |
          NEW_TASK_REVISION=$(aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME }} | jq .taskDefinition.revision)
          echo "NEW_TASK_REVISION=$NEW_TASK_REVISION" >> $GITHUB_ENV
          echo "New task revision: $NEW_TASK_REVISION"

      - name: Update ECS service
        run: |
          echo "Updating ECS service to use new task revision: ${{ env.NEW_TASK_REVISION }}"
          aws ecs update-service --cluster ${{ vars.ECS_CLUSTER_NAME }} --service ${{ vars.ECS_SERVICE_NAME }} --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME }}:${{ env.NEW_TASK_REVISION }} --force-new-deployment
