name: Build 2 and push to ECR

on: 
  workflow_dispatch:
    inputs:
      version:
        description: 'Version'
        required: true

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

jobs:

  build:

    name: Build Image
    runs-on: ubuntu-latest
    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_FOR_OIDC }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Extract branch name
        shell: bash
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
        id: extract_branch

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY }}
          VERSION: ${{ github.event.inputs.version }}
        run: |
          BRANCH=${{ steps.extract_branch.outputs.branch }}
          TAG="${VERSION} ${BRANCH}:latest"
          echo $TAG
          ECR_URI=$ECR_REGISTRY/$ECR_REPOSITORY
          docker build -t $ECR_URI:latest .
          docker push $ECR_URI:latest
          docker tag $ECR_URI:latest $ECR_URI:"$TAG"
          docker push $ECR_URI:"$TAG"
