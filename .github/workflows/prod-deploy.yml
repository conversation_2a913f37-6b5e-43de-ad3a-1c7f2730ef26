name: Build, Push to ECR, and Update ECS (Production)

on:
  push:
    branches:
      - production
  workflow_dispatch:
    inputs:
      version:
        description: 'Version'
        required: true

permissions:
  id-token: write
  contents: read

jobs:
  build:

    name: Build and Deploy for Production
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Configure AWS credentials for production
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_ROLE_FOR_OIDC_PROD }}
          aws-region: ${{ vars.AWS_REGION }}

      - name: Extract branch name
        id: extract_branch
        run: echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT

      - name: Login to Amazon ECR (Production)
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push image to Amazon ECR (Production)
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY_PROD }}
          VERSION: ${{ github.event.inputs.version }}
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            VERSION=${{ steps.extract_branch.outputs.branch }}
          else
            VERSION=${GITHUB_REF_NAME}
          fi
          VERSION=$(echo "$VERSION" | sed 's/\//-/g')
          BRANCH=${{ steps.extract_branch.outputs.branch }}
          TAG="${VERSION}+${BRANCH}:latest"
          echo $VERSION
          echo $ECR_REGISTRY
          echo $ECR_REPOSITORY
          echo $BRANCH
          echo $TAG
          ECR_URI=$ECR_REGISTRY/$ECR_REPOSITORY
          echo $ECR_URI
          echo "ECR_URI=$ECR_URI" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV

          # Build and push the Docker image to ECR
          docker build -t $ECR_URI:latest .
          docker push $ECR_URI:latest
          docker tag $ECR_URI:latest $ECR_URI:$VERSION
          docker push $ECR_URI:$VERSION

      - name: Install jq
        run: sudo apt-get install -y jq

      - name: Update ECS task definition (Production)
        id: update_task
        run: |
          echo "Current task definition:"
          aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME_PROD }} | jq
          TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME_PROD }})
          NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | jq --arg IMAGE "${{ env.ECR_URI }}:${{ env.VERSION }}" '.taskDefinition | .containerDefinitions[0].image=$IMAGE | .networkMode="awsvpc" | del(.taskDefinitionArn, .revision, .status, .requiresAttributes, .compatibilities, .registeredAt, .registeredBy)')
          echo "New task definition with updated image and network mode:"
          echo "$NEW_TASK_DEFINITION" | jq
          echo "$NEW_TASK_DEFINITION" > new-task-def.json
          aws ecs register-task-definition --cli-input-json file://new-task-def.json

      - name: Capture new task revision (Production)
        id: capture_revision
        run: |
          NEW_TASK_REVISION=$(aws ecs describe-task-definition --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME_PROD }} | jq .taskDefinition.revision)
          echo "NEW_TASK_REVISION=$NEW_TASK_REVISION" >> $GITHUB_ENV
          echo "New task revision: $NEW_TASK_REVISION"

      - name: Update ECS service (Production)
        run: |
          echo "Updating ECS service to use new task revision: ${{ env.NEW_TASK_REVISION }}"
          aws ecs update-service \
            --cluster ${{ vars.ECS_CLUSTER_NAME_PROD }} \
            --service ${{ vars.ECS_SERVICE_NAME_PROD }} \
            --task-definition ${{ vars.ECS_TASK_DEFINITION_NAME_PROD }}:${{ env.NEW_TASK_REVISION }} \
            --force-new-deployment
