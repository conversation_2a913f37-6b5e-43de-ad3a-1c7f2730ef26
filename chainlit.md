# Welcome to Rezio Playground! 🚀🤖

In order to use this playground you need to install chainlit to your environment.

```bash
pip install chainlit
```

Now get the pubnub details by calling the following api with your intended postman environment.

```bash
curl -X GET "{{url}}web/ai/<agent_id>/Agent/pubnub_user/"
```

You may see following response.

```json
{
    "message": "PubNub user Details fetched successfully",
    "data": {
        "channel_id": "<channel_id>",
        "group_channel_id": "<group_channel_id>",
        "pubnub_user_id": "<pubnub_user_id>",
        "name": "<name>",
        "phone_number": "<phone_number>"
    },
    "error": {}
}
```

Take the channel_id, group_channel_id and pubnub_user_id and add it to `assistant/pubnub_config.py` file. 

Update the URL with your intended environment url.

Now you can use the following command to start the playground.

```bash
chainlit run rezio/assistant/playground/playground.py --port 8005
```

Make sure your containers are up and running.

**Enjoy chatting with the agent 🤖**