# from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
from celery.schedules import crontab
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set the default Django settings module
os.environ.setdefault('CELERY_CONFIG_MODULE', 'app.core.config')

# Create Celery app
app = Celery('newsletter')

# Configure Celery using environment variables with prefix 'CELERY_'
app.config_from_object('app.core.celery_config', namespace='CELERY')

# Load tasks from all registered app modules
app.autodiscover_tasks(['app.tasks'])

# Configure periodic tasks
app.conf.beat_schedule = {
    'check-user-send-datetime': {
        'task': 'app.tasks.user_tasks.check_user_send_datetime',
        'schedule': 5,#crontab(hour=0, minute=0),  # Run every day at midnight (00:00 AM)
        'args': (),
    },
    'check_news_letter_send': {
        'task': 'app.tasks.user_tasks.check_news_letter_send',
        'schedule': 5,#crontab(hour=0, minute=0),  # Run every day at midnight (00:00 AM)
        'args': (),
    }
}

if __name__ == '__main__':
    app.start()
