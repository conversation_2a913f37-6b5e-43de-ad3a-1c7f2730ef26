from sqlalchemy import <PERSON>umn, Inte<PERSON>, String, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Enum as SQLEnum, JSON, UUID, Text, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
import enum
from datetime import datetime
from .database import Base

class NewsletterStatus(str, enum.Enum):
    DRAFT = "DRAFT"
    APPROVED = "APPROVED"
    SENT = "SENT"
    FAILED = "FAILED"

class TaskStatus(str, enum.Enum):
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"

class User(Base):
    __tablename__ = "users"

    id = Column(UUID, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    topics = Column(JSON, default=dict)  # Store as dynamic JSON object with categories
    send_datetime = Column(DateTime(timezone=True), nullable=True)  # Preferred send time with timezone
    recipient_emails = Column(JSON, default=list)  # Store as JSON array
    last_sent_date = Column(DateTime)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationship with newsletters
    newsletters = relationship("Newsletter", back_populates="user")

class Newsletter(Base):
    __tablename__ = "newsletters"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    year = Column(Integer, nullable=False)
    month = Column(Integer, nullable=False)
    week_number = Column(Integer, nullable=False)
    newsletter_title = Column(String, nullable=False)
    content = Column(JSON, nullable=False)
    status = Column(SQLEnum(NewsletterStatus), default=NewsletterStatus.DRAFT)
    created_at = Column(DateTime, default=datetime.utcnow)
    sent_at = Column(DateTime, nullable=True)
    opens = Column(Integer, default=0)
    clicks = Column(Integer, default=0)

    user = relationship("User", back_populates="newsletters")

class TaskExecution(Base):
    __tablename__ = "task_executions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(String, unique=True, index=True, nullable=False)  # Celery task ID
    task_name = Column(String, nullable=False)  # Name of the task
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING)
    args = Column(JSON, nullable=True)  # Task arguments
    kwargs = Column(JSON, nullable=True)  # Task keyword arguments
    result = Column(JSON, nullable=True)  # Task result
    traceback = Column(Text, nullable=True)  # Error traceback if failed
    runtime = Column(Float, nullable=True)  # Task execution time in seconds
    retries = Column(Integer, default=0)  # Number of retries
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<TaskExecution(task_id='{self.task_id}', task_name='{self.task_name}', status='{self.status}')>"