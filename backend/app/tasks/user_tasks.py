import logging
from datetime import datetime, timedelta
from celery import shared_task
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from ..database import SessionLocal
from ..models import TaskExecution, TaskStatus, User
import time
import json
import traceback
from ..celery_app import app as celery_app_v

logger = logging.getLogger(__name__)

def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    finally:
        db.close()

def log_task_execution(task_id, task_name, status, args=None, kwargs=None, result=None,
                      traceback_text=None, runtime=None, retries=0, started_at=None, completed_at=None):
    """Log task execution details to database"""
    db = get_db()
    try:
        # Convert args and kwargs to JSON serializable format
        args_json = json.dumps(args) if args else None
        kwargs_json = json.dumps(kwargs) if kwargs else None
        result_json = json.dumps(result) if result else None

        # Check if task execution record already exists
        task_execution = db.query(TaskExecution).filter(TaskExecution.task_id == task_id).first()

        if task_execution:
            # Update existing record
            task_execution.status = status
            task_execution.result = result_json
            task_execution.traceback = traceback_text
            task_execution.runtime = runtime
            task_execution.retries = retries

            if started_at:
                task_execution.started_at = started_at

            if completed_at:
                task_execution.completed_at = completed_at
        else:
            # Create new record
            task_execution = TaskExecution(
                task_id=task_id,
                task_name=task_name,
                status=status,
                args=args_json,
                kwargs=kwargs_json,
                result=result_json,
                traceback=traceback_text,
                runtime=runtime,
                retries=retries,
                started_at=started_at,
                completed_at=completed_at
            )
            db.add(task_execution)

        db.commit()
        return task_execution
    except Exception as e:
        db.rollback()
        logger.error(f"Error logging task execution: {str(e)}")
    finally:
        db.close()

@celery_app_v.task(bind=True, max_retries=3)
def check_user_send_datetime(self):
    """
    Check if the difference between current date and user's send_datetime is 4 days.
    This task runs every day at midnight (00:00 AM).
    """
    task_id = self.request.id
    task_name = self.name
    start_time = time.time()
    started_at = datetime.utcnow()

    print(f"\n\n===> Task {task_name} started with ID: {task_id} at {started_at}\n\n")

    # Log task started
    log_task_execution(
        task_id=task_id,
        task_name=task_name,
        status=TaskStatus.STARTED,
        started_at=started_at
    )

    try:
        db = get_db()
        current_date = datetime.utcnow().date()

        # Get all users with send_datetime not null
        users = db.query(User).filter(User.send_datetime.isnot(None)).all()

        processed_users = 0
        eligible_users = 0
        result = {"users_processed": len(users), "eligible_users": 0, "details": []}

        for user in users:
            processed_users += 1

            # Check if send_datetime is available
            if not user.send_datetime:
                continue

            # Get the date part of send_datetime
            user_send_date = user.send_datetime.date()

            # Calculate the difference in days
            date_diff = (current_date - user_send_date).days

            # Check if the difference is exactly 4 days
            if date_diff == 4:
                eligible_users += 1

                # Log the eligible user
                logger.info(f"User {user.id} ({user.username}) is eligible for processing. "
                           f"Current date: {current_date}, send_datetime: {user_send_date}, "
                           f"difference: {date_diff} days")

                # Add user to result details
                result["details"].append({
                    "user_id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "send_datetime": user.send_datetime.isoformat() if user.send_datetime else None,
                    "date_difference": date_diff
                })

                # Trigger draft news letter webhook
                send_draft_news_letter.apply_async(args=[user])

        result["eligible_users"] = eligible_users

        # Calculate runtime
        end_time = time.time()
        runtime = end_time - start_time
        completed_at = datetime.utcnow()

        # Log task completion
        log_task_execution(
            task_id=task_id,
            task_name=task_name,
            status=TaskStatus.SUCCESS,
            result=result,
            runtime=runtime,
            retries=self.request.retries,
            started_at=started_at,
            completed_at=completed_at
        )

        logger.info(f"Task completed. Processed {processed_users} users, found {eligible_users} eligible users.")
        print(f"\n\n===> Task {task_name} completed successfully at {completed_at}. Processed {processed_users} users, found {eligible_users} eligible users.\n\n")
        return result

    except Exception as e:
        # Calculate runtime
        end_time = time.time()
        runtime = end_time - start_time
        completed_at = datetime.utcnow()

        # Log task failure
        error_traceback = traceback.format_exc()
        log_task_execution(
            task_id=task_id,
            task_name=task_name,
            status=TaskStatus.FAILURE,
            traceback_text=error_traceback,
            runtime=runtime,
            retries=self.request.retries,
            started_at=started_at,
            completed_at=completed_at
        )

        logger.error(f"Error in check_user_send_datetime task: {str(e)}")
        logger.error(error_traceback)

        # Retry the task
        raise self.retry(exc=e, countdown=60 * 5)  # Retry after 5 minutes

@celery_app_v.task(bind=True, max_retries=3)
def send_draft_news_letter(self, user):
    logger.info(f"Sending draft news letter for {user}")

    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )

    # Filter and format topics
    filtered_topics = []
    for topic in user.topics:
        if topic.get('enabled', False):
            # Create a dictionary with only non-null values
            topic_data = {
                'title': topic['title']
            }

            # Add topics if they exist and are not null
            if topic.get('topics'):
                topic_data['topics'] = topic['topics']

            # Add url if it exists and is not null
            if topic.get('url'):
                topic_data['url'] = topic['url']

            # Only add the topic if it has more than just the title
            if len(topic_data) > 1:
                filtered_topics.append(topic_data)

    #TODO: Need to add webhook