# <PERSON>zio AI Assistant Technical Document

## Overview
The Rezio AI Assistant is a sophisticated multi-agent system designed to handle real estate queries and interactions. The system, named Iris, uses multiple specialized AI agents to process user queries, gather information, and provide relevant responses in the context of real estate transactions.

## Tech Stack
- **Backend Framework**: Django/Python
- **Message Broker**: Celery
- **Real-time Communication**: PubNub
- **AI Models**: OpenAI GPT Models
- **Monitoring**: Langfuse
- **Database**: PostgreSQL (Django ORM)

## Architecture

### System Flow Diagram
```mermaid
sequenceDiagram
    participant User
    participant InputGuardRail
    participant ContextBuilder
    participant Orchestrator
    participant Agents
    participant Aggregator
    participant RealEstateAgent
    participant OutputGuardRail

    User->>InputGuardRail: Send Query
    InputGuardRail->>ContextBuilder: Validated Query
    ContextBuilder->>Orchestrator: Build Context
    Orchestrator->>Agents: Delegate Tasks
    Note over Agents: Property Details Agent
    Note over Agents: Schedule Visit Agent
    Note over Agents: Law Assistance Agent
    Agents->>Orchestrator: Agent Responses
    Orchestrator->>ContextBuilder: Consolidated Info
    ContextBuilder->>RealEstateAgent: Processed Context
    RealEstateAgent->>OutputGuardRail: Formatted Response
    OutputGuardRail->>User: Final Response
```

### Agent Types and Responsibilities
1. **Input Guard Rail**
   - Validates incoming queries
   - Filters inappropriate content
   - Routes queries to appropriate handlers

2. **Context Builder**
   - Analyzes query intent
   - Builds context for orchestrator
   - Manages conversation flow

3. **Orchestrator**
   - Coordinates between specialized agents
   - Assigns tasks based on query context
   - Manages parallel agent execution

4. **Specialized Agents**
   - Property Details Agent: Handles property information
   - Schedule Visit Agent: Manages property viewings
   - Law Assistance Agent: Provides legal information
   - Preference Agent: Tracks user preferences

5. **Real Estate Agent**
   - Formats responses in natural language
   - Maintains consistent persona
   - Handles final user communication

## Monitoring and Debugging

### Langfuse Integration
The system uses Langfuse for monitoring and debugging AI interactions. To access logs:

1. Access the Langfuse dashboard:
```python
LANGFUSE_PUBLIC_KEY = env("LANGFUSE_PUBLIC_KEY")
LANGFUSE_SECRET_KEY = env("LANGFUSE_SECRET_KEY")
```

2. Key metrics to monitor:
   - Response times
   - Agent interactions
   - Error rates
   - Token usage

3. Debugging using Langfuse:
   - Trace specific conversations using conversation_id
   - Monitor agent-to-agent communication
   - Track tool usage and effectiveness

## Key Components

### 1. Message Processing
```python
@shared_task
def run_rezio_ai_agent(pubnub_channel_id: int, message: str):
    pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
    reasoning_agent = IrisReasoningAgent(pubnub_channel=pubnub_channel)
    ai_response = reasoning_agent.reason(message)
    # Process and send response...
```

### 2. Agent Memory Management
```python
@shared_task
def create_multi_agent_memory(pubnub_channel_id: int):
    """Creates memory objects for each sub AI agent"""
    pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
    # Initialize memory for each agent type...
```

## Development Workflow

### Adding New Features
1. Create new agent type in `types.py`
2. Add system prompt in `reasoning_prompts.py`
3. Implement agent logic in `reasoning_agent_service.py`
4. Update memory initialization in `tasks.py`

### Testing
1. Unit tests for individual agents
2. Integration tests for agent communication
3. End-to-end conversation flow tests

## Common Issues and Solutions

1. **Agent Communication Failures**
   - Check memory objects initialization
   - Verify PubNub channel configuration
   - Monitor Langfuse traces for breakpoints

2. **Response Delays**
   - Check Celery worker status
   - Monitor token usage and rate limits
   - Verify database query performance

3. **Memory Management**
   - Regular cleanup of old conversations
   - Proper initialization of agent memories
   - Monitoring memory usage patterns

## Important Links and Resources
- [OpenAI Documentation](https://platform.openai.com/docs)
- [Langfuse Dashboard](https://cloud.langfuse.com)
- [PubNub Documentation](https://www.pubnub.com/docs)
- [Celery Documentation](https://docs.celeryproject.org)

## Contact Information
- AI System Maintainers: [Team Contact]
- DevOps Support: [DevOps Contact]
- System Administrators: [Admin Contact]

## Agent Implementations

### 1. Input Guard Rail Agent
The first line of defense that validates and filters incoming queries.

```python
@observe()  # Langfuse monitoring decorator
def input_guard_rail(self, user_message: dict | List[dict]) -> Tuple[str, str]:
    iris = Iris(user=self.pubnub_channel)
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.INPUT_GUARD_RAIL,
    )
    
    # Extend memory with new message
    if isinstance(user_message, dict):
        user_message = [user_message]
    memory_object.memory.extend(user_message)
    
    # Process through AI
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        json_response=True,
        temperature=0.5,
    )
    memory_object.save()
    return ai_response
```

Key Features:
- Uses Langfuse observation for monitoring
- Maintains conversation memory
- Returns JSON formatted response
- Validates input for inappropriate content

### 2. Context Builder Agent
Analyzes queries and builds context for the orchestrator.

```python
@observe()
def context_builder(self, user_message: dict | List[dict] = None) -> str:
    iris = Iris(user=self.pubnub_channel)
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.CONTEXT_BUILDER,
    )
    
    # Process message and build context
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        json_response=True,
    )
    
    # Handle different response types
    if "thought" in ai_response and "orchestrator" in ai_response:
        message_for_orchestrator = Iris.create_message(
            role=Role.USER,
            actor=AgentType.CONTEXT_BUILDER,
            content=str(ai_response["orchestrator"]),
        )
        orchestrator_response = self.orchestrator(message_for_orchestrator)
        # Continue processing...
```

Key Features:
- Maintains conversation context
- Handles multiple response types
- Coordinates with orchestrator
- Uses ReAct framework for reasoning

### 3. Orchestrator Agent
Coordinates between specialized agents and manages task delegation.

```python
@observe()
def orchestrator(self, user_message: dict | List[dict] = None) -> str:
    iris = Iris(user=self.pubnub_channel)
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.ORCHESTRATOR,
    )
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        temperature=1,
        json_response=True,
    )
    
    # Handle agent delegation
    if "thought" in ai_response and "agents" in ai_response:
        agents_responses = []
        for agent in ai_response["agents"]:
            message_for_agent = Iris.create_message(
                role=Role.USER,
                actor=AgentType.ORCHESTRATOR,
                content=agent["context"],
            )
            
            # Route to appropriate agent
            if agent["agent"] == AgentType.PROPERTY_DETAILS_AGENT:
                response_from_agent = self.property_details_agent(message_for_agent)
            elif agent["agent"] == AgentType.SCHEDULE_VISIT_AGENT:
                response_from_agent = self.schedule_visit_agent(message_for_agent)
            # ... handle other agents
```

Key Features:
- Parallel agent execution
- Dynamic task routing
- Response aggregation
- Memory management

### 4. Specialized Agents

#### Property Details Agent
```python
@observe()
def property_details_agent(self, user_message: dict | List[dict]) -> str:
    iris = Iris(user=self.pubnub_channel)
    tools = Tools()
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.PROPERTY_DETAILS_AGENT,
    )
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        tools=[
            tools.get_agent_portfolio,
            tools.get_property_details,
        ],
    )
```

#### Schedule Visit Agent
```python
@observe()
def schedule_visit_agent(self, user_message: dict | List[dict]) -> str:
    iris = Iris(user=self.pubnub_channel)
    tools = Tools()
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        tools=[tools.send_message_to_seller],
    )
```

### 5. Real Estate Agent (Persona)
Formats responses in natural language with consistent personality.

```python
@observe()
def real_estate_agent(self, user_message: dict | List[dict]) -> str:
    iris = Iris(user=self.pubnub_channel)
    tools = Tools()
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.REAL_ESTATE_AGENT,
    )
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        json_response=True,
        tools=[tools.send_media_to_buyer],
    )
```

### 6. Output Guard Rail
Final validation before sending response to user.

```python
@observe()
def output_guard_rail(self, user_message: dict | List[dict]) -> str:
    iris = Iris(user=self.pubnub_channel)
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.OUTPUT_GUARD_RAIL,
    )
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
    )
```

## Memory Management Implementation

### Memory Creation
```python
@shared_task
def create_multi_agent_memory(pubnub_channel_id: int):
    """
    Creates memory objects for each agent when a new conversation starts
    """
    pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
    messages = Messages.objects.create(buyer=pubnub_channel)
    
    # Create memory for each agent type
    Memory.objects.create(
        agent_type=AgentType.INPUT_GUARD_RAIL,
        messages_id=messages.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.input_guard_rail(),
            )
        ],
    )
    # ... create memory for other agents
```

### Memory Structure
```python
class Memory(models.Model):
    agent_type = models.CharField(max_length=100)
    messages = models.ForeignKey(Messages, on_delete=models.CASCADE)
    memory = models.JSONField(default=list)
    
    class Meta:
        verbose_name_plural = "Memory"
```

## Tools Implementation

The Tools class provides various utilities used by agents:

```python
class Tools:
    def get_property_details(self):
        return {
            "name": "get_property_details",
            "description": "get property details",
            "parameters": {
                "type": "object",
                "properties": {
                    "property_id": {"type": "integer"},
                    "agent_portfolio_id": {"type": "integer"},
                },
                "required": ["property_id", "agent_portfolio_id"],
            },
        }
    
    def send_message_to_seller(self):
        return {
            "name": "send_message_to_seller",
            "description": "send message to seller",
            "parameters": {
                "type": "object",
                "properties": {
                    "message": {"type": "string"},
                },
                "required": ["message"],
            },
        }
```

## Iris Implementation Flow

```mermaid
sequenceDiagram
participant Client
participant PubNubView
participant AIView
participant Tasks
participant IrisReasoningAgent
participant Iris
participant Memory
participant OpenAI
%% Initial Setup
Client->>PubNubView: Register User
PubNubView->>Tasks: create_multi_agent_memory()
Note over Tasks: Creates memory objects for all agents
rect rgba(173, 216, 230, 0.5)
Note over Tasks: Memory Initialization Phase
Tasks->>Memory: Create Input Guard Rail Memory
Note over Memory: System prompt initialization
Tasks->>Memory: Create Context Builder Memory
Note over Memory: Context builder setup
Tasks->>Memory: Create Orchestrator Memory
Note over Memory: Orchestrator configuration
Tasks->>Memory: Create Specialized Agent Memories
Note over Memory: Agent-specific memory setup
Tasks->>Memory: Create Output Guard Rail Memory
Note over Memory: Output validation setup
end
%% Message Processing
Client->>AIView: Send Message
AIView->>Tasks: run_rezio_ai_agent()
Tasks->>IrisReasoningAgent: reason(message)
rect rgba(144, 238, 144, 0.5)
Note over IrisReasoningAgent: Agent Processing Phase
IrisReasoningAgent->>Iris: Create Iris Instance
loop Agent Processing
Note over IrisReasoningAgent: Process Each Agent
IrisReasoningAgent->>Memory: Get Agent Memory
Note over Memory: Retrieve conversation history
IrisReasoningAgent->>Iris: chat_completion()
Note over Iris: OpenAI API interaction
Iris->>OpenAI: API Call
OpenAI-->>Iris: Response
Note over Iris: Process API response
Iris-->>IrisReasoningAgent: Processed Response
IrisReasoningAgent->>Memory: Update Agent Memory
Note over Memory: Save updated context
end
end
IrisReasoningAgent-->>Tasks: AI Response
Tasks->>Client: Final Response
```

### Iris Usage Flow

1. **Initial Setup**
```python
# When a new user connects
@receiver(post_save, sender=PubNubChannel)
def create_multi_agent_memory(sender, instance, created, **kwargs):
    if created:
        # Initialize memory for all agents using Iris 
        create_multi_agent_memory(pubnub_channel_id=instance.id)
```

2. **Memory Creation**
```python
# Each agent gets its own memory with system prompt
Memory.objects.create(
    agent_type=AgentType.INPUT_GUARD_RAIL,
    messages_id=main_message_object.id,
    memory=[
        Iris.create_message(
            role=Role.SYSTEM,
            content=multi_agent_prompts.input_guard_rail(),
        )
    ],
)
```

3. **Message Processing**
```python
# When a message is received
@shared_task
def run_rezio_ai_agent(pubnub_channel_id: int, message: str):
    pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
    reasoning_agent = IrisReasoningAgent(pubnub_channel=pubnub_channel)
    ai_response = reasoning_agent.reason(message)
```

4. **Agent Communication**
```python
# Each agent uses Iris  for OpenAI communication
def input_guard_rail(self, user_message: dict | List[dict]):
    iris = Iris(user=self.pubnub_channel)
    memory_object = Memory.objects.get(
        messages=self.messages,
        agent_type=AgentType.INPUT_GUARD_RAIL,
    )
    
    ai_response = iris.chat_completion(
        model=Model.GPT_4o_mini,
        messages=memory_object.memory,
        json_response=True,
    )
```

### Key Points About Iris 

1. **Centralized Configuration**
   - Single point for OpenAI API configuration
   - Consistent error handling
   - Standardized response formatting

2. **Memory Management**
   - Each agent gets isolated memory
   - Conversation history tracking
   - System prompts maintenance

3. **Tool Integration**
   - Standardized tool registration
   - Function calling interface
   - Tool response handling

4. **Response Processing**
   - JSON validation
   - Error handling
   - Response formatting

### Common Iris  Methods

1. **Message Creation**
```python
@classmethod
def create_message(cls, role: Role, content: str = None, actor: Actor = None):
    message = {"role": role}
    if actor:
        message["name"] = actor
    message["content"] = content
    return message
```

2. **Chat Completion**
```python
def chat_completion(self, model: Model, messages: List[dict], 
                   tools: list = [], temperature: int = 1):
    completion_object = {
        "model": model,
        "temperature": temperature,
        "messages": messages,
    }
    if tools:
        completion_object["tools"] = tools
    return self.llm_client.chat.completions.create(**completion_object)
```
