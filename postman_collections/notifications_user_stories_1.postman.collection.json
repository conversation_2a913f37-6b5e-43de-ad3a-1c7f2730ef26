{"info": {"_postman_id": "b1c2d3e4-us1-4807-821b-810d22cdc47f", "name": "Rezio Notifications - User Stories Part 1", "description": "Cases 1-4: Welcome, Property Addition, Agent Selection, and Access Management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Case 1: Welcome Notification", "item": [{"name": "Create Welcome Notification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/welcome", "host": ["{{base_url}}"], "path": ["notifications", "welcome"]}, "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"usr_123\",\n    \"role\": \"AGENT\"\n}"}, "description": "Create welcome notification after user signup (Case 1)"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/welcome", "host": ["{{base_url}}"], "path": ["notifications", "welcome"]}, "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"usr_123\",\n    \"role\": \"AGENT\"\n}"}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "cookie": [], "body": "{\"status\": \"success\", \"message\": \"Welcome notification created successfully\", \"data\": {\"id\": \"not_001\", \"type\": \"WELCOME\", \"title\": \"Welcome to Rezio\", \"message\": \"Welcome to Rezio! We're excited to have you on board.\", \"is_read\": false, \"created_at\": \"2024-01-20T10:00:00Z\", \"relative_time\": \"just now\", \"data\": {}, \"show_actions\": false}, \"error\": {}}"}, {"name": "Error - <PERSON><PERSON><PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/welcome", "host": ["{{base_url}}"], "path": ["notifications", "welcome"]}, "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"usr_123\",\n    \"role\": \"INVALID\"\n}"}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "cookie": [], "body": "{\"status\": \"error\", \"message\": \"Invalid role provided\", \"data\": {}, \"error\": {\"error_message\": \"Role must be either AGENT or INVESTOR\", \"field\": \"role\", \"value\": \"INVALID\"}}"}, {"name": "Error - User Not Found", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/welcome", "host": ["{{base_url}}"], "path": ["notifications", "welcome"]}, "body": {"mode": "raw", "raw": "{\n    \"user_id\": \"invalid_user\",\n    \"role\": \"AGENT\"\n}"}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "cookie": [], "body": "{\"status\": \"error\", \"message\": \"User not found\", \"data\": {}, \"error\": {\"error_message\": \"User with specified ID does not exist\", \"field\": \"user_id\", \"value\": \"invalid_user\"}}"}]}, {"name": "Get Welcome Notification", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/{{role}}/?type=WELCOME", "host": ["{{base_url}}"], "path": ["notifications", "{{role}}"], "query": [{"key": "type", "value": "WELCOME"}]}, "description": "Get welcome notification after signup (Case 1)"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/{{role}}/?type=WELCOME", "host": ["{{base_url}}"], "path": ["notifications", "{{role}}"], "query": [{"key": "type", "value": "WELCOME"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "cookie": [], "body": "{\"status\": \"success\", \"message\": \"Notifications retrieved successfully\", \"data\": {\"count\": 1, \"next\": null, \"previous\": null, \"unread_count\": 1, \"results\": [{\"id\": \"not_001\", \"type\": \"WELCOME\", \"title\": \"Welcome to Rezio\", \"message\": \"Welcome to Rezio! We're excited to have you on board.\", \"is_read\": false, \"created_at\": \"2024-01-20T10:00:00Z\", \"relative_time\": \"just now\", \"data\": {}, \"show_actions\": false}]}, \"error\": {}}"}, {"name": "Error - Unauthorized", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer invalid_token", "type": "text"}], "url": {"raw": "{{base_url}}/notifications/{{role}}/?type=WELCOME", "host": ["{{base_url}}"], "path": ["notifications", "{{role}}"], "query": [{"key": "type", "value": "WELCOME"}]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "cookie": [], "body": "{\"status\": \"error\", \"message\": \"Authentication failed\", \"data\": {}, \"error\": {\"error_message\": \"Invalid or expired token\", \"error_code\": \"invalid_token\"}}"}]}]}]}