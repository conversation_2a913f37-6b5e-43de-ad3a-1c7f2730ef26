#!/bin/bash

# Start FastAPI application
echo "Starting Exercise Evaluator FastAPI application..."

# Run the FastAPI application with uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Note: 
# --host 0.0.0.0   : Makes the server accessible from any IP address
# --port 8000      : Runs the server on port 8000
# --reload         : Automatically reloads the server when code changes (for development)

# For production, remove the --reload flag and consider using:
# uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
