docker-build:
	docker-compose -f local.yml build

docker-up:
	docker-compose -f local.yml up

docker-up-d:
	docker-compose -f local.yml up -d

docker-superuser:
	docker-compose -f local.yml run --rm django python -m rezio.manage createsuperuser

docker-migrations:
	docker-compose -f local.yml run --rm django python -m rezio.manage makemigrations #properties --empty --name update_financial_attributes_name

docker-migrate:
	docker-compose -f local.yml run --rm django python -m rezio.manage migrate

production-docker-build:
	docker-compose -f production.yml build

production-docker-up:
	docker-compose -f production.yml up