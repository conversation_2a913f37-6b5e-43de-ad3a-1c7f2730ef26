docker-build:
	docker compose -f local.yml build --no-cache

docker-up:
	docker compose -f local.yml up

docker-up-d:
	docker compose -f local.yml up -d

docker-superuser:
	docker compose -f local.yml run --rm django python -m rezio.manage createsuperuser

docker-migrations:
	docker compose -f local.yml run --rm django python -m rezio.manage makemigrations

docker-migrate:
	docker compose -f local.yml run --rm django python -m rezio.manage migrate

production-docker-build:
	docker compose -f production.yml build

production-docker-up:
	docker compose -f production.yml up

setup-pre-commit:
	pre-commit install
