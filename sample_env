
# General
# ------------------------------------------------------------------------------
# DJANGO_READ_DOT_ENV_FILE=True
DJANGO_SETTINGS_MODULE=rezio.rezio.settings
DJANGO_SECRET_KEY=FIiFr5cEeXDVewpo4v3WOM8b8NjLL8eN4HqNEAXFs2XYGKWl4XJ3wKDppgmfSnsA
DJANGO_ADMIN_URL=mZzOjD1c9DYoKWXKjDLguge1p95a0WIF/
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Security
# ------------------------------------------------------------------------------
# TIP: better off using DNS, however, redirect is OK too
DJANGO_SECURE_SSL_REDIRECT=False

# Email
# ------------------------------------------------------------------------------
DJANGO_SERVER_EMAIL=

MAILGUN_API_KEY=
MAILGUN_DOMAIN=


# AWS
# ------------------------------------------------------------------------------
DJANGO_AWS_ACCESS_KEY_ID=
DJANGO_AWS_SECRET_ACCESS_KEY=
DJANGO_AWS_STORAGE_BUCKET_NAME=

# django-allauth
# ------------------------------------------------------------------------------
DJANGO_ACCOUNT_ALLOW_REGISTRATION=True

# Gunicorn
# ------------------------------------------------------------------------------
WEB_CONCURRENCY=4


# Redis
# ------------------------------------------------------------------------------
REDIS_URL=redis://redis:6379/0

# JWT Authentication (SimpleJWT)
#______________________________________________________________________--
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ACCESS_TOKEN_LIFETIME=60  # minutes
JWT_REFRESH_TOKEN_LIFETIME=30  # days

# Firebase (Legacy - being replaced by JWT)
#______________________________________________________________________--
FIREBASE_ADMIN_CREDENTIALS_PATH=/rezio
ROLES=Agent,Investor,Seller
INVESTOR_TYPES=Buyer,Seller,Buyer&Seller

#DUBAI PULSE CRED
#----------------------------------------------------------------------
DUBAI_PULSE_CLIENT_ID = ANY4qLm7sKlgn58MDHJTurk9yO9aqQkg
DUBAI_PULSE_CLIENT_SECRET = 6q3TFDIz6vygBHp4

#Firebase tokens
#---------------------------------------------------------------------
type=service_account,
FIREBASE_PROJECT_ID= rezio-ai-dev
FIREBASE_PRIVATE_KEY_ID= 519ebd0daaaa5e5d2dbd6868234fc3671bad718b
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL= <EMAIL>
FIREBASE_CLIENT_ID= 107775594518832370205
FIREBASE_AUTH_URI= https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI= https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL= https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL= https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-lysep%40rezio-ai-dev.iam.gserviceaccount.com
FIREBASE_UNIVERSE_DOMAIN= googleapis.com

#PROPERTY_MONITOR_TOKEN
#-----------------------------------------------------------------------------
PROPERTY_MONITOR_API_TOKEN=79f77ad5a34e242ad982ca88992a0f76fdac988b
PROPERTY_MONITOR_API_KEY=pVbApEOtJqa6fInVf7dJ649Vr0SvNC5xa0pFwZKw
PROPERTY_MONITOR_HOST=https://demoapi.propertymonitor.com/pm/v1/
PROPERTY_MONITOR_LOCATION_ENDPOINT=locations
PROPERTY_MONITOR_ADDRESS_ENDPOINT=address-lookup
PROPERTY_MONITOR_SEARCH_KEY=keyword=
PROPERTY_MONITOR_SEARCH_LOCATION_KEY=locationId=
PROPERTY_MONITOR_SEARCH_EMIRATE_KEY=emirate=
PROPERTY_MONITOR_PAGE_KEY=page=
PROPERTY_MONITOR_SEARCH_UNIT_KEY=unitNumber=
PROPERTY_MONITOR_VOLUME_TREND_ENDPOINT=price-volume-trend
PROPERTY_MONITOR_MASTER_DEVELOPMENT_KEY=masterDevelopment=
PROPERTY_MONITOR_PROPERTY_TYPE_KEY=propertyType=
PROPERTY_MONITOR_CATEGORY_KEY=category=
PROPERTY_MONITOR_SEARCH_LOCATION_NAME_KEY=location=

#AWS CREDS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_S3_REGION_NAME=
AWS_CLOUDFRONT_DOMAIN=
AWS_CLOUDFRONT_KEY_ID=
AWS_CLOUDFRONT_KEY=

PRICE_MAX_LIMIT=

FILE_SIZE_LIMIT_IN_MB=5
MAXIMUM_EXPIRY_TIME=3600

DLD_FEES_PERCENTAGE = 4
AGENT_COMMISSION_PERCENTAGE = 2
BANK_PROCESSING_FEE_PERCENTAGE = 0.25

DEBUG=False

DEFAULT_COVER_PHOTO=https://rezio-static-files.s3.me-central-1.amazonaws.com/default-images/Banner+Image.svg

CORS_ALLOW_ALL_ORIGINS=
ALLOWED_SELECTIVE_AGENT_COUNT=3

RATE_LIMIT=10
RATE_LIMIT_TIME_FRAME=minute

DEFAULT_CURRENCY_CODE=AED
DEFAULT_PROPERTY_CURRENCY_CODE=USD

EXCHANGE_RATE_API_URL=
EXCHANGE_RATE_API_KEY=
EXCHANGE_RATE_API_CONVERSION_ENDPOINT=pair
AI_USER_NAME=
AI_SECRET_KEY=
AI_PUBLIC_KEY=

NEWS_CACHE_TIMEOUT=3600
NEWS_MAX_AGE_DAYS=60
NEWS_FETCH_HOUR=0
NEWS_FETCH_MINUTE=0





# OpenAI
OPENAI_API_KEY=

# Google custom search
GOOGLE_CUSTOM_SEARCH_API_KEY=
SEARCH_ENGINE_ID=

# Server specific
BACKEND_URL =
WEBSOCKET_DOMAIN =


# Comet chat
COMETCHAT_APP_ID =
COMETCHAT_API_KEY =
COMETCHAT_REGION =


# Whatsapp
WHATSAPP_BUSINESS_PHONE_NUMBER_ID =
WHATSAPP_WEBHOOK_VERIFICATION_TOKEN =
WHATSAPP_ACCESS_TOKEN =


# Backend specific
REZIO_BACKEND_URL =
X_REZIO_TOKEN =


# Langfuse
LANGFUSE_SECRET_KEY=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_HOST=


# PubNub
PUBNUB_PUBLISH_KEY=******************************************
PUBNUB_SUBSCRIBE_KEY=******************************************
PUBNUB_SECRET_KEY=sec-c-NmZiNmI3YjEtYTkwNC00MWQ4LTlhYjQtMzg0ZTQwZmJkY2Vj

# Twilio
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_VERIFY_SERVICE_SID=

