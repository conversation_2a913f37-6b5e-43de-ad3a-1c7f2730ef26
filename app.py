import streamlit as st
import os

from db_operations import init_db, close_db_connection, create_evaluation_record, \
    update_evaluation_results
from evaluator.code_evaluator import CodeEvaluator
from dotenv import load_dotenv
import re
import logging
import time
import json
import base64
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

try:
    init_db()
except Exception as e:
    st.error(f"Database initialization error: {e}")
    st.stop()

def cleanup():
    close_db_connection()

# Register cleanup handler
st.session_state['_cleanup_handler'] = cleanup

# Set page config with custom icon and title
st.set_page_config(
    page_title="Code Evaluation Tool",
    page_icon="🔍",
    layout="wide"
)

# Custom CSS for better styling
st.markdown("""
    <style>
    .main {
        padding: 2rem;
    }
    .stButton>button {
        width: 100%;
        height: 3em;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
    }
    .stButton>button:hover {
        background-color: #45a049;
    }
    .info-box {
        background-color: rgba(240, 242, 246, 0.8);
        padding: 1.5rem;
        border-radius: 8px;
        margin: 1rem 0;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .info-box h3 {
        color: #4CAF50;
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }
    .info-box p {
        color: #333;
        line-height: 1.5;
        margin-bottom: 0;
    }
    /* Dark mode adjustments */
    @media (prefers-color-scheme: dark) {
        .info-box {
            background-color: rgba(30, 30, 30, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .info-box h3 {
            color: #4CAF50;
        }
        .info-box p {
            color: #e0e0e0;
        }
    }
    @media (prefers-color-scheme: dark) {
        .login-container {
            background-color: rgba(30, 30, 30, 0.8);
        }
    }
    .user-menu {
        background-color: rgba(240, 242, 246, 0.8);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .user-menu:hover {
        background-color: rgba(220, 222, 226, 0.8);
    }
    @media (prefers-color-scheme: dark) {
        .user-menu {
            background-color: rgba(30, 30, 30, 0.8);
        }
        .user-menu:hover {
            background-color: rgba(50, 50, 50, 0.8);
        }
    }
    .stApp {
        padding-top: 0;
    }
    /* Fix for login page spacing */
    .stElementContainer.element-container.st-emotion-cache-kj6hex.eu6p4el1 {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    .stElementContainer.element-container {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    /* Remove extra spacing from form elements */
    .stForm > div {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    </style>
""", unsafe_allow_html=True)

# Initialize session state for authentication
if 'authenticated' not in st.session_state:
    st.session_state.authenticated = False
if 'user_email' not in st.session_state:
    st.session_state.user_email = ""
if 'evaluation_results' not in st.session_state:
    st.session_state.evaluation_results = None
if 'error_message' not in st.session_state:
    st.session_state.error_message = None
if 'auth_token' not in st.session_state:
    st.session_state.auth_token = None

# Get credentials from environment variables
ADMIN_EMAIL = os.getenv("ADMIN_EMAIL", "")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "")

# Check if credentials are set
if not ADMIN_EMAIL or not ADMIN_PASSWORD:
    st.error("⚠️ Authentication credentials not configured. Please set ADMIN_EMAIL and ADMIN_PASSWORD environment variables.")
    st.stop()

# Function to encode authentication data
def encode_auth_data(email):
    data = {
        "email": email,
        "exp": (datetime.now() + timedelta(days=1)).timestamp()
    }
    json_data = json.dumps(data)
    return base64.b64encode(json_data.encode()).decode()

# Function to decode and validate authentication data
def decode_auth_data(encoded_data):
    try:
        json_data = base64.b64decode(encoded_data.encode()).decode()
        data = json.loads(json_data)
        
        # Check if token is expired
        if data["exp"] < datetime.now().timestamp():
            return None
        
        return data["email"]
    except:
        return None

# Strict authentication check
def verify_authentication():
    # Check if we have both session auth and valid token
    if not st.session_state.authenticated or not st.session_state.auth_token:
        st.session_state.authenticated = False
        st.session_state.user_email = ""
        st.session_state.auth_token = None
        return False
    
    # Verify token is still valid
    email = decode_auth_data(st.session_state.auth_token)
    if not email or email != st.session_state.user_email:
        st.session_state.authenticated = False
        st.session_state.user_email = ""
        st.session_state.auth_token = None
        return False
    
    return True

# Check for authentication token in query params
if not st.session_state.authenticated:
    # Try to get auth from query params
    if "auth" in st.query_params:
        auth_data = st.query_params["auth"]
        email = decode_auth_data(auth_data)
        if email:
            st.session_state.authenticated = True
            st.session_state.user_email = email
            st.session_state.auth_token = auth_data
            st.session_state.evaluation_id = None
            st.rerun()

# Verify authentication before proceeding
if not verify_authentication():
    # Clear any existing query params
    if st.query_params:
        st.query_params.clear()
        st.rerun()

# Login page
if not st.session_state.authenticated:
    # Remove any default Streamlit padding
    st.markdown("""
        <style>
        .stApp {
            padding-top: 0;
        }
        </style>
    """, unsafe_allow_html=True)
    
    # Title with minimal spacing
    st.markdown("""
        <div style='text-align: center; padding: 0.5rem 1rem; margin: 0;'>
            <h1 style='margin: 0; padding: 0;'>🔒 Code Evaluation Tool</h1>
            <p style='color: #666; margin: 0.5rem 0 0 0;'>Senior Software Engineer Interview Assessment</p>
        </div>
    """, unsafe_allow_html=True)
    
    # Center the login container
    col1, col2, col3 = st.columns([2, 1, 2])
    with col2:
        st.markdown("<h2 style='text-align: center; margin: 0 0 1rem 0;'>Login</h2>", unsafe_allow_html=True)
        
        with st.form("login_form"):
            email = st.text_input("Email", type="default", key="login_email")
            password = st.text_input("Password", type="password", key="login_password")
            
            # Create columns for button alignment
            _, button_col = st.columns([2, 1])
            with button_col:
                login_button = st.form_submit_button("🔑 Login")
            
            if login_button:
                if email == ADMIN_EMAIL and password == ADMIN_PASSWORD:
                    # Create authentication token
                    auth_token = encode_auth_data(email)
                    
                    # Set session state
                    st.session_state.authenticated = True
                    st.session_state.user_email = email
                    st.session_state.auth_token = auth_token
                    st.session_state.evaluation_id = None
                    
                    # Set authentication token in query params
                    st.query_params["auth"] = auth_token
                    
                    st.success("✅ Login successful!")
                    st.rerun()
                else:
                    st.error("❌ Invalid email or password")
    
    # Footer
    st.markdown("""
        <div style='text-align: center; padding: 2rem; color: #666;'>
            <p>🔒 Secure Evaluation Tool | AUB Recruitment</p>
        </div>
    """, unsafe_allow_html=True)
    
    st.stop()

# Main application (only shown after authentication)
# Header with icon and user menu
st.markdown("""
    <div style='display: flex; justify-content: space-between; align-items: center; padding: 1rem;'>
        <div style='text-align: left;'>
            <h1>🔍 Code Evaluation Tool</h1>
            <p style='color: #666;'>Senior Software Engineer Interview Assessment</p>
        </div>
        <div style='text-align: right;'>
            <div class='user-menu' style='display: flex; align-items: center; gap: 0.5rem;'>
                <span style='font-size: 1.2rem;'>👤</span>
                <span style='font-weight: bold;'>{}</span>
                <div style='margin-left: 0.5rem;'>
                    <form action="?logout=true" target="_self">
                        <button type="submit" style='background: none; border: none; color: #666; cursor: pointer; font-size: 1.2rem; padding: 0;' title='Logout'>⏻</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
""".format(st.session_state.user_email), unsafe_allow_html=True)

# Handle logout from query params
if "logout" in st.query_params:
    st.session_state.authenticated = False
    st.session_state.user_email = ""
    st.session_state.auth_token = None
    st.session_state.evaluation_results = None
    st.session_state.error_message = None
    st.session_state.evaluation_id = None
    # Clear all query parameters
    st.query_params.clear()
    # Add cache control headers to prevent caching
    st.markdown("""
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
    """, unsafe_allow_html=True)
    st.rerun()

# Main content
col1, col2 = st.columns([2, 1])

with col1:
    st.markdown("""
        <div class='info-box'>
            <h3>📋 About</h3>
            <p>This tool evaluates practical coding assignments for senior software engineer positions.</p>
            <p>It analyzes code quality, architecture, and best practices in the submitted repository.</p>
        </div>
    """, unsafe_allow_html=True)

with col2:
    st.markdown("""
        <div class='info-box'>
            <h3>ℹ️ Quick Guide</h3>
            <p>1. Enter the repository URL<br>
               2. Select the framework<br>
               3. Click Evaluate to start</p>
        </div>
    """, unsafe_allow_html=True)

# Input form
with st.form("evaluation_form"):
    # Repository URL input with validation
    repo_url = st.text_input(
        "Repository URL",
        placeholder="https://github.com/aub-recruitment/your-repo",
        help="Enter the full GitHub repository URL"
    )
    
    # Branch selection
    branch = st.text_input(
        "Branch",
        value="main",
        help="Enter the branch name to evaluate"
    )
    
    # Submit button
    submit_button = st.form_submit_button("🚀 Start Evaluation")

# Handle form submission
if submit_button:
    if not repo_url:
        st.error("⚠️ Please enter a repository URL")
    else:
        # Validate repository URL format
        if not re.match(r'^https?://github\.com/[\w-]+/[\w-]+/?$', repo_url):
            st.error("⚠️ Invalid GitHub repository URL format")
        else:
            try:
                # Create initial evaluation record
                if st.session_state.evaluation_id == None:
                    evaluation_id = create_evaluation_record(
                        repository_url=repo_url,
                        branch=branch,
                        evaluated_by=st.session_state.user_email
                    )

                    # Store evaluation ID in session state and query params
                    st.session_state.evaluation_id = evaluation_id
                    st.query_params["evaluation_id"] = evaluation_id

                # Initialize evaluator with token from environment
                evaluator = CodeEvaluator()
                
                # Create containers for different sections
                progress_container = st.container()
                progress_bar = st.progress(0)
                
                structure_container = st.container()
                structure_placeholder = structure_container.empty()
                
                report_container = st.container()
                report_placeholder = report_container.empty()
                
                # Define callback for repository structure and progress
                def update_structure(hierarchy_markdown, progress, processed_files, total_files, detected_framework=None):
                    # Update structure using Streamlit's native expander
                    with structure_placeholder.container():
                        with st.expander("📁 Repository Structure", expanded=True):
                            # Add framework info inside expander
                            if detected_framework:
                                framework_icon = {
                                    'python-django': '🐍',
                                    'react': '⚛️',
                                    'node-express': '🟢',
                                    'angular': '🅰️',
                                    'vue': '💚'
                                }.get(detected_framework, '📦')
                                
                                st.markdown(f"""
                                    <div style='display: flex; justify-content: flex-end; align-items: center; margin-bottom: 1rem;'>
                                        <div style='display: flex; align-items: center; gap: 0.5rem;'>
                                            <span style='font-size: 1.5rem;'>{framework_icon}</span>
                                            <span style='color: #3498db; font-weight: bold;'>{detected_framework}</span>
                                        </div>
                                    </div>
                                """, unsafe_allow_html=True)
                            
                            # Add scanning progress inside the expander
                            st.markdown(f"""
                                <div style='
                                    padding: 1rem;
                                    background-color: #2c3e50;
                                    border-radius: 0.5rem;
                                    margin: 1rem 0;
                                    color: #ecf0f1;
                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                    border-left: 4px solid #3498db;
                                '>
                                    <div style='display: flex; justify-content: space-between; margin-bottom: 0.5rem;'>
                                        <span style='color: #bdc3c7;'>Files Processed:</span>
                                        <span style='color: #2ecc71; font-weight: bold;'>{processed_files}/{total_files}</span>
                                    </div>
                                    <div style='display: flex; justify-content: space-between; margin-bottom: 0.5rem;'>
                                        <span style='color: #bdc3c7;'>Progress:</span>
                                        <span style='color: #f1c40f; font-weight: bold;'>{progress:.1f}%</span>
                                    </div>
                                    <div style='
                                        width: 100%;
                                        height: 8px;
                                        background-color: #34495e;
                                        border-radius: 4px;
                                        margin-top: 1rem;
                                        overflow: hidden;
                                    '>
                                        <div style='
                                            width: {progress}%;
                                            height: 100%;
                                            background: linear-gradient(90deg, #3498db, #2ecc71);
                                            border-radius: 4px;
                                            transition: width 0.3s ease;
                                        '></div>
                                    </div>
                                </div>
                            """, unsafe_allow_html=True)
                            
                            # Display repository structure
                            st.markdown(f"""
                                <div style='
                                    font-family: monospace;
                                    white-space: pre-wrap;
                                    line-height: 1.5;
                                    background-color: #2c3e50;
                                    padding: 1rem;
                                    border-radius: 0.3rem;
                                    color: #ecf0f1;
                                '>
{hierarchy_markdown}
                                </div>
                            """, unsafe_allow_html=True)
                    
                    # Update progress bar
                    progress_bar.progress(progress / 100)
                
                # Define callback for report updates
                def update_report(section_title, section_content):
                    with report_placeholder.container():
                        # Special styling for problem statement section
                        if "Problem Statement" in section_title:
                            st.markdown(f"""
                                <div style='
                                    background-color: #1e1e1e;
                                    border-radius: 8px;
                                    padding: 1.2rem;
                                    margin: 1rem 0;
                                    border-left: 4px solid #3498db;
                                '>
                                    <h3 style='
                                        color: #3498db;
                                        margin: 0 0 1rem 0;
                                        font-size: 1.3rem;
                                        font-weight: 600;
                                    '>{section_title}</h3>
                                    <div style='
                                        color: #e0e0e0;
                                        font-size: 1rem;
                                        line-height: 1.6;
                                    '>
                                        {section_content.strip().replace('• ', '<br>• ').lstrip('<br>')}
                                    </div>
                                </div>
                            """, unsafe_allow_html=True)
                        else:
                            # Regular styling for other sections
                            st.markdown(f"""
                                <div style='
                                    background-color: #2c3e50;
                                    padding: 1.5rem;
                                    border-radius: 0.5rem;
                                    margin: 1rem 0;
                                    color: #ecf0f1;
                                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                    border-left: 4px solid #e74c3c;
                                '>
                                    <h3 style='color: #3498db; margin-top: 0;'>{section_title}</h3>
                                    <div style='margin-top: 1rem;'>
                                        {section_content}
                                    </div>
                                </div>
                            """, unsafe_allow_html=True)
                
                # Show progress
                with st.spinner("🔄 Starting evaluation..."):
                    # Step 1: Repository Access (0-25%)
                    with progress_container:
                        st.info("🔍 Step 1: Accessing repository...")
                    try:
                        # Run evaluation with update callbacks
                        report = evaluator.evaluate_repository(
                            repo_url=repo_url,
                            branch=branch,
                            update_callback=update_structure,
                            report_callback=update_report
                        )
                        
                        # Success message
                        with progress_container:
                            st.success("✅ Evaluation completed successfully!")

                        st.session_state.evaluation_id = None
                        st.query_params.pop("evaluation_id", None)

                        # Display the complete report
                        with report_placeholder.container():
                            # Split the report into sections and display each with proper styling
                            sections = report.split('\n\n## ')
                            for section in sections:
                                if section.startswith('# '):  # Remove the initial # if present
                                    section = section[2:]
                                if section.startswith('## '):  # Remove the initial ## if present
                                    section = section[3:]
                                
                                # Split into title and content
                                parts = section.split('\n', 1)
                                if len(parts) == 2:
                                    title, content = parts
                                    st.markdown(f"""
                                        <div style='
                                            background-color: #2c3e50;
                                            padding: 1.5rem;
                                            border-radius: 0.5rem;
                                            margin: 1rem 0;
                                            color: #ecf0f1;
                                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                                            border-left: 4px solid #e74c3c;
                                        '>
                                            <h3 style='color: #3498db; margin-top: 0;'>{title}</h3>
                                            <div style='margin-top: 1rem;'>
                                                {content.strip()}
                                            </div>
                                        </div>
                                    """, unsafe_allow_html=True)
                        
                        # Add download button (with complete report including structure)
                        st.download_button(
                            label="📥 Download Report",
                            data=report,
                            file_name=f"evaluation_report_{int(time.time())}.md",
                            mime="text/markdown"
                        )
                        
                    except Exception as e:
                        error_msg = str(e)
                        with progress_container:
                            st.error("❌ Error during evaluation")
                            progress_bar.progress(0)  # Reset progress on error
                        if "404" in error_msg:
                            st.error("⚠️ Repository not found. Please check the URL and repository visibility.")
                        elif "401" in error_msg or "403" in error_msg:
                            st.error("⚠️ Authentication failed. Please check your GitHub token.")
                        elif "rate limit" in error_msg.lower():
                            st.error("⚠️ GitHub API rate limit exceeded. Please try again later.")
                        elif "timeout" in error_msg.lower():
                            st.error("⚠️ Operation timed out. Please try again with a smaller repository.")
                        else:
                            st.error(f"⚠️ Error during evaluation: {error_msg}")

                        update_evaluation_results(st.session_state.evaluation_id, error_msg, 'Failed')
                        st.session_state.evaluation_id = None
                        st.query_params.pop("evaluation_id", None)

            except Exception as e:
                error_msg = str(e)
                st.error(f"⚠️ Error during evaluation: {error_msg}")
                progress_bar = st.progress(0)
                update_evaluation_results(st.session_state.evaluation_id, error_msg, 'Failed')
                st.session_state.evaluation_id = None
                st.query_params.pop("evaluation_id", None)



# Footer
st.markdown("""
    <div style='text-align: center; padding: 2rem; color: #666;'>
        <p>🔒 Secure Evaluation Tool | AUB Recruitment</p>
    </div>
""", unsafe_allow_html=True) 