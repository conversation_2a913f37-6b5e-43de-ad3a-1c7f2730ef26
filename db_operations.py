import logging
import os
import uuid
from contextlib import contextmanager
import streamlit as st
import psycopg2
from dotenv import load_dotenv
from psycopg2.extras import RealDictCursor

load_dotenv()

DATABASE_CONFIG = {
    'host': os.getenv('POSTGRES_HOST', 'localhost'),
    'database': os.getenv('POSTGRES_DB', 'exercise_evaluation'),
    'user': os.getenv('POSTGRES_USER', 'postgres'),
    'password': os.getenv('POSTGRES_PASSWORD', ''),
    'port': os.getenv('POSTGRES_PORT', '5432')
}

logger = logging.getLogger(__name__)

def init_db_connection():
    """Initialize database connection"""
    try:
        conn = psycopg2.connect(**DATABASE_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Error initializing database connection: {e}")
        raise


@contextmanager
def get_cursor(commit=False):
    """
    Context manager for database operations.
    Uses existing connection from session state or creates a new one.
    """
    # Initialize connection if it doesn't exist
    if 'db_connection' not in st.session_state:
        st.session_state.db_connection = init_db_connection()

    try:
        # Get connection from session state
        connection = st.session_state.db_connection

        # Create cursor
        cursor = connection.cursor(cursor_factory=RealDictCursor)
        try:
            yield cursor
            if commit:
                connection.commit()
        finally:
            cursor.close()

    except psycopg2.OperationalError as e:
        # If connection is closed or broken, create a new one
        logger.warning("Database connection lost, attempting to reconnect...")
        st.session_state.db_connection = init_db_connection()

        # Retry the operation with new connection
        cursor = st.session_state.db_connection.cursor(cursor_factory=RealDictCursor)
        try:
            yield cursor
            if commit:
                st.session_state.db_connection.commit()
        finally:
            cursor.close()

    except Exception as e:
        logger.error(f"Database error: {e}")
        # Ensure any failed transaction is rolled back
        if 'db_connection' in st.session_state:
            st.session_state.db_connection.rollback()
        raise


def close_db_connection():
    """Close the database connection if it exists"""
    if 'db_connection' in st.session_state:
        try:
            st.session_state.db_connection.close()
            del st.session_state.db_connection
        except Exception as e:
            logger.error(f"Error closing database connection: {e}")


# def get_db_connection():
#     """Create and return a database connection"""
#     try:
#         conn = psycopg2.connect(**DATABASE_CONFIG)
#         return conn
#     except Exception as e:
#         logger.error(f"Database connection error: {e}")
#         raise


def init_db():
    """Initialize database tables"""
    with get_cursor(commit=True) as cur:
        # Create evaluations table
        cur.execute("""
                    CREATE TABLE IF NOT EXISTS evaluations
                    ( 
                        id VARCHAR(255) PRIMARY KEY,
                        repository_url VARCHAR(255) NOT NULL,
                        branch VARCHAR(100),
                        framework VARCHAR(100),
                        problem_statement TEXT,
                        file_structure TEXT,
                        results TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        evaluated_by VARCHAR(100),
                        status VARCHAR(50)
                    )
        """
        )

def create_evaluation_record(repository_url, branch, evaluated_by):
    """Create initial evaluation record with status 'pending'"""
    try:
        evaluation_id = str(uuid.uuid4())
        with get_cursor(commit=True) as cur:
            cur.execute("""
                INSERT INTO evaluations 
                (id, repository_url, branch, evaluated_by, status)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING id
            """, (evaluation_id, repository_url, branch, evaluated_by, 'In Progress'))
            logger.info(f"Evaluation record created with ID: {evaluation_id}")
            return evaluation_id
    except Exception as e:
        logger.error(f"Error creating evaluation record: {e}")
        raise

def update_evaluation_framework(evaluation_id, framework):
    """Update the framework for an evaluation"""
    with get_cursor(commit=True) as cur:
        cur.execute("""
            UPDATE evaluations 
            SET framework = %s
            WHERE id = %s
        """, (framework, evaluation_id))
        logger.info(f"Framework updated for evaluation ID: {evaluation_id}")

def update_evaluation_structure(evaluation_id, file_structure):
    """Update the file structure for an evaluation"""
    with get_cursor(commit=True) as cur:
        cur.execute("""
            UPDATE evaluations 
            SET file_structure = %s
            WHERE id = %s
        """, (file_structure, evaluation_id))

def update_evaluation_problem_statement(evaluation_id, problem_statement):
    """Update the problem statement for an evaluation"""
    with get_cursor(commit=True) as cur:
        cur.execute("""
            UPDATE evaluations 
            SET problem_statement = %s
            WHERE id = %s
        """, (problem_statement, evaluation_id))

def update_evaluation_results(evaluation_id, results, status='Completed'):
    """Update the results and status for an evaluation"""
    with get_cursor(commit=True) as cur:
        cur.execute("""
            UPDATE evaluations 
            SET results = %s,
                status = %s
            WHERE id = %s
        """, (results, status, evaluation_id))

# def update_evaluation_status(evaluation_id, status='Failed'):
#     """Update the status for an evaluation"""
#     with get_cursor(commit=True) as cur:
#         cur.execute("""
#             UPDATE evaluations
#             SET status = %s
#             WHERE id = %s
#         """, (status, evaluation_id))
