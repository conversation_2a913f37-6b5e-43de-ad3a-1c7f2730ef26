from typing import AsyncGenerator, List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sse_starlette.sse import EventSourceResponse

from app.core.config import settings
from app.db.database import get_db
from app.db.models import Evaluation
from app.schemas.evaluation import (
    EvaluationCreate, 
    Evaluation as EvaluationSchema,
    EvaluationUpdate,
    EvaluationResult
)
from app.services.evaluator.code_evaluator import CodeEvaluator
from app.api.deps import get_current_user

router = APIRouter()

@router.post("/evaluations/", response_model=EvaluationSchema, status_code=status.HTTP_201_CREATED)
async def create_evaluation(
    evaluation: EvaluationCreate,
    db: Session = Depends(get_db),
    # current_user: str = Depends(get_current_user)
) -> Any:
    """
    Create a new evaluation
    """
    # Create evaluation record in database
    db_evaluation = Evaluation(
        repository_url=str(evaluation.repository_url),
        branch=evaluation.branch,
        # evaluated_by=current_user,
        status="In Progress"
    )
    
    db.add(db_evaluation)
    db.commit()
    db.refresh(db_evaluation)
    
    return db_evaluation

@router.get("/evaluations/", response_model=List[EvaluationSchema])
async def read_evaluations(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> Any:
    """
    Retrieve evaluations
    """
    evaluations = db.query(Evaluation).filter(
        Evaluation.evaluated_by == current_user
    ).offset(skip).limit(limit).all()
    
    return evaluations

@router.get("/evaluations/{evaluation_id}", response_model=EvaluationSchema)
async def read_evaluation(
    evaluation_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> Any:
    """
    Get a specific evaluation by ID
    """
    evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    if evaluation.evaluated_by != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return evaluation

@router.put("/evaluations/{evaluation_id}", response_model=EvaluationSchema)
async def update_evaluation(
    evaluation_id: str,
    evaluation_update: EvaluationUpdate,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> Any:
    """
    Update an evaluation
    """
    db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not db_evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    if db_evaluation.evaluated_by != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Update evaluation fields
    update_data = evaluation_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_evaluation, field, value)
    
    db.commit()
    db.refresh(db_evaluation)
    
    return db_evaluation

@router.delete("/evaluations/{evaluation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_evaluation(
    evaluation_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> None:
    """
    Delete an evaluation
    """
    db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not db_evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    if db_evaluation.evaluated_by != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    db.delete(db_evaluation)
    db.commit()

@router.get("/evaluations/{evaluation_id}/stream", response_class=EventSourceResponse)
async def stream_evaluation(
    evaluation_id: str,
    db: Session = Depends(get_db),
    # current_user: str = Depends(get_current_user)
) -> Any:
    """
    Stream evaluation results in real-time
    """
    evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    # if evaluation.evaluated_by != current_user:
    #     raise HTTPException(
    #         status_code=status.HTTP_403_FORBIDDEN,
    #         detail="Not enough permissions"
    #     )
    
    # Initialize evaluator
    evaluator = CodeEvaluator()
    
    # Create streaming response
    return EventSourceResponse(
        evaluator.stream_evaluation(
            repo_url=evaluation.repository_url,
            branch=evaluation.branch,
            evaluation_id=evaluation_id,
            db=db
        )
    )

@router.get("/evaluations/{evaluation_id}/problem-statement/stream", response_class=EventSourceResponse)
async def stream_problem_statement(
    evaluation_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> Any:
    """
    Stream problem statement extraction in real-time
    """
    evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    if evaluation.evaluated_by != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Initialize evaluator
    evaluator = CodeEvaluator()
    
    # Create streaming response
    return EventSourceResponse(
        evaluator.stream_problem_statement(
            repo_url=evaluation.repository_url,
            branch=evaluation.branch,
            evaluation_id=evaluation_id,
            db=db
        )
    )

@router.get("/evaluations/{evaluation_id}/report/stream", response_class=EventSourceResponse)
async def stream_evaluation_report(
    evaluation_id: str,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
) -> Any:
    """
    Stream evaluation report generation in real-time
    """
    evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    if not evaluation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Evaluation not found"
        )
    
    if evaluation.evaluated_by != current_user:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Initialize evaluator
    evaluator = CodeEvaluator()
    
    # Create streaming response
    return EventSourceResponse(
        evaluator.stream_evaluation_report(
            repo_url=evaluation.repository_url,
            branch=evaluation.branch,
            evaluation_id=evaluation_id,
            db=db
        )
    )
