from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import evaluations
from app.core.config import settings

app = FastAPI(
    title="Exercise Evaluator API",
    description="API for evaluating coding exercises and generating assessment reports",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
# app.include_router(auth.router, prefix="/api", tags=["Authentication"])
app.include_router(evaluations.router, prefix="/api", tags=["Evaluations"])

@app.get("/")
async def root():
    return {
        "message": "Welcome to Exercise Evaluator API",
        "docs": "/docs",
        "version": "1.0.0"
    }
