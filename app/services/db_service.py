from typing import List, Dict, Any, Optional
import logging

from sqlalchemy.orm import Session
import uuid

from app.db.models import Evaluation

logger = logging.getLogger(__name__)

class DBService:
    """
    Service for database operations
    """
    @staticmethod
    def create_evaluation_record(
        db: Session, 
        repository_url: str, 
        branch: str, 
        evaluated_by: str
    ) -> str:
        """
        Create initial evaluation record with status 'In Progress'
        
        Args:
            db: Database session
            repository_url: URL of the repository to evaluate
            branch: Branch to evaluate
            evaluated_by: Email of the user who initiated the evaluation
            
        Returns:
            str: ID of the created evaluation record
        """
        try:
            evaluation_id = str(uuid.uuid4())
            db_evaluation = Evaluation(
                id=evaluation_id,
                repository_url=repository_url,
                branch=branch,
                evaluated_by=evaluated_by,
                status='In Progress'
            )
            
            db.add(db_evaluation)
            db.commit()
            db.refresh(db_evaluation)
            
            logger.info(f"Evaluation record created with ID: {evaluation_id}")
            return evaluation_id
        except Exception as e:
            logger.error(f"Error creating evaluation record: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def update_evaluation_framework_and_structure(
        db: Session,
        evaluation_id: str, 
        framework: str, 
        file_structure: str
    ) -> None:
        """
        Update the framework and file structure for an evaluation
        
        Args:
            db: Database session
            evaluation_id: ID of the evaluation to update
            framework: Detected framework
            file_structure: File structure of the repository
        """
        try:
            db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
            
            if db_evaluation:
                db_evaluation.framework = framework
                db_evaluation.file_structure = file_structure
                db.commit()
                
                logger.info(f"Framework and file structure updated for evaluation ID: {evaluation_id}")
        except Exception as e:
            logger.error(f"Error updating evaluation framework and structure: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def update_evaluation_problem_statement(
        db: Session,
        evaluation_id: str, 
        problem_statement: str
    ) -> None:
        """
        Update the problem statement for an evaluation
        
        Args:
            db: Database session
            evaluation_id: ID of the evaluation to update
            problem_statement: Extracted problem statement
        """
        try:
            db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
            
            if db_evaluation:
                db_evaluation.problem_statement = problem_statement
                db.commit()
                
                logger.info(f"Problem statement updated for evaluation ID: {evaluation_id}")
        except Exception as e:
            logger.error(f"Error updating evaluation problem statement: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def update_evaluation_results(
        db: Session,
        evaluation_id: str, 
        results: str, 
        status: str = 'Completed'
    ) -> None:
        """
        Update the results and status for an evaluation
        
        Args:
            db: Database session
            evaluation_id: ID of the evaluation to update
            results: Evaluation results
            status: Status of the evaluation (default: 'Completed')
        """
        try:
            db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
            
            if db_evaluation:
                db_evaluation.results = results
                db_evaluation.status = status
                db.commit()
                
                logger.info(f"Results updated for evaluation ID: {evaluation_id}")
        except Exception as e:
            logger.error(f"Error updating evaluation results: {e}")
            db.rollback()
            raise
    
    @staticmethod
    def get_evaluation(db: Session, evaluation_id: str) -> Optional[Evaluation]:
        """
        Get an evaluation by ID
        
        Args:
            db: Database session
            evaluation_id: ID of the evaluation to get
            
        Returns:
            Optional[Evaluation]: Evaluation record if found, None otherwise
        """
        return db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
    
    @staticmethod
    def get_evaluations_by_user(
        db: Session, 
        user_email: str, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Evaluation]:
        """
        Get evaluations by user email
        
        Args:
            db: Database session
            user_email: Email of the user
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Evaluation]: List of evaluation records
        """
        return db.query(Evaluation).filter(
            Evaluation.evaluated_by == user_email
        ).offset(skip).limit(limit).all()
    
    @staticmethod
    def delete_evaluation(db: Session, evaluation_id: str) -> bool:
        """
        Delete an evaluation by ID
        
        Args:
            db: Database session
            evaluation_id: ID of the evaluation to delete
            
        Returns:
            bool: True if deleted, False otherwise
        """
        try:
            db_evaluation = db.query(Evaluation).filter(Evaluation.id == evaluation_id).first()
            
            if db_evaluation:
                db.delete(db_evaluation)
                db.commit()
                logger.info(f"Evaluation deleted: {evaluation_id}")
                return True
            
            return False
        except Exception as e:
            logger.error(f"Error deleting evaluation: {e}")
            db.rollback()
            raise
