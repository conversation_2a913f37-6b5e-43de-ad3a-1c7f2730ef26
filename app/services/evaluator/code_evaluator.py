"""Code evaluator module for analyzing repositories using LlamaIndex with streaming support."""

import os
import logging
import requests
import json
import asyncio
import time
from typing import List, Tuple, Dict, Any, Optional, AsyncGenerator
import base64
from pathlib import Path
import re
from sqlalchemy.orm import Session

from llama_index.core import VectorStoreIndex, Settings
from llama_index.llms.ollama import Ollama
from llama_index.embeddings.ollama import OllamaEmbedding
from llama_index.core import Document
from llama_index.core.callbacks import CallbackManager, TokenCountingHandler, LlamaDebugHandler

from app.services.db_service import DBService
from app.services.evaluator.framework_prompts import PYTHON_PROMPT, REACT_PROMPT, NODE_PROMPT, ANGULAR_PROMPT, FLUTTER_PROMPT, BASE_PROMPT

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodeEvaluator:
    """Evaluates code repositories using LlamaIndex and CodeLlama with streaming support."""

    def __init__(self):
        """Initialize the code evaluator with CodeLlama model."""
        self.model = os.getenv("EVALUATION_MODEL", "qwen3:latest")
        self.model_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        
        # Set up the LLM and embedding model
        self.llm = Ollama(
            model=self.model,
            request_timeout=300,  # 5 minutes timeout
            temperature=0.1,      # Lower temperature for more focused responses
            context_window=4096,   # Limit context window to prevent memory issues
            base_url=self.model_url,
        )
        self.embed_model = OllamaEmbedding(
            model_name=self.model,
            base_url=self.model_url,
            request_timeout=60    # 1 minute timeout for embeddings
        )
        
        # Configure global settings
        Settings.llm = self.llm
        Settings.embed_model = self.embed_model
        
        # Initialize callback manager for streaming
        self.callback_manager = CallbackManager([TokenCountingHandler(), LlamaDebugHandler()])

    def _prepare_evaluation_prompts(self, readme_content: str, framework: str) -> str:
        """
        Prepare evaluation prompt based on framework.
        
        Args:
            readme_content: README content
            framework: Framework type (e.g., "python-django", "react", etc.)
            
        Returns:
            Combined evaluation prompt
        """
        # Get framework-specific prompt
        if framework == "python-django":
            framework_prompt = PYTHON_PROMPT
        elif framework == "react":
            framework_prompt = REACT_PROMPT
        elif framework == "node-express":
            framework_prompt = NODE_PROMPT
        elif framework == "angular":
            framework_prompt = ANGULAR_PROMPT
        elif framework == "flutter":
            framework_prompt = FLUTTER_PROMPT
        else:
            framework_prompt = ""
        
        # Combine base prompt with framework-specific prompt
        combined_prompt = f"""
{BASE_PROMPT}

{framework_prompt}

Please analyze the following codebase and provide a comprehensive evaluation report.
The report should be well-structured and include specific examples from the code where relevant.

README Content:
{readme_content}

Please provide your evaluation in a clear, structured format with specific examples and line numbers where applicable.
"""
        
        return combined_prompt

    def _build_file_hierarchy(self, file_paths: List[str]) -> Dict:
        """
        Build a hierarchical structure of files and directories.
        
        Args:
            file_paths: List of file paths
            
        Returns:
            Dictionary representing the file hierarchy
        """
        hierarchy = {}
        
        for path in file_paths:
            parts = path.split('/')
            current = hierarchy
            
            for part in parts[:-1]:  # Process directories
                if part not in current:
                    current[part] = {}
                current = current[part]
                
            current[parts[-1]] = None  # Add file
            
        return hierarchy

    def _format_hierarchy(self, hierarchy: Dict, level: int = 0) -> str:
        """
        Format the file hierarchy as a markdown string.
        
        Args:
            hierarchy: File hierarchy dictionary
            level: Current indentation level
            
        Returns:
            Formatted markdown string
        """
        result = []
        indent = "  " * level
        
        for name, content in sorted(hierarchy.items()):
            if content is None:  # It's a file
                result.append(f"{indent}└─ {name}")
            else:  # It's a directory
                result.append(f"{indent}📁 {name}/")
                # Add vertical line for directory contents
                child_indent = "  " * (level + 1)
                child_content = self._format_hierarchy(content, level + 1)
                if child_content:
                    result.append(child_content)
                
        return "\n".join(result)

    async def _fetch_repository_content(self, owner: str, repo: str, branch: str, token: str) -> Tuple[List[Document], str]:
        """
        Fetch repository content using GitHub API asynchronously.
        
        Args:
            owner: Repository owner
            repo: Repository name
            branch: Branch name
            token: GitHub token
            
        Returns:
            Tuple of (List of documents, file hierarchy markdown)
        """
        headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        # Get repository tree
        tree_url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{branch}?recursive=1"
        response = await asyncio.to_thread(requests.get, tree_url, headers=headers)
        
        if response.status_code != 200:
            raise ValueError(f"Error fetching repository tree: {response.status_code} - {response.text}")
            
        tree_data = response.json()
        documents = []
        file_paths = []
        hierarchy = {}
        
        # Calculate total files to process
        total_files = sum(1 for item in tree_data.get("tree", []) 
                         if item["type"] == "blob" and self._should_process_file(item["path"]))
        processed_files = 0
        
        # Process each file in the tree
        for item in tree_data.get("tree", []):
            if item["type"] == "blob" and self._should_process_file(item["path"]):
                try:
                    # Update progress
                    processed_files += 1
                    
                    # Get file content
                    content_url = f"https://api.github.com/repos/{owner}/{repo}/contents/{item['path']}?ref={branch}"
                    content_response = await asyncio.to_thread(requests.get, content_url, headers=headers)
                    
                    if content_response.status_code == 200:
                        content_data = content_response.json()
                        if content_data.get("content"):
                            # Decode base64 content
                            content = base64.b64decode(content_data["content"]).decode("utf-8")
                            
                            # Create document
                            doc = Document(
                                text=content,
                                metadata={
                                    "file_name": item["path"],
                                    "file_type": Path(item["path"]).suffix,
                                    "file_size": len(content)
                                }
                            )
                            documents.append(doc)
                            file_paths.append(item["path"])
                            
                            # Update hierarchy
                            parts = item["path"].split('/')
                            current = hierarchy
                            
                            for part in parts[:-1]:  # Process directories
                                if part not in current:
                                    current[part] = {}
                                current = current[part]
                                
                            current[parts[-1]] = None  # Add file
                            
                except Exception as e:
                    logger.warning(f"Error processing file {item['path']}: {str(e)}")
                    continue
        
        # Final hierarchy formatting
        hierarchy_markdown = self._format_hierarchy(hierarchy)
                    
        return documents, hierarchy_markdown

    def _should_process_file(self, file_path: str) -> bool:
        """
        Determine if a file should be processed.
        
        Args:
            file_path: Path of the file
            
        Returns:
            True if file should be processed, False otherwise
        """
        # Skip binary files and large files
        if file_path.endswith((".png", ".jpg", ".jpeg", ".gif", ".ico", ".pdf", ".zip", ".tar", ".gz")):
            return False
            
        # Skip hidden files and directories
        if any(part.startswith(".") for part in file_path.split("/")):
            return False
            
        # Skip node_modules and other large directories
        if any(part in ("node_modules", "venv", "__pycache__", ".git", "dist", "build") for part in file_path.split("/")):
            return False
            
        # Skip specific file types
        if file_path.endswith((".log", ".lock", ".min.js", ".min.css")):
            return False
            
        return True

    async def _verify_token(self, token: str) -> None:
        """
        Verify that the GitHub token is valid and has the necessary permissions.
        
        Args:
            token: GitHub token
            
        Raises:
            ValueError: If token is invalid or missing required scopes
        """
        headers = {"Authorization": f"token {token}"}
        
        # Check token validity by making a request to the GitHub API
        response = await asyncio.to_thread(requests.get, "https://api.github.com/user", headers=headers)
        
        if response.status_code == 401:
            raise ValueError("Invalid GitHub token. Please check if the token is correct and not expired.")
        elif response.status_code == 403:
            raise ValueError("GitHub token lacks necessary permissions. Please ensure it has 'repo' scope.")
        elif response.status_code != 200:
            raise ValueError(f"Error verifying GitHub token: {response.status_code} - {response.text}")
            
        # Check token scopes
        if "X-OAuth-Scopes" in response.headers:
            scopes = response.headers["X-OAuth-Scopes"].split(", ")
            
            # Check for required scopes
            required_scopes = ["repo"]
            missing_scopes = [scope for scope in required_scopes if scope not in scopes]
            
            if missing_scopes:
                raise ValueError(f"GitHub token missing required scopes: {', '.join(missing_scopes)}")
        
    def _extract_readme_content(self, documents: List[Document]) -> str:
        """
        Extract README content from repository documents.

        Args:
            documents: List of repository documents

        Returns:
            README content as string
        """
        readme_content = "No README found"

        for doc in documents:
            if doc.metadata.get("file_name", "").lower() == "readme.md":
                readme_content = doc.text
                break

        return readme_content
        
    def _parse_github_url(self, repo_url: str) -> Tuple[str, str]:
        """
        Parse GitHub URL to extract owner and repository name.
        
        Args:
            repo_url: GitHub repository URL
            
        Returns:
            Tuple of (owner, repository name)
            
        Raises:
            ValueError: If URL is not a valid GitHub repository URL
        """
        # Remove trailing slash if present
        repo_url = repo_url.rstrip('/')
        
        # Extract owner and repo from URL
        match = re.match(r'https?://github\.com/([^/]+)/([^/]+)', repo_url)
        
        if not match:
            raise ValueError(f"Invalid GitHub repository URL: {repo_url}")
            
        owner, repo = match.groups()
        return owner, repo

    def _detect_framework(self, documents: List[Document]) -> str:
        """
        Detect the framework used in the repository.
        
        Args:
            documents: List of repository documents
            
        Returns:
            Detected framework name
        """
        # Framework detection indicators
        framework_indicators = {
            'react': {
                'files': ['jsx', 'tsx', 'react'],
                'directories': ['components', 'containers'],
                'dependencies': ['react', 'react-dom', 'react-router', 'redux']
            },
            'angular': {
                'files': ['ts', 'component.ts', 'module.ts', 'service.ts'],
                'directories': ['app', 'components', 'services'],
                'dependencies': ['@angular/core', '@angular/common', '@angular/router']
            },
            'vue': {
                'files': ['vue', 'vuex'],
                'directories': ['components', 'views', 'store'],
                'dependencies': ['vue', 'vuex', 'vue-router']
            },
            'python-django': {
                'files': ['py', 'django', 'settings.py', 'urls.py', 'views.py', 'models.py'],
                'directories': ['migrations', 'templates', 'static'],
                'dependencies': ['django', 'djangorestframework']
            },
            'node-express': {
                'files': ['js', 'express', 'routes', 'middleware'],
                'directories': ['routes', 'controllers', 'models'],
                'dependencies': ['express', 'mongoose', 'body-parser']
            }
        }
        
        # Initialize scores for each framework
        framework_scores = {framework: 0 for framework in framework_indicators.keys()}
        
        # Extract file paths and content
        file_paths = [doc.metadata.get('file_name', '') for doc in documents]
        
        # Find package.json and requirements.txt content
        package_json_content = None
        requirements_txt_content = None
        readme_content = None
        
        for doc in documents:
            file_name = doc.metadata.get('file_name', '').lower()
            if file_name == 'package.json':
                package_json_content = doc.text
            elif file_name == 'requirements.txt':
                requirements_txt_content = doc.text
            elif file_name == 'readme.md':
                readme_content = doc.text
        
        # Score frameworks based on file patterns
        for framework, indicators in framework_indicators.items():
            # Check for framework-specific files
            for file_pattern in indicators['files']:
                if any(file_pattern in path.lower() for path in file_paths):
                    framework_scores[framework] += 2

            # Check for framework-specific directories
            for directory in indicators['directories']:
                if any(f"{directory}/" in path.lower() for path in file_paths):
                    framework_scores[framework] += 1

        # Check dependencies in package.json
        if package_json_content:
            try:
                package_data = json.loads(package_json_content)
                all_deps = {
                    **package_data.get('dependencies', {}),
                    **package_data.get('devDependencies', {})
                }
                for framework, indicators in framework_indicators.items():
                    for dep in indicators['dependencies']:
                        if dep in all_deps:
                            framework_scores[framework] += 3
            except json.JSONDecodeError:
                pass

        # Check dependencies in requirements.txt
        if requirements_txt_content:
            requirements = requirements_txt_content.lower().split('\n')
            for framework, indicators in framework_indicators.items():
                for dep in indicators['dependencies']:
                    if any(dep.lower() in req.lower() for req in requirements):
                        framework_scores[framework] += 3

        # Check README content for framework mentions
        if readme_content:
            readme_lower = readme_content.lower()
            for framework in framework_indicators.keys():
                # Remove special characters and check for framework name
                clean_framework = framework.replace('-', ' ').lower()
                if clean_framework in readme_lower:
                    framework_scores[framework] += 1

        # Get the framework with the highest score
        detected_framework = max(framework_scores.items(), key=lambda x: x[1])
        
        # If no clear framework is detected (score = 0), default to python-django
        if detected_framework[1] == 0:
            logger.warning("No framework clearly detected, defaulting to python-django")
            return 'python-django'
            
        logger.info(f"Detected framework: {detected_framework[0]} with score {detected_framework[1]}")
        return detected_framework[0]

    async def _get_initial_readme_content(self, owner: str, repo: str, token: str) -> str:
        """
        Get README content from the initial commit.
        
        Args:
            owner: Repository owner
            repo: Repository name
            token: GitHub token
            
        Returns:
            README content from initial commit
        """
        headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        try:
            # Get list of commits
            commits_url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {"per_page": 1, "page": 1}  # Get only the first page with one item
            response = await asyncio.to_thread(requests.get, commits_url, headers=headers, params=params)
            response.raise_for_status()
            
            # Get total number of pages
            if "Link" in response.headers:
                last_page = int(re.search(r'page=(\d+)>; rel="last"', response.headers["Link"]).group(1))
                # Get the last page to find the initial commit
                params["page"] = last_page
                response = await asyncio.to_thread(requests.get, commits_url, headers=headers, params=params)
                response.raise_for_status()
            
            commits = response.json()
            if not commits:
                return "No commits found"
            
            # Get the last commit (which is the initial commit)
            initial_commit_sha = commits[-1]["sha"]
            
            # Get the tree of the initial commit
            commit_url = f"https://api.github.com/repos/{owner}/{repo}/git/commits/{initial_commit_sha}"
            response = await asyncio.to_thread(requests.get, commit_url, headers=headers)
            response.raise_for_status()
            tree_sha = response.json()["tree"]["sha"]
            
            # Get the tree contents
            tree_url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{tree_sha}?recursive=1"
            response = await asyncio.to_thread(requests.get, tree_url, headers=headers)
            response.raise_for_status()
            
            # Find README file
            readme_blob = None
            for item in response.json()["tree"]:
                if item["path"].lower() == "readme.md":
                    readme_blob = item["sha"]
                    break
                
            if not readme_blob:
                return "No README found in initial commit"
            
            # Get README content
            blob_url = f"https://api.github.com/repos/{owner}/{repo}/git/blobs/{readme_blob}"
            response = await asyncio.to_thread(requests.get, blob_url, headers=headers)
            response.raise_for_status()
            
            content = base64.b64decode(response.json()["content"]).decode("utf-8")
            return content
            
        except Exception as e:
            logger.warning(f"Error fetching initial README: {str(e)}")
            return "Error fetching initial README"

    async def stream_evaluation(self, repo_url: str, branch: str, evaluation_id: str, db: Session) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream the entire evaluation process.
        
        Args:
            repo_url: URL of the GitHub repository
            branch: Repository branch to analyze
            evaluation_id: ID of the evaluation record
            db: Database session
            
        Yields:
            Dict containing event data for SSE
        """
        try:
            # Initial message
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Starting evaluation process..."
                })
            }
            
            # Parse GitHub URL
            owner, repo = self._parse_github_url(repo_url)
            
            # Get token from environment
            token = os.getenv("GITHUB_TOKEN")
            if not token:
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "message": "GitHub token is required. Please set the GITHUB_TOKEN environment variable."
                    })
                }
                return
            
            # Verify token
            try:
                yield {
                    "event": "message",
                    "data": json.dumps({
                        "type": "status",
                        "message": "Verifying GitHub token..."
                    })
                }
                await self._verify_token(token)
            except ValueError as e:
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "message": str(e)
                    })
                }
                return
            
            # Fetch repository content
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Fetching repository content..."
                })
            }
            
            documents, file_hierarchy = await self._fetch_repository_content(owner, repo, branch, token)
            
            if not documents:
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "message": f"No readable files found in repository: {repo_url}"
                    })
                }
                return
            
            # Update file structure in database
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "file_structure",
                    "content": file_hierarchy
                })
            }
            
            # Detect framework
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Detecting framework..."
                })
            }
            
            framework = self._detect_framework(documents)
            
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "framework",
                    "framework": framework
                })
            }
            
            # Update framework in database
            DBService.update_evaluation_framework_and_structure(db, evaluation_id, framework, file_hierarchy)
            
            # Get initial README content
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Fetching initial README content..."
                })
            }
            
            initial_readme_content = await self._get_initial_readme_content(owner, repo, token)
            
            # Stream problem statement
            async for event in self.stream_problem_statement(repo_url, branch, evaluation_id, db, documents, initial_readme_content):
                yield event
            
            # Stream evaluation report
            async for event in self.stream_evaluation_report(repo_url, branch, evaluation_id, db, documents, initial_readme_content, framework):
                yield event
            
            # Final message
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Evaluation completed successfully!"
                })
            }
            
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.error(f"Error during evaluation: {error_msg}")
            
            yield {
                "event": "error",
                "data": json.dumps({
                    "message": f"Error during evaluation: {error_msg}"
                })
            }
            
            # Update evaluation status in database
            DBService.update_evaluation_results(db, evaluation_id, f"Error during evaluation: {error_msg}", 'Failed')

    async def stream_problem_statement(
        self, 
        repo_url: str, 
        branch: str, 
        evaluation_id: str, 
        db: Session,
        documents: Optional[List[Document]] = None,
        initial_readme_content: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream problem statement extraction.
        
        Args:
            repo_url: URL of the GitHub repository
            branch: Repository branch to analyze
            evaluation_id: ID of the evaluation record
            db: Database session
            documents: Optional list of documents (if already fetched)
            initial_readme_content: Optional README content (if already fetched)
            
        Yields:
            Dict containing event data for SSE
        """
        try:
            # Initial message
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Analyzing problem statement..."
                })
            }
            
            # If documents and README content are not provided, fetch them
            if not documents or not initial_readme_content:
                # Parse GitHub URL
                owner, repo = self._parse_github_url(repo_url)
                
                # Get token from environment
                token = os.getenv("GITHUB_TOKEN")
                if not token:
                    yield {
                        "event": "error",
                        "data": json.dumps({
                            "message": "GitHub token is required. Please set the GITHUB_TOKEN environment variable."
                        })
                    }
                    return
                
                # Verify token
                try:
                    await self._verify_token(token)
                except ValueError as e:
                    yield {
                        "event": "error",
                        "data": json.dumps({
                            "message": str(e)
                        })
                    }
                    return
                
                # Fetch repository content if not provided
                if not documents:
                    documents, _ = await self._fetch_repository_content(owner, repo, branch, token)
                
                # Get initial README content if not provided
                if not initial_readme_content:
                    initial_readme_content = await self._get_initial_readme_content(owner, repo, token)
            
            # Create a focused index for README analysis
            readme_index = VectorStoreIndex.from_documents(
                [Document(text=initial_readme_content)],
                embed_model=self.embed_model,
                llm=self.llm
            )
            
            # Query for problem statement with bullet points
            problem_statement_prompt = f"""
            You are a concise problem summarizer.
            Your task is to read a README file and return:
            - A summary (1-2 lines describing the project)
            - A list of core problem statements the project aims to solve
            
            Only output the following format and nothing else:
            
            
            Summary: 
                <your summary>
            
            Core Problem Statements:
                1. <first>
                2. <second>
                3. <third>
            
            Do NOT include any internal thoughts, reasoning, explanation, or tags like <think> etc.
            """

            # Create streaming query engine
            readme_query_engine = readme_index.as_query_engine(
                streaming=True,
                llm=self.llm,
            )
            
            # Stream the problem statement
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "problem_statement_start",
                    "message": "Generating problem statement..."
                })
            }
            
            try:
                # Execute query with streaming
                response = readme_query_engine.query(problem_statement_prompt)
                
                # Stream the response
                problem_statement = ""
                
                # Use the streaming_response attribute for token-by-token streaming
                for token in response.streaming_response:
                    # Clean token (remove <think> tags)
                    token = re.sub(r'<think>.*?</think>', '', token, flags=re.DOTALL).strip()
                    
                    if token:
                        problem_statement += token
                        
                        yield {
                            "event": "message",
                            "data": json.dumps({
                                "type": "problem_statement_chunk",
                                "content": token
                            })
                        }
                        
                        # Small delay to simulate natural typing
                        await asyncio.sleep(0.01)
                
                # Update problem statement in database
                DBService.update_evaluation_problem_statement(db, evaluation_id, problem_statement)
                
                # Final message
                yield {
                    "event": "message",
                    "data": json.dumps({
                        "type": "problem_statement_complete",
                        "content": problem_statement
                    })
                }
                
            except Exception as e:
                error_msg = str(e) if str(e) else "Unknown error"
                logger.error(f"Error analyzing problem statement: {error_msg}")
                
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "message": f"Error analyzing problem statement: {error_msg}"
                    })
                }
            
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.error(f"Error during problem statement extraction: {error_msg}")
            
            yield {
                "event": "error",
                "data": json.dumps({
                    "message": f"Error during problem statement extraction: {error_msg}"
                })
            }

    async def stream_evaluation_report(
        self, 
        repo_url: str, 
        branch: str, 
        evaluation_id: str, 
        db: Session,
        documents: Optional[List[Document]] = None,
        initial_readme_content: Optional[str] = None,
        framework: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Stream evaluation report generation.
        
        Args:
            repo_url: URL of the GitHub repository
            branch: Repository branch to analyze
            evaluation_id: ID of the evaluation record
            db: Database session
            documents: Optional list of documents (if already fetched)
            initial_readme_content: Optional README content (if already fetched)
            framework: Optional detected framework (if already detected)
            
        Yields:
            Dict containing event data for SSE
        """
        try:
            # Initial message
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "status",
                    "message": "Generating evaluation report..."
                })
            }
            
            # If documents, README content, or framework are not provided, fetch them
            if not documents or not initial_readme_content or not framework:
                # Parse GitHub URL
                owner, repo = self._parse_github_url(repo_url)
                
                # Get token from environment
                token = os.getenv("GITHUB_TOKEN")
                if not token:
                    yield {
                        "event": "error",
                        "data": json.dumps({
                            "message": "GitHub token is required. Please set the GITHUB_TOKEN environment variable."
                        })
                    }
                    return
                
                # Verify token
                try:
                    await self._verify_token(token)
                except ValueError as e:
                    yield {
                        "event": "error",
                        "data": json.dumps({
                            "message": str(e)
                        })
                    }
                    return
                
                # Fetch repository content if not provided
                if not documents:
                    documents, _ = await self._fetch_repository_content(owner, repo, branch, token)
                
                # Get initial README content if not provided
                if not initial_readme_content:
                    initial_readme_content = await self._get_initial_readme_content(owner, repo, token)
                
                # Detect framework if not provided
                if not framework:
                    framework = self._detect_framework(documents)
            
            # Create vector index for full analysis
            index = VectorStoreIndex.from_documents(documents)
            
            # Create streaming query engine
            query_engine = index.as_query_engine(
                streaming=True,
                llm=self.llm,
            )
            
            # Prepare evaluation prompt
            evaluation_prompt = self._prepare_evaluation_prompts(initial_readme_content, framework)
            
            # Stream the evaluation report
            yield {
                "event": "message",
                "data": json.dumps({
                    "type": "evaluation_report_start",
                    "message": "Generating evaluation report..."
                })
            }
            
            try:
                # Execute query with streaming
                response = query_engine.query(evaluation_prompt)
                
                # Stream the response
                evaluation_report = ""
                
                # Use the streaming_response attribute for token-by-token streaming
                for token in response.streaming_response:
                    # Clean token (remove <think> tags)
                    token = re.sub(r'<think>.*?</think>', '', token, flags=re.DOTALL).strip()
                    
                    if token:
                        evaluation_report += token
                        
                        yield {
                            "event": "message",
                            "data": json.dumps({
                                "type": "evaluation_report_chunk",
                                "content": token
                            })
                        }
                        
                        # Small delay to simulate natural typing
                        await asyncio.sleep(0.01)
                
                # Update evaluation report in database
                DBService.update_evaluation_results(db, evaluation_id, evaluation_report, 'Completed')
                
                # Final message
                yield {
                    "event": "message",
                    "data": json.dumps({
                        "type": "evaluation_report_complete",
                        "content": evaluation_report
                    })
                }
                
            except Exception as e:
                error_msg = str(e) if str(e) else "Unknown error"
                logger.error(f"Error generating evaluation report: {error_msg}")
                
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "message": f"Error generating evaluation report: {error_msg}"
                    })
                }
            
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.error(f"Error during evaluation report generation: {error_msg}")
            
            yield {
                "event": "error",
                "data": json.dumps({
                    "message": f"Error during evaluation report generation: {error_msg}"
                })
            }
            
            # Update evaluation status in database
            DBService.update_evaluation_results(db, evaluation_id, f"Error during evaluation: {error_msg}", 'Failed')
