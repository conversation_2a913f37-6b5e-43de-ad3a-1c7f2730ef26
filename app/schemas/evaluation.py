from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List
from datetime import datetime

class EvaluationBase(BaseModel):
    """
    Base schema for evaluation
    """
    repository_url: HttpUrl
    branch: str = "main"

class EvaluationCreate(EvaluationBase):
    """
    Schema for creating a new evaluation
    """
    pass

class EvaluationUpdate(BaseModel):
    """
    Schema for updating an evaluation
    """
    framework: Optional[str] = None
    problem_statement: Optional[str] = None
    file_structure: Optional[str] = None
    results: Optional[str] = None
    status: Optional[str] = None

class EvaluationInDB(EvaluationBase):
    """
    Schema for evaluation in database
    """
    id: str
    framework: Optional[str] = None
    problem_statement: Optional[str] = None
    file_structure: Optional[str] = None
    results: Optional[str] = None
    created_at: datetime
    evaluated_by: str
    status: str

    class Config:
        orm_mode = True

class Evaluation(EvaluationCreate):
    """
    Schema for evaluation response
    """
    pass

class EvaluationResult(BaseModel):
    """
    Schema for evaluation result
    """
    positive_findings: List[str]
    negative_findings: List[str]
    overall_findings: str
    rating: float = Field(..., ge=0, le=10)
    recommendation: str
