from sqlalchemy import Column, String, Text, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from app.db.database import Base
import uuid

class Evaluation(Base):
    """
    SQLAlchemy model for evaluations table
    """
    __tablename__ = "evaluations"
    
    id = Column(String(255), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    repository_url = Column(String(255), nullable=False)
    branch = Column(String(100))
    framework = Column(String(100))
    problem_statement = Column(Text)
    file_structure = Column(Text)
    results = Column(Text)
    created_at = Column(DateTime, default=func.now())
    evaluated_by = Column(String(100))
    status = Column(String(50), default="In Progress")
