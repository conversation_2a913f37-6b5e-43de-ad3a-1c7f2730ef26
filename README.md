# Rezio Backend

Rezio Backend is a comprehensive real estate management platform built with Django, providing features for property
management, user authentication, real-time communication, and AI-powered insights. The platform supports multiple user
roles including Agents, Investors, and Sellers, with integrated payment processing and property monitoring capabilities.

## Getting Started

### Prerequisites

- **Python** 3.10
- **PostgreSQL** 14
- **Docker** and **Docker Compose**

### Installation

1. Clone the repository:

- **Using SSH:**
  ```sh
  <NAME_EMAIL>:aub-rezio/rezio-backend.git
  cd rezio-backend
  ```
- **Using HTTPS:**
  ```sh
  git clone https://<username>@github.com/aub-rezio/rezio-backend.git
  cd rezio-backend
  ```

2. Create and activate a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
```

4. Set up pre-commit hooks (for code quality and formatting):

```bash
make setup-pre-commit
```

### Environment Variables (.env)

Key environment variables to configure:

- `DJANGO_SETTINGS_MODULE`
- `Database credentials`
- `AWS credentials`
- `Firebase credentials`
- `API keys for various services`

1. Navigate to the cloned repository:
   ```sh
   cd rezio-backend
   ```
2. Create a nested directory for environment variables:
   ```sh
   mkdir -p .envs/.local
   ```
3. Obtain the `.django` and `.postgres` environment files from the team and place them inside `.envs/.local`.
4. To get the `env` variables refer to `sample.env`

## Running the Project

### Development Mode

Using Docker Compose:
Use the following **make commands** to build and run the project:

- **Build the project:**
  ```sh
  make docker-build
  ```
- **Create migrations based on database changes:**
  ```sh
  make docker-migrations
  ```
- **Apply the latest migrations:**
  ```sh
  make docker-migrate
  ```
- **Create a superuser (optional):**
  ```sh
  make docker-superuser
  ```
- **Start the server locally:**
  ```sh
  make docker-up
  ```
- **Start the server in the background mode (optional):**
  ```sh
  make docker-up-d
  ```

## Database Setup & Migrations

The project uses PostgreSQL as the primary database. Migrations can be managed using:

```bash
# Create migrations
make docker-migrations

# Apply migrations
make docker-migrate
```

## Authentication & Authorization

The project implements:

- Firebase Authentication (For Mobile APIs)
- JWT-based authentication (For AI APIs)
- Role-based access control (Agent, Investor)

## Project Structure

```
rezio-backend/
├── rezio/                 # Main Django project directory
├── docs/                  # Documentation
├── scripts/              # Utility scripts
├── static/               # Static files
├── postman_collections/  # API testing collections
├── compose/             # Docker compose configurations
├── .envs/               # Environment configurations
├── manage.py            # Django management script
├── requirements.txt     # Python dependencies
├── Dockerfile          # Docker configuration
└── local.yml           # Docker compose configuration
```

## Code Quality & Formatting

The project uses Ruff for code formatting and linting. Ruff is a fast Python linter written in Rust that can replace multiple Python linting tools.

### Setup

#### Manual Setup (Recommended)
If you prefer to set up manually, follow these steps:

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Install pre-commit hooks:
```bash
pre-commit install
```

3. Run initial check:
```bash
pre-commit run --all-files
```

#### Using Makefile (Alternative)
1. Install pre-commit hooks and Ruff:
```bash
make setup-pre-commit
```

This will:
- Install all required dependencies
- Set up pre-commit hooks
- Run an initial check on all files

### Usage

- Pre-commit hooks will automatically run on every commit
- To manually run the formatter:
```bash
pre-commit run --all-files
```

### Configuration

The Ruff configuration is in `pyproject.toml`. It includes:
- Code style rules
- Import sorting
- Line length limits
- And other formatting rules

## License & Credits

This project is proprietary software. All rights reserved.

## Additional Resources

- [Django Documentation](https://docs.djangoproject.com/)
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## Add User into Firebase

The APIs require an **authorization token**. The user should be added to the database via the **login API**. For login,
the user must first exist in **Firebase**. If a user logs in via the app, they are added to Firebase after a successful
OTP verification.

### Steps to Add a User to Firebase

1. **Request** the team for the **service account credentials file**.
2. Place the provided file inside the `scripts` directory:
   ```sh
   rezio-backend/scripts
   ```
3. Open `firebase_operations.py` in the `scripts` directory and update the following variables:
   ```python
   PHONE_NUMBER = "<Your Phone Number>"
   EMAIL = "<Your Email>"
   DISPLAY_NAME = "<User Display Name>"
   CRED_FILE = "<Path to Service Account Credentials File>"
   ```
4. Execute the function to create a test user:
   ```sh
   python firebase_operations.py create_test_user
   ```
5. The user will be added to Firebase, and a **UID** will be printed on the terminal.

### Generate an Authorization Token

1. Open `firebase_operations.py` and update the `UID` variable with the generated UID.
   ```python
   UID = "<Generated UID>"
   ```
2. Execute the function to retrieve an ID token:
   ```sh
   python firebase_operations.py get_id_token
   ```
3. The terminal will display various tokens:
    - Custom token
    - ID token
    - Refresh token

4. Use the **ID token** in subsequent API requests by adding it to the **Authorization header**:
   ```sh
   Authorization: <ID_TOKEN>
   ```
