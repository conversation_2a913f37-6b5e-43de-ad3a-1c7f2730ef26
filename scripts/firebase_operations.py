import firebase_admin
from firebase_admin import auth, credentials
import json
import requests


class Firebase:
    def __init__(self, cred_file, phone_number) -> None:
        self.cred = credentials.Certificate(cred_file)
        firebase_admin.initialize_app(self.cred)
        self.phone_number = phone_number
        self.custom_token = "eyJhbGciOiAiUlMyNTYiLCAidHlwIjogIkpXVCIsICJraWQiOiAiYzdiMDIwN2ZhODdlNGQyZGVjOGNmMGQ4OTk3NDAzYzcwYTZhNzRjNiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hZyYeXIlcos0KFTyAIAcky1HtXRt0l1Q04nsb4gWsR-ktazpxvJW2EpSRPlQtpo8Lnh8Hg05Bbdvo1JHJW-34TgFs0J8JerLeo_LszfiQcGJDBJTPHvSVAdK8Y6qu9QkQXhs_GDo369LDW3CE3t_R8LClcgXcYV-fWRHs6ewE3sviztCHG2_ta_LXPV74PHE2hzXs6DziJtci1yIQc0kfKcf7wc0-4WkCSOhgx9oStlLUgWquvacZRKxmXkVt4jdJXZAU8tycYAfimvoJYqObZIjFIUl1C9Y19HTD904KZB52JUtxND-umM_-TxIzAfTBDmKU7dqGvpVQgwnhIJ4qA"
        self.id_token = "eyJhbGciOiJSUzI1NiIsImtpZCI6ImUwM2E2ODg3YWU3ZjNkMTAyNzNjNjRiMDU3ZTY1MzE1MWUyOTBiNzIiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j4KrHKJIGbmYP7FISTmj28nQFUhsqpaLpV7GnGlWm1KdRBQlJD0KQUzKpZzIcoU0BYdfSXWm-Mr-LPdVtgmS3csevBo8m2xR5zSAVHQ5V2tht9ILJH3WkbYFvetAQWityioTJGbztiwH17DvrWQabiviZe-t3oIQupQ-5FuY1poRs9Rz6nMkKoPj33oa6R-f9wUrwC2ke3Q2rONjEkpUCZYXl9HHCen5rkC6hqApxbVXklKAt50_EAKtkO0wBLltcaLp2QJap9OsUOrQCFuSiimpE-5ogbxqimKKj0Vr7oJqwyF6F-LoaNex8kMFHzTsDsBW2JJM6a6cZxdq1cC-Jw"

    def get_user_by_phone_number(self):
        user = auth.get_user_by_phone_number(self.phone_number)
        print("Successfully fetched user data: {0}".format(user.__dict__))

    def get_user_by_uid(self, uid):
        user = auth.get_user(uid)
        print("Successfully fetched user data: {0}".format(user.__dict__))

    def create_test_user(self, email, display_name):
        user = auth.create_user(
            email=email,
            email_verified=False,
            phone_number=self.phone_number,
            display_name=display_name,
            disabled=False,
        )
        print("Successfully created new user: {0}".format(user.uid))

    def create_custom_token(self, uid):
        custom_token = auth.create_custom_token(uid)
        print("Custom Token: ", custom_token.decode("utf-8"))
        return custom_token

    def verify_id_token(self):
        decoded_token = auth.verify_id_token(self.id_token)
        # uid = decoded_token['uid']
        print("user: ", decoded_token)

    def refresh_token(self, uid):
        # Revoke tokens on the backend.
        auth.revoke_refresh_tokens(uid)
        user = auth.get_user(uid)
        # Convert to seconds as the auth_time in the token claims is in seconds.
        revocation_second = user.tokens_valid_after_timestamp / 1000
        print("Tokens revoked at: {0}".format(revocation_second))

    def get_id_token(self, uid):
        api_key = "AIzaSyCRolkdTzWMtZht_k0OqgIEEbaNv1TMWa4"
        url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={api_key}"
        custom_token = self.create_custom_token(uid)
        payload = json.dumps(
            {
                "token": custom_token.decode(),  # decode() may be necessary if custom_token is in bytes
                "returnSecureToken": True,
            }
        )
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, data=payload, headers=headers)

        if response.status_code == 200:
            id_token = response.json().get("idToken")
            print("ID Token:", id_token)
            refresh_token = response.json().get("refreshToken")
            print("Refrsh Token:", refresh_token)
            self.id_token = id_token
            self.verify_id_token()
        else:
            print("Error exchanging custom token:", response.json())

    def delete_user(self, uid):
        a = auth.delete_user(uid)
        print("Delete User:", a)


# Executes below required functions with your details
UID = "<your_uid>"  # i.e., 'KVWIFFTSxVg3SLS2AL3dyYQRngW2'
PHONE_NUMBER = "<your_phone_number>"  # i.e., '+************'
EMAIL = "<your_email>"  # i.e., <EMAIL>'
DISPLAY_NAME = "<your_display_name>"  # i.e., 'Parth Jasani Investor'
CRED_FILE = "cred_file"  # i.e.,'rezio.json'

firebase = Firebase(CRED_FILE, PHONE_NUMBER)
# firebase.create_test_user(EMAIL, DISPLAY_NAME)
# firebase.get_id_token(UID)
# firebase.get_user_by_phone_number()
# firebase.create_custom_token(UID)
# firebase.verify_id_token()
# firebase.refresh_token(UID)
# firebase.delete_user(UID)
# firebase.get_user_by_uid(UID)
