# This script help us to fetch broker data from Dubai land department, we are using their open API ad looping on it to fetch that data

import requests
import csv

url = "https://gateway.dubailand.gov.ae/open-data/brokers"
headers = {
    "Accept": "application/json, */*",
    "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
    "AppUser": "",
    "Connection": "keep-alive",
    "Content-Type": "application/json; charset=UTF-8",
    "Origin": "https://dubailand.gov.ae",
    "Referer": "https://dubailand.gov.ae/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-site",
    "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "consumer-id": "gkb3WvEG0rY9eilwXC0P2pTz8UzvLj9F",
    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Linux"',
}


def fetch_data(skip, gender):
    payload = {
        "P_GENDER": gender,
        "P_TAKE": "100",
        "P_SKIP": str(skip),
        "P_SORT": "BROKER_NUMBER_ASC",
    }
    response = requests.post(url, headers=headers, json=payload)
    return response.json()


def save_to_csv(data, filename):
    print("performing here")
    keys = data[0].keys()
    with open(filename, "w", newline="", encoding="utf-8") as output_file:
        dict_writer = csv.DictWriter(output_file, fieldnames=keys)
        dict_writer.writeheader()
        dict_writer.writerows(data)


all_brokers = []
skip = 0
total_brokers = None

for gender in ["0", "1"]:
    while True:
        response_data = fetch_data(skip, gender)
        print(f"this is response {response_data}")
        result = response_data["response"]["result"]
        if total_brokers is None:
            total_brokers = result[0]["TOTAL"]  # Total number of brokers
        all_brokers.extend(result)
        skip += 100
        if skip >= total_brokers:
            break

print(len(all_brokers))
save_to_csv(all_brokers, "all_brokers_data.csv")
print("Data saved to brokers_data.csv")
