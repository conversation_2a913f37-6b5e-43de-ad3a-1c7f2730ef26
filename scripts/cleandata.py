import pandas as pd
import numpy as np
import re
import logging
from datetime import datetime
import pytz  # Ensure pytz is installed: pip install pytz

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")


def load_data(file_path):
    """
    Loads the Excel data into a pandas DataFrame.
    Reads all columns as strings to preserve leading zeros.
    """
    try:
        df = pd.read_excel(file_path, dtype=str)
        logging.info(f"Data loaded successfully from {file_path}")
        return df
    except Exception as e:
        logging.error(f"Error loading data: {e}")
        raise


def clean_column_names(df):
    """
    Strips leading/trailing whitespace from column names.
    """
    df.columns = df.columns.str.strip()
    return df


def remove_unnamed_columns(df):
    """
    Removes columns that are unnamed (typically extra empty columns).
    """
    unnamed_cols = df.columns[df.columns.str.contains("^Unnamed")]
    if not unnamed_cols.empty:
        df = df.drop(columns=unnamed_cols)
        logging.info(f"Removed unnamed columns: {unnamed_cols.tolist()}")
    return df


def handle_missing_values(df):
    """
    Handles missing values in critical columns.
    - Fills missing emails with a placeholder.
    - Flags rows missing the 'CardNumber', 'CardHolderNameEn', or 'CardHolderNameAr'.
    """
    # Fill missing emails with placeholder
    if "CardHolderEmail" in df.columns:
        df["CardHolderEmail"] = df["CardHolderEmail"].fillna("<EMAIL>")
        logging.info("Missing emails filled with placeholder.")
    else:
        logging.warning(
            "'CardHolderEmail' column not found. Email validation will be skipped."
        )

    # Flag rows where 'CardNumber' is missing (Broker Number)
    if "CardNumber" in df.columns:
        df["MissingBrokerNumber"] = df["CardNumber"].isna()
        missing_count = df["MissingBrokerNumber"].sum()
        logging.info(
            f"Flagged {missing_count} rows with missing Broker Number (CardNumber)."
        )
    else:
        logging.error("'CardNumber' column not found in the dataset.")
        raise KeyError("'CardNumber' column is missing from the dataset.")

    # Flag rows where 'CardHolderNameEn' is missing
    if "CardHolderNameEn" in df.columns:
        df["MissingNameEnglish"] = df["CardHolderNameEn"].isna()
        missing_count = df["MissingNameEnglish"].sum()
        logging.info(f"Flagged {missing_count} rows with missing Name English.")
    else:
        logging.error("'CardHolderNameEn' column not found in the dataset.")
        raise KeyError("'CardHolderNameEn' column is missing from the dataset.")

    # Flag rows where 'CardHolderNameAr' is missing
    if "CardHolderNameAr" in df.columns:
        df["MissingNameArabic"] = df["CardHolderNameAr"].isna()
        missing_count = df["MissingNameArabic"].sum()
        logging.info(f"Flagged {missing_count} rows with missing Name Arabic.")
    else:
        logging.error("'CardHolderNameAr' column not found in the dataset.")
        raise KeyError("'CardHolderNameAr' column is missing from the dataset.")

    return df


def remove_duplicates(df, subset_columns):
    """
    Removes duplicate rows based on specified subset of columns.
    """
    missing_cols = [col for col in subset_columns if col not in df.columns]
    if missing_cols:
        logging.error(f"Missing columns for duplicate removal: {missing_cols}")
        # Skip duplicate removal if columns are missing
        return df
    initial_count = len(df)
    df = df.drop_duplicates(subset=subset_columns, keep="last")
    final_count = len(df)
    logging.info(
        f"Removed {initial_count - final_count} duplicate rows based on {subset_columns}."
    )
    return df


def format_phone_number(phone):
    """
    Formats the phone number to adhere to the E.164 standard for UAE.
    - Replaces leading '0' with '+971'.
    - Handles '971|' prefixes.
    - Ensures the phone number starts with '+971' followed by 6 to 9 digits.
    """
    if pd.isnull(phone):
        return np.nan, False  # Return NaN and flag as invalid

    # Remove any non-digit characters except '+'
    phone_clean = re.sub(r"[^\d+]", "", str(phone).strip())

    # Handle '971|' prefix by replacing it with '+971'
    if phone_clean.startswith("971|"):
        phone_clean = phone_clean.replace("971|", "+971")
    elif phone_clean.startswith("971") and not phone_clean.startswith("+"):
        phone_clean = "+" + phone_clean
    elif phone_clean.startswith("0"):
        # Replace leading '0' with '+971'
        phone_clean = "+971" + phone_clean[1:]
    elif not phone_clean.startswith("+971"):
        # Prepend '+971' if missing
        phone_clean = "+971" + phone_clean.lstrip("+")

    # After replacements, ensure no redundant '+' signs
    phone_clean = re.sub(r"\++", "+", phone_clean)

    # Remove any remaining non-digit characters except '+'
    phone_clean = re.sub(r"[^\d+]", "", phone_clean)

    # Validate that the phone number starts with '+971'
    if not phone_clean.startswith("+971"):
        logging.warning(f"Phone number does not start with '+971': {phone}")
        return np.nan, False

    # Extract the digits after '+971'
    digits_after_country = phone_clean[4:]

    # Ensure that the remaining characters are all digits
    if not digits_after_country.isdigit():
        logging.warning(f"Non-digit characters found after '+971': {phone}")
        return np.nan, False

    # Validate length: 6 to 9 digits after '+971' (total length 11 to 13 characters)
    if len(digits_after_country) > 9:
        logging.warning(f"Phone number has more than 9 digits after '+971': {phone}")
        return np.nan, False
    elif len(digits_after_country) < 6:
        logging.warning(f"Phone number has less than 6 digits after '+971': {phone}")
        return np.nan, False

    return phone_clean, True  # Return cleaned phone number and flag as valid


def clean_phone_numbers(df):
    """
    Cleans the 'CardHolderMobile' column by formatting it as per E.164 standards.
    Assigns the cleaned number to a new 'Phone Number' column and flags invalid entries.
    """
    if "CardHolderMobile" in df.columns:
        # Apply formatting function and create 'Phone Number' and 'ValidPhoneNumber' columns
        phone_results = df["CardHolderMobile"].apply(format_phone_number)
        df["Phone Number"] = phone_results.apply(lambda x: x[0])
        df["ValidPhoneNumber"] = phone_results.apply(lambda x: x[1])
        logging.info(
            "CardHolderMobile column formatted and assigned to 'Phone Number'."
        )
    else:
        logging.warning(
            "CardHolderMobile column not found. 'Phone Number' column will not be created."
        )
        df["Phone Number"] = np.nan
        df["ValidPhoneNumber"] = False

    return df


def validate_emails(df):
    """
    Validates email addresses using a simple regex pattern.
    Flags invalid emails.
    """
    if "CardHolderEmail" in df.columns:
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
        df["ValidEmail"] = df["CardHolderEmail"].apply(
            lambda x: True if email_pattern.match(str(x)) else False
        )
        # Replace invalid emails with a placeholder for clarity
        df["Email"] = df["CardHolderEmail"].apply(
            lambda x: x if email_pattern.match(str(x)) else "<EMAIL>"
        )
        logging.info("Email addresses validated and assigned to 'Email'.")
    else:
        logging.warning("CardHolderEmail column not found. Email validation skipped.")
        df["ValidEmail"] = False
        df["Email"] = "<EMAIL>"

    return df


def convert_dates(df, date_columns):
    """
    Converts specified columns to timezone-aware datetime objects.
    Then converts them to UTC and makes them timezone-naive.
    """
    for col in date_columns:
        if col in df.columns:
            # Convert to datetime, coerce errors to NaT
            df[col] = pd.to_datetime(df[col], errors="coerce")

            # Make timezone-aware using UTC
            df[col] = df[col].apply(
                lambda x: x.replace(tzinfo=pytz.UTC) if pd.notnull(x) else None
            )

            # Convert to UTC and make timezone-naive by removing tzinfo
            df[col] = df[col].apply(
                lambda x: x.astimezone(pytz.UTC).replace(tzinfo=None)
                if pd.notnull(x)
                else None
            )

            logging.info(f"Converted {col} to timezone-naive datetime format.")
    return df


def standardize_text_fields(df):
    """
    Standardizes text fields, e.g., converting to uppercase and stripping whitespace.
    """
    if "OfficeNameEn" in df.columns:
        df["OfficeNameEn"] = df["OfficeNameEn"].str.upper().str.strip()
        logging.info("OfficeNameEn standardized to uppercase and stripped.")

    if "OfficeNameAr" in df.columns:
        df["OfficeNameAr"] = df["OfficeNameAr"].str.strip()
        logging.info("OfficeNameAr stripped of leading/trailing whitespace.")

    # Add more text standardizations if necessary
    return df


def handle_numeric_fields(df):
    """
    Converts specified columns to numeric types.
    """
    numeric_columns = [
        "CardNumber",
        "RealEstateNumber",
    ]  # 'RealEstateNumber' exists in data
    for col in numeric_columns:
        if col in df.columns:
            # Convert to numeric, coercing errors to NaN
            df[col] = pd.to_numeric(df[col], errors="coerce")
            # For integer fields, convert float NaN to integer NaN
            if col in ["CardNumber", "RealEstateNumber"]:
                df[col] = df[col].astype("Int64")  # Allows for NaN in integer columns
            logging.info(f"Converted {col} to numeric format.")
    return df


def flag_issues(df):
    """
    Flags rows with missing or invalid Broker Number, Name English, Name Arabic, Phone Number, or Email.
    Compiles all issues into a single 'Issues' column.
    """
    # Initialize 'Issues' column
    df["Issues"] = ""

    # Populate 'Issues' column based on flags
    df.loc[df["MissingBrokerNumber"], "Issues"] += "Missing Broker Number; "
    df.loc[df["MissingNameEnglish"], "Issues"] += "Missing Name English; "
    df.loc[df["MissingNameArabic"], "Issues"] += "Missing Name Arabic; "
    df.loc[~df["ValidPhoneNumber"], "Issues"] += "Invalid Phone Number; "
    df.loc[~df["ValidEmail"], "Issues"] += "Invalid Email; "

    # Remove trailing semicolon and space
    df["Issues"] = df["Issues"].str.rstrip("; ")

    # Replace empty strings with NaN for better readability (optional)
    df["Issues"] = df["Issues"].replace("", np.nan)

    # Log the number of rows with issues
    issues_count = df["Issues"].notna().sum()
    logging.info(f"Flagged {issues_count} rows with issues.")

    return df


def clean_data(file_path):
    """
    Executes the data cleaning steps sequentially.
    """
    df = load_data(file_path)

    # Clean column names
    df = clean_column_names(df)
    logging.info(f"Cleaned DataFrame columns: {df.columns.tolist()}")

    # Remove unnamed columns
    df = remove_unnamed_columns(df)

    # Step 1: Handle Missing Values
    df = handle_missing_values(df)

    # Step 2: Remove Duplicates based on 'CardNumber'
    df = remove_duplicates(df, subset_columns=["CardNumber"])

    # Step 3: Format Phone Numbers (Use 'CardHolderMobile' as mobile number)
    df = clean_phone_numbers(df)

    # Step 4: Validate Emails
    df = validate_emails(df)

    # Step 5: Convert Date Fields
    date_columns = ["CardIssueDate", "CardExpiryDate"]
    df = convert_dates(df, date_columns)

    # Step 6: Standardize Text Fields
    df = standardize_text_fields(df)

    # Step 7: Handle Numeric Fields
    df = handle_numeric_fields(df)

    # Step 8: Flag rows with issues
    df = flag_issues(df)

    # Step 9: Create 'fax' column by mapping 'Email' to 'fax' (since 'FAX' is not available)
    df["fax"] = df["Email"]

    # Step 10: Select and Rename Relevant Columns while retaining the 'Issues' column
    target_columns = {
        "CardHolderNameEn": "Name English",
        "CardHolderNameAr": "Name Arabic",
        "CardNumber": "Broker Number",  # Updated mapping
        "OfficeNameEn": "Office Name English",
        "OfficeNameAr": "Office Name Arabic",
        "Email": "Email",
        "Phone Number": "Phone Number",
        "CardIssueDate": "license_start_date",  # From 'CardIssueDate'
        "CardExpiryDate": "license_end_date",  # From 'CardExpiryDate'
        "RealEstateNumber": "real_estate_number",  # Renamed to lowercase
        "fax": "fax",  # Mapped from 'Email'
    }

    # Check for missing target columns
    missing_target_cols = [
        orig for orig in target_columns.keys() if orig not in df.columns
    ]
    if missing_target_cols:
        logging.error(f"Missing target columns in data: {missing_target_cols}")
        raise KeyError(f"Missing target columns: {missing_target_cols}")

    # Select and rename columns while retaining the 'Issues' column
    selected_columns = list(target_columns.keys()) + ["Issues"]
    df = df[selected_columns].rename(columns=target_columns)

    # Step 11: Export Data
    # All data is retained in 'cleaned_output.xlsx' with 'Issues' column
    save_cleaned_data(df, "cleaned_output.xlsx")  # All data with 'Issues' column

    logging.info("Data cleaning completed successfully.")
    return df


def save_cleaned_data(df, output_path):
    """
    Saves the cleaned DataFrame to an Excel file.
    """
    try:
        # Before exporting, ensure all datetime columns are timezone-naive
        datetime_columns = ["license_start_date", "license_end_date"]
        for col in datetime_columns:
            if col in df.columns:
                # Convert datetime columns to timezone-naive if they aren't already
                df[col] = pd.to_datetime(df[col], errors="coerce").dt.tz_localize(None)

        df.to_excel(output_path, index=False)
        logging.info(f"Cleaned data saved to {output_path}")
    except Exception as e:
        logging.error(f"Error saving cleaned data: {e}")
        raise


if __name__ == "__main__":
    input_file = "output_fixed.xlsx"  # Update with your actual input file path
    # Output file
    cleaned_output_file = "cleaned_output.xlsx"  # All data with 'Issues' column

    # Perform data cleaning
    cleaned_df = clean_data(input_file)
