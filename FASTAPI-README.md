# Exercise Evaluator API

A FastAPI backend service for evaluating coding exercises and generating assessment reports.

## Features

- Authentication system with JWT tokens
- Repository analysis and evaluation
- Framework detection
- README analysis for problem statement extraction
- Comprehensive evaluation report generation
- Database integration for storing evaluation results

## Project Structure

```
exercise_evaluator_fastapi/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config.py               # Configuration and environment variables
│   ├── dependencies.py         # Dependency injection
│   ├── db/
│   │   ├── __init__.py
│   │   ├── database.py         # Database connection and session management
│   │   └── models.py           # SQLAlchemy models
│   ├── api/
│   │   ├── __init__.py
│   │   ├── endpoints/
│   │   │   ├── __init__.py
│   │   │   ├── auth.py         # Authentication endpoints
│   │   │   └── evaluations.py  # Evaluation endpoints
│   │   └── deps.py             # API specific dependencies
│   ├── core/
│   │   ├── __init__.py
│   │   ├── auth.py             # Authentication logic
│   │   ├── security.py         # Security utilities
│   │   └── config.py           # Core configuration
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── auth.py             # Authentication schemas
│   │   └── evaluation.py       # Evaluation schemas
│   └── services/
│       ├── __init__.py
│       ├── evaluator/
│       │   ├── __init__.py
│       │   ├── code_evaluator.py  # Main evaluation logic
│       │   ├── framework_detector.py  # Framework detection
│       │   └── report_generator.py  # Report generation
│       └── db_service.py       # Database service
├── tests/
│   ├── __init__.py
│   ├── conftest.py
│   ├── test_api/
│   │   ├── __init__.py
│   │   ├── test_auth.py
│   │   └── test_evaluations.py
│   └── test_services/
│       ├── __init__.py
│       └── test_evaluator.py
├── .env.example                # Example environment variables
├── requirements.txt            # Project dependencies
└── README.md                   # Project documentation
```

## Setup and Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Copy `.env.example` to `.env` and configure environment variables
6. Run the application: `uvicorn app.main:app --reload`

## API Documentation

Once the application is running, you can access the API documentation at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Environment Variables

- `ADMIN_EMAIL`: Admin email for authentication
- `ADMIN_PASSWORD`: Admin password for authentication
- `SECRET_KEY`: Secret key for JWT token generation
- `ALGORITHM`: Algorithm for JWT token (default: HS256)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token expiration time in minutes
- `POSTGRES_HOST`: PostgreSQL host
- `POSTGRES_PORT`: PostgreSQL port
- `POSTGRES_DB`: PostgreSQL database name
- `POSTGRES_USER`: PostgreSQL username
- `POSTGRES_PASSWORD`: PostgreSQL password
- `GITHUB_TOKEN`: GitHub token for repository access (optional)
