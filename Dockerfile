# Use UV's Python image as base
FROM ghcr.io/astral-sh/uv:python3.10-bookworm-slim

# Set environment variables
ENV UV_COMPILE_BYTECODE=1
ENV UV_LINK_MODE=copy
ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV BUILD_ENV=production
ENV APP_HOME=/app

WORKDIR ${APP_HOME}

# Install system dependencies
RUN apt-get update && apt-get install --no-install-recommends -y \
    # psycopg2 dependencies
    libpq-dev \
    gcc \
    python3-dev \
    # Translations dependencies
    gettext \
    # Additional build dependencies
    clang \
    curl \
    # cleaning up unused files
    && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN uv venv

# Copy dependency files first for better caching
COPY pyproject.toml uv.lock* ./

# Install dependencies using UV with caching
RUN --mount=type=cache,target=/root/.cache/uv \
    . .venv/bin/activate && \
    uv pip install --system psycopg2-binary && \
    uv sync --frozen --no-install-project --no-dev

# Create logs directory
RUN mkdir -p ${APP_HOME}/rezio/logs \
    && chmod -R 777 ${APP_HOME}/rezio/logs

# Copy and setup entrypoint scripts
COPY ./compose/production/django/entrypoint /entrypoint
RUN chmod +x /entrypoint

COPY ./compose/production/django/start /start
RUN chmod +x /start

COPY ./compose/production/django/start-celerybeat /start-celerybeat
RUN chmod +x /start-celerybeat

COPY ./compose/production/django/start-celeryworker /start-celeryworker
RUN chmod +x /start-celeryworker

# Copy New Relic configuration file
COPY ./compose/production/django/newrelic.ini /app/newrelic.ini

# Set environment variables
ENV NEW_RELIC_CONFIG_FILE=/app/newrelic.ini
ENV NEW_RELIC_ENVIRONMENT=staging
ENV NEW_RELIC_LICENSE_KEY=42d79e90195d133ab538c11024b39680FFFFNRAL

# Copy the rest of the application
COPY . .

# Install the project
RUN --mount=type=cache,target=/root/.cache/uv \
    . .venv/bin/activate && \
    uv sync --frozen --no-dev

# Set Python path
ENV PATH="/app/.venv/bin:$PATH"

EXPOSE 80 443

CMD ["newrelic-admin", "run-program", "/start"]