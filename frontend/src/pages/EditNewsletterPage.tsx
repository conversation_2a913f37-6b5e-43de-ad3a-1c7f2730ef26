import {
  Box,
  Button,
  Code,
  Container,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  HStack,
  IconButton,
  Link,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  Tab,
  Table,
  TabList,
  TabPanel,
  TabPanels,
  Tabs,
  Tbody,
  Td,
  Text,
  Textarea,
  Th,
  Thead,
  Tr,
  useColorModeValue,
  useDisclosure,
  useToast,
  VStack
} from '@chakra-ui/react';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { FiArrowLeft } from 'react-icons/fi';
import ReactMarkdown from 'react-markdown';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { API_BASE_URL } from '../config';

interface Newsletter {
  id: string;
  year: number;
  month: number;
  week_number: number;
  newsletter_title: string;
  content: Array<{
    title: string;
    content: string;
  }>;
  status: 'DRAFT' | 'APPROVED' | 'SENT' | 'FAILED';
  created_at: string;
  sent_at: string | null;
}

const EditNewsletterPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const [newsletter, setNewsletter] = useState<Newsletter | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isViewOnly, setIsViewOnly] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [markdownContent, setMarkdownContent] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  useEffect(() => {
    if (location.state?.newsletter) {
      const newsletterData = {
        ...location.state.newsletter,
        content: Array.isArray(location.state.newsletter.content) 
          ? location.state.newsletter.content 
          : [{ title: '', content: '' }]
      };
      setNewsletter(newsletterData);
      setIsViewOnly(newsletterData.status === 'SENT');
      generateMarkdown(newsletterData);
    } else {
      fetchNewsletter();
    }
  }, [id, location.state]);

  const generateMarkdown = (newsletter: Newsletter) => {
    let markdown = '';
    
    newsletter.content.forEach(section => {
      if (section.title) {
        markdown += `${section.title}\n\n`;
      }
      if (section.content) {
        // Split content by horizontal rule and process each part
        const parts = section.content.split('---');
        parts.forEach((part, index) => {
          if (part.trim()) {
            // Find the link in the content
            const linkMatch = part.match(/\[([^\]]+)\]\(([^)]+)\)/);
            if (linkMatch) {
              // Get the text before the link
              const beforeLink = part.substring(0, part.indexOf(linkMatch[0])).trim();
              // Get the text after the link
              const afterLink = part.substring(part.indexOf(linkMatch[0]) + linkMatch[0].length).trim();
              
              // Add the content before the link
              if (beforeLink) {
                markdown += beforeLink.replace(/\n+$/, '') + '\n\n';
              }
              
              // Add the link on its own line
              markdown += linkMatch[0] + '\n\n';
              
              // Add any content after the link
              if (afterLink) {
                markdown += afterLink + '\n\n';
              }
            } else {
              // Remove any trailing newlines from the content
              markdown += part.trim().replace(/\n+$/, '') + '\n\n';
            }
          }
        });
      }
    });
    
    setMarkdownContent(markdown);
  };

  const generatePreviewMarkdown = (newsletter: Newsletter) => {
    let markdown = `# ${newsletter.newsletter_title}\n\n`;
    markdown += markdownContent;
    return markdown;
  };

  const fetchNewsletter = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await axios.post(
        `${API_BASE_URL}/newsletters/${id}/edit`,
        {},
        {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data) {
        const newsletterData = {
          ...response.data,
          content: Array.isArray(response.data.content) 
            ? response.data.content 
            : [{ title: '', content: '' }]
        };
        
        setNewsletter(newsletterData);
        setIsViewOnly(newsletterData.status === 'SENT');
        generateMarkdown(newsletterData);
      }
    } catch (error: any) {
      console.error('Error fetching newsletter:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to load newsletter';
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      navigate('/home');
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentChange = (field: string, value: string) => {
    if (!newsletter || isViewOnly) return;
    
    setNewsletter(prev => {
      if (!prev) return null;
      const updated = {
        ...prev,
        [field]: value
      };
      generateMarkdown(updated);
      return updated;
    });
    setHasChanges(true);
  };

  const handleSave = async (status?: 'DRAFT' | 'APPROVED') => {
    if (!newsletter || isViewOnly) return;

    try {
      setIsSaving(true);
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      // Split the content into sections based on headers
      const sections = markdownContent.split(/(?=^##\s)/m).filter(Boolean);
      const formattedContent = sections.map(section => {
        const lines = section.trim().split('\n');
        const title = lines[0].replace(/^##\s+/, '');
        const content = lines.slice(1).join('\n').trim();
        return { title, content };
      });

      const response = await axios.post(
        `${API_BASE_URL}/newsletters/${id}/edit`,
        {
          newsletter_title: newsletter.newsletter_title,
          content: formattedContent,
          status: status || newsletter.status
        },
        {
          headers: { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data) {
        toast({
          title: 'Success',
          description: status === 'APPROVED' ? 'Newsletter approved successfully' : 'Newsletter saved successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        setHasChanges(false);
        if (status === 'APPROVED') {
          navigate('/home', { state: { activeTab: 1 } });
        }
      }
    } catch (error: any) {
      console.error('Error saving newsletter:', error);
      const errorMessage = error.response?.data?.detail || 'Failed to save newsletter';
      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    const activeTab = newsletter?.status === 'APPROVED' ? 1 : 0;
    navigate('/home', { state: { activeTab } });
  };

  if (isLoading) {
    return (
      <Container maxW="container.xl" py="8">
        <Box>Loading...</Box>
      </Container>
    );
  }

  if (!newsletter) {
    return (
      <Container maxW="container.xl" py="8">
        <Box>Newsletter not found</Box>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py="8">
      <VStack spacing="8" align="stretch">
        <Flex justify="space-between" align="center" bg={bgColor} p="4" borderRadius="lg" boxShadow="sm">
          <HStack spacing="4">
            <IconButton
              aria-label="Go back"
              icon={<FiArrowLeft />}
              onClick={handleBack}
              variant="ghost"
              size="lg"
            />
            <Heading size="lg" color={useColorModeValue('gray.700', 'white')}>
              {isViewOnly ? 'View Newsletter' : 'Edit Newsletter'}
            </Heading>
          </HStack>
          <HStack spacing="4">
            {!isViewOnly && (
              <>
                <Button
                  colorScheme="blue"
                  onClick={() => handleSave()}
                  isLoading={isSaving}
                  isDisabled={!hasChanges}
                  size="lg"
                  px="6"
                >
                  Save
                </Button>
                {newsletter.status === 'DRAFT' && (
                  <Button
                    colorScheme="green"
                    onClick={() => handleSave('APPROVED')}
                    isLoading={isSaving}
                    size="lg"
                    px="6"
                  >
                    Approve
                  </Button>
                )}
              </>
            )}
          </HStack>
        </Flex>

        <Tabs variant="soft-rounded" colorScheme="blue" onChange={setActiveTab} isLazy>
          <TabList mb="4">
            <Tab fontSize="md" fontWeight="medium">Edit</Tab>
            <Tab fontSize="md" fontWeight="medium">Preview</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>
              <VStack spacing="8" align="stretch">
                <Box bg={bgColor} p="6" borderRadius="lg" boxShadow="sm">
                  <FormControl>
                    <FormLabel fontSize="lg" fontWeight="medium" mb="2">Newsletter Title</FormLabel>
                    <Textarea
                      value={newsletter.newsletter_title}
                      onChange={(e) => handleContentChange('newsletter_title', e.target.value)}
                      placeholder="Enter newsletter title"
                      size="lg"
                      isReadOnly={isViewOnly}
                    />
                  </FormControl>
                </Box>

                <Box bg={bgColor} p="6" borderRadius="lg" boxShadow="sm">
                  <FormControl>
                    <Flex justify="space-between" align="center" mb="2">
                      <FormLabel fontSize="lg" fontWeight="medium" mb="0">Content</FormLabel>
                      <Button
                        size="sm"
                        variant="ghost"
                        colorScheme="blue"
                        onClick={onOpen}
                      >
                        Markdown Guide
                      </Button>
                    </Flex>
                    <Text fontSize="sm" color="gray.500" mb="2">
                      Use # for main title and ## for section headers. Each section will be automatically separated.
                    </Text>
                    <Textarea
                      value={markdownContent}
                      onChange={(e) => {
                        setMarkdownContent(e.target.value);
                        setHasChanges(true);
                      }}
                      placeholder="Enter newsletter content using markdown format"
                      size="lg"
                      minH="600px"
                      fontFamily="monospace"
                      isReadOnly={isViewOnly}
                    />
                  </FormControl>
                </Box>

                {/* Markdown Guide Modal */}
                <Modal isOpen={isOpen} onClose={onClose} size="xl">
                  <ModalOverlay />
                  <ModalContent>
                    <ModalHeader>Markdown Guide</ModalHeader>
                    <ModalCloseButton />
                    <ModalBody pb={6}>
                      <VStack spacing={4} align="stretch">
                        <Text fontSize="lg" fontWeight="bold">Basic Formatting</Text>
                        <Table variant="simple">
                          <Thead>
                            <Tr>
                              <Th>Markdown</Th>
                              <Th>Result</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            <Tr>
                              <Td><Code># Title</Code></Td>
                              <Td>Main Title (Large)</Td>
                            </Tr>
                            <Tr>
                              <Td><Code>## Section</Code></Td>
                              <Td>Section Header</Td>
                            </Tr>
                            <Tr>
                              <Td><Code>**Bold**</Code></Td>
                              <Td><Text fontWeight="bold">Bold</Text></Td>
                            </Tr>
                            <Tr>
                              <Td><Code>*Italic*</Code></Td>
                              <Td><Text fontStyle="italic">Italic</Text></Td>
                            </Tr>
                            <Tr>
                              <Td><Code>[Link Text](url)</Code></Td>
                              <Td><Link color="blue.500">Link Text</Link></Td>
                            </Tr>
                            <Tr>
                              <Td><Code>- Item</Code></Td>
                              <Td>• Item</Td>
                            </Tr>
                          </Tbody>
                        </Table>

                        <Text fontSize="lg" fontWeight="bold" mt={4}>Example</Text>
                        <Box bg="gray.50" p={4} borderRadius="md">
                          <Code display="block" whiteSpace="pre">
{`# Newsletter Title

## Featured Article
This is a **bold** and *italic* text.

- First point
- Second point

[Read More](https://example.com)`}
                          </Code>
                        </Box>
                      </VStack>
                    </ModalBody>
                  </ModalContent>
                </Modal>

              </VStack>
            </TabPanel>
            <TabPanel>
              <Box
                p="6"
                bg={bgColor}
                borderWidth="1px"
                borderColor={borderColor}
                borderRadius="lg"
                boxShadow="sm"
                className="markdown-body"
                sx={{
                  '& h1': {
                    fontSize: '3em',
                    marginBottom: '0.8em',
                    color: useColorModeValue('gray.800', 'white'),
                    fontWeight: 'bold',
                    borderBottom: 'none'
                  },
                  '& h2': {
                    fontSize: '2em',
                    marginTop: '1.5em',
                    marginBottom: '0.8em',
                    color: useColorModeValue('gray.700', 'white'),
                    fontWeight: 'semibold',
                    borderBottom: 'none'
                  },
                  '& p': {
                    marginBottom: '1.2em',
                    lineHeight: '1.8',
                    fontSize: '1.1em',
                    color: useColorModeValue('gray.600', 'gray.300')
                  },
                  '& ul, & ol': {
                    paddingLeft: '2em',
                    marginBottom: '1.2em',
                    fontSize: '1.1em'
                  },
                  '& li': {
                    marginBottom: '0.6em',
                    lineHeight: '1.8'
                  },
                  '& blockquote': {
                    padding: '1em 1.5em',
                    color: useColorModeValue('gray.600', 'gray.300'),
                    borderLeft: '0.25em solid #dfe2e5',
                    margin: '1.2em 0',
                    backgroundColor: useColorModeValue('gray.50', 'gray.700'),
                    borderRadius: '0.5em'
                  },
                  '& pre': {
                    backgroundColor: useColorModeValue('gray.50', 'gray.700'),
                    padding: '1.2em',
                    borderRadius: '0.5em',
                    overflow: 'auto',
                    fontSize: '0.95em'
                  },
                  '& code': {
                    backgroundColor: useColorModeValue('gray.50', 'gray.700'),
                    padding: '0.2em 0.4em',
                    borderRadius: '0.3em',
                    fontSize: '0.95em'
                  },
                  '& a': {
                    color: useColorModeValue('blue.500', 'blue.300'),
                    textDecoration: 'none',
                    fontWeight: 'medium',
                    display: 'inline-block',
                    marginTop: '0.5em',
                    '&:hover': {
                      textDecoration: 'underline'
                    }
                  },
                  '& hr': {
                    margin: '1.5em 0',
                    border: 'none',
                    borderTop: '2px solid',
                    borderColor: useColorModeValue('gray.200', 'gray.600')
                  }
                }}
              >
                <ReactMarkdown
                  components={{
                    a: ({ node, ...props }) => (
                      <Link
                        {...props}
                        isExternal
                        color={useColorModeValue('blue.500', 'blue.300')}
                        _hover={{ textDecoration: 'underline' }}
                      />
                    )
                  }}
                >
                  {newsletter ? generatePreviewMarkdown(newsletter) : ''}
                </ReactMarkdown>
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </VStack>
    </Container>
  );
};

export default EditNewsletterPage; 