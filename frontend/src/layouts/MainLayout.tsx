import {
    Box,
    Button,
    Link as <PERSON><PERSON><PERSON><PERSON>,
    Flex,
    HStack,
    IconButton,
    Menu,
    MenuButton,
    MenuItem,
    MenuList,
    Stack,
    useColorModeValue,
    useDisclosure,
    useToast,
} from '@chakra-ui/react';
import axios from 'axios';
import { ReactNode } from 'react';
import { FiChevronDown, FiMenu, FiX } from 'react-icons/fi';
import { Link, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import { ROUTES } from '../constants/routes';

interface NavLinkProps {
  children: ReactNode;
  to: string;
}

const NavLink = ({ children, to }: NavLinkProps) => (
  <ChakraLink
    as={Link}
    px={2}
    py={1}
    rounded="md"
    _hover={{
      textDecoration: 'none',
      bg: useColorModeValue('gray.200', 'gray.700'),
    }}
    to={to}
  >
    {children}
  </ChakraLink>
);

const Links = [
  { name: 'Home', path: ROUTES.HOME },
  { name: 'Newsletters', path: ROUTES.NEWSLETTERS },
];

export default function MainLayout({ children }: { children: ReactNode }) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const navigate = useNavigate();
  const toast = useToast();

  const handleSignout = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      // Call the signout endpoint
      await axios.post(`${API_BASE_URL}/auth/signout`, null, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Clear local storage
      localStorage.clear();

      // Show success message
      toast({
        title: 'Signed out successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Redirect to login
      navigate('/login');
    } catch (error) {
      console.error('Error during signout:', error);
      // Even if the backend call fails, we should still clear local storage and redirect
      localStorage.clear();
      navigate('/login');
    }
  };

  return (
    <Box minH="100vh" bg={useColorModeValue('gray.50', 'gray.900')}>
      <Box bg={useColorModeValue('white', 'gray.800')} px={4} boxShadow="sm">
        <Flex h={16} alignItems="center" justifyContent="space-between">
          <IconButton
            size="md"
            icon={isOpen ? <FiX /> : <FiMenu />}
            aria-label="Open Menu"
            display={{ md: 'none' }}
            onClick={isOpen ? onClose : onOpen}
          />
          <HStack spacing={8} alignItems="center">
            <Box
              fontWeight="bold"
              fontSize="xl"
              cursor="pointer"
              onClick={() => navigate(ROUTES.HOME)}
            >
              Newsletter App
            </Box>
            <HStack as="nav" spacing={4} display={{ base: 'none', md: 'flex' }}>
              {Links.map((link) => (
                <NavLink key={link.path} to={link.path}>
                  {link.name}
                </NavLink>
              ))}
            </HStack>
          </HStack>
          <Flex alignItems="center">
            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<FiChevronDown />}
                variant="ghost"
              >
                Account
              </MenuButton>
              <MenuList>
                <MenuItem onClick={() => navigate(ROUTES.SETTINGS)}>
                  Profile
                </MenuItem>
                <MenuItem onClick={handleSignout}>
                  Sign out
                </MenuItem>
              </MenuList>
            </Menu>
          </Flex>
        </Flex>

        {isOpen ? (
          <Box pb={4} display={{ md: 'none' }}>
            <Stack as="nav" spacing={4}>
              {Links.map((link) => (
                <NavLink key={link.path} to={link.path}>
                  {link.name}
                </NavLink>
              ))}
            </Stack>
          </Box>
        ) : null}
      </Box>

      <Box p={4}>{children}</Box>
    </Box>
  );
} 