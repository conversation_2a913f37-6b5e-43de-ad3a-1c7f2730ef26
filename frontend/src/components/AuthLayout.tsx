import { Box, Container, Heading, Text, useColorModeValue, VStack } from '@chakra-ui/react';
import { ReactNode } from 'react';

interface AuthLayoutProps {
  children: ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  return (
    <Box 
      minH="100vh" 
      bg={useColorModeValue('gray.50', 'gray.900')} 
      position="relative" 
      overflowY="auto" 
      py={8}
    >
      {/* Background Design Elements */}
      <Box
        position="fixed"
        top="-10%"
        right="-10%"
        width="600px"
        height="600px"
        transform="rotate(-45deg)"
        bg={useColorModeValue('blue.50', 'blue.900')}
        borderRadius="xl"
        opacity="0.4"
        zIndex="0"
      />
      <Box
        position="fixed"
        bottom="-15%"
        left="-15%"
        width="600px"
        height="600px"
        transform="rotate(45deg)"
        bg={useColorModeValue('purple.50', 'purple.900')}
        borderRadius="xl"
        opacity="0.4"
        zIndex="0"
      />

      <Container 
        maxW="lg" 
        py={{ base: 10, md: 20 }} 
        position="relative" 
        zIndex="1"
      >
        <VStack spacing={8} align="center" mb={8}>
          <Heading
            fontSize={{ base: "4xl", md: "5xl" }}
            color={useColorModeValue('blue.600', 'blue.400')}
            fontWeight="extrabold"
            letterSpacing="tight"
            textAlign="center"
          >
            Newsletter Pro
          </Heading>
          <Text 
            fontSize={{ base: "lg", md: "xl" }} 
            color={useColorModeValue('gray.600', 'gray.300')} 
            textAlign="center" 
            maxW="md"
          >
            Your all-in-one platform for creating, managing, and sending beautiful newsletters weekly.
          </Text>
        </VStack>

        {children}
      </Container>
    </Box>
  );
};

export default AuthLayout; 