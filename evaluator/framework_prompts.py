"""Framework-specific evaluation prompts."""

# Base prompt template with common sections
BASE_PROMPT = """
Evaluate the provided implementation thoroughly. The evaluation must cover the following core aspects, ensuring each is implemented correctly, adheres to industry standards, and functions as expected. Additionally, note that certain factors (like testing and documentation) are treated as optional enhancements. Their presence will be credited as bonus points, but their absence will not heavily detract from the overall evaluation if the core requirements are met.

### Core Evaluation Criteria (Mandatory):

1. **Functionality & Requirements Compliance**
   - Validate that all specified features (e.g., CRUD operations, dynamic routing, API integrations, state management, etc.) are correctly implemented.
   - Ensure that edge cases and error conditions are handled gracefully.

2. **Code Quality & Maintainability**
   - Assess overall structure, modularity, and adherence to design principles (e.g., SOLID).
   - Review clarity, readability, code comments, and inline documentation.
   - Verify consistent coding conventions and use of design patterns.

3. **Best Practices & Standards**
   - Confirm adherence to the technology's or framework's best practices.
   - Validate the effective use of core libraries, utilities, or idioms.
   - Check for proper implementation of logging, exception handling, and monitoring.

4. **Security & Permissions**
   - Verify that proper security measures are in place, including authentication, authorization, and permission schemes.
   - Ensure protections against common vulnerabilities (e.g., CSRF, XSS, SQL injection).
   - Validate data validation and secure configuration management.

5. **Performance, Query Optimization & Scalability**
   - Evaluate performance aspects such as response times, resource efficiency, and caching strategies.
   - Verify that the implementation is optimized and scalable for increased loads.

6. **Configuration, Deployment & Environment Management**
   - Check for correct management of environment-specific configurations and secrets.
   - Assess deployment-readiness including logging, error monitoring, and scalability considerations.

### Optional Enhancements (Good-to-Have, Bonus Points):

7. **Testing**
   - Evaluate the coverage and quality of unit, integration, or end-to-end tests.
   - Bonus: The inclusion of comprehensive tests for critical paths, including edge cases and error conditions.

8. **Documentation**
   - Assess the clarity and thoroughness of inline code documentation, API documentation (using tools like Swagger, DRF schema generation, etc.), and overall project documentation.
   - Bonus: Detailed user/developer guides and well-documented deployment/configuration steps.

**Output Structure:**

Your evaluation must be organized into the following sections:


- **Positive Findings:**
  - List all bullet points for areas where the implementation meets or exceeds expectations.
  - Clearly indicate if any optional enhancements (testing, documentation) are present and their quality.

- **Negative Findings:**
  - List all bullet points for areas that require improvement.
  - Mention all the functionality that is missing with reference file name if possible.
  - Mention all the negative points with all the aspects of area.
  - Distinguish between deficiencies in core areas versus the absence or weakness of optional features, noting that core functionality is prioritized.

- **Overall Ratings:**
  - Provide a rating for each core category on a scale of 1 to 10.
  - Include bonus ratings/comments for optional enhancements if applicable.
  - Add average rating for the whole project. Consider 7.5 and above as good to hire.
  - Provide a clear recommendation: "Hire" or "Do Not Hire" based on the average rating.
"""

PYTHON_PROMPT = """
-- Additional Evaluation Criteria for Django --

1. **Django-Specific Criteria**
   - Verify that the project follows Django's project/app structure.
   - Check for correct use of Django's ORM and models.
   - Ensure django standards are followed.
   - Check all the API endpoints are working as expected.
   - Check if any kind of errors occurring for any type of use case.
2. **API Design & Error Handling**
   - Evaluate the RESTful API implementation for clarity, consistency, and robust error handling.
   - Check for effective middleware usage for logging and errors.
3. **Security Best Practices**
   - Confirm secure handling of inputs, use of rate limiting, and secure management of environment variables.
4. **Documentation**
    - Check if comments are added for complex, long, or difficult elements of the code.
"""

REACT_PROMPT = """
-- Additional Evaluation Criteria for React --
1. **Component Design & Reusability**
   - Assess the modularity and reusability of components.
   - Verify that components are appropriately stateless and maintain clear separation of concerns.

2. **Hooks & State Management**
   - Evaluate the proper use of React hooks and best practices in managing component lifecycle.
   - Confirm effective state management using Context API, Redux, or another state library.

3. **Performance Optimization**
   - Check for techniques such as memoization, lazy loading, and code splitting.
   - Evaluate error handling and usage of error boundaries.

4. **Styling & Theming**
   - Verify that styling (via CSS-in-JS, Sass, etc.) is managed consistently and supports responsive design.

5. **Use of Typescript**
   - Check if custom TypeScript types are defined wherever required.
   - Check if `any` is not used as a type anywhere.
   - Check if "Typescript Generics" are used properly wherever required.
   - Check if "Typescript Utility" types are used properly wherever required.

6. **Code Formatting**
   - Check if the code is properly formatted or not with consistent.
   - Check if the code doesn't have unnecessary white spaces and does have a new line wherever required.
   - Check if the code is consistent with variable and file naming conventions.

7. **ESLint**
   - Check if the codebase has proper ESLint setup and it is not disabled.
   - Check if ESLint rules are not disabled and followed properly.

8. **Modular Codebase**
   - Check if the DRY(Don't Repeat Yourself) principle is followed.
   - Check if the code is divided into reusable components and hooks.
   - Check if the code has the utils defined wherever required.
   - Check if the code doesn't repeat the same logic in multiple functions, methods or components.
   - Check if the code doesn't have long functions or methods.

9. **Performance**
   - Check if the code doesn't have unnecessary 'useEffect` hooks.
   - Check if the code doesn't have any 'console' statements.
   - Check if the code doesn't have any 'debugger' statements.
   - Check if the code doesn't have any dead or commented-out sections.

10. **Documentation**
    - Check if comments are added for complex, long, or difficult elements of the code.
"""

NODE_PROMPT = """
-- Additional Evaluation Criteria for Node.js --
1. **Server Architecture & Routing**
   - Evaluate the design and organization of server routes (e.g., using Express or Koa).
   - Assess the separation of business logic from routing and middleware.

2. **Asynchronous Operations & Performance**
   - Verify that asynchronous operations are handled properly using async/await or Promises.
   - Assess any performance optimizations such as clustering or caching.

3. **API Design & Error Handling**
   - Evaluate the RESTful API or GraphQL implementation for clarity, consistency, and robust error handling.
   - Check for effective middleware usage for logging and errors.

4. **Security Best Practices**
   - Confirm secure handling of inputs, use of rate limiting, and secure management of environment variables.
"""

ANGULAR_PROMPT = """
-- Additional Evaluation Criteria for Angular --
1. **Component Architecture & Modular Design**
   - Evaluate the use of Angular Modules for lazy loading and component organization.
   - Assess design patterns used within components and services.

2. **Data Binding & Observables**
   - Verify proper usage of two-way data binding, reactive forms, and RxJS for handling asynchronous operations.
   - Assess the management of data subscriptions.

3. **State Management & Dependency Injection**
   - Confirm effective use of dependency injection and state management libraries (e.g., NgRx) where applicable.

4. **UI/UX Integration & Material Design**
   - Evaluate adherence to UI component libraries (e.g., Angular Material) and overall integration of UI/UX standards.

5. **Build Optimization & CLI Usage**
   - Check usage of Angular CLI for optimizing production builds and managing environment configurations.

6. **Use of Typescript**
   - Check if custom TypeScript types are defined wherever required.
   - Check if `any` is not used as a type anywhere.
   - Check if "Typescript Generics" are used properly wherever required.
   - Check if "Typescript Utility" types are used properly wherever required.

7. **Code Formatting**
   - Check if the code is properly formatted or not with consistent.
   - Check if the code doesn't have unnecessary white spaces and does have a new line wherever required.
   - Check if the code is consistent with variable and file naming conventions.

8. **Modular Codebase**
   - Check if the DRY(Don't Repeat Yourself) principle is followed.
   - Check if the code is divided into reusable components and hooks.
   - Check if the code has the utils defined wherever required.
   - Check if the code doesn't repeat the same logic in multiple functions, methods or components.
   - Check if the code doesn't have long functions or methods.

9. **Performance**
   - Check if all the Observables are unsubscribed.
   - Check if the correct RxJs operators are used wherever required.

10. **Documentation**
    - Check if comments are added for complex, long, or difficult elements of the code.
"""

FLUTTER_PROMPT = """
-- Additional Evaluation Criteria for Flutter --
1. **Widget Architecture & UI Composition**
   - Evaluate the design and organization of widgets ensuring a clean widget tree.
   - Verify that widgets are decoupled and reusable where possible.

2. **State Management & Navigation**
   - Assess the implementation of state management solutions (e.g., Provider, Bloc, Redux) and navigation strategies.
   - Confirm predictable data flow and state updates.

3. **Performance & Responsiveness**
   - Evaluate app performance in terms of smooth UI transitions and optimized rendering.
   - Check the proper use of asynchronous operations such as futures and streams.

4. **Cross-Platform Consistency & Testing**
   - Verify that platform-specific configurations are handled effectively.
   - Bonus: Evaluate the quality of widget tests, integration tests, and overall testing strategy.
"""

# Explicitly export all prompts
__all__ = [
    'BASE_PROMPT',
    'PYTHON_PROMPT',
    'REACT_PROMPT',
    'NODE_PROMPT',
    'ANGULAR_PROMPT',
    'FLUTTER_PROMPT'
]