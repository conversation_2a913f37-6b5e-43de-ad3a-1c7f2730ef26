"""Code evaluator module for analyzing repositories using LlamaIndex."""

import os
import logging
import requests
import json
from typing import List, Tu<PERSON>, Dict, Optional
import base64
from pathlib import Path
import re

from llama_index.core import VectorStoreIndex, Settings
from llama_index.llms.ollama import Ollama
from llama_index.embeddings.ollama import OllamaEmbedding
from llama_index.core import Document

from .framework_prompts import PYTHON_PROMPT, REACT_PROMPT, NODE_PROMPT, ANGULAR_PROMPT, FLUTTER_PROMPT, BASE_PROMPT

# Configure logging
logger = logging.getLogger(__name__)

class CodeEvaluator:
    """Evaluates code repositories using LlamaIndex and CodeLlama."""

    def __init__(self):
        """Initialize the code evaluator with CodeLlama model."""
        self.model = os.getenv("EVALUATION_MODEL", "deepseek-r1:32b")
        self.model_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        # Set up the LLM and embedding model
        self.llm = Ollama(
            model=self.model,
            request_timeout=300,  # 5 minutes timeout
            temperature=0.1,      # Lower temperature for more focused responses
            context_window=4096,   # Limit context window to prevent memory issues
            base_url=self.model_url,
        )
        self.embed_model = OllamaEmbedding(
            model_name=self.model,
            base_url=self.model_url,
            request_timeout=60    # 1 minute timeout for embeddings
        )
        
        # Configure global settings
        Settings.llm = self.llm
        Settings.embed_model = self.embed_model

    def _prepare_evaluation_prompts(self, readme_content: str, framework: str) -> str:
        """
        Prepare evaluation prompt based on framework.
        
        Args:
            readme_content: README content
            framework: Framework type (e.g., "python-django", "react", etc.)
            
        Returns:
            Combined evaluation prompt
        """
        # Get framework-specific prompt
        if framework == "python-django":
            framework_prompt = PYTHON_PROMPT
        elif framework == "react":
            framework_prompt = REACT_PROMPT
        elif framework == "node-express":
            framework_prompt = NODE_PROMPT
        elif framework == "angular":
            framework_prompt = ANGULAR_PROMPT
        elif framework == "flutter":
            framework_prompt = FLUTTER_PROMPT
        else:
            framework_prompt = ""
        
        # Combine base prompt with framework-specific prompt
        combined_prompt = f"""
{BASE_PROMPT}

{framework_prompt}

Please analyze the following codebase and provide a comprehensive evaluation report.
The report should be well-structured and include specific examples from the code where relevant.

README Content:
{readme_content}

Please provide your evaluation in a clear, structured format with specific examples and line numbers where applicable.
"""
        
        return combined_prompt

    def _build_file_hierarchy(self, file_paths: List[str]) -> Dict:
        """
        Build a hierarchical structure of files and directories.
        
        Args:
            file_paths: List of file paths
            
        Returns:
            Dictionary representing the file hierarchy
        """
        hierarchy = {}
        
        for path in file_paths:
            parts = path.split('/')
            current = hierarchy
            
            for part in parts[:-1]:  # Process directories
                if part not in current:
                    current[part] = {}
                current = current[part]
                
            current[parts[-1]] = None  # Add file
            
        return hierarchy

    def _format_hierarchy(self, hierarchy: Dict, level: int = 0) -> str:
        """
        Format the file hierarchy as a markdown string.
        
        Args:
            hierarchy: File hierarchy dictionary
            level: Current indentation level
            
        Returns:
            Formatted markdown string
        """
        result = []
        indent = "  " * level
        
        for name, content in sorted(hierarchy.items()):
            if content is None:  # It's a file
                result.append(f"{indent}└─ {name}")
            else:  # It's a directory
                result.append(f"{indent}📁 {name}/")
                # Add vertical line for directory contents
                child_indent = "  " * (level + 1)
                child_content = self._format_hierarchy(content, level + 1)
                if child_content:
                    result.append(child_content)
                
        return "\n".join(result)

    def _fetch_repository_content(self, owner: str, repo: str, branch: str, token: str, update_callback: Optional[callable] = None) -> Tuple[List[Document], str]:
        """
        Fetch repository content using GitHub API.
        
        Args:
            owner: Repository owner
            repo: Repository name
            branch: Branch name
            token: GitHub token
            update_callback: Optional callback function to receive updates
            
        Returns:
            Tuple of (List of documents, file hierarchy markdown)
        """
        headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        # Get repository tree
        tree_url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{branch}?recursive=1"
        response = requests.get(tree_url, headers=headers)
        
        if response.status_code != 200:
            raise ValueError(f"Error fetching repository tree: {response.status_code} - {response.text}")
            
        tree_data = response.json()
        documents = []
        file_paths = []
        hierarchy = {}
        
        # Calculate total files to process
        total_files = sum(1 for item in tree_data.get("tree", []) 
                         if item["type"] == "blob" and self._should_process_file(item["path"]))
        processed_files = 0
        
        # Process each file in the tree
        for item in tree_data.get("tree", []):
            if item["type"] == "blob" and self._should_process_file(item["path"]):
                try:
                    # Update progress
                    processed_files += 1
                    progress = (processed_files / total_files) * 100
                    
                    # Get file content
                    content_url = f"https://api.github.com/repos/{owner}/{repo}/contents/{item['path']}?ref={branch}"
                    content_response = requests.get(content_url, headers=headers)
                    
                    if content_response.status_code == 200:
                        content_data = content_response.json()
                        if content_data.get("content"):
                            # Decode base64 content
                            content = base64.b64decode(content_data["content"]).decode("utf-8")
                            
                            # Create document
                            doc = Document(
                                text=content,
                                metadata={
                                    "file_name": item["path"],
                                    "file_type": Path(item["path"]).suffix,
                                    "file_size": len(content)
                                }
                            )
                            documents.append(doc)
                            file_paths.append(item["path"])
                            
                            # Update hierarchy and notify
                            parts = item["path"].split('/')
                            current = hierarchy
                            
                            for part in parts[:-1]:  # Process directories
                                if part not in current:
                                    current[part] = {}
                                current = current[part]
                                
                            current[parts[-1]] = None  # Add file
                            
                            # Call update callback if provided
                            if update_callback:
                                hierarchy_markdown = self._format_hierarchy(hierarchy)
                                update_callback(hierarchy_markdown, progress, processed_files, total_files)
                            
                except Exception as e:
                    logger.warning(f"Error processing file {item['path']}: {str(e)}")
                    continue
        
        # Final hierarchy formatting
        hierarchy_markdown = self._format_hierarchy(hierarchy)
                    
        return documents, hierarchy_markdown

    def _should_process_file(self, file_path: str) -> bool:
        """
        Determine if a file should be processed.
        
        Args:
            file_path: Path of the file
            
        Returns:
            True if file should be processed, False otherwise
        """
        # Skip binary files and large files
        if file_path.endswith((".png", ".jpg", ".jpeg", ".gif", ".ico", ".pdf", ".zip", ".tar", ".gz")):
            return False
            
        # Skip hidden files and directories
        if any(part.startswith(".") for part in file_path.split("/")):
            return False
            
        # Skip node_modules and other large directories
        if any(part in ("node_modules", "venv", "__pycache__", ".git", "dist", "build") for part in file_path.split("/")):
            return False
            
        # Skip specific file types
        if file_path.endswith((".log", ".lock", ".min.js", ".min.css")):
            return False
            
        return True

    def _evaluate_code_with_timeout(self, query_engine, readme_content: str, framework: str) -> str:
        """
        Evaluate code with timeout handling using structured prompts.
        
        Args:
            query_engine: LlamaIndex query engine
            readme_content: README content
            framework: Framework type (e.g., "python-django", "react", etc.)
            
        Returns:
            Evaluation report
        """
        evaluation_parts = []
        prompts = self._prepare_evaluation_prompts(readme_content, framework)
        
        for prompt_data in prompts:
            section_result = self._evaluate_section(query_engine, prompt_data)
            evaluation_parts.append(section_result)
        
        # Join all parts and ensure proper formatting
        report = "\n".join(evaluation_parts)
        return report.strip()

    def _evaluate_section(self, query_engine, prompt_data: Dict) -> str:
        """
        Evaluate a single section of the code.
        
        Args:
            query_engine: LlamaIndex query engine
            prompt_data: Dictionary containing section information and prompt
            
        Returns:
            Formatted section evaluation
        """
        try:
            evaluation = self._run_query_with_timeout(
                query_engine,
                prompt_data["prompt"],
                timeout=120  # 2 minutes timeout
            )
            
            # Ensure evaluation is a string
            if not isinstance(evaluation, str):
                evaluation = str(evaluation)
                
            return f"## {prompt_data['emoji']} {prompt_data['section']}\n\n{evaluation}\n\n"
            
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.warning(f"Error evaluating {prompt_data['section']}: {error_msg}")
            
            return f"## {prompt_data['emoji']} {prompt_data['section']}\n\n" \
                   f"Error evaluating this section: {error_msg}\n\n"

    def _run_query_with_timeout(self, query_engine, query: str, timeout: int = 120) -> str:
        """
        Run a query with timeout handling.
        
        Args:
            query_engine: LlamaIndex query engine
            query: Query to run
            timeout: Timeout in seconds
            
        Returns:
            Query response
            
        Raises:
            ValueError: If query execution fails
            TimeoutError: If query execution times out
        """
        import threading
        import queue
        
        # Create a queue to store the result
        result_queue = queue.Queue()
        
        # Define a function to run the query
        def run_query():
            try:
                result = query_engine.query(query)
                result_queue.put(result)
            except Exception as e:
                error_msg = str(e) if str(e) else "Unknown error"
                result_queue.put(ValueError(f"Error running query: {error_msg}"))
        
        # Start a thread to run the query
        thread = threading.Thread(target=run_query)
        thread.daemon = True
        thread.start()
        
        # Wait for the thread to complete or timeout
        thread.join(timeout)
        
        # Check if the thread is still running
        if thread.is_alive():
            raise TimeoutError(f"Query timed out after {timeout} seconds")
        
        # Get the result from the queue
        try:
            result = result_queue.get_nowait()
            if isinstance(result, Exception):
                raise result
            return str(result)
        except queue.Empty:
            raise TimeoutError(f"Query timed out after {timeout} seconds")

    def _verify_token(self, token: str) -> None:
        """
        Verify that the GitHub token is valid and has the necessary permissions.
        
        Args:
            token: GitHub token
            
        Raises:
            ValueError: If token is invalid or missing required scopes
        """
        headers = {"Authorization": f"token {token}"}
        
        # Check token validity by making a request to the GitHub API
        response = requests.get("https://api.github.com/user", headers=headers)
        
        if response.status_code == 401:
            raise ValueError("Invalid GitHub token. Please check if the token is correct and not expired.")
        elif response.status_code == 403:
            raise ValueError("GitHub token lacks necessary permissions. Please ensure it has 'repo' scope.")
        elif response.status_code != 200:
            raise ValueError(f"Error verifying GitHub token: {response.status_code} - {response.text}")
            
        # Check token scopes
        if "X-OAuth-Scopes" in response.headers:
            scopes = response.headers["X-OAuth-Scopes"].split(", ")
            
            # Check for required scopes
            required_scopes = ["repo"]
            missing_scopes = [scope for scope in required_scopes if scope not in scopes]
            
            if missing_scopes:
                raise ValueError(f"GitHub token missing required scopes: {', '.join(missing_scopes)}")
        
    def _extract_readme_content(self, documents: List[Dict]) -> str:
        """
        Extract README content from repository documents.

        Args:
            documents: List of repository documents

        Returns:
            README content as string
        """
        readme_content = "No README found"

        for doc in documents:
            if doc.metadata.get("file_name", "").lower() == "readme.md":
                readme_content = doc.text
                break

        return readme_content
        
    def _parse_github_url(self, repo_url: str) -> Tuple[str, str]:
        """
        Parse GitHub URL to extract owner and repository name.
        
        Args:
            repo_url: GitHub repository URL
            
        Returns:
            Tuple of (owner, repository_name)
        """
        # Remove trailing .git if present
        repo_url = repo_url.rstrip('.git')
        
        # Split URL and get the last two parts
        parts = repo_url.rstrip('/').split('/')
        if len(parts) >= 2:
            return parts[-2], parts[-1]
        raise ValueError(f"Invalid GitHub URL: {repo_url}") 

    def _detect_framework(self, documents: List[Document]) -> str:
        """
        Detect the framework used in the repository based on files and README.
        
        Args:
            documents: List of repository documents
            
        Returns:
            Detected framework name
        """
        # Initialize framework indicators
        framework_indicators = {
            'python-django': {
                'files': ['manage.py', 'wsgi.py', 'asgi.py', 'settings.py'],
                'dependencies': ['django', 'djangorestframework'],
                'directories': ['migrations']
            },
            'react': {
                'files': ['package.json', 'react-scripts', 'index.jsx', 'index.tsx', 'App.js', 'App.jsx', 'App.tsx'],
                'dependencies': ['react', 'react-dom', 'react-scripts'],
                'directories': ['components', 'src']
            },
            'node-express': {
                'files': ['app.js', 'server.js', 'index.js', 'package.json'],
                'dependencies': ['express', 'body-parser'],
                'directories': ['routes', 'controllers', 'middleware']
            },
            'angular': {
                'files': ['angular.json', 'tsconfig.json', 'karma.conf.js'],
                'dependencies': ['@angular/core', '@angular/common'],
                'directories': ['src/app']
            },
            'vue': {
                'files': ['vue.config.js', 'package.json'],
                'dependencies': ['vue', 'vuex', 'vue-router'],
                'directories': ['src/components']
            }
        }

        # Initialize scores for each framework
        framework_scores = {framework: 0 for framework in framework_indicators.keys()}
        
        # Extract file paths and content
        file_paths = set()
        package_json_content = None
        readme_content = None
        requirements_txt_content = None
        
        for doc in documents:
            file_path = doc.metadata.get('file_name', '')
            file_paths.add(file_path)
            
            # Check for specific files
            if file_path.lower() == 'package.json':
                package_json_content = doc.text
            elif file_path.lower() == 'readme.md':
                readme_content = doc.text
            elif file_path.lower() == 'requirements.txt':
                requirements_txt_content = doc.text

        # Check file patterns and directories
        for framework, indicators in framework_indicators.items():
            # Check for framework-specific files
            for file_pattern in indicators['files']:
                if any(file_pattern in path.lower() for path in file_paths):
                    framework_scores[framework] += 2

            # Check for framework-specific directories
            for directory in indicators['directories']:
                if any(f"{directory}/" in path.lower() for path in file_paths):
                    framework_scores[framework] += 1

        # Check dependencies in package.json
        if package_json_content:
            try:
                package_data = json.loads(package_json_content)
                all_deps = {
                    **package_data.get('dependencies', {}),
                    **package_data.get('devDependencies', {})
                }
                for framework, indicators in framework_indicators.items():
                    for dep in indicators['dependencies']:
                        if dep in all_deps:
                            framework_scores[framework] += 3
            except json.JSONDecodeError:
                pass

        # Check dependencies in requirements.txt
        if requirements_txt_content:
            requirements = requirements_txt_content.lower().split('\n')
            for framework, indicators in framework_indicators.items():
                for dep in indicators['dependencies']:
                    if any(dep.lower() in req.lower() for req in requirements):
                        framework_scores[framework] += 3

        # Check README content for framework mentions
        if readme_content:
            readme_lower = readme_content.lower()
            for framework in framework_indicators.keys():
                # Remove special characters and check for framework name
                clean_framework = framework.replace('-', ' ').lower()
                if clean_framework in readme_lower:
                    framework_scores[framework] += 1

        # Get the framework with the highest score
        detected_framework = max(framework_scores.items(), key=lambda x: x[1])
        
        # If no clear framework is detected (score = 0), default to python-django
        if detected_framework[1] == 0:
            logger.warning("No framework clearly detected, defaulting to python-django")
            return 'python-django'
            
        logger.info(f"Detected framework: {detected_framework[0]} with score {detected_framework[1]}")
        return detected_framework[0]

    def _get_initial_readme_content(self, owner: str, repo: str, token: str) -> str:
        """
        Get README content from the initial commit.
        
        Args:
            owner: Repository owner
            repo: Repository name
            token: GitHub token
            
        Returns:
            README content from initial commit
        """
        headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json"
        }
        
        try:
            # Get list of commits
            commits_url = f"https://api.github.com/repos/{owner}/{repo}/commits"
            params = {"per_page": 1, "page": 1}  # Get only the first page with one item
            response = requests.get(commits_url, headers=headers, params=params)
            response.raise_for_status()
            
            # Get total number of pages
            if "Link" in response.headers:
                last_page = int(re.search(r'page=(\d+)>; rel="last"', response.headers["Link"]).group(1))
                # Get the last page to find the initial commit
                params["page"] = last_page
                response = requests.get(commits_url, headers=headers, params=params)
                response.raise_for_status()
            
            commits = response.json()
            if not commits:
                return "No commits found"
            
            # Get the last commit (which is the initial commit)
            initial_commit_sha = commits[-1]["sha"]
            
            # Get the tree of the initial commit
            commit_url = f"https://api.github.com/repos/{owner}/{repo}/git/commits/{initial_commit_sha}"
            response = requests.get(commit_url, headers=headers)
            response.raise_for_status()
            tree_sha = response.json()["tree"]["sha"]
            
            # Get the tree contents
            tree_url = f"https://api.github.com/repos/{owner}/{repo}/git/trees/{tree_sha}?recursive=1"
            response = requests.get(tree_url, headers=headers)
            response.raise_for_status()
            
            # Find README file
            readme_blob = None
            for item in response.json()["tree"]:
                if item["path"].lower() == "readme.md":
                    readme_blob = item["sha"]
                    break
                
            if not readme_blob:
                return "No README found in initial commit"
            
            # Get README content
            blob_url = f"https://api.github.com/repos/{owner}/{repo}/git/blobs/{readme_blob}"
            response = requests.get(blob_url, headers=headers)
            response.raise_for_status()
            
            content = base64.b64decode(response.json()["content"]).decode("utf-8")
            return content
            
        except Exception as e:
            logger.warning(f"Error fetching initial README: {str(e)}")
            return "Error fetching initial README"

    def evaluate_repository(self, repo_url: str, branch: str = "main", update_callback: Optional[callable] = None, report_callback: Optional[callable] = None) -> str:
        """
        Evaluate a GitHub repository.

        Args:
            repo_url: URL of the GitHub repository
            branch: Repository branch to analyze
            update_callback: Optional callback function to receive updates
            report_callback: Optional callback function to receive report section updates

        Returns:
            Evaluation report as string
        """
        try:
            logger.info(f"Starting evaluation for URL: {repo_url}")
            owner, repo = self._parse_github_url(repo_url)
            
            # Get token from environment
            token = os.getenv("GITHUB_TOKEN")
            if not token:
                raise ValueError("GitHub token is required. Please set the GITHUB_TOKEN environment variable.")
            
            # Verify token is valid
            logger.info("Verifying GitHub token...")
            self._verify_token(token)
            logger.info("GitHub token verified successfully")
            
            # Get initial README content first
            logger.info("Fetching initial README content...")
            initial_readme_content = self._get_initial_readme_content(owner, repo, token)
            
            # Fetch repository content
            logger.info("Fetching repository content...")
            documents, file_hierarchy = self._fetch_repository_content(owner, repo, branch, token, update_callback)
            
            if not documents:
                raise ValueError(f"No readable files found in repository: {repo_url}")
            logger.info(f"Successfully loaded {len(documents)} documents from repository")

            # Create a focused index for README analysis with proper model configuration
            readme_index = VectorStoreIndex.from_documents(
                [Document(text=initial_readme_content)],
                embed_model=self.embed_model,
                llm=self.llm
            )

            readme_query_engine = readme_index.as_query_engine(
                response_mode="tree_summarize",
                streaming=False,
                llm=self.llm
            )
            
            # Query for problem statement with bullet points
            problem_statement_prompt = """
            You are a text extraction tool. Your only job is to extract the core problem statement from a README file (from the initial commit) as bullet points.

Instructions:
- ONLY extract actual problem descriptions or goals mentioned in the README.
- DO NOT explain what you're doing or repeat these instructions.
- Each bullet point MUST begin with: • (bullet and space)
- Be concise. Avoid implementation details unless they're key to the problem.
- DO NOT include commentary, reasoning, formatting notes, or blank lines.
- DO NOT invent content — only extract from the provided README.

Example output format:
• A short sentence summarizing the first part of the problem
• A second core objective or goal
• A third important requirement or constraint
            """
            
            try:
                problem_statement = self._run_query_with_timeout(
                    readme_query_engine,
                    problem_statement_prompt,
                    timeout=700
                )
                
                # Clean up and format the response
                bullet_points = []
                for line in problem_statement.strip().split('\n'):
                    line = line.strip()
                    if not line or line == '•':
                        continue  # Skip empty or meaningless bullet

                    if line:
                        # Replace various bullet point styles with consistent format
                        line = line.replace('- ', '• ').replace('* ', '• ')
                        if not line.startswith('• '):
                            line = '• ' + line

                        # Skip lines that look like AI instructions or commentary
                        if any(phrase in line.lower() for phrase in [
                            "i need to", "looking at the", "make sure", "the user wants", "so the", "that makes",
                            "the first point", "the second bullet", "that's the"
                        ]):
                            continue

                        bullet_points.append(line)
                
                final_statement = '\n'.join(bullet_points)
                
                # Stream the problem statement if callback is provided
                if report_callback:
                    report_callback("🎯 Problem Statement", final_statement)
                
            except Exception as e:
                logger.error(f"Error analyzing problem statement: {str(e)}")
                if report_callback:
                    report_callback("🎯 Problem Statement", 
                                  "Error analyzing problem statement. Proceeding with evaluation...")
            
            # Detect framework immediately after scanning
            logger.info("Detecting framework...")
            framework = self._detect_framework(documents)
            logger.info(f"Detected framework: {framework}")
            
            # Notify about framework detection if callback is provided
            if update_callback:
                update_callback(file_hierarchy, 100, len(documents), len(documents), framework)

            # Create vector index for full analysis
            logger.info("Creating vector index from documents...")
            index = VectorStoreIndex.from_documents(documents)
            logger.info("Vector index created successfully")
            
            # Create query engine
            logger.info("Creating query engine...")
            query_engine = index.as_query_engine(
                response_mode="tree_summarize",
                streaming=False
            )
            logger.info("Query engine created successfully")
            
            # Prepare evaluation prompt
            evaluation_prompt = self._prepare_evaluation_prompts(initial_readme_content, framework)
            
            # Run the evaluation
            logger.info("Starting code evaluation...")
            evaluation = self._run_query_with_timeout(query_engine, evaluation_prompt, timeout=700)
            logger.info("Code evaluation completed successfully")
            
            # Create the complete report
            report_sections = []
            
            # Add problem statement section
            if 'final_statement' in locals():
                report_sections.append(("🎯 Problem Statement", final_statement))
            
            # Add evaluation section
            report_sections.append(("📊 Evaluation Report", evaluation))
            
            # Generate final report
            final_report = "\n\n".join([f"## {title}\n{content}" for title, content in report_sections])
            
            return final_report
            
        except Exception as e:
            error_msg = str(e) if str(e) else "Unknown error"
            logger.error(f"Error during evaluation: {error_msg}")
            raise ValueError(f"Error during evaluation: {error_msg}") 