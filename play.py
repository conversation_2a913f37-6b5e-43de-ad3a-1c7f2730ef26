from rezio.utils.constants import DJ<PERSON>GO_LOGGER_NAME
import logging
from langchain_openai import ChatOpenAI
import json
from rezio.ai.agents.property_monitor.prompt_1 import property_monitor_prompt as prompt_1
from rezio.ai.agents.property_monitor.prompt_2 import property_monitor_prompt as prompt_2, schema
import traceback
from rezio.ai.constants import Model
from langchain_core.messages import SystemMessage, HumanMessage


logger = logging.getLogger(DJANGO_LOGGER_NAME)
import environ
env = environ.Env()
env.read_env("./.envs/.local/.django")
OPENAI_API_KEY = env("OPENAI_API_KEY")

def property_inquiries_agent(message):
    try:

        llm = ChatOpenAI(
            model=Model.GPT_4o,
            temperature=0,
            api_key=OPENAI_API_KEY
        )
        
        pre_prompt = prompt_1.format(
            input=message,
        )

        post_prompt = prompt_2.format(
            original_message=message,
        )

        pre_response = llm.invoke([
            SystemMessage(pre_prompt),
            HumanMessage(content=message),
        ])

        print("PRE RESPONSE....")
        print(pre_response.content) 
        # return

        response = llm.with_structured_output(schema).invoke([
            SystemMessage(post_prompt),
            HumanMessage(content=pre_response.content),
        ])

        json_string = json.dumps(response, indent=4)

        print("RESPONSE THAT NEED TO BE STORED....")
        print(json_string)

        return response

    except Exception as e:
        print(f"Unexpected error in property detail agent : {str(e)}")
        traceback.print_exc()
        return {
            "errors": [str(e)]
        }



property_inquiries_agent(
"""

"""
)

# Available for rent office space* 
#  _Location: galaxy blue sapphire plaza_ 
# - *Semi-Furnished Offices:*
#     - 622 sqft: ₹34,500
#     - 328 sqft: ₹18,000
#     - 481 sqft: ₹26,500
#     - 226 sqft: ₹12,500
# - *Fully Furnished Offices:*
#     - 480 sqft: ₹34,000
#     - 865 sqft: ₹60,500
#     - 653 sqft: ₹46,000
#     - 650 sqft: ₹45,500
#     - 345 sqft: ₹25,000
#     - 360 sqft: ₹26,000
#     - 326 sqft: ₹23,000
#     - 318 sqft: ₹22,500
#     - 565 sqft: ₹40,000
#     - 963 sqft: ₹67,500

# To schedule a visit or get more information, you can contact **********.

# _🔑 *NON MARKET  - MANDATE DEAL*
# 🔑*
# _      *• IDFC Bank_*
#         *• Area 2200+ 2200 sqft_*
# _     *•  Sushant lok 1_*
# _     *• Rent 1.55lac. + 1.55lac. per month_*
# ~     *• ROI 4.25%~*
#        *• Cheque- Flexible*
#        *• Asking-4.35cr.+ 4.35cr.*
#        *• Fresh Lease*

# *More Information Than Call__*
# _____________________________

# _Khushi Singh_
# *_Khushi Singh_*