import json
import traceback
from datetime import datetime
from typing import List, Dict, Any
from django.conf import settings
from pubnub.exceptions import PubNubException
from pubnub.pnconfiguration import PNConfiguration
from pubnub.pubnub import PubNub
from rezio.rezio.settings import env
import logging
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PubNubHandler:
    def __init__(self, agent_pubnub_uuid: str):
        # Configure PubNub
        self.pnconfig = PNConfiguration()
        self.pnconfig.publish_key = settings.PUBNUB_PUBLISH_KEY
        self.pnconfig.subscribe_key = settings.PUBNUB_SUBSCRIBE_KEY
        self.pnconfig.secret_key = settings.PUBNUB_SECRET_KEY
        self.pnconfig.uuid = agent_pubnub_uuid

        # MAXIMIZED TIMEOUT SETTINGS
        self.pnconfig.connect_timeout = 60
        self.pnconfig.non_subscribe_request_timeout = 60
        self.pnconfig.subscribe_request_timeout = 600

        # logging
        self.pnconfig.log_verbosity = True

        # origin
        # self.pnconfig.origin = "ps.pndsn.com"
        self.pubnub = PubNub(self.pnconfig)

    @log_input_output
    def generate_channel_name(self, prefix: str, unique_id: str) -> str:
        """
        Generate a unique channel name

        Args:
            prefix: Prefix for the channel name
            unique_id: Unique identifier to append

        Returns:
            str: Generated channel name
        """
        channel_name = f"{prefix}-{unique_id}"
        logger.info(f"Generated channel name: {channel_name}")
        return channel_name

    @log_input_output
    def add_channels_to_group(self, channel_group: str, channels: List[str]) -> bool:
        """
        Add channels to an existing channel group

        Args:
            channel_group: Name of the channel group
            channels: List of channel names to add

        Returns:
            bool: True if successful, False otherwise
        """

        logger.info(
            f"PUBNUB CONFIG {self.pnconfig.publish_key} {self.pnconfig.subscribe_key}"
        )
        logger.info(f"ADDING CHANNELS {channels} TO GROUPS {channel_group}")

        try:
            response = (
                self.pubnub.add_channel_to_channel_group()
                .channel_group(channel_group)
                .channels(channels)
                .sync()
            )

            logger.info(f"Successfully added channels to group")

            # Send greeting message
            for channel in channels:
                connection_message = {
                    "text": "You are now connected to the agent's AI assistant",
                    "sender": "System",
                    "timestamp": datetime.utcnow().isoformat(),
                    "type": "system_message",
                }

                self.publish_message(
                    target=channel_group,
                    message=json.dumps(
                        {
                            "role": "assistant",
                            "content": connection_message["text"],
                            "channel_id": channel,
                            "group_channel_id": channel_group,
                        }
                    ),
                    is_channel_group=True,
                    message_type="system_message",
                )
                logger.info(
                    f"Greeting message sent to {channel} and message {connection_message}"
                )

                greeting_message = {
                    "text": "Hi there! 👋 I'm Agent's AI assistant. Let me know how I can help!",
                    "sender": "Rezio AI",
                    "timestamp": datetime.utcnow().isoformat(),
                }

                self.publish_message(
                    target=channel_group,
                    message=json.dumps(
                        {
                            "role": "assistant",
                            "content": greeting_message["text"],
                            "channel_id": channel,
                            "group_channel_id": channel_group,
                        }
                    ),
                    is_channel_group=True,
                    message_type="text",
                )
                logger.info(
                    f"Greeting message sent to {channel} and message {greeting_message}"
                )

                predefined_questions = {
                    "text": "Here are some quick options to get started:",
                    "sender": "Rezio AI",
                    "timestamp": datetime.utcnow().isoformat(),
                    "type": "predefined_questions",
                    "questions": [
                        "Help me find a property within my budget",
                        "What properties does the agent have?",
                        "Summarise agent’s portfolio",
                        "Do you have any properties listed in Dubai?",
                    ],
                }

                self.publish_message(
                    target=channel_group,
                    message=json.dumps(
                        {
                            "role": "assistant",
                            "content": predefined_questions["questions"],
                            "channel_id": channel,
                            "group_channel_id": channel_group,
                        }
                    ),
                    is_channel_group=True,
                    message_type="predefined_questions",
                )
                logger.info(
                    f"Greeting message sent to {channel} and message {greeting_message}"
                )

            return True
        except PubNubException as e:
            logger.error(f"PubNubException: {str(e)}")
            traceback.print_exc()
            return False

        except Exception as e:
            logger.error(f"Unexpected error adding channels: {str(e)}")
            traceback.print_exc()
            return False

    @log_input_output
    def remove_channels_from_group(
        self, channel_group: str, channels: List[str]
    ) -> bool:
        """
        Remove channels from a channel group

        Args:
            channel_group: Name of the channel group
            channels: List of channel names to remove

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = (
                self.pubnub.remove_channel_from_channel_group()
                .channel_group(channel_group)
                .channels(channels)
                .sync()
            )
            logger.info(
                f"Successfully removed channels {channels} from group {channel_group}"
            )
            return True
        except PubNubException as e:
            logger.error(f"Failed to remove channels from group: {str(e)}")
            return False

    @log_input_output
    def delete_channel_group(self, channel_group: str) -> bool:
        """
        Delete an existing channel group

        Args:
            channel_group: Name of the channel group to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = (
                self.pubnub.remove_channel_from_channel_group()
                .channel_group(channel_group)
                .channels([])
                .sync()
            )
            logger.info(f"Successfully deleted channel group: {channel_group}")
            return True
        except PubNubException as e:
            logger.error(f"Failed to delete channel group: {str(e)}")
            return False

    @log_input_output
    def publish_message(
        self,
        target,
        message,
        is_channel_group=False,
        message_type="text",
    ) -> str | None:
        """
        Publish a message to either a channel or channel group

        Args:
            target: Name of the channel or channel group to publish to
            message: Dictionary containing the message data
            message example:
            ```python
            {
                "text": "Hello, world!"
            }
            ```
            is_channel_group: If True, publishes to channel group. If False, publishes to channel

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if is_channel_group:
                result = (
                    self.pubnub.list_channels_in_channel_group()
                    .channel_group(target)
                    .sync()
                )
                timetoken = []
                channels = result.result.channels
                for channel in channels:
                    result = (
                        self.pubnub.publish()
                        .channel(channel)
                        .message(message)
                        .custom_message_type(message_type)
                        .sync()
                    )
                    timetoken.append(result.result.timetoken)
                logger.info(
                    f"Successfully published message to channel group: {target}"
                )
            else:
                result = self.pubnub.publish().channel(target).message(
                    message
                ).custom_message_type(message_type).sync()
                timetoken.append(result.result.timetoken)
                logger.info(f"Successfully published message to channel: {target}")
            return timetoken
        except PubNubException as e:
            logger.error(f"Failed to publish message: {str(e)}")
            return None

    @log_input_output
    def publish_media(
        self,
        target,
        message,
        is_channel_group=False,
        message_type="media",
    ) -> bool:
        """
        Publish a message to either a channel or channel group

        Args:
            target: Name of the channel or channel group to publish to
            message: Dictionary containing the message data
            message example:
            ```python
            {
                "text": "Hello, world!"
            }
            ```
            is_channel_group: If True, publishes to channel group. If False, publishes to channel

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if is_channel_group:
                result = (
                    self.pubnub.list_channels_in_channel_group()
                    .channel_group(target)
                    .sync()
                )
                channels = result.result.channels
                for channel in channels:
                    self.pubnub.publish().channel(channel).message(
                        message
                    ).custom_message_type(message_type).sync()
                logger.info(
                    f"Successfully published message to channel group: {target}"
                )
            else:
                self.pubnub.publish().channel(target).message(
                    message
                ).custom_message_type(message_type).sync()
                logger.info(f"Successfully published message to channel: {target}")
            return True
        except PubNubException as e:
            logger.error(f"Failed to publish message: {str(e)}")
            return False

    @log_input_output
    def create_channel_group(self, channel_group_name: str) -> bool:
        """
        Create a new channel group
        Args:
            channel_group_name: Name of the channel group to create
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            response = (
                self.pubnub.add_channel_to_channel_group()
                .channel_group(channel_group_name)
                .channels([])
                .sync()
            )
            logger.info(f"Successfully created channel group: {channel_group_name}")
            return True
        except PubNubException as e:
            logger.error(f"Failed to create channel group: {str(e)}")
            return False

    @log_input_output
    def get_message_history(self, channel, count=100):
        """
        Fetch message history for a channel using fetch_messages API

        Args:
            channel (str): Channel ID to fetch history for
            count (int): Maximum number of messages to retrieve

        Returns:
            List of messages in chronological order (oldest first)
        """
        try:
            logger.info(f"Fetching message history for channel: {channel}")

            # Make the fetch_messages request
            result = (
                self.pubnub.fetch_messages()
                .channels([channel])
                .maximum_per_channel(count)
                .include_meta(True)
                .include_uuid(True)
                .sync()
            )

            if not result:
                logger.info("No history result returned")
                return []

            # Debug the result structure
            logger.info(f"Result type: {type(result)}")
            logger.info(f"Result attributes: {dir(result)}")

            # Try to access the messages based on the actual structure
            messages = []

            # Check if result has a 'result' attribute (newer SDK versions)
            if hasattr(result, "result"):
                logger.info("Using result.result structure")
                if (
                    hasattr(result.result, "channels")
                    and channel in result.result.channels
                ):
                    messages = result.result.channels[channel]
            # Check if result directly has 'channels' attribute (older SDK versions)
            elif hasattr(result, "channels") and channel in result.channels:
                logger.info("Using result.channels structure")
                messages = result.channels[channel]
            else:
                logger.info(f"Could not find messages in result structure: {result}")

            logger.info(
                f"Retrieved {len(messages)} messages from history for channel {channel}"
            )

            # Debug: logger.info first message if available
            if messages and len(messages) > 0:
                logger.info(f"First message sample: {messages[0]}")
                logger.info(f"First message attributes: {dir(messages[0])}")

                # Try to access message content based on actual structure
                if hasattr(messages[0], "message"):
                    logger.info(f"Message content: {messages[0].message}")
                elif hasattr(messages[0], "entry"):
                    logger.info(f"Message content: {messages[0].entry}")

            # Return messages in chronological order (oldest first)

            message_content = []
            for messageItem in messages:
                # Access the message content
                message_content.append(messageItem.message)

            return message_content

        except Exception as e:
            logger.info(f"Error fetching message history: {str(e)}")
            import traceback

            traceback.logger.info_exc()
            return []
