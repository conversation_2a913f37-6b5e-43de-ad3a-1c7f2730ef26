import os
from contextlib import contextmanager
from psycopg import Connection
from django.conf import settings
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import tools_condition
from rezio.ai.agents.single_agent.agent import single_agent
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import AgentType, AgentTools
from rezio.ai.tools import (
    single_agent_tool_node,
)

class Workflow:

    memory = MemorySaver()

    def __init__(self):
        self.workflow = StateGraph(AgentState)
        self.graph = None

        # Initialize the workflow with the state graph
        self.init_agent_nodes()
        self.init_edges()
        self.workflow.add_edge(START, AgentType.SINGLE_AGENT)
        self.checkerpoint = self.init_checkerpoint()
        self.graph = self.workflow.compile(checkpointer=self.checkerpoint)

    @contextmanager
    def get_checkpointer(self):
        connection = None
        try:
            db_config = settings.DATABASES["default"]
            DB_URI = (
                f"postgres://{db_config['USER']}:{db_config['PASSWORD']}@"
                f"{db_config['HOST']}:{db_config.get('PORT', '5432')}/{db_config['NAME']}"
            )
            # Create connection
            connection = Connection.connect(
                DB_URI,
                autocommit=True,
                prepare_threshold=0
            )

            # Create and setup checkpointer
            checkpointer = PostgresSaver(connection)
            checkpointer.setup()

            yield checkpointer

        finally:
            pass

    def init_checkerpoint(self):
        try:
            with self.get_checkpointer() as checkpointer:
                return checkpointer
        except Exception as e:
            print(f"error: {e}")

    def init_agent_nodes(self):

        self.workflow.add_node(AgentType.SINGLE_AGENT, single_agent)
        self.workflow.add_node(AgentTools.SINGLE_AGENT, single_agent_tool_node)


    def init_edges(self):
        # Tool routing

        self.workflow.add_conditional_edges(
            AgentType.SINGLE_AGENT,
            tools_condition,
            {
                "tools": AgentTools.SINGLE_AGENT,
                END: END
            }
        )
        self.workflow.add_edge(
            AgentTools.SINGLE_AGENT,
            AgentType.SINGLE_AGENT
        )

    def generate_graph_image(self):
        try:
            print("Generating graph...", self.graph)
            # Get the Mermaid PNG bytes directly
            png_bytes = self.graph.get_graph().draw_mermaid_png()
            output_file = "mermaid_graph.png"
            with open(output_file, "wb") as f:
                f.write(png_bytes)
            os.system(
                f"start {output_file}" if os.name == "nt" else
                f"open {output_file}" if os.name == "posix" else
                f"xdg-open {output_file}"
            )
            print(
                f"Graph saved as {output_file} and should open automatically."
            )
        except Exception as e:
            print(f"Error: {e}")


workflow = Workflow()
