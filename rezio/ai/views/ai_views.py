import logging
import traceback
import boto3
from botocore.exceptions import NoCredentialsError, ClientError
from django.conf import settings
from django.core.cache import cache
from aiohttp import ClientError
from django.conf import settings
from django.db import transaction
from django_redis import get_redis_connection
from rest_framework.views import APIView
from rest_framework.response import Response
from rezio.ai.utils import is_real_estate_inquiry
from rezio.assistant.serializers.ai_serializers import RezioAIAgentSerializer
from rest_framework.permissions import AllowAny
from rezio.assistant.models import ChatGroups, ChatHistory, PropertyInquiries, PubNubChannel, PropertyInquiriesMessages, AIMessageAnnotation
from rezio.assistant.tasks import run_rezio_langgraph_ai_agent, message_reader
from rest_framework import status
import json
from rezio.pubnub.handler import PubNubHandler
from rezio.properties.models import UserLevelPropertyData
from rezio.rezio.aws import S3Client
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, <PERSON><PERSON>Y_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.decorators import general_exception_handler
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.ai.constants import Actor
from rest_framework import serializers
from django.db.models import F

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class AIAgentView(APIView):
    permission_classes = [AllowAny]

    @general_exception_handler
    @transaction.atomic
    def post(self, request):
        """
        Generate a response for the buyer query using Iris reasoning agent
        Args:
            request: {
                "pubnub_channel_id": "pubnub_channel_id",
                "group_channel_id": "group_channel_id",
                "content": "Hello, world!",
                "role": "user"
            }
        Returns:
            An acknowledgement that the AI agent is running in background
        """
        serializer = RezioAIAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        pubnub_queryset = PubNubChannel.objects.filter(
            pubnub_channel_id=serializer.validated_data.get(
                "pubnub_channel_id"
            ),
            group_channel_id=serializer.validated_data.get("group_channel_id"),
        )
        if not pubnub_queryset.exists():
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    KEY_MESSAGE: "Invalid pubnub or group channel id",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Invalid pubnub or group channel id"
                    },
                },
            )

        pubnub_channel = pubnub_queryset.first()
        pubnub_handler = PubNubHandler(
            agent_pubnub_uuid=serializer.validated_data.get("uuid")
        )
        result = pubnub_handler.publish_message(
            target=pubnub_channel.group_channel_id,
            message=json.dumps(
                {
                    "role": "user",
                    "content": serializer.validated_data.get("content"),
                    "channel_id": pubnub_channel.pubnub_channel_id,
                    "group_channel_id": pubnub_channel.group_channel_id,
                }
            ),
            is_channel_group=True,
        )
        if result:
            ChatHistory.objects.create(
                pubnub_channel=pubnub_channel,
                author=Actor.Buyer,
                message=serializer.validated_data.get("content"),
                message_id=result[0],
            )

        try:
            # Test Redis connection first
            redis_client = get_redis_connection("default")
            print(redis_client.ping())
            print(redis_client)

            # Your existing code here
            task = run_rezio_langgraph_ai_agent.apply_async(
                kwargs={
                    "uuid": request.data.get("uuid"),
                    "agent_portfolio_id": request.data.get("agent_portfolio_id"),
                    "pubnub_channel_id": pubnub_channel.id,
                    "message": serializer.validated_data.get("content"),
                    "message_id": result[0],
                },
                queue="ai_tasks",
            )
            logger.info(f"Task ID: {task.id}")

        except ConnectionError as e:
            # Log the error and return a proper response
            logger.error(f"Redis connection error: {e}")
            return Response(
                {"error": "Service temporarily unavailable"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )
        except Exception as e:
            logger.error(f"Unexpected error in AIAgentView: {e}")
            traceback.print_exc()
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "AI agent running in background",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class WhatsAppWebHookView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:

            messages = request.data.get("messages")
            channel_id = request.data.get("channel_id")

            if not messages:
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={
                        KEY_MESSAGE: "Invalid request",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid request"},
                    },
                )

            message = messages[0]
            event = request.data.get("event")
            
            chat_id = message.get("chat_id")
            chat_name = message.get("chat_name")
            
            ChatGroups.objects.get_or_create(
                id=chat_id,
                defaults={
                    "name": chat_name,
                    "meta": {
                        "channel_id": channel_id,
                    },
                },
            )
            
            json_string = json.dumps(message, indent=4)

            print("INCOMING MESSAGE:")
            print(json_string)

            type_of_message = message.get("type")

            if type_of_message in ["reaction", "image"]:
                return Response(status=status.HTTP_200_OK)

            body = ""

            if "text" in message:
                body = message["text"]["body"]
            elif "link_preview" in message:
                body = message["link_preview"]["body"]

            if not is_real_estate_inquiry(body):
                return Response(status=status.HTTP_200_OK)

            message_reader.apply_async(
                kwargs={
                    "message": message,
                    "body": body,
                    "event": event,
                    "channel_id": channel_id,
                    "retry": False,
                },
                queue="ai_tasks",
            )

            return Response(status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Unexpected error in AIAgentView: {e}")
            traceback.print_exc()
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


# create a new view which will be used to ret-try creating new property
class RetryPropertyCreationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):

        inquiries = PropertyInquiriesMessages.objects.exclude(
            id__in=UserLevelPropertyData.objects.filter(
                source='WHATS_APP'
            ).values_list('source_ref_id', flat=True).distinct()
        )

        for inquiry in inquiries:
            message = inquiry.meta.get("message")
            event = inquiry.meta.get("event")
            channel_id = inquiry.channel_id

            message_reader.apply_async(
                kwargs={
                    "message": message,
                    "body": inquiry.raw_message,
                    "event": event,
                    "channel_id": channel_id,
                    "retry": True,
                },
                queue="ai_tasks",
            )

        return Response(status=status.HTTP_200_OK)


class PropertyInquiriesSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyInquiries
        fields = '__all__'


class InputOutputDataset(APIView):
    permission_classes = [AllowAny]

    def post(self, request):        
        page = request.data.get('page', 1)      
        limit = request.data.get('limit', 100)
        offset = (int(page) - 1) * int(limit)
        
        # Get property inquiries with their related messages
        inquiries = PropertyInquiriesMessages.objects.filter(
            propertyinquiries__isnull=False
        ).select_related('propertyinquiries').values(
            'id',
            'raw_message',  
            output=F('propertyinquiries__output')
        )[offset:offset+limit]
        
        return Response(status=status.HTTP_200_OK, data=list(inquiries))



class GetPresignedUrlForAnnotationUpload(APIView):
    """
    APIView to retrieve a pre-signed URL for uploading files to S3.
    This view uses AWS S3 to generate a temporary URL for uploading files.
    The URL is cached for subsequent requests to optimize performance.
    """
    permission_classes = [AllowAny]

    def post(self, request):
            try:
                
                key = request.data.get("key")
                content_type = request.data.get("contentType")
                
                s3_client = S3Client()
                
                url = s3_client.generate_presigned_url(key, content_type)

                return Response({"url": url}, status=status.HTTP_200_OK)

            except NoCredentialsError:
                
                logger.error("AWS credentials not provided")
                return Response(
                    {"detail": "Internal server error: AWS credentials missing"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

            except ClientError as e:
                error_code = e.response.get("Error", {}).get("Code", None)
                if error_code == "NoSuchKey":
                    logger.error("Privacy policy file not found in S3")
                    return Response(
                        {"detail": "Privacy policy file not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
                else:
                    logger.error(f"AWS ClientError occurred: {e}")
                    return Response(
                        {"detail": "Internal server error"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
            except Exception as e:
                logger.error(f"Unexpected error occurred: {e}")
                return Response(
                    {"detail": "An unexpected error occurred"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
class GetAnnotationData(APIView):
    """
    APIView to retrieve JSON data from annotation files.
    This view fetches and parses JSON files from a specific annotation directory.
    """
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            # Get the annotation ID from query params
            annotation_id = request.query_params.get('annotation_id')
            if not annotation_id:
                return Response(
                    {"detail": "annotation_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Construct the S3 prefix
            prefix = f"ai/annotations/{annotation_id}/"

            # Get S3 client and fetch JSON data
            s3_client = S3Client()
            json_data = s3_client.get_json_files_from_prefix(prefix)

            return Response({
                "data": json_data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting annotation data: {e}")
            return Response(
                {"detail": "An error occurred while fetching annotation data"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CreateAnnotation(APIView):
    """
    APIView to create or update an annotation in AIMessageAnnotation model.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            sample_id = request.data.get('sampleId')
            if not sample_id:
                return Response(
                    {"detail": "sampleId is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            inquiry = PropertyInquiriesMessages.objects.get(id=sample_id)

            # Try to get existing annotation
            annotation, created = AIMessageAnnotation.objects.get_or_create(
                inquiry=inquiry,
                defaults={'data': request.data}
            )

            # If annotation exists, update it
            if not created:
                annotation.data = request.data
                annotation.save()

            return Response({
                "message": "Annotation created successfully" if created else "Annotation updated successfully",
                "data": annotation.data
            }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except PropertyInquiriesMessages.DoesNotExist:
            return Response(
                {"detail": "Inquiry message not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error creating/updating annotation: {e}")
            traceback.print_exc()
            return Response(
                {"detail": "An error occurred while creating/updating annotation"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class GetAnnotation(APIView):
    """
    APIView to retrieve annotation data for a specific inquiry.
    """
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            annotation_id = request.query_params.get('annotation_id')
            if not annotation_id:
                return Response(
                    {"detail": "annotation_id is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get annotations filtered by inquiry_id and ordered by last modified
            annotations = AIMessageAnnotation.objects.filter(
                inquiry_id=annotation_id
            ).order_by('-updated_at')
            
            # Convert queryset to list of dictionaries in the required format
            annotation_list = []
            for annotation in annotations:
                annotation_list.append({
                    'key': f'ai/annotations/{annotation.inquiry.id}/{annotation.created_at.isoformat()}.json',
                    'data': annotation.data,
                    'size': len(str(annotation.data)),
                    'last_modified': annotation.updated_at.isoformat()
                })

            return Response({'data': annotation_list}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error getting annotations: {e}")
            return Response(
                {"detail": "An error occurred while fetching annotations"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )