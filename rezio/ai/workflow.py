import sys
import os
import environ
from pathlib import Path
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import tools_condition
from rezio.ai.constants import AgentType

sys.path.append(str(Path(__file__).parent.parent.parent))

from rezio.ai.agents.output_guardrails.agent import output_guard_rail
from rezio.ai.agents.preference_builder.agent import preference_agent
from rezio.ai.agents.real_estate.agent import real_estate_agent
from rezio.ai.agents.law_assistance.agent import law_assistance_agent
from rezio.ai.agents.schedule_visit_agent.agent import schedule_visit_agent
from rezio.ai.agents.property_details.agent import property_details_agent
from rezio.ai.agents.orchestrator.agent import orchestrator, orchestrator_router
from rezio.ai.agents.input_guardrails.agent import (
    input_guard_rail,
    input_guard_rail_router,
)
from rezio.ai.agents.context_builder.agent import context_builder
from rezio.ai.workflow_state import AgentState
from rezio.ai.tools import (
    context_builder_tool_node,
    property_details_tool_node,
    law_assistance_tool_node,
    preference_tool_node,
)

# env = environ.Env()
# env.read_env("./.envs/.local/.django")
from django.conf import settings

LANGCHAIN_TRACING_V2 = settings.LANGCHAIN_TRACING_V2
LANGSMITH_ENDPOINT = settings.LANGSMITH_ENDPOINT
LANGSMITH_API_KEY = settings.LANGSMITH_API_KEY
LANGSMITH_PROJECT = settings.LANGSMITH_PROJECT

# Graph init.
workflow = StateGraph(AgentState)

# Nodes
workflow.add_node(AgentType.INPUT_GUARD_RAIL, input_guard_rail)
workflow.add_node(AgentType.CONTEXT_BUILDER, context_builder)
workflow.add_node(AgentType.ORCHESTRATOR, orchestrator)
workflow.add_node(AgentType.PROPERTY_DETAILS_AGENT, property_details_agent)
workflow.add_node(AgentType.SCHEDULE_VISIT_AGENT, schedule_visit_agent)
workflow.add_node(AgentType.LAW_ASSISTANCE_AGENT, law_assistance_agent)
workflow.add_node(AgentType.PREFERENCE_AGENT, preference_agent)
workflow.add_node(AgentType.REAL_ESTATE_AGENT, real_estate_agent)
workflow.add_node(AgentType.OUTPUT_GUARD_RAIL, output_guard_rail)

# Tool Node
workflow.add_node("context_builder_tools", context_builder_tool_node)
workflow.add_node("property_details_tools", property_details_tool_node)
workflow.add_node("law_assistance_tools", law_assistance_tool_node)
workflow.add_node("preference_tools", preference_tool_node)

# Edges
workflow.add_conditional_edges(
    AgentType.INPUT_GUARD_RAIL,
    input_guard_rail_router,
    {
        AgentType.CONTEXT_BUILDER: AgentType.CONTEXT_BUILDER,
        AgentType.OUTPUT_GUARD_RAIL: AgentType.OUTPUT_GUARD_RAIL,
    },
)


workflow.add_edge(AgentType.CONTEXT_BUILDER, AgentType.ORCHESTRATOR)

# Conditional routing
workflow.add_conditional_edges(
    AgentType.ORCHESTRATOR,
    orchestrator_router,
    {
        AgentType.PREFERENCE_AGENT: AgentType.PREFERENCE_AGENT,
        AgentType.LAW_ASSISTANCE_AGENT: AgentType.LAW_ASSISTANCE_AGENT,
        AgentType.SCHEDULE_VISIT_AGENT: AgentType.SCHEDULE_VISIT_AGENT,
        AgentType.PROPERTY_DETAILS_AGENT: AgentType.PROPERTY_DETAILS_AGENT,
    },
)

workflow.add_edge(AgentType.PREFERENCE_AGENT, AgentType.REAL_ESTATE_AGENT)
workflow.add_edge(AgentType.SCHEDULE_VISIT_AGENT, AgentType.REAL_ESTATE_AGENT)
workflow.add_edge(AgentType.LAW_ASSISTANCE_AGENT, AgentType.REAL_ESTATE_AGENT)
workflow.add_edge(AgentType.PROPERTY_DETAILS_AGENT, AgentType.REAL_ESTATE_AGENT)
workflow.add_edge(AgentType.REAL_ESTATE_AGENT, AgentType.OUTPUT_GUARD_RAIL)

# waiting for all nodes
# workflow.add_edge([
#     AgentType.PREFERENCE_AGENT,
#     AgentType.LAW_ASSISTANCE_AGENT,
#     AgentType.SCHEDULE_VISIT_AGENT,
#     AgentType.PROPERTY_DETAILS_AGENT,
# ],
#     AgentType.REAL_ESTATE_AGENT
# )
workflow.add_conditional_edges(
    AgentType.CONTEXT_BUILDER,
    tools_condition,
    {
        "tools": "context_builder_tools",
        # END: AgentType.CONTEXT_BUILDER
    },
)
workflow.add_edge("context_builder_tools", AgentType.CONTEXT_BUILDER)

workflow.add_conditional_edges(
    AgentType.PROPERTY_DETAILS_AGENT,
    tools_condition,
    {
        "tools": "property_details_tools",
        # END: AgentType.PROPERTY_DETAILS_AGENT
    },
)
workflow.add_edge("property_details_tools", AgentType.PROPERTY_DETAILS_AGENT)


workflow.add_conditional_edges(
    AgentType.PREFERENCE_AGENT,
    tools_condition,
    {
        "tools": "preference_tools",
        # END: AgentType.PREFERENCE_AGENT
    },
)

workflow.add_edge("preference_tools", AgentType.PREFERENCE_AGENT)

workflow.add_conditional_edges(
    AgentType.LAW_ASSISTANCE_AGENT,
    tools_condition,
    {
        "tools": "law_assistance_tools",
        # END: AgentType.LAW_ASSISTANCE_AGENT
    },
)

workflow.add_edge("law_assistance_tools", AgentType.LAW_ASSISTANCE_AGENT)

# Entry point
workflow.set_entry_point(AgentType.INPUT_GUARD_RAIL)

# Exit point
workflow.add_edge(AgentType.OUTPUT_GUARD_RAIL, END)

# Compile the graph
graph = workflow.compile()

# try:
#     # Get the Mermaid PNG bytes directly
#     png_bytes = graph.get_graph().draw_mermaid_png()

#     # Save to a file
#     output_file = "mermaid_graph.png"
#     with open(output_file, "wb") as f:
#         f.write(png_bytes)

#     # Optionally open the file with the system viewer
#     os.system(
#         f"start {output_file}" if os.name == "nt" else
#         f"open {output_file}" if os.name == "posix" else
#         f"xdg-open {output_file}"
#     )

#     print(f"Graph saved as {output_file} and should open automatically.")
# except Exception as e:
#     print(f"Error: {e}")

payload = {
    "conversation_id": 101,
    "agent_portfolio_id": "Ylqp5YqkvFg6PFP88GDt0TJJLRN2",
}

graph.invoke(
    {
        "payload": payload,  # Placeholder for any additional parameters
        "user_query": "I want to know about 4BHK properties in Dubai",
        "is_valid": True,
        "response": "",
        "context": {},
        "agents": [],
        "agent_responses": {},
        "response": "",
    }
)
