from typing import TypedDict, Annotated, List, Dict, Union
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.messages import HumanMessage
import operator

from langgraph.checkpoint.memory import MemorySaver
from IPython.display import Image, display
from langchain_openai import ChatOpenAI
from langgraph.graph.message import add_messages
from langgraph.graph import StateGraph, START, END
from langchain.prompts import PromptTemplate
from typing_extensions import TypedDict
from typing import Annotated
import os

# State Schema


class AgentState(TypedDict):
    # User input
    user_query: str
    # Input guardrail results
    is_valid: bool
    validation_message: str
    # Context builder outputs
    context: Dict[str, str]
    # Orchestrator outputs
    agent_tasks: List[str]
    # Agent responses
    agent_responses: Dict[str, str]
    # Final output
    response: str


# Input Guardrail
def input_guardrail(state: AgentState):
    query = state["user_query"]

    # Basic validation checks
    if not query or len(query) < 3:
        return {
            "is_valid": False,
            "validation_message": "Query is too short. Please provide more details.",
        }

    # Check for inappropriate content (simplified)
    banned_terms = ["illegal", "scam", "fraud", "hate"]
    if any(term in query.lower() for term in banned_terms):
        return {
            "is_valid": False,
            "validation_message": "Your query contains content we cannot process.",
        }

    return {"is_valid": True, "validation_message": "Query validated successfully"}


# Context Builder
def build_context(state: AgentState):
    if not state["is_valid"]:
        return {"context": {}}

    query = state["user_query"].lower()
    context = {"intent": "unknown"}

    # Determine intent
    if "property" in query or "house" in query or "apartment" in query:
        if "details" in query or "information" in query:
            context["intent"] = "property_details"
        elif "visit" in query or "schedule" in query or "see" in query:
            context["intent"] = "schedule_visit"
    elif "legal" in query or "law" in query or "contract" in query:
        context["intent"] = "legal_assistance"

    # Extract entities (simplified)
    if "new york" in query:
        context["location"] = "New York"
    elif "san francisco" in query:
        context["location"] = "San Francisco"

    return {"context": context}


# Orchestrator
def orchestrate_tasks(state: AgentState):
    if not state["is_valid"]:
        return {"agent_tasks": []}

    context = state["context"]
    tasks = []

    if context["intent"] == "property_details":
        tasks = ["property_details_agent"]
    elif context["intent"] == "schedule_visit":
        tasks = ["property_details_agent", "schedule_visit_agent"]
    elif context["intent"] == "legal_assistance":
        tasks = ["law_assistance_agent"]
    else:
        tasks = ["general_assistant"]

    return {"agent_tasks": tasks}


# Agents
def property_details_agent(state: AgentState):
    # Simulate property database lookup
    location = state["context"].get("location", "unknown location")
    return {
        "agent_responses": {
            "property_details": f"Here are 3 properties in {location}: \n"
            "1. 3-bedroom condo, $750k\n"
            "2. 2-bedroom apartment, $550k\n"
            "3. 5-bedroom house, $1.2M"
        }
    }


def schedule_visit_agent(state: AgentState):
    # Simulate scheduling system
    return {
        "agent_responses": {
            "schedule_visit": "Available viewing times:\n"
            "- Monday 10am-12pm\n"
            "- Wednesday 2pm-4pm\n"
            "- Friday 9am-11am"
        }
    }


def law_assistance_agent(state: AgentState):
    # Simulate legal knowledge base
    return {
        "agent_responses": {
            "legal_advice": "Standard real estate contracts in this area include:\n"
            "1. Purchase agreement\n"
            "2. Disclosure forms\n"
            "3. Financing addendums\n\n"
            "I recommend consulting a real estate attorney."
        }
    }


def preference_builder_agent(state: AgentState):
    return {
        "agent_responses": {
            "general_response": "I'm a real estate assistant. How can I help you with:\n"
            "- Property information\n"
            "- Scheduling visits\n"
            "- Legal questions about real estate"
        }
    }


def general_assistant(state: AgentState):
    return {
        "agent_responses": {
            "general_response": "I'm a real estate assistant. How can I help you with:\n"
            "- Property information\n"
            "- Scheduling visits\n"
            "- Legal questions about real estate"
        }
    }


# Real Estate Agent (Response Formatter)
def format_response(state: AgentState):
    if not state["is_valid"]:
        return {"response": state["validation_message"]}

    responses = []
    for agent, resp in state["agent_responses"].items():
        responses.append(resp)

    formatted = "\n\n".join(responses)
    return {"response": f"Real Estate Assistant Response:\n{formatted}"}


workflow = StateGraph(AgentState)

# Add nodes
workflow.add_node("input_guardrail", input_guardrail)
workflow.add_node("context_builder", build_context)
workflow.add_node("orchestrator", orchestrate_tasks)
workflow.add_node("property_details_agent", property_details_agent)
workflow.add_node("schedule_visit_agent", schedule_visit_agent)
workflow.add_node("law_assistance_agent", law_assistance_agent)
workflow.add_node("general_assistant", general_assistant)
workflow.add_node("format_response", format_response)
workflow.add_node("preference_builder_agent", preference_builder_agent)

# Add edges
workflow.add_edge("input_guardrail", "context_builder")
workflow.add_edge("context_builder", "orchestrator")


# Conditional agent routing
def route_to_agents(state: AgentState):
    tasks = state["agent_tasks"]
    if not tasks:
        return "format_response"
    elif len(tasks) == 1:
        return tasks[0]
    else:
        return tasks  # Parallel execution


workflow.add_conditional_edges(
    "orchestrator",
    route_to_agents,
    {
        "property_details_agent": "property_details_agent",
        "schedule_visit_agent": "schedule_visit_agent",
        "law_assistance_agent": "law_assistance_agent",
        "general_assistant": "general_assistant",
        "preference_builder_agent": "preference_builder_agent",
        "format_response": "format_response",
    },
)

# From agents to response formatter
workflow.add_edge("property_details_agent", "format_response")
workflow.add_edge("preference_builder_agent", "format_response")
workflow.add_edge("schedule_visit_agent", "format_response")
workflow.add_edge("law_assistance_agent", "format_response")
workflow.add_edge("general_assistant", "format_response")

# Final output
workflow.add_edge("format_response", END)

# Set entry point
workflow.set_entry_point("input_guardrail")

# Compile the graph
graph = workflow.compile()

try:
    # Get the Mermaid PNG bytes directly
    png_bytes = graph.get_graph().draw_mermaid_png()

    # Save to a file
    output_file = "mermaid_graph.png"
    with open(output_file, "wb") as f:
        f.write(png_bytes)

    # Optionally open the file with the system viewer
    os.system(
        f"start {output_file}"
        if os.name == "nt"
        else f"open {output_file}"
        if os.name == "posix"
        else f"xdg-open {output_file}"
    )

    print(f"Graph saved as {output_file} and should open automatically.")
except Exception as e:
    print(f"Error: {e}")
