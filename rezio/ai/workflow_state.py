"""
This module defines the TypedDicts used to represent the state and context of the workflow.

It includes the AgentState and AngetContext classes, which store information about user input,
validation results, context, tasks, and responses.
"""

from typing_extensions import TypedDict, Dict, Annotated
from langgraph.graph.message import add_messages


class AgentContext(TypedDict):
    agent: str
    context: str


class AgentState(TypedDict):
    """
    Represents the state of the workflow, including user input, validation results,
    context, tasks, and responses.
    """

    payload: Dict[str, str]

    # All messages
    messages: Annotated[list, add_messages]

    # All errors
    errors: Annotated[list, add_messages]

    # User input
    user_query: str

    # Input guardrail results
    is_valid: bool
    response_to: str

    # Context builder outputs
    context_builder_response: None
    context: Dict[str, str]

    # Law assistance agent response
    law_agent_messages: Annotated[list, add_messages]

    # Schedule visit agent response
    schedule_agent_messages: Annotated[list, add_messages]

    # Property details response
    property_agent_messages: Annotated[list, add_messages]

    # Property details response
    preference_agent_messages: Annotated[list, add_messages]

    # Real estate agent response
    real_estate_agent_messages: Annotated[list, add_messages]

    # Orchestrator outputs
    agents: Dict[str, AgentContext]

    # Agent responses
    wait: bool
    agent_responses: Dict[str, str]

    # Final output
    response: str
