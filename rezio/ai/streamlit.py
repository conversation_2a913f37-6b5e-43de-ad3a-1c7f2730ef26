import streamlit as st
from playground import Workflow
from langchain_core.messages import ToolMessage, HumanMessage, AIMessage
from utils import get_tool_state


graph = Workflow().graph

payload = {
    "conversation_id": 101,
    "agent_portfolio_id": '8eoNMp33YuWbLuU0dJrm8itrcLQ2',
}

checkpoint_config = {
    # "recursion_limit": 5,
    "configurable": {
        "thread_id": payload["conversation_id"],
        "checkpoint_ns": "rezio",
        "checkpoint_id": "agent_workflow"
    }
}


if "graph" not in st.session_state:
    st.session_state.graph = Workflow().graph

# if "state_output" not in st.session_state:
#     st.session_state.graph = []

# Initialize chat history
if "messages" not in st.session_state:
    st.session_state.messages = [{"role": "assistant", "content": "Let's start chatting! Type your query below 👇"}]

# Display chat messages from history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Accept user input
if prompt := st.chat_input("What is up?"):
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.markdown(prompt)

    # Process the workflow and stream updates
    with st.chat_message("assistant"):
        message_placeholder = st.empty()
        full_response = ""
        state_output = {"messages": [], "agent_responses": {}}

        message_placeholder.markdown("Working on your query...")

        try:
            # Initialize workflow input
            initial_state = {
                "user_query": prompt,
                "payload": payload,
                "messages": [],
                "agent_responses": {}
            }
            # Stream workflow state updates
            for event in st.session_state.graph.stream(initial_state, config=checkpoint_config):
                # Extract state updates
                if isinstance(event, dict):
                    for node, state in event.items():

                        if isinstance(state, dict):
                            if "messages" in state:
                                state_output["messages"] = state["messages"]
                            

                message_placeholder.markdown("Processing...")

                # Format the output for display
                display_text = ""

                if state_output["messages"]:

                    display_text += "Messages:\n"
                    for msg in state_output["messages"]:

                        if isinstance(msg, HumanMessage):
                            display_text += f"- User: {msg.content}\n"

                        elif isinstance(msg, AIMessage):

                            tools = msg.tool_calls
                            if len(tools) > 0:
                                tool = tools[0]
                                tool_name = tool.get("name", "")
                                tool_args = " ".join(str(value) for value in tool.get("args", {}).values())
                                display_text += get_tool_state(tool_name, "pending", tool_args) + "\n"
                            else:
                                skip_llm_logs = msg.additional_kwargs.get("skip_llm_logs", None)

                                if skip_llm_logs:
                                    display_text += "Processing...\n"
                                else:
                                    display_text += f"- {msg.content}\n"

                        elif isinstance(msg, ToolMessage):
                            display_text += get_tool_state(msg.name, "resolved") + "\n"

                        else:
                            display_text += f"- {msg.content}\n"

                # Update Streamlit UI
                message_placeholder.markdown(display_text or "Processing...")

                # Accumulate final response (last message content for simplicity)
                if state_output["messages"] and isinstance(state_output["messages"][-1], HumanMessage):
                    full_response = state_output["messages"][-1].content

            # Final update to remove cursor
            message_placeholder.markdown(display_text)
            st.session_state.messages.append({"role": "assistant", "content": display_text})

        except Exception as e:
            error_message = f"Error: {str(e)}"
            message_placeholder.error(error_message)
            full_response = error_message