import googlemaps
import environ

# TODO: get the from rezio.settings
# env = environ.Env()
# env.read_env("./.envs/.local/.django")
from django.conf import settings


class LocationService:
    googlemaps_client: googlemaps.Client = None

    def __init__(self):
        self.googlemaps_client = googlemaps.Client(key=settings.GOOGLE_MAPS_API_KEY)

    def getLatLongCoordinates(self, address: str):
        """Get coordinates using Google Geocoding API"""

        error = "Unable to fetch coordinates for the given address"

        try:
            geocode_result = self.googlemaps_client.geocode(address)
            if geocode_result:
                location = geocode_result[0]["geometry"]["location"]
                return {"lat": location["lat"], "long": location["lng"]}
                # return f" lat: {location['lat']}, long: {location['lng']}"
            raise Exception(error)

        except Exception as e:
            return None


locationService = LocationService()
# print(locationService.getLatLongCoordinates("<PERSON><PERSON><PERSON>"))
