import json
import environ
from langchain_openai import ChatOpenAI
from rezio.ai.constants import Model
from rezio.ai.services.db_raw_service import dbRawService

env = environ.Env()
env.read_env("./.envs/.local/.django")
OPENAI_API_KEY = env("OPENAI_API_KEY")


def get_agent_portfolio_properties_for_given_user_query(
    agent_portfolio_id: str, relevent_context_for_query_on_user_query: str
):
    """Get agent portfolio properties for given user query."""
    agent_profile = dbRawService.get_agent_profile(agent_portfolio_id)
    agent_associated_property_list = dbRawService.get_agent_associated_property_list(
        agent_profile.id
    )

    properties = dbRawService.get_property_details_by_property_ids(
        agent_associated_property_list
    )
    short_query = properties.values("id")

    schema = """{"properties_property":{"id":{"type":"UUID","constraints":"Primary Key","description":"Unique identifier for the property."},"area_id":{"type":"ForeignKey","constraints":"References Area(id), Nullable","description":"Reference to the area where the property is located.","schema":{"id":"UUID","country_id":"ForeignKey References Country(id)","state_id":"ForeignKey References State(id), Nullable","city_id":"ForeignKey References City(id), Nullable","name":"CharField(64)","is_active":"BooleanField","is_deleted":"BooleanField"}},"community_id":{"type":"ForeignKey","constraints":"References Community(id), Nullable","description":"Reference to the community where the property is located.","schema":{"id":"UUID","state_id":"ForeignKey References State(id), Nullable","city_id":"ForeignKey References City(id), Nullable","area_id":"ForeignKey References Area(id), Nullable","property_monitor_location_id":"IntegerField, Nullable","name":"CharField(128)","sub_loc_1":"CharField(128), Nullable","sub_loc_2":"CharField(128), Nullable","sub_loc_3":"CharField(128), Nullable","sub_loc_4":"CharField(128), Nullable","sub_loc_5":"CharField(128), Nullable","is_active":"BooleanField","is_deleted":"BooleanField","added_by":"CharField(32)"}},"property_type":{"type":"CharField(32)","constraints":{"Apartment":"Apartment","Villa":"Villa","Townhouse":"Townhouse","Office Space":"Office Space","Co-working":"Co-working","Shop":"Shop","Showroom":"Showroom","Godown/Warehouse":"Godown/Warehouse","Industrial Shed":"Industrial Shed","Industrial Building":"Industrial Building","Hospital/Clinic":"Hospital/Clinic"},"description":"Type of property."},"property_unit_type":"CharField(32), Default: sqft","total_area":"FloatField, Nullable","carpet_area":"FloatField, Nullable","number_of_bedrooms":"IntegerField, Nullable","user_unit_preference":"CharField(8), Default: sqft","tenancy_start_date":"DateField, Nullable","tenancy_end_date":"DateField, Nullable","property_category":{"type":"IntegerField","constraints":{"0":"Residential","1":"Commercial"},"description":"Category of the property."},"total_floors":"IntegerField, Nullable","building_type":{"type":"IntegerField","constraints":{"0":"Independent House","1":"Business Park","2":"Mall","3":"Standalone Building","4":"Independent Shop"},"description":"Type of building structure."}}}"""

    for attempt in range(3):
        try:
            llm = ChatOpenAI(
                model=Model.GPT_4o_mini, temperature=0.5, openai_api_key=OPENAI_API_KEY
            )
            dict = llm.invoke(
                [
                    {
                        "role": "system",
                        "content": f"""You are a (dialect) expert.
                        Please help to generate a (dialect) query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions.
                        === Property Table Schema
                        {schema}

                        === Main Filter Query
                        This query filters the properties for the given user agent. Your job is to add additional WHERE filter if needed based on user query. You do not modify the original query just add the additional filter.
                        {short_query.query}

                        Response Guidelines
                        1. If the provided context is sufficient, please generate a valid query without any explanations for the question. The query should start with a comment containing the question being asked.
                        2. If the provided context is insufficient, please explain why it can't be generated.
                        3. Make sure the query is within the scope of provided table schema, if not then please explain why it can't be generated and return out_of_scope as true. For example, if the user asks about the price of a property, and if the table schema does not have a price column, then you should return out_of_scope as true. If user has provided some information about query that is in schema and some that isn't then find based on the information that is there in schema and generate the query.
                        4. Use the main query and then append the required filters to it. Format main query if required without changing its logic. Like adding inverted commas around the values say not for sale becomes 'not for sale'
                        5. Query should strictly return IDs of the properties only. Even if user asks for a count/sum etc, you fetch the property ids only and return them in the query.
                        6. Operator may not match the given name and argument types.
                        7. Explicitly cast every single value based on the schema provided above in the query to the correct type like but not limited to `CAST(U0."action_status" AS INTEGER) = 2`, `CAST("properties_property"."property_publish_status" AS INTEGER) = 1` or `CAST("properties_property"."property_type" AS VARCHAR) = 'Villa'` etc.
                        8. Please use the most relevant table(s).
                        9. Please format the query before responding.
                        10. Query should be parsable an should never include anything uncompatible with SQL like new line character, tabs or any other special characters
                        11. Please always respond with a valid well-formed JSON object with the following format
                        ===Response Format
                        {{
                        "query": "A generated SQL query when context is sufficient.", "explanation": "An explanation of failing to generate the query.", "success_generating_query": "true or false", "out_of_scope": "true or false"
                        }}""",
                    },
                    {
                        "role": "user",
                        "content": f"Property Retrieval SQL Query For User Query: {relevent_context_for_query_on_user_query}",
                    },
                ],
            )

            print("##### DICT #####", dict)
            generated_query = dict["query"]

            if dict["out_of_scope"] == "true":
                return "This query is out of scope for this tool, answer the question based on the context that you have as of now."

            if dict["success_generating_query"] == "true":
                if generated_query:
                    print("##### GENERATED QUERY #####", generated_query)

            break

        except Exception as e:
            print(
                "##################################################### value of dict #####################################################\n\n\n\n\n",
                dict,
            )
            print(f"Attempt {attempt + 1} failed: {e}")

        try:
            final_query = None
            if generated_query:
                final_query = generated_query
            else:
                final_query = short_query.query

            print(
                "##################################################### final_query #####################################################\n\n\n\n\n",
                final_query,
            )

            from django.db import connection

            cursor = connection.cursor()
            cursor.execute(final_query)
            filtered_properties_ids = [row[0] for row in cursor.fetchall()]

            result = [
                {"property_id": property_id, "meta_data": {}}
                for property_id in filtered_properties_ids
            ]
            print("FILTERED PROPERTY ID____s", result)
            return json.dumps(result)
        except Exception as e:
            print(
                "##################################################### value of dict #####################################################\n\n\n\n\n",
                dict,
            )
            print(
                f"There was an error while fetching the properties: {e} for query: {final_query}"
            )
            return "There was an error while fetching the properties"
