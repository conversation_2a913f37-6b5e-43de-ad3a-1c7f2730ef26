import os
from langchain_core.tools import Tool
from langchain_google_community import GoogleSearchAPIWrapper
from langchain_community.document_loaders import AsyncHtmlLoader
from langchain_community.document_transformers import Html2TextTransformer

import asyncio
from playwright.async_api import async_playwright

# import environ
# env = environ.Env()
# env.read_env("./.envs/.local/.django")
# GOOGLE_CSE_ID = env("GOOGLE_CSE_ID")
# GOOGLE_API_KEY = env("GOOGLE_API_KEY")

from django.conf import settings

GOOGLE_CSE_ID = settings.GOOGLE_CSE_ID
GOOGLE_API_KEY = settings.GOOGLE_API_KEY

os.environ["GOOGLE_CSE_ID"] = GOOGLE_CSE_ID
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY


async def scrape_url(url: str):
    """Asynchronously scrapes the content of a single URL using Playwright."""
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        try:
            await page.goto(url, timeout=6000)  # Increased timeout if needed
            content = await page.content()
        except Exception as e:
            print(f"Error scraping {url}: {e}")
            content = ""
        finally:
            await browser.close()
    return content


from langchain_core.documents import Document


async def google_search_and_scrape(query: str, google_api_key: str, google_cse_id: str):
    """
    Searches Google for the top 5 results and scrapes the content of these websites
    concurrently, then converts HTML to text.

    Returns:
        A list of strings, where each string is the plain text content of one of the
        top 5 Google search results.
    """
    search = GoogleSearchAPIWrapper(
        google_api_key=google_api_key, google_cse_id=google_cse_id, k=5
    )
    results = search.results(query, num_results=5)
    urls = [result["link"] for result in results]

    # Scrape content concurrently
    tasks = [scrape_url(url) for url in urls]
    html_contents = await asyncio.gather(*tasks)

    # Wrap HTML strings in LangChain Document objects
    documents = [
        Document(page_content=html, metadata={"source": url})
        for html, url in zip(html_contents, urls)
    ]

    # Convert HTML to plain text
    transformer = Html2TextTransformer()
    text_documents = transformer.transform_documents(documents)

    # Return just the text content
    return [doc.page_content for doc in text_documents]


def just_make_google_search(query: str):
    search = GoogleSearchAPIWrapper(k=5)

    result = search.results("Dubai Land Department, " + query, num_results=5)

    print(result)

    return result


if __name__ == "__main__":
    result_texts = asyncio.run(
        google_search_and_scrape("What is a contract F?", GOOGLE_API_KEY, GOOGLE_CSE_ID)
    )
    for idx, content in enumerate(result_texts, 1):
        print(f"\n--- Result {idx} ---\n")
        print(content[:1000])  # Show first 1000 characters for brevity
