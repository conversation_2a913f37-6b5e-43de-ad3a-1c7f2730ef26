import json
import logging

# Django imports
from django.db import connection
from django.db.models import F

# Model imports
from rezio.assistant.models import (
    PubNubWebUser,
    Messages,
    Preferences,
)
from rezio.properties.models import AgentAssociatedProperty, Property
from rezio.properties.serializers.property_details_serializer import (
    AIAgentPortfolioViewSerializer,
    AIPropertyDetailSerializer,
)
from rezio.properties.text_choices import (
    OwnerIntentForProperty,
    PropertyPublishStatus,
    UserRequestActions,
)

from rezio.user.utils import get_agent_profile_object
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import general_exception_handler, log_input_output

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class DBService:
    def __init__(self):
        # debug mode can be configured here.
        pass

    @general_exception_handler
    def get_agent_portfolio(self, agent_portfolio_id: str):
        agent_profile = get_agent_profile_object(agent_portfolio_id)

        agent_associated_properties = (
            AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=agent_profile,
                is_request_expired=False,
            )
            .order_by("-created_ts")
            .values_list("property_id", flat=True)
        )

        properties = (
            Property.objects.filter(
                id__in=agent_associated_properties,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .order_by("-created_ts")
        )

        properties = (
            properties.select_related("propertyfinancialdetails").annotate(
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )
        ).order_by("-created_ts")

        serializer = AIAgentPortfolioViewSerializer(
            properties, context={"agent_profile": agent_profile}, many=True
        )
        properties_under_agent_portfolio = serializer.data

        portfolio = str(properties_under_agent_portfolio)

        return portfolio

    def get_property_details(self, properties):
        property_ids = list(map(lambda x: x["property_id"], properties))

        properties = Property.objects.filter(id__in=property_ids).order_by(
            "-created_ts"
        )

        properties = properties.select_related("propertyfinancialdetails").annotate(
            property_currency_code=F(
                "propertyfinancialdetails__property_currency_code"
            ),
            asking_price=F("propertyfinancialdetails__asking_price"),
            original_price=F("propertyfinancialdetails__original_price"),
            valuation=F("propertyfinancialdetails__valuation"),
            annual_rent=F("propertyfinancialdetails__annual_rent"),
        )

        serializer = AIPropertyDetailSerializer(properties, many=True)

        print("##### FETCHED PROPERTIES IDS #####", property_ids)

        return str(serializer.data)

    def get_properties_by_community_or_building(
        self, agent_portfolio_id: str, search_term: str
    ):
        agent_profile = get_agent_profile_object(agent_portfolio_id)
        property_ids = []

        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_community(%s, %s)",
                    [agent_profile.id, search_term],
                )
                property_ids = [row[0] for row in cursor.fetchall()]

            result = [
                {"property_id": property_id, "meta_data": {}}
                for property_id in property_ids
            ]
            return json.dumps(result)

        except Exception as e:
            return "There was an error while fetching the properties"

    def format_property_details(self, property_id):
        try:
            item = Property.objects.get(id=property_id)
        except Property.DoesNotExist:
            return f"Property with ID {property_id} does not exist."

        def format_images(images):
            return "\n".join(
                [
                    f"- Section Type: {image['section_type']}, Balcony Attached: {image['attached_balcony']}, Section Name: {image.get('other_section', 'N/A')}\n  Images: "
                    + ", ".join([media["media_file"] for media in image["media"]])
                    for image in images
                ]
            )

        try:
            property_details = (
                f"Property ID: {item.id}\n"
                f"Community: {item.community}\n"
                f"Unit Number: {item.unit_number}\n"
                f"Building Number: {item.building_number}\n"
                f"Building Name: {item.building_name}\n"
                f"Property Type: {item.property_type}\n"
                f"Floor Number: {item.floor_number}\n"
                f"Owner Verified: {item.owner_verified}\n"
                f"Postal Code: {item.postal_code}\n"
                f"Unit Type: {item.property_unit_type}\n"
                f"Tenancy Type: {item.tenancy_type}\n\n"
                f"### Area Details ###\n"
                f"Total Area: {item.total_area} sq. ft.\n"
                f"Carpet Area: {item.carpet_area} sq. ft.\n"
                f"Balcony Area: {item.balcony_area} sq. ft.\n\n"
                f"### Room Details ###\n"
                f"Number of Bedrooms: {item.number_of_bedrooms}\n"
                f"Number of Bathrooms (Common): {item.number_of_common_bathrooms}\n"
                f"Number of Bathrooms (Attached): {item.number_of_attached_bathrooms}\n"
                f"Number of Powder Rooms: {item.number_of_powder_rooms}\n\n"
                f"### Parking Details ###\n"
                f"Parking Available: {item.parking_available}\n"
                f"Covered Parking Spaces: {item.number_of_covered_parking}\n"
                f"Open Parking Spaces: {item.number_of_open_parking}\n"
                f"Parking Numbers: {', '.join(item.parking_number)}\n"
                f"Address: {item.address}\n\n"
                f"### Property Features ###\n"
                f"Furnished: {item.furnished}\n"
                f"Premium View: {item.premium_view}\n"
                f"Distressed Deal: {item.distressed_deal}\n\n"
                f"### Availability ###\n"
                f"Status: {item.status}\n"
                f"Occupancy: {item.occupancy_status}\n"
                f"Enable Payment Plan: {item.enable_payment_plan}\n\n"
                f"### Financial Details ###\n"
                f"Original Price: {item.property_currency_original_price} {item.property_currency_code}\n"
                f"Asking Price: {item.property_currency_asking_price} {item.property_currency_code}\n"
                f"Valuation: {item.property_currency_valuation} {item.property_currency_code}\n"
                f"Annual Service Charges: {item.property_currency_annual_service_charges}\n"
                f"Gains: {item.gains} ({item.gains_in_percentage}%)\n\n"
                f"### Sales History ###\n"
                + "\n".join(
                    [
                        f"- Date: {sale['evidence_date']}, Price: {sale['total_sales_price']} AED, Recurrence: {sale['sale_recurrence']}, Evidence: {sale['evidence']}"
                        for sale in item.sales_history
                    ]
                )
                + "\n\n"
                f"### Rental History ###\n"
                + "\n".join(
                    [
                        f"- Start Date: {rent['start_date']}, End Date: {rent['end_date']}, Total Rent: {rent['total_rent']} AED, Recurrence: {rent['rent_recurrence']}"
                        for rent in item.rental_history
                    ]
                )
                + "\n\n"
                f"### Unit Images ###\n" + format_images(item.unit_images) + "\n"
            )

        except Exception as e:
            return f"Error in formatting property details: {e}"
        return property_details

    @log_input_output
    def get_property_details(self, properties):
        property_ids = list(map(lambda x: x["property_id"], properties))

        properties = Property.objects.filter(id__in=property_ids).order_by(
            "-created_ts"
        )

        properties = properties.select_related("propertyfinancialdetails").annotate(
            property_currency_code=F(
                "propertyfinancialdetails__property_currency_code"
            ),
            asking_price=F("propertyfinancialdetails__asking_price"),
            original_price=F("propertyfinancialdetails__original_price"),
            valuation=F("propertyfinancialdetails__valuation"),
            annual_rent=F("propertyfinancialdetails__annual_rent"),
        )

        serializer = AIPropertyDetailSerializer(properties, many=True)

        print("##### FETCHED PROPERTIES IDS #####", property_ids)

        return str(serializer.data)

    def get_preferences(self, conversation_id: int) -> str:
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id as preference_id, preferences
                FROM assistant_preferences  
                WHERE messages_id = %s
                AND deleted_at IS NULL
                """,
                [conversation_id],
            )
            preferences_raw = cursor.fetchall()

        if preferences_raw:
            print("Raw preferences 1: ", preferences_raw)
            return f"User preferences are {preferences_raw}"

        print("No preferences found for this conversation_id")
        return "This user's preferences are not set yet"

    def create_preferences(
        self, user, conversation_id: str, preference_details: object
    ):
        preference_object = Preferences.objects.filter(messages=conversation_id).first()
        message_object = Messages.objects.get(buyer=user)

        Preferences.objects.create(
            buyer=user,
            messages=message_object,
            preferences=preference_details,
        )
        return "Preferences updated successfully, act upon the new preferences."

    @log_input_output
    def update_preferences(self, preference_id: str, preference_details: dict):
        try:
            # Fetch the existing preference object
            preference_object = Preferences.objects.filter(id=preference_id).first()

            if preference_object:
                print("Existing preferences: ", preference_object.preferences)

                # Update the preferences field with the new preference_details
                preference_object.preferences = preference_details
                preference_object.save()  # Save the changes to the database

                return f"User preferences with id {preference_id} have been updated."
            else:
                print("No preferences found for this preference_id")
                return "This user's preferences are not set yet."

        except Exception as e:
            # Handle any exceptions that may occur
            print(f"An error occurred: {str(e)}")
            return f"An error occurred while updating preferences: {str(e)}"

    def delete_preferences(self, preference_id: int) -> str:
        with connection.cursor() as cursor:
            # First, fetch the preferences to check if they exist
            cursor.execute(
                """
                SELECT preferences
                FROM assistant_preferences  
                WHERE id = %s::bigint
                """,
                [preference_id],
            )

            preferences_raw = cursor.fetchone()

            if preferences_raw:
                print("Raw preferences 1: ", preferences_raw)

                # If preferences exist, update the deleted_at field
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET deleted_at = (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::bigint
                    WHERE id = %s::bigint
                    """,
                    [preference_id],
                )

                connection.commit()  # Commit the transaction

                return f"User preferences with id {preference_id} have been marked as deleted."

            print("No preferences found for this preference_id")
            return "This user's preferences are not set yet"

    def get_nearby_properties(
        self, agent_portfolio_id: str, lat: float, long: float, range: int = 5
    ) -> str:
        try:
            agent_profile = get_agent_profile_object(agent_portfolio_id)

            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_location(%s, %s, %s, %s)",
                    [agent_profile.id, long, lat, range],
                )
                columns = [col[0] for col in cursor.description]
                properties = [dict(zip(columns, row)) for row in cursor.fetchall()]

            result = [
                {
                    "property_id": prop["property_id"],
                    "meta_data": {"distance": round(float(prop["distance_km"]), 2)},
                }
                for prop in properties
            ]

            return json.dumps(result)

        except Exception:
            return json.dumps([])  # Return empty array on failure


dbService = DBService()
