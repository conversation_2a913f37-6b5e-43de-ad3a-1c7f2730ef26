import json
import psycopg2
from psycopg2 import pool
import environ
from decimal import Decimal

# TODO: get the from rezio.settings
# env = environ.Env()
# env.read_env("./.envs/.local/.postgres")
# OPENAI_API_KEY = env("OPENAI_API_KEY")
from django.conf import settings
from django.db import connection

# Create a connection pool
# connection_pool = psycopg2.pool.SimpleConnectionPool(
#     1,  # Minimum number of connections
#     10,  # Maximum number of connections
#     dbname=settings.POSTGRES_DB,
#     user=settings.POSTGRES_USER,
#     password=settings.POSTGRES_PASSWORD,
#     host='localhost',
#     port=settings.POSTGRES_PORT,
# )


def fetch_as_dict(cursor):
    """Fetch all rows from the cursor and return as a list of dictionaries."""
    columns = [desc[0] for desc in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def decimal_default(obj):
    """Convert Decimal objects to float for JSON serialization."""
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError


class DBRawService:

    def __init__(self):
        self.conn = connection

    def __del__(self):
        print("Closing connection")
        # Return the connection to the pool
        # connection_pool.putconn(self.conn)

    def get_agent_portfolio(self, agent_portfolio_id: str) -> dict:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT *
                    FROM 
                        "user_agentprofile" 
                    WHERE 
                        "user_agentprofile"."user_id" =  %s
                    """,
                    [agent_portfolio_id]
                )
                portfolio = fetch_as_dict(cursor)

                if portfolio:
                    return portfolio[0]  # Convert to JSON string
                else:
                    return {}

        except Exception as e:
            print(f"Error: {str(e)}")
            return {}

    def get_property_details(self, property_ids: list[str]) -> list:
        try:
            with self.conn.cursor() as cursor:

                placeholders = ','.join(['%s'] * len(property_ids))
                query = f"SELECT * FROM properties_property as p WHERE p.id IN ({placeholders})"
                cursor.execute(query, property_ids)

                properties = fetch_as_dict(cursor)
                return properties
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def get_properties_by_community_or_building(self, agent_portfolio_id: str, searchTerm: str) -> list:
        try:
            agentProfile = self.get_agent_portfolio(agent_portfolio_id)
            agentProfileId = agentProfile["id"]

            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_community(%s, %s)", [agentProfileId, searchTerm])
                properties = fetch_as_dict(cursor)

                result = [
                    {
                        "propertyId": prop["property_id"],
                        "meta_data": {}
                    }
                    for prop in properties
                ]
                return result
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def get_preferences(self, conversation_id: int) -> list:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT id as preference_id, preferences
                    FROM assistant_preferences  
                    WHERE buyer_id = %s
                    AND deleted_at IS NULL
                    """,
                    [conversation_id]
                )

                preferences = fetch_as_dict(cursor)
                return preferences
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def create_preferences(self, conversation_id: str, preference_details: object) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO assistant_preferences (buyer_id, preferences, created_at, updated_at)
                    VALUES (%s, %s::jsonb, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """,
                    [
                        conversation_id,
                        json.dumps(preference_details),
                    ]
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def update_preferences(self, preference_id: str, preference_details: dict) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET preferences = %s::jsonb
                    WHERE id = %s
                    """,
                    [json.dumps(preference_details), preference_id]
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def delete_preferences(self, preference_id: int) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET deleted_at = (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::bigint
                    WHERE id = %s::bigint
                    """,
                    [preference_id]
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def get_nearby_properties(self, agent_id: str, lat: float, long: float, range: int = 5) -> list:
        try:

            agent_portfolio = self.get_agent_portfolio(agent_id)
            agent_portfolio_id = agent_portfolio["id"]

            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_location(%s, %s, %s, %s)",
                    [agent_portfolio_id, long, lat, range]
                )
                properties = fetch_as_dict(cursor)

                result = [
                    {
                        "property_id": prop["property_id"],
                        "meta_data": {
                            "distance": round(float(prop["distance_km"]), 2)
                        }
                    }
                    for prop in properties
                ]

                return result

        except Exception as e:
            print(f"Error: {str(e)}")
            return []  # Return empty array on failure
    
    def get_agent_associated_property_list(self, agent_profile_id: str) -> list:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT property_id FROM agent_associated_property WHERE action_status = %s AND is_associated = TRUE AND agent_profile_id = %s AND is_request_expired = FALSE ORDER BY created_ts DESC;", ["ACCEPTED", agent_profile_id]
                )
                agent_associated_property_list = fetch_as_dict(cursor)
                return agent_associated_property_list
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def execute_raw_sql(self, query: str, params: list = None) -> list:
        try:
            with self.conn.cursor() as cursor:
                print(f"Executing query: {query}")
                print(f"With parameters: {params}")
                cursor.execute(query, params or [])
                results = fetch_as_dict(cursor)
                return results
        except Exception as e:
            print(f"Error executing raw SQL: {str(e)}")
            return []


dbRawService = DBRawService()

# preferences = dbRawService.get_agent_portfolio('8eoNMp33YuWbLuU0dJrm8itrcLQ2')
# print(preferences)
# pref = {'area': 'Burj Khalifa', 'size': '4,000 sq.ft.', 'type': 'Apartment', 'budget': '2000 AED', 'number_of_bedrooms': 2}
