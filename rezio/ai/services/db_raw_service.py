import json
from decimal import Decimal

# TODO: get the from rezio.settings
# env = environ.Env()
# env.read_env("./.envs/.local/.postgres")
# OPENAI_API_KEY = env("OPENAI_API_KEY")
from django.db import connection


# Create a connection pool
# connection_pool = psycopg2.pool.SimpleConnectionPool(
#     1,  # Minimum number of connections
#     10,  # Maximum number of connections
#     dbname=settings.POSTGRES_DB,
#     user=settings.POSTGRES_USER,
#     password=settings.POSTGRES_PASSWORD,
#     host='localhost',
#     port=settings.POSTGRES_PORT,
# )


def fetch_as_dict(cursor):
    """Fetch all rows from the cursor and return as a list of dictionaries."""
    columns = [desc[0] for desc in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def decimal_default(obj):
    """Convert Decimal objects to float for JSON serialization."""
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError


class DBRawService:
    def __init__(self):
        self.conn = None

    def __enter__(self):
        self.conn = connection
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.conn:
            self.conn.close()

    def get_agent_portfolio(self, agent_portfolio_id: str) -> dict:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT *
                    FROM 
                        "user_agentprofile" 
                    WHERE 
                        "user_agentprofile"."user_id" =  %s
                    """,
                    [agent_portfolio_id],
                )
                portfolio = fetch_as_dict(cursor)

                if portfolio:
                    return portfolio[0]  # Convert to JSON string
                else:
                    return {}

        except Exception as e:
            print(f"Error: {str(e)}")
            return {}

    def get_property_details(self, property_ids: list[str]) -> list:
        try:
            with self.conn.cursor() as cursor:
                placeholders = ",".join(["%s"] * len(property_ids))
                query = f"""
                    SELECT 
                        p.id,
                        p.property_type,
                        p.floor_number,
                        p.total_area,
                        p.carpet_area,
                        p.number_of_bedrooms,
                        p.unit_number,
                        p.owner_intent,
                        p.owner_verified,
                        p.default_image,
                        p.total_floors,
                        p.number_of_bathrooms,
                        p.user_unit_preference,
                        pfd.original_price,
                        pfd.asking_price,
                        pfd.annual_rent,
                        COALESCE(
                            NULLIF(c.sub_loc_5, ''),
                            NULLIF(c.sub_loc_4, ''),
                            NULLIF(c.sub_loc_3, ''),
                            NULLIF(c.sub_loc_2, ''),
                            NULLIF(c.sub_loc_1, ''),
                            c.name
                        ) as building_name,
                        c.name as community_name 
                    FROM properties_property as p
                    LEFT JOIN properties_community c ON p.community_id = c.id
                    LEFT JOIN properties_propertyfinancialdetails pfd ON p.id = pfd.property_id
                    LEFT JOIN properties_propertyavailabilityandstatus pas ON p.id = pas.property_id
                    WHERE p.id IN ({placeholders})
                """
                # query = f"SELECT p.id, p.number_of_bathrooms, p.number_of_master_bedrooms, p.total_floors, p.postal_code, p.total_area, p.carpet_area, p.balcony_area, p.number_of_bedrooms, p.number_of_common_bathrooms, p.number_of_attached_bathrooms, p.number_of_powder_rooms, p.parking_available, p.number_of_covered_parking, p.number_of_open_parking, p.property_type, p.default_image, c.name as community_name FROM properties_property as p LEFT JOIN properties_community c ON p.community_id = c.id WHERE p.id IN ({placeholders})"
                cursor.execute(query, property_ids)

                properties = fetch_as_dict(cursor)
                return properties
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def get_properties_by_community_or_building(
        self, agent_portfolio_id: str, searchTerm: str
    ) -> list:
        try:
            agentProfile = self.get_agent_portfolio(agent_portfolio_id)
            agentProfileId = agentProfile["id"]

            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_community(%s, %s)",
                    [agentProfileId, searchTerm],
                )
                properties = fetch_as_dict(cursor)

                result = [
                    {"propertyId": prop["property_id"], "meta_data": {}}
                    for prop in properties
                ]
                return result
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def get_preferences(self, conversation_id: int) -> list:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT id as preference_id, preferences
                    FROM assistant_preferences  
                    WHERE buyer_id = %s
                    AND deleted_at IS NULL
                    """,
                    [conversation_id],
                )

                preferences = fetch_as_dict(cursor)
                return preferences
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def create_preferences(
        self, conversation_id: str, preference_details: object
    ) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO assistant_preferences (buyer_id, preferences, created_at, updated_at)
                    VALUES (%s, %s::jsonb, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """,
                    [
                        conversation_id,
                        json.dumps(preference_details),
                    ],
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def update_preferences(self, preference_id: str, preference_details: dict) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET preferences = %s::jsonb
                    WHERE id = %s
                    """,
                    [json.dumps(preference_details), preference_id],
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def delete_preferences(self, preference_id: int) -> bool:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET deleted_at = (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::bigint
                    WHERE id = %s::bigint
                    """,
                    [preference_id],
                )
                self.conn.commit()
            return True
        except Exception as e:
            print(f"Error: {str(e)}")
            return False

    def get_nearby_properties(
        self, agent_portfolio_id: str, lat: float, long: float, range: int = 5
    ) -> list:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_location(%s, %s, %s, %s)",
                    [agent_portfolio_id, long, lat, range],
                )
                properties = fetch_as_dict(cursor)

                result = [
                    {
                        "property_id": prop["property_id"],
                        "meta_data": {"distance": round(float(prop["distance_km"]), 2)},
                    }
                    for prop in properties
                ]

                return result

        except Exception as e:
            print(f"Error: {str(e)}")
            return []  # Return empty array on failure

    def get_agent_associated_property_list(self, agent_profile_id: str) -> list:
        try:
            with self.conn.cursor() as cursor:
                cursor.execute(
                    "SELECT property_id FROM agent_associated_property WHERE action_status = %s AND is_associated = TRUE AND agent_profile_id = %s AND is_request_expired = FALSE ORDER BY created_ts DESC;",
                    ["ACCEPTED", agent_profile_id],
                )
                agent_associated_property_list = fetch_as_dict(cursor)
                return agent_associated_property_list
        except Exception as e:
            print(f"Error: {str(e)}")
            return []

    def execute_raw_sql(self, query: str, params: list = None) -> list:
        try:
            with self.conn.cursor() as cursor:
                print(f"Executing query: {query}")
                print(f"With parameters: {params}")
                cursor.execute(query, params or [])
                results = fetch_as_dict(cursor)
                return results
        except Exception as e:
            print(f"Error executing raw SQL: {str(e)}")
            return []


dbRawService = DBRawService()

# preferences = dbRawService.get_agent_portfolio('8eoNMp33YuWbLuU0dJrm8itrcLQ2')
# print(preferences)
# pref = {'area': 'Burj Khalifa', 'size': '4,000 sq.ft.', 'type': 'Apartment', 'budget': '2000 AED', 'number_of_bedrooms': 2}
