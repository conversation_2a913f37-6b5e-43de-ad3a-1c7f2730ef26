from django.db.models import Q
def get_tool_state(tool_name, state_type="pending", args=""):

    thinking_states = {
        "get_property_for_user_query": f"Searching for properties matching your query...",
        "get_preferences": "Retrieving your preferences...",
        "create_preference": "Saving your new preferences...",
        "update_preference": "Updating your preferences...",
        "delete_preferences": "Removing the selected preferences...",
        "get_nearby_properties": f"Finding some properties for you...",
        "get_lat_long_coordinates": f"Fetching latitude and longitude coordinates...",
        "get_properties_by_community_or_building": f"Looking up for properties...",
        "get_property_details": "Gathering details for the selected properties...",
        "get_web_content_from_google": f"Searching the web..."
    }

    success_states = {
        "get_property_for_user_query": "Found some properties matching your query...",
        "get_preferences": "Retrieved your preferences!",
        "create_preference": "Saved your new preferences!",
        "update_preference": "Updated your preferences!",
        "delete_preferences": "Removed the selected preferences!",
        "get_nearby_properties": "Found some properties...",
        "get_lat_long_coordinates": "Got the coordinates!",
        "get_properties_by_community_or_building": "Retrieved properties!",
        "get_property_details": "Fetching the details for the selected properties...",
        "get_web_content_from_google": "Found web content!"
    }

    if state_type == "pending" and tool_name in thinking_states:
        return thinking_states[tool_name]
    elif state_type == "resolved" and tool_name in success_states:
        return success_states[tool_name]
    else:
        return "Thinking"


def apply_conditions(field_name: str, conditions: list[dict]) -> Q:
    """
    Constructs a Django Q object for filtering based on specified conditions.

    Args:
        field_name (str): The name of the field to apply conditions to.
        conditions (list[dict]): A list of dictionaries where each dictionary
                                 contains an operator and a value for filtering.

    Returns:
        Q: A Django Q object representing the combined filter conditions.
    """
    operator_map = {
        "eq": "",              # Equals — no suffix
        "lt": "__lt",          # Less than
        "lte": "__lte",        # Less than or equal to
        "gt": "__gt",          # Greater than
        "gte": "__gte",        # Greater than or equal to
        "in": "__in",          # In list
        "contains": "__icontains",  # Case-insensitive contains
    }

    conditions_filter = Q()
    for condition in conditions or []:
        op = condition.get('operator')
        value = condition.get('value')
        
        if op == "eq":
            q_filter = Q(**{field_name: value})
            conditions_filter &= q_filter
            
        else:
            suffix = operator_map[op]
            lookup = f"{field_name}{suffix}"
            q_filter = Q(**{lookup: value})
            conditions_filter &= q_filter
            
    return conditions_filter