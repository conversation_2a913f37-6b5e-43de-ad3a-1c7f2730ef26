from django.db.models import Q
import re

REAL_ESTATE_KEYWORDS = {
    # keywords
    "flat", "apartment", "villa", "house", "plot", "land", "property",
    "for sale", "for rent", "available", "bhk", "sqft", "rera", "furnished",
    "semi furnished", "unfurnished", "ready to move", "possession", "inventory",
    "carpet area", "built-up", "super built-up", "price", "rent", "lease",

    # Additional property types
    "bungalow", "studio", "duplex", "row house", "penthouse",

    # Land/space
    "site", "layout", "corner plot", "commercial space", "residential land",

    # Additional transaction terms
    "to let", "buy", "sell", "resale", "rental", "investment property",
    "under construction", "pre-launch",

    # Additional features & specs
    "rk", "square feet", "construction quality", "floor plan", "parking", "lift",
    "balcony", "terrace", "amenities", "security", "clubhouse", "swimming pool",
    "gym", "gated community", "green space", "vaastu", "modular kitchen",

    # Pricing & financing
    "rate per sqft", "down payment", "emi", "home loan", "bank approved",
    "registration", "stamp duty", "negotiable", "all inclusive", "maintenance charges",

    # Legal & administrative
    "rera approved", "title deed", "property tax", "noc", "legal clearance",

    # People & organizations
    "builder", "developer", "broker", "agent", "realty", "real estate",

    # Projects & locations
    "project", "township", "locality"
}

REAL_ESTATE_PATTERNS = [
    r"\b\d+\s?bhk\b",
    r"\b\d{3,5}\s?(sq\s?ft|sqft|sft|sf)\b",
    r"for\s+(sale|rent|lease)",
    r"\bprice\s*[:\-]?\s*\d+",
    r"\b(available|inventory|possession)\b",
    r"\bsemi[-\s]?furnished\b",
    r"\b(rera\s+approved)\b"
]

def get_tool_state(tool_name, state_type="pending", args=""):
    thinking_states = {
        "get_property_for_user_query": f"Searching for properties matching your query...",
        "get_preferences": "Retrieving your preferences...",
        "create_preference": "Saving your new preferences...",
        "update_preference": "Updating your preferences...",
        "delete_preferences": "Removing the selected preferences...",
        "get_nearby_properties": f"Finding some properties for you...",
        "get_lat_long_coordinates": f"Fetching latitude and longitude coordinates...",
        "get_properties_by_community_or_building": f"Looking up for properties...",
        "get_property_details": "Gathering details for the selected properties...",
        "get_web_content_from_google": f"Searching the web...",
    }

    success_states = {
        "get_property_for_user_query": "Found some properties matching your query...",
        "get_preferences": "Retrieved your preferences!",
        "create_preference": "Saved your new preferences!",
        "update_preference": "Updated your preferences!",
        "delete_preferences": "Removed the selected preferences!",
        "get_nearby_properties": "Found some properties...",
        "get_lat_long_coordinates": "Got the coordinates!",
        "get_properties_by_community_or_building": "Retrieved properties!",
        "get_property_details": "Fetching the details for the selected properties...",
        "get_web_content_from_google": "Found web content!",
    }

    if state_type == "pending" and tool_name in thinking_states:
        return thinking_states[tool_name]
    elif state_type == "resolved" and tool_name in success_states:
        return success_states[tool_name]
    else:
        return "Thinking"


def apply_conditions(field_name: str, conditions: list[dict]) -> Q:
    """
    Constructs a Django Q object for filtering based on specified conditions.

    Args:
        field_name (str): The name of the field to apply conditions to.
        conditions (list[dict]): A list of dictionaries where each dictionary
                                 contains an operator and a value for filtering.

    Returns:
        Q: A Django Q object representing the combined filter conditions.
    """
    operator_map = {
        "eq": "",  # Equals — no suffix
        "lt": "__lt",  # Less than
        "lte": "__lte",  # Less than or equal to
        "gt": "__gt",  # Greater than
        "gte": "__gte",  # Greater than or equal to
        "in": "__in",  # In list
        "contains": "__icontains",  # Case-insensitive contains
        "approx": "approx",  # Approximate match
    }

    conditions_filter = Q()
    for condition in conditions or []:
        op = condition.get("operator")
        value = condition.get("value")

        if op == "approx":
            lower = int(value) * 0.9
            upper = int(value) * 1.1
            q_filter = Q(**{f"{field_name}__gte": lower}) & Q(**{f"{field_name}__lte": upper})
            conditions_filter &= q_filter

        elif op == "eq":
            q_filter = Q(**{field_name: value})
            conditions_filter &= q_filter

        else:
            suffix = operator_map[op]
            lookup = f"{field_name}{suffix}"
            q_filter = Q(**{lookup: value})
            conditions_filter &= q_filter

    return conditions_filter


def format_property_details(property_details: list[dict]) -> str:
    """
    Formats a list of property details into a string.

    Args:
        property_details (list[dict]): A list of dictionaries where each dictionary
        contains property details.

    Returns:
        str: A formatted string containing property details.
    """
    property_field_schema = [
        ("Property ID", "id"),
        ("Property Type", "property_type"),
        ("Floor Number", "floor_number"),
        ("Unit Number", "unit_number"),
        ("Default Image", "default_image"),

        ("# Area Details", None),
        ("Total Area", "total_area"),
        ("Carpet Area", "carpet_area"),

        ("# Room Details", None),
        ("Number of Bathrooms", "number_of_bathrooms"),
        ("Number of Bedrooms", "number_of_bedrooms"),
        ("User Unit Preference", "user_unit_preference"),

        ("# Financial Details", None),
        ("Original Price", "original_price"),
        ("Asking Price", "asking_price"),
        ("Annual Rent", "annual_rent"),
        ("Security Deposit", "security_deposit"),

        ("# Ownership Details", None),
        ("Owner Intent", "owner_intent"),
        ("Owner Verified", "owner_verified"),

        ("# Location Details", None),
        ("Building Name", "building_name"),
        ("Community Name", "community_name"),

        ("# Building Details", None),
        ("Total Floors in the building", "total_floors"),
    ]

    formatted_property_details = []

    for item in property_details:
        line_item = []
        for label, key in property_field_schema:
            if key is None:
                line_item.append(f"\n{label}")
            else:
                value = item.get(key, "N/A")
                line_item.append(f"{label}: {value}")
        formatted_property_details.append("\n".join(line_item))
    return "\n\n----\n\n".join(formatted_property_details)


def is_real_estate_inquiry(message: str) -> bool:
    """
    Determines whether a message is related to real estate and should be sent to the LLM.
    """
    if not message or not message.strip():
        return False

    message_lower = message.lower().strip()

    if len(message_lower) < 10:
        return False

    if any(kw in message_lower for kw in REAL_ESTATE_KEYWORDS):
        return True

    for pattern in REAL_ESTATE_PATTERNS:
        if re.search(pattern, message_lower):
            return True

    return False


def generate_slug(message):
    slug_base = re.sub(r'[^a-zA-Z0-9\s]', '', message).lower()
    slug = '-'.join(slug_base.split())
    return f"{slug}"
