"""
This agent is responsible for building the context around the user's query to 
help the orchestrator assign tasks to relevant agents.
"""
import logging
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from django.conf import settings
from langchain_openai import ChatOpenAI
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.ai.services.db_raw_service import dbRawService
from rezio.ai.agents.single_agent.prompt import single_agent_prompt
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import Model
from rezio.ai.tools import single_agent_tools
OPENAI_API_KEY = settings.OPENAI_API_KEY
logger = logging.getLogger(DJANGO_LOGGER_NAME)


def single_agent(state: AgentState) -> AgentState:
    """
    Builds context for the conversation by analyzing user queries and retrieving relevant 
    property information.

    This agent uses tools to search for properties based on user preferences, location, 
    and specific queries. It processes conversation history and current user input to 
    determine what property information is needed.

    Args:
        state (AgentState): The current state containing user query, conversation history,
        and payload information.

    Returns:
        AgentState: Updated state with context information and agent response.
    """


    try:
        history = state["messages"]
        messages = []
        logger.info("INSIDE SINGLE AGENT :: HISTORY %d", len(history))

        if len(history) == 0 or (history and isinstance(history[-1], AIMessage) and history[-1].content != ""):
            history.append(HumanMessage(content=state["user_query"]))

        llm = ChatOpenAI(
            model=Model.GPT_4o,
            temperature=0,
            openai_api_key=OPENAI_API_KEY
        )

        llm_with_tools = llm.bind_tools(single_agent_tools, parallel_tool_calls=True)

        payload = state["payload"]

        preferences = dbRawService.get_preferences(payload["conversation_id"])

        prompt = single_agent_prompt.format(
            conversation_id=payload["conversation_id"],
            agent_portfolio_id=payload["agent_portfolio_id"],
            user_preferences=preferences,
            message_id=payload["message_id"]
        )

        llm_messages = [
            SystemMessage(content=prompt),
            *history
        ]

        response = llm_with_tools.invoke(llm_messages)

        response.pretty_print()

        messages.append(response)

        return {
            "messages": messages
        }

    except Exception as e:
        logger.error("Unexpected error in context builder: %s", str(e))
        return {
            "errors": [str(e)]
        }
