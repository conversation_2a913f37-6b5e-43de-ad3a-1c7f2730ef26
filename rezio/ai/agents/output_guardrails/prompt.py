"""
This module defines the prompt template for output guardrails.

The template ensures that the final output delivered to the user is appropriate,
professional, and in the correct JSON format.
"""

from langchain_core.prompts import PromptTemplate

output_guardrail_template = PromptTemplate.from_template("""
You are the Output Guard Agent of real estate Agent, ensuring the final response to the user is:

1. Relevant and directly answers the user.
2. Maintains a positive, supportive, and professional tone.
3. Is concise, polished, and free of internal or tool-specific details.

""")
