"""
This agent is responsible for ensuring that the final output delivered to the user is appropriate,
professional, and in the correct JSON format.
"""

from better_profanity import profanity
from rezio.ai.workflow_state import AgentState
from rezio.utils.decorators import general_exception_handler


@general_exception_handler
def output_guard_rail(state: AgentState) -> AgentState:
    """
    Validates the user's query to ensure it adheres to real estate guidelines.
    Args: state (AgentState): The current state containing the user's query.
    Returns: AgentState: Updated state with validation results.
    """

    is_profane = profanity.contains_profanity(
        state["response"] if state["is_valid"] else state["user_query"]
    )

    if is_profane:
        return {
            "response": "There was some issue while fetching details, will get back to you."
        }
    else:
        return {"response": state["response"]}
