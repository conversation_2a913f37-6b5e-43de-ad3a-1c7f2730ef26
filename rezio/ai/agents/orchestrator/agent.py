from rezio.utils.constants import DJ<PERSON>GO_LOGGER_NAME
import logging
import traceback
from langchain_openai import ChatOpenAI
import environ

from rezio.ai.agents.orchestrator.prompt import orchestrator_prompt
from rezio.ai.workflow_state import AgentState, AgentContext
from rezio.ai.constants import Model
from rezio.ai.agents.orchestrator.schema import response_schema
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
import json

# Initialize environment variables
# env = environ.Env()
# env.read_env("./.envs/.local/.django")
# OPENAI_API_KEY = env("OPENAI_API_KEY")
from django.conf import settings

OPENAI_API_KEY = settings.OPENAI_API_KEY


logger = logging.getLogger(DJANGO_LOGGER_NAME)


def orchestrator(state: AgentState) -> AgentState:
    logger.info(f"INSIDE ORCHESTRATOR :: HISTORY {len(state['messages'])}")

    try:
        payload = state["payload"]

        llm_model = ChatOpenAI(
            model=Model.GPT_4o,
            temperature=0.1,
            openai_api_key=OPENAI_API_KEY,
        )

        prompt = orchestrator_prompt.format(
            instructions=state["context_builder_response"],
            conversation_id=payload["conversation_id"],
            agent_portfolio_id=payload["agent_portfolio_id"],
        )

        response = llm_model.with_structured_output(response_schema).invoke(
            [
                SystemMessage(prompt),
                AIMessage(content=state["context_builder_response"]),
            ]
        )

        agents = response.get("agents")
        agentDict: AgentContext = {}

        for agent in agents:
            agentName = agent.get("agent")
            agentDict[agentName] = agent

        return {"agents": agentDict}

    except Exception as e:
        logger.error(f"Unexpected error in orchestrator : {str(e)}")
        return {"errors": [str(e)]}


def orchestrator_router(state: AgentState):
    agentsContext = state["agents"]
    agents = list(agentsContext.keys())
    logger.info(f"AGENTS TO BE CALLED NEXT {agents}")
    return agents
