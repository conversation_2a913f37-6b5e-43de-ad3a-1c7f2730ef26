response_schema = {
    "title": "OrchestratorResponse",
    "description": "Response from the Orchestrator agent containing a list of agents to be called with their contexts or a context for the real estate agent if no agents are required. The context includes property IDs, metadata, and task instructions for the assigned agents.",
    "type": "object",
    "properties": {
        "agents": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "agent": {
                        "type": "string",
                        "description": "Name of the agent to be called",
                    },
                    "context": {
                        "type": "string",
                        "description": "Markdown-formatted context for the agent, including property IDs, metadata, and task instructions",
                    },
                },
                "required": ["agent", "context"],
            },
            "description": "List of agents to be called with their respective contexts.",
        }
    },
    "required": ["agents"],
}
