import traceback
from rezio.assistant.models import ChatGroups
from rezio.utils.constants import DJANGO_LOGGER_NAME
import logging
from langchain_openai import ChatOpenAI

from rezio.ai.agents.property_monitor.prompt_1 import property_monitor_prompt as prompt_1
from rezio.ai.agents.property_monitor.prompt_2 import property_monitor_prompt as prompt_2, schema

from rezio.ai.constants import Model
from langchain_core.messages import SystemMessage, HumanMessage

# TODO: get the from rezio.settings
# import environ
# env = environ.Env()
# env.read_env("./.envs/.local/.django")
# OPENAI_API_KEY = env("OPENAI_API_KEY")
from django.conf import settings
OPENAI_API_KEY = settings.OPENAI_API_KEY

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def property_inquiries_agent(message, chat_id):
    try:

        llm = ChatOpenAI(
            model=Model.GPT_4o,
            temperature=0,
            openai_api_key=OPENAI_API_KEY
        )
        
        try:
            chat_group = ChatGroups.objects.get(id=chat_id)
            broker_group_info = chat_group.meta.get('group_info', None) if hasattr(chat_group, 'meta') else None
        except ChatGroups.DoesNotExist:
            broker_group_info = None



        pre_prompt = prompt_1.format(
            input=message,
            broker_group_info=broker_group_info,
        )

        post_prompt = prompt_2.format(
            original_message=message,
        )

        pre_response = llm.invoke([
            SystemMessage(pre_prompt),
            HumanMessage(content=message),
        ])

        response = llm.with_structured_output(schema).invoke([
            SystemMessage(post_prompt),
            HumanMessage(content=pre_response.content),
        ])

        logger.info(f"Response from property monitor agent: {response}")

        return response

    except Exception as e:
        traceback.print_exc()
        return {
            "errors": [str(e)]
        }
