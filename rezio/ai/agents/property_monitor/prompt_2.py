from langchain_core.prompts import PromptTemplate


property_monitor_prompt = PromptTemplate.from_template('''
You are a Real Estate AI Agent (Agent 2) schema engine that extracts structured property data from enrich structured property (inventory/requirement) data from previous agent. (Agent 1)

Your goal is to:
- Identify and extract concrete property details from each listing
- Support rental, sale, and investment (lease-backed sale) listings
- Fill all fields according to the schema provided
- Use default values strictly when data is missing
- Never assume or fabricate values

### General Instructions:

1. **Multiple Properties in One Message**:
   - If the message includes more than one listing, extract each as a separate object in the `properties` array.
   - Each property should independently carry fields based on the schema provided.

2. **Intent Classification**:
   - If the message includes "for rent", "available from", or "expected rent", classify `intent` as `"rent"`.
   - If the message includes "for sale", "asking", "ROI", or "price", classify `intent` as `"sale"`.

3. **Cheque & Payment Terms**:
   - If the message mentions terms like:
     - "Cheque circle"
     - "Cheque 1cr"
     - "Adjustment possible"
     - "60-40"
     - "Paper adjustment"
   - Copy these phrases **verbatim** into `meta.notes`. Do not modify or interpret. Do not add terms like "cash" or "black".

6. **Contact Handling**:
   - Extract all contact names and phone numbers, even if listed at the end.
   - Apply the same contact to all property entries in the message if no unique contact is tied to each.

7. **Rent Handling**
   - If a message mentions "rent" (not "rented"), this means the property is "available for rent," and the rent amount goes into only in `expected_rent` field.
   - If a message explicitly mentions "rented," put the rent amount into `annual_rent` field.
   - Never put rent into both fields simultaneously.

8. **Sale Handling:**
   - If property is for sale, then put the sale price in the `asking_price` field.

9. **Community Inheritance from Section Headers**
   - If the message contains section headers like “DLF Phase 2” or “Sushant Lok 1”, assume all following listings belong to that community until a new section starts. Assign the header as community.name.

9. **Tenant Preferences**
   - Preserve broker preferences like “Family only”, “All allowed”, “Company guest house allowed” exactly in meta.notes. Optionally map them to preferred_tenant_profile if supported.


### Golden Rules:
- Do not infer or assume any values
- Do not explain or interpret broker phrases — quote them directly
- Always return fields exactly matching the schema
''')

schema = {
    'name': 'extract_property_data',
    'description': 'Extract structured property data from a user message. Make sure if nothing is found for any values, STRICTLY use the default value specified in the schema.',
    'parameters': {
      'type': 'object',
      'properties': {
        'properties': {
          'type': 'array',
          'description': 'List of properties extracted from the message.',
          'items': {
            'type': 'object',
            'properties': {
              'meta': {
                'type': 'object',
                'description': 'Additional metadata about the property including notes, contact infos, and other extra data if necessary.',
                'properties': {
                  'notes': {
                    'type': 'string',
                    'description': "Notes about the property"
                  },
                  'contact': {
                    'type': 'array',
                    'description': 'Extract all contact details mentioned in the message such as name, phone number, or email of the broker, agent, or owner.',
                    'items': {
                      'type': 'object',
                      'properties': {
                        'name': {
                          'type': 'string',
                          'description': 'Contact name'
                        },
                        'phone': {
                          'type': [
                            'string',
                            'null'
                          ],
                          'description': 'Contact phone number'
                        },
                        'email': {
                          'type': [
                            'string',
                            'null'
                          ],
                          'description': 'Contact email address'
                        },
                        'address': {
                          'type': [
                            'string',
                            'null'
                          ],
                          'description': 'Contact address'
                        }
                      }
                    }
                  }
                }
              },
              'is_requirement': {
                'type': 'boolean',
                'description': 'Identifies the listing_intent of property. Is it inventory or a requirement?'
              },
              'rent_available_start_date': {
                    'type': [
                      'string',
                      'null'
                    ],
                    'format': 'date',
                    'description': 'Start date of rent availability if provided (do not assume).'
              },
              'intent': {
                'type': 'string',
                'description': "Identify the property's transaction intent. Choose 'rent' if the property is being offered for lease or for rent. Choose 'sale' if the property is being sold, even if it has a tenant or rental income. For lease-backed sales or investment properties showing ROI or asking price, always select 'sale'.",
                'enum': [
                  'rent',
                  'sale'
                ]
              },
              'address': {
                'type': 'object',
                'description': 'Address-related data.',
                'properties': {
                  'community': {
                    'type': 'string',
                    'description': 'Community name (no city/state/country).'
                  },
                  'area': {
                    'type': 'string',
                    'description': 'Area name (no city/state/country).'
                  },
                  'city': {
                    'type': 'string',
                    'description': 'City name.'
                  },
                  'state': {
                    'type': 'string',
                    'description': 'State name.'
                  },
                  'country': {
                    'type': 'string',
                    'description': 'Country name ie India, United States, etc.'
                  },
                  'country_short_name': {
                    'type': 'string',
                    'description': 'Country short name use the short name of the country ie IN for India, US for United States, etc.'
                  },
                  'formatted_address': {
                    'type': 'string',
                    'description': 'Formatted address.'
                  },
                  'confidence_score': {
                    'type': 'number',
                    'description': 'Confidence score for the address.'
                  }
                }
              },
              'property': {
                'type': 'object',
                'description': "Property details only include what's available in the message.",
                'properties': {
                  'unit_number': {
                    'type': [
                      'string',
                      'null'
                    ]
                  },
                  'building_number': {
                    'type': [
                      'string',
                      'null'
                    ]
                  },
                  'property_type': {
                    'type': 'string',
                    'description': "Select the best matching type based on how the space is used: commercial properties like Office, Shop, or Showroom; or residential properties like Apartment or Villa. Choose 'Office Space' for bank-leased or corporate listings.",
                    'enum': [
                      'Apartment',
                      'Villa',
                      'Townhouse',
                      'Builder Floor',
                      'Residential Plot',
                      'Residential Land',
                      'Office Space',
                      'Co-working',
                      'Shop',
                      'Showroom',
                      'Godown/Warehouse',
                      'Industrial Shed',
                      'Industrial Building',
                      'Hospital/Clinic',
                      'School',
                      'Retail Space',
                      'Hotel',
                      'Guest House',
                      'S.C.O Plot',
                      'Commercial Plot',
                      'Commercial Land',
                      'Factory'
                    ]
                  },
                  'floor_number': {
                    'type': [
                      'string',
                      'null'
                    ],
                    'description': "Indicates the numeric floor level(s) of the property. Accepts numeric values as strings (e.g., '0', '1', '2', etc.). For multiple floors, use a comma-separated list (e.g., '0,1,2'). Use '0' to represent the ground floor if applicable."
                  },
                  'postal_code': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'property_unit_type': {
                    'type': 'string',
                    'description': "0 = sqft, 1 = sqm, 2 = sqyd, 3 = acre, 4 = hectare, 5 = bigha",
                    'enum': [
                      '0',
                      '1',
                      '2',
                      '3',
                      '4',
                      '5'
                    ],
                    'default': '0'
                  },
                  'total_area': {
                    'description': 'Total area',
                    'type': [
                      'number',
                      'null'
                    ]
                  },
                  'carpet_area': {
                    'type': [
                      'number',
                      'null'
                    ]
                  },
                  'balcony_area': {
                    'type': [
                      'number',
                      'null'
                    ]
                  },
                  'number_of_bedrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_common_bathrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_attached_bathrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_powder_rooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'parking_available': {
                    'type': 'boolean',
                    'default': False
                  },
                  'number_of_covered_parking': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_open_parking': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'parking_number': {
                    'type': [
                      'string',
                      'null'
                    ]
                  },
                  'owner_intent': {
                    'type': 'string',
                    'description': "Identify the property's transaction intent. Choose 'rent' if the property is being offered for lease or for rent. Choose 'sale' if the property is being sold, even if it has a tenant or rental income. For lease-backed sales or investment properties showing ROI or asking price, always select 'sale'.",
                    'enum': [
                      'available for sale',
                      'available for rent',
                      'not for sale',
                      'open to bids'
                    ]
                  },
                  'tenancy_type': {
                    'type': [
                      'string',
                      'null'
                    ],
                    'description': "Type of tenancy such as 'New', 'Renewal', or 'Vacant'. Use 'New' if the property has just been leased and is being sold with tenant in place. Use 'Vacant' if the property is vacant and not rented. Use 'Owner Occupied' if the property is owner occupied. Use 'Holiday Home' if the property is a holiday home.",
                    'enum': [
                      'New',
                      'Renewal',
                      'Vacant',
                      'Owner Occupied',
                      'Holiday Home'
                    ]
                  },
                  'tenancy_start_date': {
                    'type': [
                      'string',
                      'null'
                    ],
                    'format': 'date',
                    'description': 'Start date of tenancy if provided (do not assume).'
                  },
                  'tenancy_end_date': {
                    'type': [
                      'string',
                      'null'
                    ],
                    'format': 'date',
                    'description': 'End date of tenancy if provided (do not assume).'
                  },
                  'lead_owner_percentage': {
                    'type': 'number',
                    'default': 100
                  },
                  'agent_type': {
                    'type': 'string',
                    'description': 'Classify access to the deal: 0 = OPEN_TO_ALL, 1 = SELECTIVE (few agents), 2 = EXCLUSIVE (mandate-based deal).',
                    'enum': [
                      '0',
                      '1',
                      '2'
                    ],
                    'default': '0'
                  },
                  'number_of_master_bedrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_other_bedrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_maid_rooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'number_of_study_rooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'is_archived': {
                    'type': 'boolean',
                    'default': False
                  },
                  'default_image': {
                    'type': [
                      'string',
                      'null'
                    ]
                  },
                  'property_category': {
                    'type': 'integer',
                    'description': '0 = Residential, 1 = Commercial. Classify based on intended use and type. Leased spaces to banks or offices are always Commercial.',
                    'enum': [
                      0,
                      1
                    ]
                  },
                  'total_floors': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'building_type': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': '0 = Independent House, 1 = Business Park, 2 = Mall, 3 = Standalone Building, 4 = Independent Shop',
                    'enum': [
                      0,
                      1,
                      2,
                      3,
                      4
                    ]
                  },
                  'number_of_bathrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  }
                },
                'required': [
                  'parking_available',
                  'property_unit_type',
                  'owner_intent',
                  'property_category'
                ]
              },
              'financial_details': {
                'type': 'object',
                'description': 'Contains all monetary and pricing-related details of the property. All values are in whole numbers (integers), without commas or currency symbols.',
                'properties': {
                  'original_price': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': "This field is rarely present and should only be filled if the broker explicitly mentions the original or purchase price of the property. Do NOT assume or infer this value from the asking price or market context. Example: 'bought in 2021 for 1.2 Cr' or 'purchase price was 80L'."
                  },
                  'asking_price': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': 'Current price at which the property is being offered for sale. Capture the total price only (not per square foot or per unit). Example: For 2.6 Cr, capture 26000000.'
                  },
                  'annual_rent': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': 'Total annual rent received from the property if it is already rented. Use only if it is explicitly mentioned that the property is currently rented or leased or pre-rented. Monthly rent must be multiplied by 12 before saving here.'
                  },
                  'annual_service_charges': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'security_deposit': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'valuation': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'property_currency_code': {
                    'type': 'string',
                    'description': "Currency used in pricing. Use standard ISO codes like 'INR', 'AED', etc."
                  },
                  'preferred_payment_frequency': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'price_negotiable': {
                    'type': 'boolean',
                    'description': 'Set to true if the price is explicitly stated as negotiable or open to offers.',
                    'default': False
                  },
                  'expected_rent': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': 'Annual rent that is expected from the property if it is **available for rent** (not already rented). Only use if the message says the property is available for rent. Monthly rent must be multiplied by 12 before saving.'
                  },
                  'expected_security_deposit': {
                    'type': [
                      'integer',
                      'null'
                    ],
                    'description': 'Security deposit expected to be paid by the incoming tenant, if property is listed for rent.'
                  }
                },
                'required': [
                  'original_price',
                  'asking_price',
                  'property_currency_code'
                ]
              },
              'country': {
                'type': 'string',
                'description': 'Country of the property. Keep in short form. Like IN, AE, DE etc.',
                'default': None
              },
              'property_features': {
                'type': 'object',
                'description': 'Features and amenities of the property.',
                'properties': {
                  'branded_building': {
                    'type': 'boolean',
                    'default': False
                  },
                  'furnished': {
                    'type': 'boolean',
                    'default': False
                  },
                  'premium_view': {
                    'type': 'boolean',
                    'default': False
                  },
                  'common_lift': {
                    'type': 'boolean',
                    'default': False
                  },
                  'corner_property': {
                    'type': 'boolean',
                    'default': False
                  },
                  'property_on_main_road': {
                    'type': 'boolean',
                    'default': False
                  },
                  'public_parking': {
                    'type': 'boolean',
                    'default': False
                  },
                  'security_available': {
                    'type': 'boolean',
                    'default': False
                  },
                  'water_storage_available': {
                    'type': 'boolean',
                    'default': False
                  },
                  'no_of_personal_lift': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'no_of_private_restrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'no_of_reserved_parking': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'no_of_shared_restrooms': {
                    'type': [
                      'integer',
                      'null'
                    ]
                  },
                  'reserved_parking_number': {
                    'type': [
                      'string',
                      'null'
                    ]
                  },
                  'is_lift_available': {
                    'type': 'boolean',
                    'default': False
                  },
                  'is_parking_available': {
                    'type': 'boolean',
                    'default': False
                  },
                  'is_restroom_available': {
                    'type': 'boolean',
                    'default': False
                  }
                }
              }
            },
            'required': [
              'country',
              'community',
              'property',
              'financial_details',
              'property_features'
            ]
          }
        },
        'is_valid_real_estate_message': {
          'type': 'boolean',
          'description': 'Set to true only if the message includes extractable, concrete property-level data such as price, unit size, number of bedrooms, availability status, or specific property details or message is related to requirement'
        }
      },
      'required': [
        'properties',
        'is_valid_real_estate_message'
      ]
    }
  }