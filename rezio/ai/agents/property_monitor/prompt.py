from langchain_core.prompts import PromptTemplate

property_monitor_prompt = PromptTemplate.from_template('''
You are a Real Estate AI Agent. Your task is to analyze the following user message and extract detailed, structured real estate data.

Message: {input}

Your goals:

1. Classify intent: One of:
   - "seller" — listing a property for sale or rent
   - "buyer" — looking to buy or rent
   - "agent" — acting as broker or intermediary
   - "unknown" — if unclear

2. Classify listing_type: One of:
   - "for_sale" — property offered for sale
   - "for_rent" — property offered for rent
   - "inventory" — availability update or bulk listing
   - "inquiry" — intent to purchase or rent
   - "unknown" — if unclear

3. Extract structured JSON data for each property or set of criteria in the message. Output an array of objects, where each object represents a distinct property/inquiry/inventory etc.

Schema for each object:
{{
  "intent": "seller | buyer | agent | unknown",
  "listing_type": "for_sale | for_rent | inventory | inquiry | unknown",
  "property": {{
    "type": "apartment | house | plot | commercial | land | project | other | unknown",
    "sub_type": "string (e.g., 'studio', 'penthouse', 'office', 'retail', or null if not applicable)",
    "configuration": "string (e.g., '1 bedroom', '2 bedroom', '3 bedroom', or descriptive like '2 or 3 bedroom')",
    "furnishing": "unfurnished | semi furnished | fully furnished | unknown",
    "condition": "new | used | under_construction | ready_to_move | unknown",
    "floor": {{
      "number": number | null,
      "total_floors": number | null,  
    }},
    "building_name": "string | null",
    "community_name": "string | null", 
    "area": {{
      "min_value": number | null,
      "max_value": number | null,
      "unit": "string | unknown"
    }},
    "price": {{
      "min_amount": number | null,
      "max_amount": number | null,
      "currency": "string | unknown",
      "negotiable": boolean
    }},
    "amenities": ["string"],
    "features": ["string"],
    "facing": ["string"],
    "available_from": "string | null",
    "project_name": "string | null",
    "builder_name": "string | null",
    "certifications": ["string"]
  }},
  "location": {{
    "area": string | null,
    "city": "string | null",
    "state": "string | null",
    "country": "string | null",
    "road": "string | null",
    "landmark": "string | null",
    "pincode": "string | null"
  }},
  "contact": [
    {{
      "name": "string | null",
      "phone": "string | null",
      "email": "string | null",
      "role": "owner | agent | builder | unknown"
    }}
  ],
  "meta": {{
    "reference": "string | null",
    "notes": "string | null",
    "source": "message_reader | email | web | unknown",
    "language": "en | hi | other",
    "received_at": "string | null",
    "youtube_links": ["string"],
    "preview_links": ["string"]
  }}
}}

Guidelines:
- Identify multiple properties by context (e.g., building_name, project_name, community_name etc).
- If there are multiple inventory items, please group them together in a single object. 
- Output a JSON array, even if only one property or inquiry is detected.
- If a field is missing, use null, 0, or an empty list as shown in the schema.
- Do not hallucinate or assume values not in the input.
- Standardize text where possible (e.g., "2BR" → "2 bedroom", "semi-furn" → "semi furnished").
- Make sure all units are consistent shourtform and are in the same format (e.g., "sq ft" or "sq m" for area, "USD" or "INR" for currency).
- For ranges (e.g., "1000-1500 square feet" or "40-50 thousand"), use min_value and max_value in area or price. For single values, set both min and max to that value.
- Extract YouTube and preview links and include them in the "youtube_links" and "preview_links" fields in the "meta" section of the JSON output.
- Ensure each inventory item is extracted as a separate object in the JSON array, capturing all relevant details such as property type, configuration, area, price, and additional features or conditions.
- Whatever additional information you get but is not in the schema, then put that additional information in "meta"
Do not use markdown; return the JSON array directly.
''')