"""
This module defines the law assistance agent functionality.

It processes legal queries related to Dubai real estate and provides structured, accurate responses
using the law assistance agent prompt template.
"""

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI
from django.conf import settings
from rezio.ai.agents.law_assistance.prompt import law_assistance_agent_template
from rezio.ai.constants import Model
from rezio.ai.workflow_state import AgentState
from rezio.utils.decorators import general_exception_handler
from rezio.ai.tools import tavily_search

OPENAI_API_KEY = settings.OPENAI_API_KEY


@general_exception_handler
def law_assistance_agent(state: AgentState) -> AgentState:
    """
    Processes legal queries related to Dubai real estate and provides structured, accurate responses
    using the law assistance agent.

    Args:
        state (AgentState): The current state of the agent.

    Returns:
        AgentState: The updated state of the agent.
    """
    tools = [tavily_search]

    llm = ChatOpenAI(
        model=Model.GPT_4o_mini, temperature=0, openai_api_key=OPENAI_API_KEY
    ).bind_tools(tools)

    law_assistance_agent_prompt = law_assistance_agent_template.format()

    messages = [
        SystemMessage(content=law_assistance_agent_prompt),
        *state["law_agent_messages"],
        HumanMessage(content=state["user_query"]),
    ]
    response = llm.invoke(messages)

    if isinstance(response, dict):
        response["skip_llm_logs"] = True
    else:
        if not hasattr(response, "additional_kwargs"):
            response.additional_kwargs = {}
        response.additional_kwargs["skip_llm_logs"] = True

    print("Law Assistance Agent Response: ", response)

    return {"law_agent_messages": [response]}
