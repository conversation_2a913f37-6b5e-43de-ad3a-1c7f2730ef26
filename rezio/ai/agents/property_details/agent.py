from rezio.utils.constants import DJANGO_LOGGER_NAME
import logging
from langchain_openai import ChatOpenAI
from rezio.ai.agents.property_details.prompt import property_details_agent_template
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import Model
from rezio.ai.tools import get_property_details
from rezio.ai.workflow_state import AgentState
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from rezio.ai.constants import AgentType
import traceback

# TODO: get the from rezio.settings
import environ

env = environ.Env()
env.read_env("./.envs/.local/.django")
OPENAI_API_KEY = env("OPENAI_API_KEY")

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def property_details_agent(state: AgentState) -> AgentState:
    try:
        logger.info(f"INSIDE PROPERTY DETAILS AGENT {len(state['messages'])}")

        agentPayload = state["agents"].get(AgentType.PROPERTY_DETAILS_AGENT)
        if not agentPayload:
            raise ValueError("Agent context is missing.")

        agentContext = agentPayload.get("context")

        history = state["property_agent_messages"]
        messages = []

        logger.info(" =============== HISTORY =============== ")
        logger.info(history)
        logger.info(" ======================================= ")

        if len(history) == 0 or (
            history and isinstance(history[-1], AIMessage) and history[-1].content != ""
        ):
            history.append(HumanMessage(content=state["user_query"]))

        llm = ChatOpenAI(
            model=Model.GPT_4o, temperature=0, openai_api_key=OPENAI_API_KEY
        )

        llm_with_tools = llm.bind_tools([get_property_details])

        prompt = property_details_agent_template.format(
            instructions=agentContext,
            user_query=state["user_query"],
            conversation_id=state["payload"]["conversation_id"],
            agent_portfolio_id=state["payload"]["agent_portfolio_id"],
        )

        messages = [
            SystemMessage(prompt),
            *history,
        ]

        response = llm_with_tools.invoke(messages)

        if isinstance(response, dict):
            response["skip_llm_logs"] = True
        else:
            if not hasattr(response, "additional_kwargs"):
                response.additional_kwargs = {}
            response.additional_kwargs["skip_llm_logs"] = True

        response.pretty_print()

        if isinstance(response, dict):
            response["skip_llm_logs"] = True
        else:
            if not hasattr(response, "additional_kwargs"):
                response.additional_kwargs = {}
            response.additional_kwargs["skip_llm_logs"] = True

        return {
            "property_agent_messages": [response],
        }

    except Exception as e:
        logger.error(f"Unexpected error in property detail agent : {str(e)}")
        return {"errors": [str(e)]}
