"""
This module defines the JSON schema for the response from the real estate agent.

The schema ensures that the response includes fields for the intended recipient of the response
and either a final message or an error message.
"""

response_schema = {
    "title": "RealEstateAgentResponse",
    "description": "Response from the real estate agent",
    "type": "object",
    "properties": {
        "response_to": {
            "type": "string",
            "description": "The intended recipient of the response (BUYER or Agent)"
        },
        "response": {
            "type": "string",
            "description": "The final message to the user",
            "default": ""
        },
    },
    "required": ["response_to", "response"]
} 