"""
This module defines the real estate agent functionality.

It processes user queries and ensures responses are formatted correctly
using the real estate agent prompt template.
"""

import logging
from django.conf import settings
from langgraph.graph import END
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, AIMessage, HumanMessage
from rezio.ai.agents.real_estate.prompt import real_estate_agent_template
from rezio.ai.tools import send_media_to_buyer
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import AgentMessages, Model, AgentType
from rezio.utils.decorators import general_exception_handler
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)

OPENAI_API_KEY = settings.OPENAI_API_KEY


@general_exception_handler
def real_estate_agent(state: AgentState) -> AgentState:
    """
    Processes the response for the real estate agent, ensuring it is in the correct format.
    Args: state (AgentState): The current state containing the user's query.
    Returns: AgentState: Updated state with the processed response.
    """

    agent_states = state.get("agents", {})

    history = state["real_estate_agent_messages"]
    messages = []

    logger.info(" =============== HISTORY =============== ")
    logger.info(history)
    logger.info(" ======================================= ")

    if len(history) == 0 or (
        history and isinstance(history[-1], AIMessage) and history[-1].content != ""
    ):
        history.append(HumanMessage(content=state["user_query"]))

    llm = ChatOpenAI(model=Model.GPT_4o, temperature=0.7, openai_api_key=OPENAI_API_KEY)
    tools = [send_media_to_buyer]
    llm_with_tools = llm.bind_tools(tools)

    # Handle response from only real estate agent

    if agent_states.get(AgentType.REAL_ESTATE_AGENT):
        agent_payload = state.get("agents", {}).get(AgentType.REAL_ESTATE_AGENT)

        agent_context = agent_payload.get("context")

        prompt = real_estate_agent_template.format(
            user_query=state["user_query"],
            agent_responses="\n".join(state["agent_responses"]),
            instructions=agent_context.strip(),
        )

        messages = [SystemMessage(content=prompt), *history]

        response = llm.invoke(messages)

        if isinstance(response, dict):
            response["skip_llm_logs"] = False
        else:
            if not hasattr(response, "additional_kwargs"):
                response.additional_kwargs = {}
            response.additional_kwargs["skip_llm_logs"] = False

        response.pretty_print()

        return {
            "real_estate_agent_messages": [response],
        }

    print(f"AGENT AGGREGATOR RESPONSES {state['agent_responses']}")

    # Else handle response from aggregator
    prompt = real_estate_agent_template.format(
        user_query=state["user_query"],
        instructions="\n".join(state["agent_responses"].values()),
        agent_responses="",
    )

    messages = [SystemMessage(content=prompt), *history]

    response = llm_with_tools.invoke(messages)

    if isinstance(response, dict):
        response["skip_llm_logs"] = False
    else:
        if not hasattr(response, "additional_kwargs"):
            response.additional_kwargs = {}
        response.additional_kwargs["skip_llm_logs"] = False

    response.pretty_print()

    return {"real_estate_agent_messages": [response]}


def aggregatorNode(state: AgentState) -> AgentState | None:
    print(f"INSIDE AGGREGATOR NODE")

    combined_messages = {}

    agent_messages = {
        AgentType.PROPERTY_DETAILS_AGENT: state.get(
            AgentMessages.PROPERTY_DETAILS_AGENT
        ),
        AgentType.PREFERENCE_AGENT: state.get(AgentMessages.PREFERENCE_AGENT),
        AgentType.SCHEDULE_VISIT_AGENT: state.get(AgentMessages.SCHEDULE_VISIT_AGENT),
        AgentType.LAW_ASSISTANCE_AGENT: state.get(AgentMessages.LAW_ASSISTANCE_AGENT),
    }

    wait = False

    for agent in state["agents"]:
        if agent == AgentType.REAL_ESTATE_AGENT:
            continue

        messages = agent_messages.get(agent)
        if not messages:
            wait = True
            break

        last_message = messages[-1]
        if not isinstance(last_message, AIMessage):
            wait = True
            break

        if not last_message.content:
            wait = True
            break

        combined_messages[agent] = last_message.content

    if wait:
        return {"wait": True}

    print(f"INSIDE AGGREGATOR NODE = RETURNING STATE")

    return {"wait": False, "agent_responses": combined_messages}


def aggregatorRouter(state: AgentState) -> AgentType:
    if state["wait"]:
        return END
    return AgentType.REAL_ESTATE_AGENT
