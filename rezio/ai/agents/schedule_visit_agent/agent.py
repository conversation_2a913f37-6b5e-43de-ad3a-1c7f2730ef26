"""
This module defines the schedule visit agent functionality.

It manages and coordinates property visit discussions, ensuring a structured internal dialogue
process using the schedule visit agent prompt template.
"""

from datetime import datetime
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from django.conf import settings
from rezio.ai.agents.schedule_visit_agent.prompt import schedule_visit_agent_template
from rezio.ai.constants import AgentType, Model
from rezio.ai.workflow_state import AgentState
from rezio.utils.decorators import general_exception_handler

OPENAI_API_KEY = settings.OPENAI_API_KEY


@general_exception_handler
def schedule_visit_agent(state: AgentState):
    """
    Manages and coordinates property visit discussions.
    Args:
        state (AgentState): The current state containing the user's query and agent context.

    Returns:
        dict: A dictionary containing the response messages.
    """
    agent_payload = state["agents"].get(AgentType.SCHEDULE_VISIT_AGENT)
    if not agent_payload:
        raise ValueError("Agent context is missing.")

    agent_context = agent_payload.get("context")

    llm = ChatOpenAI(model=Model.GPT_4o, temperature=0.5, openai_api_key=OPENAI_API_KEY)

    current_date = datetime.now().strftime("%A, %B %d")
    prompt = schedule_visit_agent_template.format(
        instructions=agent_context,
        conversation_id=state["payload"]["conversation_id"],
        agent_portfolio_id=state["payload"]["agent_portfolio_id"],
        current_date=current_date,
    )

    response = llm.invoke(
        [
            SystemMessage(prompt),
            *state["schedule_agent_messages"],
            HumanMessage(state["user_query"]),
        ]
    )

    if isinstance(response, dict):
        response["skip_llm_logs"] = True
    else:
        if not hasattr(response, "additional_kwargs"):
            response.additional_kwargs = {}
        response.additional_kwargs["skip_llm_logs"] = True

    return {"schedule_agent_messages": [response]}
