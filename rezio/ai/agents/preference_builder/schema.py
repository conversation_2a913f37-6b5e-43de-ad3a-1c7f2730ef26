response_schema = {
    "title": "OrchestratorResponse",
    "description": "Response from the orchestrator agent containing agents and their contexts",
    "type": "object",
    "properties": {
        "agents": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "agent": {
                        "type": "string",
                        "description": "Name of the agent to be called",
                    },
                    "context": {
                        "type": "string",
                        "description": "Markdown formatted context for the agent",
                    },
                },
                "required": ["agent", "context"],
            },
            "description": "List of agents with their contexts",
        }
    },
    "required": ["agents"],
}
