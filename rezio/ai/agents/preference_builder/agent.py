from rezio.utils.constants import DJANGO_LOGGER_NAME
import logging
from langchain_openai import ChatOpenAI
import environ
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, ToolMessage

from rezio.ai.agents.preference_builder.prompt import preference_builder_prompt
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import Model
from rezio.ai.services.db_raw_service import dbRawService
from rezio.ai.constants import AgentType
from rezio.ai.tools import (
    get_preferences,
    create_preference,
    update_preference,
    delete_preferences,
)

# Initialize environment variables
# env = environ.Env()
# env.read_env("./.envs/.local/.django")
# OPENAI_API_KEY = env("OPENAI_API_KEY")
from django.conf import settings

OPENAI_API_KEY = settings.OPENAI_API_KEY

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def preference_agent(state: AgentState) -> AgentState:
    logger.info(f"INSIDE PREFERENCE AGENT  HISTORY {len(state['messages'])}")

    try:
        history = state["preference_agent_messages"]
        messages = []

        logger.info(" =============== HISTORY =============== ")
        logger.info(history)
        logger.info(" ======================================= ")

        if len(history) == 0 or (
            history and isinstance(history[-1], AIMessage) and history[-1].content != ""
        ):
            history.append(HumanMessage(content=state["user_query"]))
        agent_payload = state["agents"].get(AgentType.PREFERENCE_AGENT)
        if not agent_payload:
            raise ValueError("Agent context is missing.")

        agent_context = agent_payload.get("context")

        tools = [
            get_preferences,
            create_preference,
            update_preference,
            delete_preferences,
        ]

        llm_model = ChatOpenAI(
            model=Model.GPT_4o, temperature=0.2, openai_api_key=OPENAI_API_KEY
        )

        llm_with_tools = llm_model.bind_tools(tools)

        payload = state["payload"]

        preferences = dbRawService.get_preferences(payload["conversation_id"])

        prompt = preference_builder_prompt.format(
            preferences=preferences,
            instructions=agent_context.strip(),
            conversation_id=payload["conversation_id"],
            agent_portfolio_id=payload["agent_portfolio_id"],
        )

        messages = [
            SystemMessage(content=prompt),
            *history,
        ]

        response = llm_with_tools.invoke(messages)

        if isinstance(response, dict):
            response["skip_llm_logs"] = True
        else:
            if not hasattr(response, "additional_kwargs"):
                response.additional_kwargs = {}
            response.additional_kwargs["skip_llm_logs"] = True

        response.pretty_print()

        if isinstance(response, dict):
            response["skip_llm_logs"] = True
        else:
            if not hasattr(response, "additional_kwargs"):
                response.additional_kwargs = {}
            response.additional_kwargs["skip_llm_logs"] = True

        response.pretty_print()

        return {
            "preference_agent_messages": [response],
        }

    except Exception as e:
        logger.error(f"Unexpected error in preference builder : {str(e)}")
        return {"errors": [str(e)]}
