"""
This module defines the JSON schema for the response from the input guardrail agent.

The schema ensures that the response includes fields for validation status, response message,
and the intended recipient of the response.
"""

response_schema = {
    "title": "InputGuardrailResponse",
    "description": "Response from the input guardrail agent",
    "type": "object",
    "properties": {
        "is_valid": {"type": "boolean", "description": "Whether the input is valid"},
        "response": {
            "type": "string",
            "description": "The response message to the user",
        },
        "response_to": {
            "type": "string",
            "description": "The intended recipient of the response",
        },
    },
    "required": ["is_valid", "response", "response_to"],
}
