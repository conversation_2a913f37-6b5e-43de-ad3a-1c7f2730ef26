"""
This agent is responsible for ensuring that the user's query is appropriate and relevant to
real estate. It will return a boolean value indicating whether the query is valid and a message
to the user.
"""

from better_profanity import profanity
from rezio.ai.workflow_state import AgentState
from rezio.ai.constants import AgentType
from rezio.utils.decorators import general_exception_handler


@general_exception_handler
def input_guard_rail(state: AgentState) -> AgentState:
    """
    Validates the user's query to ensure it adheres to real estate guidelines.
    Args: state (AgentState): The current state containing the user's query.
    Returns: AgentState: Updated state with validation results.
    """

    is_profane = profanity.contains_profanity(state["user_query"])

    response_to = (
        AgentType.OUTPUT_GUARD_RAIL if is_profane else AgentType.CONTEXT_BUILDER
    )

    return {
        "is_valid": not is_profane,
        "response_to": response_to,
    }
