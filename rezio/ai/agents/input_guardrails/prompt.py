"""
This module defines the prompt template for input guardrails.

The template ensures that user queries adhere to specific guidelines and formats
responses in a consistent JSON structure.
"""
from langchain_core.prompts import PromptTemplate

input_guardrail_template = PromptTemplate.from_template("""
To ensure user queries are appropriate, the following guardrails apply:

1. Queries must not contain vulgar, offensive, or harmful language, including NSFW or similar content.
    - If the query is vulgar, offensive, or harmful, respond in JSON format:
    {{
        "is_valid": false,
        "response": "A short, simple and polite rejection message",
        "response_to": "BUYER"
    }}
    
2. Casual greetings or small talk, such as 'Hi', 'Hey', or 'Hello,' should be marked as valid, and respond wiyh a friendly message redirecting the user to real estate help.
   {{
      "is_valid": false,
      "response": "Hi there! How can I assist you today?",
      "response_to": "BUYER"
   }}
   
3. If the query is deemed safe and relevant, if the query is related to asking about the property details or saving a preference preferences, schedule a visit or law related details, forward it to the context builder as-is in JSON format:
   {{
      "is_valid": true,
      "response": "User's Query: {query}",
      "response_to": "CONTEXT_BUILDER"
   }}

4. If the query is unrelated to real estate but not harmful, still mark it as valid and provide a polite message redirecting the user to real estate topics.
   {{
      "is_valid": false,
      "response": "I'm sorry, I can only help with real estate related questions. How can I assist you today?",
      "response_to": "BUYER"
   }}
   
   output_schema:
   {response_schema}
""")
