response_schema = {
    "title": "ContextBuilderResponse",
    "description": "Response from the context builder containing proper context for orchestrator",
    "type": "object",
    "properties": {
        "description": {
            "type": "string",
            "description": "Proper MD formatted context for orchestrator with following details: Action to be taken by orchestrator. Explaining your thought process. Agent's portfolio id. Formatted property details with property ids and their attached meta_data (if fetched) DO NOT ASSUME ANY META_DATA use only if fetched. Formatted preferences if retrieved from 'get_preferences' tool. Suggested preference updates (if applicable).",
        }
    },
    "required": ["description"],
}
