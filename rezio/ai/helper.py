from langchain_core.messages import ToolMessage, AIMessage


def filter_message_history(messages, n=10):
    """
    Returns a trimmed and safe slice of recent messages.
    - Removes a starting ToolMessage (since it must follow a tool_call).
    - Removes a trailing AIMessage with unresponded tool_calls.

    Args:
        messages (list): List of LangChain message objects.
        n (int): Number of recent messages to keep.

    Returns:
        list: Cleaned recent message history.
    """
    history = messages[-n:]

    # Remove leading ToolMessage if not preceded by a tool_call
    if history and isinstance(history[0], ToolMessage):
        history = history[1:]

    # Remove trailing AIMessage with pending tool_calls
    if (
        history
        and isinstance(history[-1], AIMessage)
        and getattr(history[-1], "tool_calls", None)
    ):
        history = history[:-1]

    return history
