import os
from langgraph.graph import StateGraph, START, END
from rezio.ai.agents.preference_builder.agent import preference_agent
from rezio.ai.agents.real_estate.agent import (
    real_estate_agent,
    aggregatorNode,
    aggregatorRouter,
)
from rezio.ai.agents.law_assistance.agent import law_assistance_agent
from rezio.ai.agents.schedule_visit_agent.agent import schedule_visit_agent
from rezio.ai.agents.property_details.agent import property_details_agent
from rezio.ai.agents.orchestrator.agent import orchestrator, orchestrator_router
from rezio.ai.agents.input_guardrails.agent import input_guard_rail
from rezio.ai.agents.context_builder.agent import context_builder
from rezio.ai.agents.output_guardrails.agent import output_guard_rail
from rezio.ai.workflow_state import AgentState
from langgraph.prebuilt import tools_condition
from rezio.ai.constants import AgentMessages, AgentType, AgentTools
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from rezio.ai.tools import (
    preference_tool_node,
    law_assistance_tool_node,
    context_builder_tool_node,
    property_details_tool_node,
    real_estate_tool_node,
)

from contextlib import contextmanager
from langgraph.checkpoint.postgres import PostgresSaver
from psycopg import Connection
from django.conf import settings


class Workflow:
    memory = MemorySaver()

    def __init__(self):
        self.workflow = StateGraph(AgentState)
        self.graph = None

        # Initialize the workflow with the state graph
        self.init_agent_nodes()
        self.init_edges()

        # Entry - Exit point
        self.workflow.add_edge(START, AgentType.INPUT_GUARD_RAIL)
        self.workflow.add_edge(AgentType.REAL_ESTATE_AGENT, END)
        self.workflow.add_edge(AgentType.OUTPUT_GUARD_RAIL, END)
        self.checkerpoint = self.init_checkerpoint()
        self.graph = self.workflow.compile(checkpointer=self.checkerpoint)

    @contextmanager
    def get_checkpointer(self):
        connection = None
        try:
            db_config = settings.DATABASES["default"]
            DB_URI = (
                f"postgres://{db_config['USER']}:{db_config['PASSWORD']}@"
                f"{db_config['HOST']}:{db_config.get('PORT', '5432')}/{db_config['NAME']}"
            )
            # Create connection
            connection = Connection.connect(
                DB_URI, autocommit=True, prepare_threshold=0
            )

            # Create and setup checkpointer
            checkpointer = PostgresSaver(connection)
            checkpointer.setup()

            yield checkpointer

        finally:
            if connection:
                connection.close()

    def init_checkerpoint(self):
        try:
            with self.get_checkpointer() as checkpointer:
                return checkpointer
        except Exception as e:
            print(f"error: {e}")

    def init_agent_nodes(self):
        agent_mappings = {
            # nodes
            AgentType.INPUT_GUARD_RAIL: input_guard_rail,
            AgentType.CONTEXT_BUILDER: context_builder,
            AgentType.ORCHESTRATOR: orchestrator,
            AgentType.PROPERTY_DETAILS_AGENT: property_details_agent,
            AgentType.SCHEDULE_VISIT_AGENT: schedule_visit_agent,
            AgentType.LAW_ASSISTANCE_AGENT: law_assistance_agent,
            AgentType.REAL_ESTATE_AGENT: real_estate_agent,
            AgentType.PREFERENCE_AGENT: preference_agent,
            AgentType.OUTPUT_GUARD_RAIL: output_guard_rail,
            # tools nodes
            AgentTools.CONTEXT_BUILDER: context_builder_tool_node,
            AgentTools.PROPERTY_DETAILS_AGENT: property_details_tool_node,
            AgentTools.LAW_ASSISTANCE_AGENT: law_assistance_tool_node,
            AgentTools.PREFERENCE_AGENT: preference_tool_node,
            AgentTools.REAL_ESTATE_AGENT: real_estate_tool_node,
        }

        for agent_type, agent_func in agent_mappings.items():
            self.workflow.add_node(agent_type, agent_func)

        self.workflow.add_node("aggregatorNode", aggregatorNode)

    def init_edges(self):
        # Edges
        self.workflow.add_conditional_edges(
            AgentType.INPUT_GUARD_RAIL,
            lambda state: state["response_to"],
            {
                AgentType.CONTEXT_BUILDER: AgentType.CONTEXT_BUILDER,
                AgentType.OUTPUT_GUARD_RAIL: AgentType.OUTPUT_GUARD_RAIL,
            },
        )

        # Conditional routing
        self.workflow.add_conditional_edges(
            AgentType.ORCHESTRATOR,
            orchestrator_router,
            {
                AgentType.PREFERENCE_AGENT: AgentType.PREFERENCE_AGENT,
                AgentType.LAW_ASSISTANCE_AGENT: AgentType.LAW_ASSISTANCE_AGENT,
                AgentType.SCHEDULE_VISIT_AGENT: AgentType.SCHEDULE_VISIT_AGENT,
                AgentType.PROPERTY_DETAILS_AGENT: AgentType.PROPERTY_DETAILS_AGENT,
                AgentType.REAL_ESTATE_AGENT: AgentType.REAL_ESTATE_AGENT,
            },
        )

        # Tool routing

        self.workflow.add_conditional_edges(
            AgentType.CONTEXT_BUILDER,
            tools_condition,
            {"tools": AgentTools.CONTEXT_BUILDER, END: AgentType.ORCHESTRATOR},
        )
        self.workflow.add_edge(AgentTools.CONTEXT_BUILDER, AgentType.CONTEXT_BUILDER)

        self.workflow.add_conditional_edges(
            AgentType.PROPERTY_DETAILS_AGENT,
            lambda state: tools_condition(
                state, messages_key=AgentMessages.PROPERTY_DETAILS_AGENT
            ),
            {"tools": AgentTools.PROPERTY_DETAILS_AGENT, END: "aggregatorNode"},
        )
        self.workflow.add_edge(
            AgentTools.PROPERTY_DETAILS_AGENT, AgentType.PROPERTY_DETAILS_AGENT
        )

        self.workflow.add_conditional_edges(
            AgentType.LAW_ASSISTANCE_AGENT,
            lambda state: tools_condition(
                state, messages_key=AgentMessages.LAW_ASSISTANCE_AGENT
            ),
            {"tools": AgentTools.LAW_ASSISTANCE_AGENT, END: "aggregatorNode"},
        )
        self.workflow.add_edge(
            AgentTools.LAW_ASSISTANCE_AGENT, AgentType.LAW_ASSISTANCE_AGENT
        )

        self.workflow.add_conditional_edges(
            AgentType.PREFERENCE_AGENT,
            lambda state: tools_condition(
                state, messages_key=AgentMessages.PREFERENCE_AGENT
            ),
            {"tools": AgentTools.PREFERENCE_AGENT, END: "aggregatorNode"},
        )
        self.workflow.add_edge(AgentTools.PREFERENCE_AGENT, AgentType.PREFERENCE_AGENT)

        self.workflow.add_conditional_edges(
            AgentType.REAL_ESTATE_AGENT,
            lambda state: tools_condition(
                state, messages_key=AgentMessages.REAL_ESTATE_AGENT
            ),
            {"tools": AgentTools.REAL_ESTATE_AGENT, END: END},
        )

        self.workflow.add_edge(
            AgentTools.REAL_ESTATE_AGENT, AgentType.REAL_ESTATE_AGENT
        )

        self.workflow.add_edge(AgentType.SCHEDULE_VISIT_AGENT, "aggregatorNode")

        self.workflow.add_conditional_edges(
            "aggregatorNode",
            aggregatorRouter,
            {AgentType.REAL_ESTATE_AGENT: AgentType.REAL_ESTATE_AGENT, END: END},
        )

    def generate_graph_image(self):
        try:
            print("Generating graph...", self.graph)
            # Get the Mermaid PNG bytes directly
            png_bytes = self.graph.get_graph().draw_mermaid_png()
            output_file = "mermaid_graph.png"
            with open(output_file, "wb") as f:
                f.write(png_bytes)
            os.system(
                f"start {output_file}"
                if os.name == "nt"
                else f"open {output_file}"
                if os.name == "posix"
                else f"xdg-open {output_file}"
            )
            print(f"Graph saved as {output_file} and should open automatically.")
        except Exception as e:
            print(f"Error: {e}")


workflow = Workflow()

checkpoint_config = {
    "configurable": {
        "thread_id": "workflow_1",
        "checkpoint_ns": "rezio",
        "checkpoint_id": "agent_workflow",
    }
}

payload = {
    "conversation_id": 101,
    "agent_portfolio_id": "8eoNMp33YuWbLuU0dJrm8itrcLQ2",
}


def main():
    graph = workflow.graph
    print("Workflow in CLI MODE")

    while True:
        user_query = input("Query: ")

        if not user_query:
            print("Please enter a valid query.")
            continue

        if user_query.lower() in ["quit", "exit"]:
            print("Goodbye!")
            break

        graph.invoke(
            {
                "user_query": user_query,
                "payload": payload,
                "messages": [],
                "agent_responses": {},
            },
            checkpoint_config,
        )


# main()
