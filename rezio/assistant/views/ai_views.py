import logging
import traceback

from django.conf import settings
from django_redis import get_redis_connection
from rest_framework.views import APIView
from rest_framework.response import Response
from rezio.assistant.serializers.ai_serializers import RezioAIAgentSerializer
from rest_framework.permissions import AllowAny
from rezio.assistant.models import PubNubChannel
from rezio.assistant.tasks import run_rezio_ai_agent
from rest_framework import status
import json
from rezio.pubnub.handler import <PERSON>NubHandler
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.decorators import general_exception_handler
from rezio.utils.constants import DJANGO_LOGGER_NAME


logger = logging.getLogger(DJANGO_LOGGER_NAME)


class AIAgentView(APIView):
    permission_classes = [AllowAny]

    @general_exception_handler
    def post(self, request):
        """
        Generate a response for the buyer query using Iris reasoning agent
        Args:
            request: {
                "pubnub_channel_id": "pubnub_channel_id",
                "group_channel_id": "group_channel_id",
                "content": "Hello, world!",
                "role": "user"
            }
        Returns:
            An acknowledgement that the AI agent is running in background
        """
        serializer = RezioAIAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        pubnub_queryset = PubNubChannel.objects.filter(
            pubnub_channel_id=serializer.validated_data.get("pubnub_channel_id"),
            group_channel_id=serializer.validated_data.get("group_channel_id"),
        )
        if not pubnub_queryset.exists():
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    KEY_MESSAGE: "Invalid pubnub or group channel id",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Invalid pubnub or group channel id"
                    },
                },
            )

        pubnub_channel = pubnub_queryset.first()
        pubnub_handler = PubNubHandler(
            agent_pubnub_uuid=serializer.validated_data.get("uuid")
        )
        pubnub_handler.publish_message(
            target=pubnub_channel.group_channel_id,
            message=json.dumps(
                {
                    "role": "user",
                    "content": serializer.validated_data.get("content"),
                    "channel_id": pubnub_channel.pubnub_channel_id,
                    "group_channel_id": pubnub_channel.group_channel_id,
                }
            ),
            is_channel_group=True,
        )

        try:
            # Test Redis connection first
            redis_client = get_redis_connection("default")
            print(redis_client.ping())
            print(redis_client)

            # Your existing code here
            task = run_rezio_ai_agent.apply_async(
                kwargs={
                    "pubnub_channel_id": pubnub_channel.id,
                    "message": serializer.validated_data.get("content"),
                },
                queue="ai_tasks",
            )
            logger.info(f"Task ID: {task.id}")

        except ConnectionError as e:
            # Log the error and return a proper response
            logger.error(f"Redis connection error: {e}")
            return Response(
                {"error": "Service temporarily unavailable"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )
        except Exception as e:
            logger.error(f"Unexpected error in AIAgentView: {e}")
            traceback.print_exc()
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "AI agent running in background",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )
