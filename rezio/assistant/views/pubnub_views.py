import logging

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from rezio.assistant.models import PubN<PERSON>WebUser, PubNubChannel
from rezio.pubnub.handler import <PERSON><PERSON>ub<PERSON>andler
import uuid

from rezio.twilio_integration.models import TwilioVerification
from rezio.twilio_integration.services.twilio_service import TwilioService
from rezio.twilio_integration.text_choices import VerificationType
from rezio.user.utils import get_user_object, get_role_object
from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    DJANGO_LOGGER_NAME,
)
from rezio.utils.custom_exceptions import InvalidSerializerDataException
from rezio.utils.decorators import general_exception_handler

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PubNubUserView(APIView):
    """
    API view to register a user and create or retrieve their PubNub channel.
    """

    @general_exception_handler
    def post(self, request, viewed_user_id, viewed_user_role_name):
        name = request.data.get("name")
        phone_number = request.data.get("phone_number")
        actor = request.data.get("actor", "Buyer")  # Default Buyer
        otp = request.data.get("otp")
        receiver_user_id = viewed_user_id
        receiver_user_role_name = viewed_user_role_name

        viewed_user = get_user_object(receiver_user_id)
        viewer_user_role_object = get_role_object(receiver_user_role_name)

        logger.info(
            f"Viewed user: {viewed_user} and viewer role: {viewer_user_role_object}"
        )

        if not receiver_user_role_name or not receiver_user_id:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Receiver user role name and ID are required.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Receiver user role name and ID are required."
                    },
                }
            )

        # if receiver_user_id not in [
        #     "Ylqp5YqkvFg6PFP88GDt0TJJLRN2",
        #     "PdLs6YwQeEgVXBVlRkUFQ4tCkzr1",
        #     "8eoNMp33YuWbLuU0dJrm8itrcLQ2"
        # ]:
        #     if not name or not phone_number or not otp:
        #         raise InvalidSerializerDataException(
        #             {
        #                 KEY_MESSAGE: "Name, OTP and  phone number are required.",
        #                 KEY_PAYLOAD: {},
        #                 KEY_ERROR: {
        #                     KEY_ERROR_MESSAGE: "Name, phone number, and OTP are required."
        #                 },
        #             }
        #         )

        #     # Verify OTP first
        #     twilio_service = TwilioService()
        #     verification_result = twilio_service.verify_otp(phone_number, otp)

        #     if verification_result.get("error"):
        #         return Response(
        #             status=status.HTTP_400_BAD_REQUEST,
        #             data={
        #                 KEY_MESSAGE: "OTP verification failed",
        #                 KEY_PAYLOAD: {},
        #                 KEY_ERROR: verification_result["error"],
        #             },
        #         )

        #     # Update verification status
        #     verification = TwilioVerification.objects.filter(
        #         phone_number=phone_number, viewed_user_id=viewed_user, verification_type=VerificationType.CHAT
        #     ).first()
        #     if verification:
        #         verification.is_verified = True
        #         verification.is_active = False
        #         verification.save()

        # Check if user exists
        pubnub_user, created = PubNubWebUser.objects.get_or_create(
            phone_number=phone_number, defaults={"name": name}
        )
        logger.info(f"PubNub user {pubnub_user} created: {created}")

        if not created:
            # User exists, return their details
            logger.info(f"PubNub user {pubnub_user} already exists.")
            existing_channel = PubNubChannel.objects.filter(
                web_user=pubnub_user,
                actor=actor,
                receiver_user=viewed_user,
                receiver_user_role=viewer_user_role_object,
            ).first()
            if existing_channel:
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "PubNub user Details fetched successfully",
                        KEY_PAYLOAD: {
                            "channel_id": existing_channel.pubnub_channel_id,
                            "group_channel_id": existing_channel.group_channel_id,
                            "pubnub_user_id": pubnub_user.id,
                            "name": pubnub_user.name,
                            "phone_number": phone_number,
                        },
                        KEY_ERROR: {},
                    },
                )
            else:
                pass

        # Generate Channel IDs
        user_id = pubnub_user.id
        unique_id = str(uuid.uuid4().hex)
        channel_id = (
            f"{user_id}_{receiver_user_role_name}_{receiver_user_id}_{unique_id}"
        )
        group_channel_id = f"{receiver_user_role_name}_{receiver_user_id}_{unique_id}"

        logger.info(
            f"Generated channel ID: {channel_id} and group channel ID: {group_channel_id}"
        )
        # Check if group exists for the agent
        # group_exists = PubNubChannel.objects.filter(
        #     group_channel_id=group_channel_id
        # ).exists()

        pubnub_obj = PubNubHandler(agent_pubnub_uuid=receiver_user_id)

        # if not group_exists:
        #     logger.info(f"Group channel {group_channel_id} does not exist.")
        #     # Create new group channel
        #
        #     if not pubnub_obj.create_channel_group(channel_group_name=group_channel_id):
        #         if not name or not phone_number or not receiver_user_id:
        #             raise InvalidSerializerDataException(
        #                 {
        #                     KEY_MESSAGE: "Failed to create PubNub group.",
        #                     KEY_PAYLOAD: {},
        #                     KEY_ERROR: {
        #                         KEY_ERROR_MESSAGE: "Failed to create PubNub group."
        #                     },
        #                 }
        #             )

        # Add the new channel to the group
        if not pubnub_obj.add_channels_to_group(group_channel_id, [channel_id]):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Failed to add channel to PubNub group.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Failed to add channel to PubNub group."
                    },
                }
            )

        # Create new PubNubChannel entry
        pubnub_channel = PubNubChannel.objects.create(
            web_user=pubnub_user,
            actor=actor,
            pubnub_channel_id=channel_id,
            group_channel_id=group_channel_id,
            receiver_user=viewed_user,
            receiver_user_role=viewer_user_role_object,
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "PubNub user Details fetched successfully",
                KEY_PAYLOAD: {
                    "channel_id": pubnub_channel.pubnub_channel_id,
                    "group_channel_id": pubnub_channel.group_channel_id,
                    "pubnub_user_id": pubnub_user.id,
                    "name": pubnub_user.name,
                    "phone_number": phone_number,
                },
                KEY_ERROR: {},
            },
        )


class PubNubUserUpdateView(APIView):
    """
    API view to register a user and create or retrieve their PubNub channel.
    """

    @general_exception_handler
    def post(self, request, viewed_user_id, viewed_user_role_name):
        name = request.data.get("name")
        phone_number = request.data.get("phone_number")
        actor = request.data.get("actor", "Buyer")  # Default Buyer
        otp = request.data.get("otp")
        pubnub_user_id = request.data.get("pubnub_user_id")
        receiver_user_id = viewed_user_id
        receiver_user_role_name = viewed_user_role_name

        viewed_user = get_user_object(receiver_user_id)
        viewer_user_role_object = get_role_object(receiver_user_role_name)

        logger.info(
            f"Viewed user: {viewed_user} and viewer role: {viewer_user_role_object}"
        )

        if not receiver_user_role_name or not receiver_user_id:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Receiver user role name and ID are required.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Receiver user role name and ID are required."
                    },
                }
            )

        if not pubnub_user_id:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "pubnub_user_id is required.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "pubnub_user_id is required."},
                }
            )

            # Verify OTP first
        twilio_service = TwilioService()
        verification_result = twilio_service.verify_otp(phone_number, otp)

        if verification_result.get("error"):
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    KEY_MESSAGE: "OTP verification failed",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: verification_result["error"],
                },
            )

        # Update verification status
        verification = TwilioVerification.objects.filter(
            phone_number=phone_number, viewed_user_id=viewed_user
        ).first()
        if verification:
            verification.is_verified = True
            verification.is_active = False
            verification.save()

        try:
            pubnub_user = get_object_or_404(PubNubWebUser, id=pubnub_user_id)
            pubnub_user.phone_number = phone_number
            pubnub_user.name = name
            pubnub_user.save()
            logger.info(f"PubNub user {pubnub_user} updated successfully")

        except ResourceNotFoundException as invalid_data:
            return ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Pubnub user details with ID {pubnub_user_id}"
                    },
                }
            )

        logger.info(f"PubNub user {pubnub_user} already exists.")
        existing_channel = PubNubChannel.objects.filter(
            web_user=pubnub_user,
            actor=actor,
            receiver_user=viewed_user,
            receiver_user_role=viewer_user_role_object,
        ).first()
        if existing_channel:
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "PubNub user Details updated successfully",
                    KEY_PAYLOAD: {
                        "channel_id": existing_channel.pubnub_channel_id,
                        "group_channel_id": existing_channel.group_channel_id,
                        "pubnub_user_id": pubnub_user.id,
                        "name": pubnub_user.name,
                        "phone_number": phone_number,
                    },
                    KEY_ERROR: {},
                },
            )
        else:
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    KEY_MESSAGE: "Invalid data",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid data."},
                },
            )


class AgentChannelsView(APIView):
    """
    API view to retrieve all PubNub channels for a given agent ID.
    """

    @general_exception_handler
    def get(self, request, agent_id):
        """
        Get all PubNub channels for a specific agent.

        Args:
            agent_id: The ID of the agent to retrieve channels for

        Returns:
            List of channels with their details
        """
        logger.info(f"Retrieving PubNub channels for agent: {agent_id}")

        # Get the user object for the agent
        try:
            agent_user = get_user_object(agent_id)
        except Exception as e:
            logger.error(f"Error retrieving user object for agent {agent_id}: {str(e)}")
            return Response(
                status=status.HTTP_404_NOT_FOUND,
                data={
                    KEY_MESSAGE: "Agent not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"No agent found with ID: {agent_id}"
                    },
                },
            )

        # Query all channels where this agent is the receiver
        channels = PubNubChannel.objects.filter(receiver_user=agent_user)

        # Format the response
        channel_data = []
        for channel in channels:
            # Convert PhoneNumber to string to make it JSON serializable
            phone_number = (
                str(channel.web_user.phone_number)
                if channel.web_user.phone_number
                else ""
            )

            channel_data.append(
                {
                    "channel_id": channel.pubnub_channel_id,
                    "group_channel_id": channel.group_channel_id,
                    "web_user_id": channel.web_user.id,
                    "web_user_name": channel.web_user.name,
                    "web_user_phone": phone_number,
                    "actor": channel.actor,
                    "created_at": channel.created_at.isoformat(),
                }
            )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent channels retrieved successfully",
                KEY_PAYLOAD: {
                    "agent_id": agent_id,
                    "channels": channel_data,
                    "total_channels": len(channel_data),
                },
                KEY_ERROR: {},
            },
        )
