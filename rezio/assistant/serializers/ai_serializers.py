from rest_framework import serializers
from rezio.assistant.types import Role


class RezioAIAgentSerializer(serializers.Serializer):
    role = serializers.CharField(required=True)
    content = serializers.CharField(required=True)
    pubnub_channel_id = serializers.CharField(required=True)
    group_channel_id = serializers.CharField(required=True)
    uuid = serializers.CharField(required=True)
    type = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs["role"] not in [Role.USER, Role.ASSISTANT]:
            raise serializers.ValidationError("Invalid role")
        return attrs
