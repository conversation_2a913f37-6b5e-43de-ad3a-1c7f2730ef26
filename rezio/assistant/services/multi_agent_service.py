import traceback

from rezio.assistant.error_messages import custom_error_message
from rezio.assistant.services.iris_service import <PERSON>, Tools
from rezio.assistant.models import PubNubChannel, Messages, Memory
from rezio.user.models import AgentProfile
from rezio.assistant.services.system_prompts.multi_agent_prompts import (
    MultiAgentPrompts,
)
from rezio.assistant.types import AgentType, Role, Model, Actor
from rezio.assistant.services.system_prompts.multi_agent_prompts import (
    MultiAgentPrompts,
)
from rezio.user.models import AgentProfile
from rezio.pubnub.handler import PubNubHandler
import json
from typing import Tuple, List
from urllib.parse import urlparse
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from langfuse import Langfuse
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output
import logging

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class MultiAgent:
    def __init__(self, pubnub_channel: PubNubChannel, agent_pubnub_uuid):
        self.messages = pubnub_channel.buyer.first()
        self.pubnub_channel = pubnub_channel
        self.console = Console()
        self.multi_agent_prompts = MultiAgentPrompts()
        self.pubnub_handler = PubNubHandler(agent_pubnub_uuid=agent_pubnub_uuid)
        self.history = self.pubnub_handler.get_message_history(
            pubnub_channel.pubnub_channel_id
        )

    # def __getattribute__(self, name):
    #
    #     attr = super().__getattribute__(name)
    #     if callable(attr) and name in {
    #         "input_guard_rail",
    #         "context_builder",
    #         "react_agent",
    #         "orchestrator",
    #         "property_details_agent",
    #         "schedule_visit_agent",
    #         "law_assistance_agent",
    #         "preference_agent",
    #         "aggregator",
    #         "real_estate_agent",
    #         "output_guard_rail",
    #     }:
    #
    #         def wrapped(*args, **kwargs):
    #             with observe():
    #                 return attr(*args, **kwargs)
    #
    #         return wrapped
    #     return attr

    @log_input_output
    def structured_response(self, guard_rail_string: str) -> Tuple[str, str]:
        try:
            input_guard_rail = json.loads(guard_rail_string)
            response_to = input_guard_rail["response_to"]
            response = input_guard_rail["response"]
        except Exception as e:
            logger.error(f"Error parsing guard rail string: {str(e)}")
            response_to = None
            response = None
        return response_to, response

    @log_input_output
    def input_guard_rail(self, user_message: dict | List[dict]) -> Tuple[str, str]:
        iris = Iris(user=self.pubnub_channel)

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.INPUT_GUARD_RAIL,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            json_response=True,
            temperature=0.5,
        )

        memory_object.save()
        return ai_response

    def context_builder(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.CONTEXT_BUILDER,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        agent_profile = AgentProfile.objects.get(user=self.pubnub_channel.receiver_user)
        agent_portfolio_id = agent_profile.user_id

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.context_builder(
                conversation_id=self.pubnub_channel.id,
                agent_portfolio_id=agent_portfolio_id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o,
            messages=memoryHistory,
            tools=[
                tools.get_preferences,
                tools.get_nearby_properties,
                tools.get_lat_long_coordinates,
                tools.get_properties_by_community_or_building,
                tools.get_agent_portfolio_properties_for_given_user_query,
            ],
            json_response=True,
        )

        memory_object.save()

        return ai_response

    @log_input_output
    def react_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.REACT_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            json_response=True,
        )
        memory_object.save()

        return ai_response

    @log_input_output
    def orchestrator(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.ORCHESTRATOR,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.orchestrator(
                conversation_id=self.pubnub_channel.id,
                agents=[
                    AgentType.PROPERTY_DETAILS_AGENT,
                    AgentType.SCHEDULE_VISIT_AGENT,
                    AgentType.LAW_ASSISTANCE_AGENT,
                    AgentType.PREFERENCE_AGENT,
                    AgentType.REAL_ESTATE_AGENT,
                ],
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            temperature=1,
            json_response=True,
        )

        memory_object.save()
        return ai_response

    @log_input_output
    def property_details_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)

        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.PROPERTY_DETAILS_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        agent_profile = AgentProfile.objects.get(user=self.pubnub_channel.receiver_user)
        agent_portfolio_id = agent_profile.user_id

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.property_details_agent(
                conversation_id=self.pubnub_channel.id,
                agent_portfolio_id=agent_portfolio_id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            tools=[
                tools.get_property_details,
            ],
        )

        memory_object.save()
        return ai_response

    @log_input_output
    def schedule_visit_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.SCHEDULE_VISIT_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.schedule_visit_agent(
                conversation_id=self.pubnub_channel.id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            tools=[tools.send_message_to_seller],
        )

        memory_object.save()
        return ai_response

    @log_input_output
    def law_assistance_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.LAW_ASSISTANCE_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.law_assistance_agent(
                conversation_id=self.pubnub_channel.id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            # tools=[tools.internet_search, tools.get_web_content],
        )

        memory_object.save()
        return ai_response

    @log_input_output
    def preference_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)

        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.PREFERENCE_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.preference_agent(
                conversation_id=self.pubnub_channel.id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            tools=[
                tools.get_preferences,
                tools.create_preferences,
                tools.update_preferences,
                tools.delete_preferences,
            ],
        )

        memory_object.save()

        return ai_response

    @log_input_output
    def aggregator(self, user_message: dict | List[dict]) -> str:
        # iris = Iris(user=self.pubnub_channel)
        # memory_object = Memory.objects.get(
        #     messages=self.messages,
        #     agent_type=AgentType.AGGREGATOR,
        # )
        # if isinstance(user_message, dict):
        #     user_message = [user_message]
        # memory_object.memory.extend(user_message)
        # ai_response = iris.chat_completion(
        #     model=Model.GPT_4o_mini,
        #     messages=memory_object.memory,
        # )
        # memory_object.save()
        aggregator_response = str(user_message)
        logger.info(f"Aggregator response: {aggregator_response}")
        return aggregator_response

    @log_input_output
    def real_estate_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel, pubnub_handler=self.pubnub_handler)

        tools = Tools()

        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.REAL_ESTATE_AGENT,
        )

        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.real_estate_agent(
                conversation_id=self.pubnub_channel.id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            json_response=True,
            tools=[tools.send_media_to_buyer],
        )

        memory_object.save()

        return ai_response

    @log_input_output
    def output_guard_rail(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        # tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.OUTPUT_GUARD_RAIL,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]

        memory_object.memory.extend(user_message)

        current_prompt = Iris.create_message(
            role=Role.SYSTEM,
            content=self.multi_agent_prompts.output_guard_rail(
                conversation_id=self.pubnub_channel.id,
            ),
        )

        memoryHistory = [current_prompt] + memory_object.memory[1:][-10:]

        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memoryHistory,
            # tools=[
            #     tools.send_media_to_buyer
            # ],
        )

        memory_object.save()

        return ai_response

    @log_input_output
    def update_current_scenario(
        self, messages_id: int, user_query: str, extra_prompt: str = ""
    ):
        try:
            main_message_object = Messages.objects.get(id=messages_id)
            iris = Iris()
            current_sitation = "This is what currently user has asked: \n" + user_query
            system_message = iris.create_message(
                role=Role.SYSTEM,
                content=current_sitation,
            )
            for memory_of_agent in Memory.objects.filter(
                messages_id=main_message_object.id
            ):
                memory_of_agent.memory.append(system_message)
                memory_of_agent.save()

            logger.info("Current scenario updated successfully")
            return "Current scenario updated successfully."
        except Exception as e:
            logger.error(f"Error updating current scenario: {str(e)}")
            return custom_error_message

    @log_input_output
    def is_valid_url(self, url):
        try:
            result = urlparse(url)
            # A valid URL should have at least scheme (like http/https) and netloc (domain)
            return all([result.scheme, result.netloc])
        except ValueError:
            logger.warning(f"Invalid URL format: {url}")
            return False

    @log_input_output
    def generate_response(self, query: str) -> str:
        """
        Generate a response for the buyer query using the multi-agent system
        Args:
            query: The query to generate a response for
        Returns:
            The response to the query
        """
        try:
            if self.is_valid_url(query):
                base64_image_url = Iris.generate_base64_image_url(query)
                user_message = Iris.create_message(
                    role=Role.USER, actor=Actor.Buyer, images=[base64_image_url]
                )
            else:
                user_message = Iris.create_message(
                    role=Role.USER, actor=Actor.Buyer, content=query
                )

            self.messages.messages.append(user_message)

            self.update_current_scenario(messages_id=self.messages.id, user_query=query)
            # --- Input Guard Rail ---

            input_guard_rail_response = self.input_guard_rail(user_message=user_message)

            self.console.print(
                Panel(
                    Syntax(
                        json.dumps(input_guard_rail_response, indent=2),
                        "json",
                        theme="monokai",
                        word_wrap=True,
                    ),
                    title="🛡️ Input Guard Rail Response",
                    border_style="green",
                    width=120,
                )
            )

            match input_guard_rail_response["response_to"]:
                case AgentType.CONTEXT_BUILDER:
                    user_message = Iris.create_message(
                        role=Role.ASSISTANT,
                        content=input_guard_rail_response["response"],
                    )
                    # --- Context Builder ---
                    context_builder_response = self.context_builder(user_message)
                    self.console.print(
                        Panel(
                            Syntax(
                                json.dumps(context_builder_response, indent=2),
                                "json",
                                theme="monokai",
                                word_wrap=True,
                            ),
                            title="🏗️ Context Builder Response",
                            border_style="blue",
                            width=120,
                        )
                    )
                    match context_builder_response["response_to"]:
                        case AgentType.ORCHESTRATOR:
                            # --- Orchestrator ---
                            context_builder_message = Iris.create_message(
                                role=Role.USER,
                                actor=AgentType.CONTEXT_BUILDER,
                                content=context_builder_response["response"],
                            )
                            orchestrator_response = self.orchestrator(
                                context_builder_message
                            )
                            self.console.print(
                                Panel(
                                    Syntax(
                                        json.dumps(orchestrator_response, indent=2),
                                        "json",
                                        theme="monokai",
                                        word_wrap=True,
                                    ),
                                    title="🎭 Orchestrator Response",
                                    border_style="yellow",
                                    width=120,
                                )
                            )
                            for_aggregation = []
                            if "agents" in orchestrator_response:
                                for response in orchestrator_response["agents"]:
                                    orchestrator_message_to_agent = Iris.create_message(
                                        role=Role.USER,
                                        actor=AgentType.ORCHESTRATOR,
                                        content=response["context"],
                                    )
                                    match response["agent"]:
                                        case AgentType.PROPERTY_DETAILS_AGENT:
                                            response_from_agent = (
                                                self.property_details_agent(
                                                    orchestrator_message_to_agent
                                                )
                                            )
                                        case AgentType.SCHEDULE_VISIT_AGENT:
                                            response_from_agent = (
                                                self.schedule_visit_agent(
                                                    orchestrator_message_to_agent
                                                )
                                            )
                                        case AgentType.LAW_ASSISTANCE_AGENT:
                                            response_from_agent = (
                                                self.law_assistance_agent(
                                                    orchestrator_message_to_agent
                                                )
                                            )
                                        case AgentType.PREFERENCE_AGENT:
                                            response_from_agent = self.preference_agent(
                                                orchestrator_message_to_agent
                                            )
                                    for_aggregation.append(
                                        {
                                            "agent": response["agent"],
                                            "response": response_from_agent,
                                        }
                                    )
                                for agent_response in for_aggregation:
                                    self.console.print(
                                        Panel(
                                            Syntax(
                                                json.dumps(
                                                    agent_response["response"], indent=2
                                                ),
                                                "json",
                                                theme="monokai",
                                                word_wrap=True,
                                            ),
                                            title=f"🤖 {agent_response['agent']} Response",
                                            border_style="magenta",
                                            width=120,
                                        )
                                    )

                                orchestrator_message = Iris.create_message(
                                    role=Role.USER,
                                    actor=AgentType.ORCHESTRATOR,
                                    content=str(for_aggregation),
                                )

                                # --- Aggregator ---

                                aggregator_response = self.aggregator(
                                    orchestrator_message
                                )

                                self.console.print(
                                    Panel(
                                        str(aggregator_response),
                                        title="🔄 Aggregator Response",
                                        border_style="cyan",
                                    )
                                )
                            else:
                                if (
                                    orchestrator_response["agent"]
                                    == AgentType.REAL_ESTATE_AGENT
                                ):
                                    aggregator_response = orchestrator_response[
                                        "context"
                                    ]
                                else:
                                    aggregator_response = (
                                        "No valid agent found in orchestrator response."
                                    )
                                    print(
                                        "##### THIS SHOULD NOT HAPPEN #####",
                                        aggregator_response,
                                    )
                                self.console.print(
                                    Panel(
                                        str(aggregator_response),
                                        title="🤖 Bypassed Response",
                                        border_style="magenta",
                                    )
                                )

                            aggregator_message = Iris.create_message(
                                role=Role.USER,
                                actor=AgentType.AGGREGATOR,
                                content=aggregator_response,
                            )
                            # --- Real Estate Persona Agent ---
                            real_estate_agent_response = self.real_estate_agent(
                                aggregator_message
                            )

                            self.console.print(
                                Panel(
                                    Syntax(
                                        json.dumps(
                                            real_estate_agent_response, indent=2
                                        ),
                                        "json",
                                        theme="monokai",
                                        word_wrap=True,
                                    ),
                                    title="🏠 Real Estate Agent Response",
                                    border_style="red",
                                    width=120,
                                )
                            )

                            match real_estate_agent_response["response_to"]:
                                case Actor.Buyer:
                                    main_response = real_estate_agent_response[
                                        "response"
                                    ]
                                case _:
                                    main_response = real_estate_agent_response

                            assistant_response = Iris.create_message(
                                role=Role.ASSISTANT,
                                content=str(real_estate_agent_response),
                            )
                            self.messages.messages.append(assistant_response)
                            return main_response
                        case Actor.Buyer:
                            return context_builder_response["response"]
                case Actor.Buyer:
                    return input_guard_rail_response["response"]

        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return custom_error_message

    @log_input_output
    def react_agent_pipeline(self, query: str) -> str:
        """
        Generate a response for the buyer query using the multi-agent system
        Args:
            query: The query to generate a response for
        Returns:
            The response to the query
        """
        try:
            if self.is_valid_url(query):
                base64_image_url = Iris.generate_base64_image_url(query)
                user_message = Iris.create_message(
                    role=Role.USER, images=[base64_image_url]
                )
            else:
                user_message = Iris.create_message(role=Role.USER, content=query)

            self.messages.messages.append(user_message)
            self.update_current_scenario(self.messages.id)
            # --- Input Guard Rail ---
            input_guard_rail_response = self.input_guard_rail(user_message=user_message)
            self.console.print(
                Panel(
                    Syntax(
                        json.dumps(input_guard_rail_response, indent=2),
                        "json",
                        theme="monokai",
                        word_wrap=True,
                    ),
                    title="🛡️ Input Guard Rail Response",
                    border_style="green",
                    width=120,
                )
            )

            match input_guard_rail_response["response_to"]:
                case AgentType.REACT_AGENT:
                    user_message = Iris.create_message(
                        role=Role.ASSISTANT,
                        content=input_guard_rail_response["response"],
                    )

                    # --- React Agent ---
                    react_agent_response = self.react_agent(user_message)
                    self.console.print(
                        Panel(
                            Syntax(
                                json.dumps(react_agent_response, indent=2),
                                "json",
                                theme="monokai",
                                word_wrap=True,
                            ),
                            title="🤖 React Agent Response",
                            border_style="magenta",
                            width=120,
                        )
                    )
                    match react_agent_response["response_to"]:
                        case Actor.Agent:
                            match react_agent_response["agent"]:
                                case AgentType.PROPERTY_DETAILS_AGENT:
                                    response_from_agent = self.property_details_agent(
                                        user_message
                                    )
                                case AgentType.SCHEDULE_VISIT_AGENT:
                                    response_from_agent = self.schedule_visit_agent(
                                        user_message
                                    )
                                case AgentType.LAW_ASSISTANCE_AGENT:
                                    response_from_agent = self.law_assistance_agent(
                                        user_message
                                    )

                case Actor.Buyer:
                    return input_guard_rail_response["response"]
        except Exception as e:
            logger.error(f"Error in react agent pipeline: {str(e)}")
            return custom_error_message
