from rezio.assistant.services.iris_service import <PERSON>, Tools
from rezio.assistant.models import PubNubChannel, Messages, Memory
from rezio.assistant.types import AgentType, Role, Model, Actor
import json
from typing import Tuple, List
from urllib.parse import urlparse
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from langfuse.decorators import observe
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output
import logging

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class IrisReasoningAgent:
    def __init__(self, pubnub_channel: PubNubChannel):
        self.messages = pubnub_channel.buyer.first()
        self.pubnub_channel = pubnub_channel
        self.console = Console()

    @observe()
    @log_input_output
    def input_guard_rail(self, user_message: dict | List[dict]) -> Tuple[str, str]:
        iris = Iris(user=self.pubnub_channel)
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.INPUT_GUARD_RAIL,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            json_response=True,
            temperature=0.5,
        )
        memory_object.save()
        logger.info(f"Input guard rail response: {ai_response}")
        return ai_response

    @observe()
    @log_input_output
    def context_builder(self, user_message: dict | List[dict] = None) -> str:
        iris = Iris(user=self.pubnub_channel)
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.CONTEXT_BUILDER,
        )
        if user_message is not None:
            if isinstance(user_message, dict):
                user_message = [user_message]
            memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            json_response=True,
        )
        logger.info(f"Context builder response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Context Builder Internal reasoning",
                border_style="blue",
            )
        )
        if "thought" in ai_response and "orchestrator" in ai_response:
            message_for_orchestrator = Iris.create_message(
                role=Role.USER,
                actor=AgentType.CONTEXT_BUILDER,
                content=str(ai_response["orchestrator"]),
            )
            memory_object.save()
            orchestrator_response = self.orchestrator(message_for_orchestrator)
            response_message_from_orchestrator = Iris.create_message(
                role=Role.USER,
                actor=AgentType.ORCHESTRATOR,
                content=str(orchestrator_response["response"]),
            )
            whatever_response_from_context_builder = self.context_builder(
                response_message_from_orchestrator
            )
            return whatever_response_from_context_builder

        elif "thought" in ai_response and "orchestrator" not in ai_response:
            memory_object.save()
            return self.context_builder()

        elif "response_to" in ai_response:
            memory_object.save()
            return ai_response
        return ai_response

    @observe()
    @log_input_output
    def orchestrator(self, user_message: dict | List[dict] = None) -> str:
        iris = Iris(user=self.pubnub_channel)
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.ORCHESTRATOR,
        )
        if user_message is not None:
            if isinstance(user_message, dict):
                user_message = [user_message]
            memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            temperature=1,
            json_response=True,
        )
        logger.info(f"Orchestrator response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Orchestrator Internal reasoning",
                border_style="blue",
            )
        )
        if "thought" in ai_response and "agents" in ai_response:
            if ai_response["agents"].__len__() == 0:
                memory_object.save()
                return self.orchestrator()
            agents_responses = []
            for agent in ai_response["agents"]:
                message_for_agent = Iris.create_message(
                    role=Role.USER,
                    actor=AgentType.ORCHESTRATOR,
                    content=agent["context"],
                )
                if agent["agent"] == AgentType.PROPERTY_DETAILS_AGENT:
                    response_from_agent = self.property_details_agent(message_for_agent)
                elif agent["agent"] == AgentType.SCHEDULE_VISIT_AGENT:
                    response_from_agent = self.schedule_visit_agent(message_for_agent)
                elif agent["agent"] == AgentType.LAW_ASSISTANCE_AGENT:
                    response_from_agent = self.law_assistance_agent(message_for_agent)

                message_from_agent = Iris.create_message(
                    role=Role.USER,
                    actor=agent["agent"],
                    content=response_from_agent,
                )
                agents_responses.append(message_from_agent)

            memory_object.save()
            return self.orchestrator(agents_responses)

        elif "thought" in ai_response and "agents" not in ai_response:
            memory_object.save()
            return self.orchestrator()

        elif "response_to" in ai_response:
            memory_object.save()
            return ai_response
        return ai_response

    @observe()
    @log_input_output
    def property_details_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.PROPERTY_DETAILS_AGENT,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            tools=[
                tools.get_agent_portfolio,
                tools.get_property_details,
                # tools.send_message_to_real_estate_agent,
            ],
        )
        memory_object.save()
        logger.info(f"Property details agent response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Property Details Agent Internal reasoning",
                border_style="blue",
            )
        )
        return ai_response

    @observe()
    @log_input_output
    def schedule_visit_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.SCHEDULE_VISIT_AGENT,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            tools=[tools.send_message_to_seller],
        )
        memory_object.save()
        logger.info(f"Schedule visit agent response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Schedule Visit Agent Internal reasoning",
                border_style="blue",
            )
        )
        return ai_response

    @observe()
    @log_input_output
    def law_assistance_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.LAW_ASSISTANCE_AGENT,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            tools=[tools.internet_search],
        )
        memory_object.save()
        logger.info(f"Law assistance agent response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Law Assistance Agent Internal reasoning",
                border_style="blue",
            )
        )
        return ai_response

    @observe()
    @log_input_output
    def preference_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.PREFERENCE_AGENT,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            tools=[
                tools.get_preferences,
                tools.update_preferences,
            ],
        )
        memory_object.save()
        logger.info(f"Preference agent response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Preference Agent Internal reasoning",
                border_style="blue",
            )
        )
        return ai_response

    @observe()
    @log_input_output
    def aggregator(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.AGGREGATOR,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
        )
        memory_object.save()
        logger.info(f"Aggregator response: {ai_response}")
        return ai_response

    @observe()
    @log_input_output
    def real_estate_agent(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.REAL_ESTATE_AGENT,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            json_response=True,
            tools=[tools.send_media_to_buyer],
        )
        memory_object.save()
        logger.info(f"Real estate agent response: {ai_response}")
        self.console.print(
            Panel(
                Syntax(
                    json.dumps(ai_response, indent=2),
                    "json",
                    theme="monokai",
                ),
                title="🏗️ Real Estate Agent Internal reasoning",
                border_style="blue",
            )
        )
        return ai_response

    @observe()
    @log_input_output
    def output_guard_rail(self, user_message: dict | List[dict]) -> str:
        iris = Iris(user=self.pubnub_channel)
        # tools = Tools()
        memory_object = Memory.objects.get(
            messages=self.messages,
            agent_type=AgentType.OUTPUT_GUARD_RAIL,
        )
        if isinstance(user_message, dict):
            user_message = [user_message]
        memory_object.memory.extend(user_message)
        ai_response = iris.chat_completion(
            model=Model.GPT_4o_mini,
            messages=memory_object.memory,
            # tools=[
            #     tools.send_media_to_buyer
            # ],
        )
        memory_object.save()
        logger.info(f"Output guard rail response: {ai_response}")
        return ai_response

    @log_input_output
    def update_current_scenario(
        self, messages_id: int, user_query: str, extra_prompt: str = ""
    ):
        try:
            main_message_object = Messages.objects.get(id=messages_id)
            iris = Iris()
            current_sitation = "This is what currently user has asked: \n" + user_query
            system_message = iris.create_message(
                role=Role.SYSTEM,
                content=current_sitation,
            )
            for memory_of_agent in Memory.objects.filter(
                messages_id=main_message_object.id,
            ):
                memory_of_agent.memory.append(system_message)
                memory_of_agent.save()

            logger.info("Current scenario updated successfully")
            return "Current scenario updated successfully."
        except Exception as e:
            logger.error(f"Error updating current scenario: {str(e)}")
            return f"Error updating current scenario: {str(e)}"

    @log_input_output
    def is_valid_url(self, url):
        try:
            result = urlparse(url)
            # A valid URL should have at least scheme (like http/https) and netloc (domain)
            return all([result.scheme, result.netloc])
        except ValueError:
            logger.warning(f"Invalid URL format: {url}")
            return False

    @observe()
    @log_input_output
    def reason(self, query: str) -> str:
        """
        Generate a response for the buyer query using Iris reasoning agent
        Args:
            query: The query to generate a response for
        Returns:
            The response to the query
        """
        try:
            if self.is_valid_url(query):
                base64_image_url = Iris.generate_base64_image_url(query)
                input_for_input_guard_rail = Iris.create_message(
                    role=Role.USER, actor=Actor.Buyer, images=[base64_image_url]
                )
            else:
                input_for_input_guard_rail = Iris.create_message(
                    role=Role.ASSISTANT, content=f"Buyer's query: {query}"
                )

            user_message = Iris.create_message(
                role=Role.USER, actor=Actor.Buyer, content=query
            )
            self.messages.messages.append(user_message)
            self.messages.save()

            # --- Input Guard Rail ---
            input_guard_rail_response = self.input_guard_rail(
                user_message=input_for_input_guard_rail
            )
            self.console.print(
                Panel(
                    Syntax(
                        json.dumps(input_guard_rail_response, indent=2),
                        "json",
                        theme="monokai",
                    ),
                    title="🛡️ Input Guard Rail Response",
                    border_style="green",
                )
            )

            match input_guard_rail_response["response_to"]:
                case AgentType.CONTEXT_BUILDER:
                    user_message = Iris.create_message(
                        role=Role.ASSISTANT,
                        content=input_guard_rail_response["response"],
                    )
                    # --- Context Builder ---
                    context_builder_response = self.context_builder(user_message)
                    if context_builder_response["response_to"] == Actor.Buyer:
                        context_builder_message = Iris.create_message(
                            role=Role.USER,
                            actor=AgentType.CONTEXT_BUILDER,
                            content=context_builder_response["response"],
                        )
                        real_estate_agent_response = self.real_estate_agent(
                            context_builder_message
                        )
                        assistant_response = Iris.create_message(
                            role=Role.ASSISTANT,
                            content=real_estate_agent_response["response"],
                        )
                        self.messages.messages.append(assistant_response)
                        self.messages.save()
                        return real_estate_agent_response["response"]
                case Actor.Buyer:
                    assistant_response = Iris.create_message(
                        role=Role.ASSISTANT,
                        content=input_guard_rail_response["response"],
                    )
                    self.messages.messages.append(assistant_response)
                    self.messages.save()
                    return input_guard_rail_response["response"]
        except Exception as e:
            logger.error(f"Error in reasoning: {str(e)}")
            return f"Error processing request: {str(e)}"
