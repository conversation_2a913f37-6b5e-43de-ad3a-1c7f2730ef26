import json
from langfuse.openai import OpenAI
from openai.types.chat import Cha<PERSON><PERSON><PERSON>pletion
from openai import BadRequestError
from rezio.assistant.models import (
    PubNubChannel,
    PubNubWebUser,
    Messages,
    Preferences,
)
from typing import Dict, List, Union
from channels.layers import get_channel_layer
import inspect
from datetime import timed<PERSON><PERSON>, datetime
from django_celery_beat.models import PeriodicTask, ClockedSchedule
import json
import uuid
from rezio.assistant.types import (
    Actor,
    Model,
    BuyerTone,
    AgentTone,
    NegotiationStyle,
    BackendActions,
    Role,
)
import requests
from celery import shared_task
from asgiref.sync import async_to_sync
import base64
import mimetypes
import logging
from rezio.rezio.settings import env
from rezio.user.utils import get_agent_profile_object
from rezio.properties.models import AgentAssociatedProperty, Property
from rezio.utils.text_choices import CustomMessageType
from rezio.properties.text_choices import (
    OwnerIntentForProperty,
    PropertyPublishStatus,
    UserRequestActions,
)
from django.db.models import F
from rezio.properties.serializers.property_details_serializer import (
    AIAgentPortfolioViewSerializer,
    AIPropertyDetailSerializer,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import general_exception_handler, log_input_output
from rezio.pubnub.handler import PubNubHandler
from django.db import connection
import googlemaps

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class Iris:
    def __init__(
        self,
        buyer_agent_group: str = None,
        agent_group: str = None,
        buyer_group: str = None,
        user: PubNubChannel = None,
        pubnub_handler: PubNubHandler = None,
    ):
        self.user = user
        self.llm_client = OpenAI(
            api_key=env(
                "OPENAI_API_KEY",
                default="********************************************************************************************************************************************************************",
            )
        )
        self.gmap_client = googlemaps.Client(key=env("GOOGLE_MAPS_API_KEY"))
        self.buyer_agent_group = buyer_agent_group
        self.agent_group = agent_group
        self.buyer_group = buyer_group
        self.pubnub_handler = pubnub_handler
        self.functions = Functions(
            user=user,
            buyer_agent_group=self.buyer_agent_group,
            agent_group=self.agent_group,
            buyer_group=self.buyer_group,
            iris=self,
            pubnub_handler=self.pubnub_handler,
            gmap_client=self.gmap_client,
        )

    @classmethod
    @log_input_output
    def create_message(
        cls,
        role: Role,
        content: str = None,
        actor: Actor = None,
        images: List[str] = None,
    ) -> dict:
        """
        Create a message object for LLM chat completion APIs
        params:
            role: Role of the message sender
            content: text message content
            actor: Actor of the message sender
            images: List of image URLs
        returns:
            message: Message object
        """
        message = {
            "role": role,
        }
        if actor:
            message["name"] = actor
        if not images:
            message["content"] = content
        else:
            message["content"] = []
            if content not in ["", None]:
                message["content"].append(
                    {
                        "type": "text",
                        "text": content,
                    }
                )
            if images:
                for image in images:
                    logger.info(f"Processing image: {image}")
                    message["content"].append(
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image,
                            },
                        }
                    )

        return message

    @classmethod
    @log_input_output
    def generate_base64_image_url(cls, image_url: str):
        """
        Generate a base64 image URL from a given image URL
        params:
            image_url: URL of the image to be converted to base64
        returns:
            base64 image URL
        """
        try:
            response = requests.get(image_url)
            image_data = response.content
            mime = mimetypes.guess_type(image_url)[0]
            base64_image = base64.b64encode(image_data).decode("utf-8")
            return f"data:{mime};base64,{base64_image}"
        except Exception as e:
            logger.error(f"Error generating base64 image URL: {str(e)}")
            raise

    @log_input_output
    def handle_model_response(self, response_message: str):
        try:
            response_message = json.loads(response_message)
            channel_layer = get_channel_layer()
            message = self.create_message(
                role=Role.ASSISTANT,
                content=response_message["response"],
            )
            match response_message["response_to"]:
                case Actor.Buyer:
                    group = self.buyer_agent_group
                case Actor.Agent:
                    group = self.agent_group

            async_to_sync(channel_layer.group_send)(
                group,
                {
                    "type": "send.message",
                    "action": BackendActions.DISPLAY_MESSAGES,
                    "message": [message],
                },
            )
        except Exception as e:
            logger.error(f"Error handling model response: {str(e)}")
            raise

    @general_exception_handler
    def chat_completion(
        self,
        model: Model,
        messages: List[dict],
        tools: list = [],
        temperature: int = 1,
        json_response: bool = False,
        max_tokens=None,
        query: str | dict = None,
        seed: int = None,
        actor: Actor = None,
    ) -> Union[str, dict]:
        try:
            completion_object = {
                "model": model,
                "temperature": temperature,
                "messages": messages,
            }
            if query:
                if type(query) == str:
                    messages.append(
                        {
                            "role": "user",
                            "name": actor,
                            "content": query,
                        },
                    )
                elif type(query) == dict:
                    messages.append(query)
            if max_tokens:
                completion_object["max_tokens"] = max_tokens
            if tools.__len__() > 0:
                completion_object["tools"] = [
                    {"type": "function", "function": tool()} for tool in tools
                ]
                completion_object["tool_choice"] = "auto"
            if json_response:
                completion_object["response_format"] = {"type": "json_object"}
            if seed:
                completion_object["seed"] = seed

            # logger.info(f"Making chat completion request with: {completion_object}")

            chat_completion: ChatCompletion = self.llm_client.chat.completions.create(
                **completion_object
            )
            # logger.info(f"Chat completion response: {chat_completion}")

            message = chat_completion.choices[0].message
            finish_reason = chat_completion.choices[0].finish_reason
            # logger.info(f"Finish reason: {finish_reason}")

            if finish_reason == "stop":
                messages.append({"role": message.role, "content": message.content})
                content = (
                    json.loads(message.content) if json_response else message.content
                )
                return content
            elif finish_reason == "tool_calls":
                messages.append(
                    {
                        "role": message.role,
                        "content": message.content,
                        "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": tool_call.function.name,
                                    "arguments": tool_call.function.arguments,
                                },
                            }
                            for tool_call in message.tool_calls
                        ],
                    }
                )

                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    function_arguments = tool_call.function.arguments
                    logger.info(f"CALLING TOOL NAME: {function_name}")
                    logger.info(f"TOOL ARGUMENTS: {function_arguments}")
                    function_response = self.functions.call_a_function(
                        function_name, function_arguments
                    )
                    messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": function_name,
                            "content": function_response,
                        }
                    )

                for tool_call in message.tool_calls:
                    if (
                        tool_call.function.name
                        == self.functions.send_message_to_seller.__name__
                    ):
                        func_args = json.loads(tool_call.function.arguments)
                        message_to_agent = func_args["message"]
                        assistant_message_to_agent = json.dumps(
                            {
                                "response_to": Actor.Agent,
                                "response": message_to_agent,
                            }
                        )
                        assistant_message = self.create_message(
                            role=Role.ASSISTANT,
                            content=assistant_message_to_agent,
                        )
                        messages.append(assistant_message)

                # logger.info(f"Messages after tool calls: {messages}")
                response = self.chat_completion(
                    actor=actor,
                    model=model,
                    messages=messages,
                    tools=tools,
                    temperature=temperature,
                    json_response=json_response,
                )
                return response

        except json.decoder.JSONDecodeError as error:
            logger.error(f"JSON decoding error: {error}")
            query = "response is not in correct json format, correct the format."
            return self.chat_completion(
                actor=actor,
                model=model,
                query=query,
                messages=messages,
                tools=tools,
                temperature=temperature,
                json_response=False,
                max_tokens=max_tokens,
            )
        except BadRequestError as error:
            logger.error(f"Bad request error: {error}")
            raise
        except Exception as error:
            print(f"Iris Unexpected error: {error}")
            print(f"Iris Unexpected error: {error.args}")
            print(f"Iris Unexpected error: {error.with_traceback}")
            raise

    @log_input_output
    def extract_json_objects(self, text_contains_json_object: str):
        try:
            json_objects = []
            start_index = text_contains_json_object.find("{")
            while start_index != -1:
                end_index = text_contains_json_object.rfind("}")
                if end_index == -1:
                    break
                json_str = text_contains_json_object[start_index : end_index + 1]
                try:
                    json_data = json.loads(json_str)
                    json_objects.append(json_data)
                except json.JSONDecodeError:
                    logger.error(f"Failed to decode JSON object: {json_str}")
                    raise json.JSONDecodeError(
                        "Failed to decode JSON object", json_str, 0
                    )

                text_contains_json_object = text_contains_json_object[end_index + 1 :]
                start_index = text_contains_json_object.find("{")

            return json_objects[0]
        except Exception as e:
            logger.error(f"Error extracting JSON objects: {str(e)}")
            raise


class Tools:
    def internet_search(self):
        return {
            "name": "internet_search",
            "description": "this tool will take google search query and return list of links.",
            "parameters": {
                "type": "object",
                "properties": {
                    "search_query": {
                        "type": "string",
                        "description": "pass the seo optimized google search query to get the best results",
                    },
                },
                "required": ["search_query"],
            },
        }

    def get_property_details(self):
        return {
            "name": "get_property_details",
            "description": "Get more details about list of properties all at once. Use this when you need extra information related to properties, including metadata.",
            "parameters": {
                "type": "object",
                "properties": {
                    "properties": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "property_id": {
                                    "type": "integer",
                                    "description": "The unique identifier of the property.",
                                },
                                "meta_data": {
                                    "type": "object",
                                    "properties": {
                                        "distance": {
                                            "type": "string",
                                            "description": "The distance to the property, expressed as a string with unit (e.g., '2km').",
                                        }
                                    },
                                    "description": "Optional metadata providing additional information about the property.",
                                },
                            },
                            "required": ["property_id"],
                            "description": "An object representing a property with its ID and optional metadata.",
                        },
                        "description": "Array of property objects to fetch details for multiple properties at once. Send all properties you want to lookup in a single array.",
                    }
                },
                "required": ["properties"],
            },
        }

    def send_message_to_real_estate_agent(self):
        return {
            "name": "send_message_to_real_estate_agent",
            "description": "send a message to real estate agent of this property.",
            "parameters": {
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "message that you want to send to the real estate agent",
                    },
                },
                "required": ["message"],
            },
        }

    def set_visit_followup_reminder(self):
        """
        Creates a reminder for the agent to follow up with the buyer or seller after a property visit.
        The reminder is triggered a specified number of seconds after the visit timing is confirmed.
        This ensures timely follow-ups to gather feedback or take necessary next steps.

        Returns:
            dict: Schema for configuring the follow-up reminder.
        """
        return {
            "name": "set_visit_followup_reminder",
            "description": (
                "Creates a reminder for the agent to follow up on how the visit went "
                "after the buyer's property visit. The reminder is triggered a specified number "
                "of seconds after the visit timing is confirmed. for demo purpose set it for 10 seconds"
            ),
            "parameters": {
                "type": "object",
                "properties": {
                    "conversation_id": {
                        "type": "integer",
                        "description": "The ID of the conversation related to this reminder.",
                    },
                    "after": {
                        "type": "integer",
                        "description": (
                            "The time in seconds after which the reminder should be sent. "
                            "Usually between 1 to 10 seconds."
                        ),
                    },
                    "response_to": {
                        "type": "string",
                        "description": "The person to whom the reminder should be directed.",
                        "enum": ["Agent"],
                    },
                    "response": {
                        "type": "string",
                        "description": "The message to be sent as a reminder.",
                    },
                },
                "required": ["conversation_id", "after", "response_to", "response"],
            },
        }

    def set_reminder(self):
        return {
            "name": "set_reminder",
            "description": "set a reminder for the agent/seller or the buyer as an when needed.",
            "parameters": {
                "type": "object",
                "properties": {
                    "property_id": {
                        "type": "integer",
                        "description": "id of the property",
                    },
                    "after": {
                        "type": "integer",
                        "description": "time in seconds after which message should be sent",
                    },
                    "response_to": {
                        "type": "string",
                        "description": "the actor to whom the reminder should be sent",
                    },
                    "response": {
                        "type": "string",
                        "description": "the message to be sent as a reminder",
                    },
                },
                "required": ["property_id", "after", "response_to", "response"],
            },
        }

    def send_message_to_seller(self):
        return {
            "name": "send_message_to_seller",
            "description": "you can send the considered offer price, or any other message.",
            "parameters": {
                "type": "object",
                "properties": {
                    "message": {
                        "type": "string",
                        "description": "message to send to the seller",
                    },
                },
                "required": ["conversation_id", "message"],
            },
        }

    def send_media_to_buyer(self):
        return {
            "name": "send_media_to_buyer",
            "description": 'send media to the buyer, only send media if there is an actual url. NEVER EVER send empty {"media_data": []} as a response. Don\'t call this function with empty media data array or it gets stuck in infinite loop. Whenever you\'re sending media, it should always be like this. {"media_data": [{"media_type":"image","media_url":"URL","caption":"Caption"}, {"media_type":"video","media_url":"URL","caption":"Caption","thumbnail":"Thumbnail URL"}]}. Never share the thumbnail as a separate media or image, it should only be associated with its video.',
            "parameters": {
                "type": "object",
                "properties": {
                    "media_data": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "media_type": {
                                    "type": "string",
                                    "description": "type of the media. Strictly only image or video.",
                                },
                                "media_url": {
                                    "type": "string",
                                    "description": "URL of the media, either video or image, but never a thumbnail url",
                                },
                                "caption": {
                                    "type": "string",
                                    "description": "name of the section or other section of the property, based on the media (image or video) meta data.",
                                },
                                "thumbnail": {
                                    "type": "string",
                                    "description": "thumbnail url of the video of media type is video. remember, never send a video url here.",
                                },
                            },
                            "required": [
                                "media_type",
                                "media_url",
                                "caption",
                                "thumbnail",
                            ],
                        },
                    },
                },
                "required": ["media_data"],
            },
        }

    def send_documents_to_buyer(self):
        return {
            "name": "send_documents_to_buyer",
            "description": "send documents to the buyer",
            "parameters": {
                "type": "object",
                "properties": {
                    "document_data": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "document_url": {
                                    "type": "string",
                                    "description": "URL of the document",
                                },
                                "caption": {
                                    "type": "string",
                                    "description": "Caption for the document",
                                },
                                "document_name": {
                                    "type": "string",
                                    "description": "name of the document",
                                },
                            },
                            "required": ["document_url", "caption", "document_name"],
                        },
                    },
                },
                "required": ["document_data"],
            },
        }

    def get_agent_portfolio(self):
        return {
            "name": "get_agent_portfolio",
            "description": "get your agent portfolio",
            "parameters": {
                "type": "object",
                "properties": {
                    "agent_portfolio_id": {
                        "type": "string",
                        "description": "id of the agent portfolio",
                    },
                },
                "required": ["agent_portfolio_id"],
            },
        }

    def get_agent_portfolio_properties_for_given_user_query(self):
        return {
            "name": "get_agent_portfolio_properties_for_given_user_query",
            "description": "get the properties of the agent portfolio for a given user query",
            "parameters": {
                "type": "object",
                "properties": {
                    "agent_portfolio_id": {
                        "type": "string",
                        "description": "id of the agent portfolio",
                    },
                    "relevent_context_for_query_on_user_query": {
                        "type": "string",
                        "description": "This gives relevent context to the tool which is an SQL query generator which will filter the properties on agent's portfolio based on your given context. You will have to be clear and include all relevent keywords that the query builder can use to generate the query. Don't worry about generating the SQL by yourself, but ensure you give clear informmation to the tool so that it understands what aspects it may need to search the properties.",
                    },
                },
                "required": [
                    "agent_portfolio_id",
                    "relevent_context_for_query_on_user_query",
                ],
            },
        }

    def get_properties_by_community_or_building(self):
        return {
            "name": "get_properties_by_community_or_building",
            "description": "get the properties of the agent portfolio for a given user query based on community or building",
            "parameters": {
                "type": "object",
                "properties": {
                    "agent_portfolio_id": {
                        "type": "string",
                        "description": "id of the agent portfolio",
                    },
                    "search_term": {
                        "type": "string",
                        "description": "The search term to search for community name or building name in the database",
                    },
                },
                "required": ["agent_portfolio_id", "search_term"],
            },
        }

    def get_nearby_properties(self):
        return {
            "name": "get_nearby_properties",
            "description": "get the nearby properties for a given user query",
            "parameters": {
                "type": "object",
                "properties": {
                    "agent_portfolio_id": {
                        "type": "string",
                        "description": "id of the agent portfolio",
                    },
                    "lat": {
                        "type": "number",
                        "description": "The lat long fetched from user query",
                    },
                    "long": {
                        "type": "number",
                        "description": "The lat long fetched from user query",
                    },
                    "range": {
                        "type": "number",
                        "description": "The range in kms to search for",
                    },
                },
                "required": ["agent_portfolio_id", "lat", "long"],
            },
        }

    def get_preferences(self):
        return {
            "name": "get_preferences",
            "description": "get the current preferences of the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "conversation_id": {
                        "type": "string",
                        "description": "the id of the conversation",
                    },
                },
                "required": ["conversation_id"],
            },
        }

    def create_preferences(self):
        return {
            "name": "create_preferences",
            "description": "Create the preferences of the user, use this tool when you want to create the preferences of the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "conversation_id": {
                        "type": "string",
                        "description": "the id of the conversation",
                    },
                    "preference_details": {
                        "type": "object",
                        "description": "Generated 'preference_details' JSON from the 'preference_agent' ",
                    },
                },
                "required": ["preference_details", "conversation_id"],
            },
        }

    def update_preferences(self):
        return {
            "name": "update_preferences",
            "description": "update the preferences of the user, use this tool when you want to update the preferences of the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "conversation_id": {
                        "type": "string",
                        "description": "the id of the conversation",
                    },
                    "preference_id": {
                        "type": "string",
                        "description": "the id of the user_preference to be updated",
                    },
                    "preference_details": {
                        "type": "object",
                        "description": "Generated 'preference_details' JSON from the 'preference_agent' ",
                    },
                },
                "required": ["preference_id", "preference_details"],
            },
        }

    def delete_preferences(self):
        return {
            "name": "delete_preferences",
            "description": "update the preferences of the user, use this tool when you want to update the preferences of the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "conversation_id": {
                        "type": "string",
                        "description": "the id of the conversation",
                    },
                    "preference_id": {
                        "type": "object",
                        "description": "id of the prefrence to be removed' ",
                    },
                },
                "required": ["preference_id"],
            },
        }

    def get_lat_long_coordinates(self):
        return {
            "name": "get_lat_long_coordinates",
            "description": "Get the lat, long coordinates from user mentioned query",
            "parameters": {
                "type": "object",
                "properties": {
                    "address": {
                        "type": "string",
                        "description": "The location/area mentioned in user query whose lat, long is to be fetched",
                    },
                },
                "required": ["address"],
            },
        }


class Functions:
    def __init__(
        self,
        agent_group: str = None,
        buyer_agent_group: str = None,
        buyer_group: str = None,
        user: PubNubWebUser = None,
        iris: Iris = None,
        pubnub_handler: PubNubHandler = None,
        gmap_client: googlemaps.Client = None,
    ) -> None:
        self.user = user
        self.agent_group = agent_group
        self.buyer_agent_group = buyer_agent_group
        self.buyer_group = buyer_group
        self.iris = iris
        self.pubnub_handler = pubnub_handler
        self.gmap_client = gmap_client

    @classmethod
    @log_input_output
    def all_functions(cls):
        """
        Get all the actual functions of Iris AI Agent
        """
        methods = [
            func
            for func in dir(cls)
            if callable(getattr(cls, func))
            and not func.startswith("__")
            and func not in ["all_functions", "call_a_function"]
        ]
        # Create a dictionary with function names and function objects
        return {method: getattr(cls, method) for method in methods}

    @log_input_output
    def validate_function_call(self, function_name: str, function_arguments: str):
        """
        Validate the function call to ensure it is valid and has the correct arguments.
        """
        # Check if function name contains invalid characters
        logger.info("Validating function call")
        if "." in function_name or "multi_tool_use" in function_name:
            logger.warning(f"Invalid function name attempted: {function_name}")
            return f"Invalid function name, you are trying to call {function_name}"

        # Remove newline characters and load arguments from JSON
        function_arguments = function_arguments.replace("\n", "")
        try:
            function_arguments = json.loads(function_arguments)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format for function arguments: {e}")
            return f"Invalid JSON format for function arguments: {e}"

        # Retrieve the actual function to be called
        actual_function = self.all_functions().get(function_name)
        if actual_function is None:
            logger.warning(f"Attempted to call non-existent function: {function_name}")
            return f"Invalid function name, you are trying to call {function_name}"

        # Get the signature of the actual function
        signature = inspect.signature(actual_function)
        required_params = [
            param.name
            for param in signature.parameters.values()
            if param.default == inspect.Parameter.empty
            and param.kind == inspect.Parameter.POSITIONAL_OR_KEYWORD
        ]
        required_params.pop(required_params.index("self"))

        # Check if all required parameters are provided
        missing_params = [
            param for param in required_params if param not in function_arguments
        ]
        if missing_params:
            logger.warning(f"Missing required parameters: {missing_params}")
            return f"Missing required parameters: {', '.join(missing_params)}"

        # Check for unexpected arguments
        unexpected_params = [
            param for param in function_arguments if param not in signature.parameters
        ]
        if unexpected_params:
            logger.warning(f"Unexpected arguments provided: {unexpected_params}")
            return f"Unexpected arguments: {', '.join(unexpected_params)}"

        # If all validations pass, return None
        return None, function_arguments, actual_function

    @log_input_output
    def call_a_function(self, function_name: str, function_arguments: str):
        """
        Call an actual function by name with the provided arguments
        """
        # logger.info(f"Calling function {function_name} with arguments: {function_arguments}")

        try:
            # Call the validation function
            validation_result, function_arguments, actual_function = (
                self.validate_function_call(function_name, function_arguments)
            )

            # If there is a validation error, return it
            if validation_result:
                return validation_result

            # Call the function with the provided arguments
            response = actual_function(self, **function_arguments)
            return response

        except Exception as error:
            # logger.error(f"Error calling function {function_name}: {str(error)}")
            return str(error)

    @log_input_output
    def schedule_task(
        self, task_name: str, task_path: str, interval_seconds: int, task_kwargs: dict
    ):
        """
        Schedule a one-time task to run after a specified interval.

        :param task_name: A unique name for the task.
        :param task_path: The import path to the task function (e.g., 'myapp.tasks.my_task').
        :param interval_seconds: The time in seconds after which the task should be executed.
        :param task_kwargs: A dictionary of keyword arguments to pass to the task.
        :return: A message indicating whether the task was scheduled successfully or not.
        """
        try:
            # Calculate the exact time when the task should run
            execution_time = datetime.now() + timedelta(seconds=interval_seconds)

            # Create or get the ClockedSchedule for the given time
            clocked_schedule, _ = ClockedSchedule.objects.get_or_create(
                clocked_time=execution_time
            )

            uuid_str = uuid.uuid4()  # Generate a unique UUID for the task
            logger.info(f"Scheduling task {task_name} with UUID {uuid_str}")

            # Create the one-time PeriodicTask
            PeriodicTask.objects.create(
                clocked=clocked_schedule,
                name=f"{task_name} - UUID: {uuid_str}",
                description=f"Task to run {task_name} with {task_kwargs}",
                task=task_path,
                kwargs=json.dumps(task_kwargs),
                one_off=True,  # Ensure the task runs only once
                enabled=True,
            )

            return "Task scheduled successfully."

        except Exception as error:
            logger.error(f"Failed to schedule task {task_name}: {str(error)}")
            return f"Failed to schedule task: {error}"

    @log_input_output
    def set_reminder(
        self, conversation_id: int, after: int, response_to: Actor, response: str
    ):
        """
        Set a reminder for the user.
        :param conversation_id: The ID of the conversation.
        :param after: The time in seconds after which message should be sent.
        :param response_to: The actor to whom the reminder should be sent.
        :param response: The message to be sent as a reminder.
        :return: A message indicating whether the reminder was set successfully or not.
        """
        try:
            logger.info(f"Setting reminder for conversation {conversation_id}")
            self.schedule_task(
                task_name="set_reminder_for_message",
                task_path="assistant.tasks.set_reminder_for_message",
                interval_seconds=after,
                task_kwargs={
                    "conversation_id": conversation_id,
                    "response_to": response_to,
                    "response": response,
                },
            )

            return f"Reminder set successfully, continue your conversation."
        except Exception as error:
            logger.error(f"Failed to set reminder: {str(error)}")
            return f"Failed to set reminder: {error}"

    @log_input_output
    def set_visit_followup_reminder(
        self, conversation_id: int, after: int, response_to: Actor, response: str
    ):
        response = self.set_reminder(conversation_id, after, response_to, response)
        return response

    @shared_task
    @log_input_output
    def send_message_to_playground(message: str, group: str):
        try:
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                group,
                {
                    "type": "send.message",
                    "action": BackendActions.DISPLAY_MESSAGES,
                    "message": [
                        {
                            "role": "assistant",
                            "content": message,
                        }
                    ],
                },
            )
        except Exception as e:
            logger.error(f"Error sending message to playground: {str(e)}")
            raise

    @log_input_output
    def send_message_to_seller(self, message: str):
        try:
            self.send_message_to_playground.delay(
                message,
                self.seller_group,
            )
            return "message has been sent to the seller."
        except Exception as e:
            logger.error(f"Error sending message to seller: {str(e)}")
            return "Failed to send message to seller."

    @log_input_output
    def send_message_to_real_estate_agent(self, message: str):
        try:
            self.send_message_to_playground.delay(
                message,
                self.agent_group,
            )
            return "message has been sent to the real estate agent."
        except Exception as e:
            logger.error(f"Error sending message to real estate agent: {str(e)}")
            return "Failed to send message to real estate agent."

    @log_input_output
    def internet_search(self, search_query: str):
        results = []
        api_key = env(
            "GOOGLE_CUSTOM_SEARCH_API_KEY",
            default="AIzaSyAtGfe8vcMqpbap5FVs2I8jFtp4TW-keGE",
        )
        cx = env("SEARCH_ENGINE_ID", default="c0836d9eed01f48be")
        params = {
            "num": 5,  # Number of search results to return,
        }
        url = f"https://customsearch.googleapis.com/customsearch/v1?key={api_key}&cx={cx}&q={search_query}"

        try:
            logger.info(f"Making search request for query: {search_query}")
            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                for item in data.get("items", []):
                    title = item.get("title")
                    link = item.get("link")
                    results.append({"title": title, "link": link})
                return str(results) + "\n get the web content from these links"
            else:
                logger.error(
                    f"Search request failed with status code: {response.status_code}"
                )
                return None
        except Exception as e:
            logger.error(f"Error performing internet search: {str(e)}")
            return None

    @log_input_output
    def send_media_to_buyer(self, media_data: List[Dict]):
        try:
            sent_media_count = 0
            for media in media_data:
                if media["media_type"] == CustomMessageType.IMAGE.value:
                    sent_media_count += 1
                    self.pubnub_handler.publish_media(
                        target=self.user.group_channel_id,
                        message_type=CustomMessageType.IMAGE.value,
                        message=json.dumps(
                            {
                                "role": Role.ASSISTANT,
                                "content": media["media_url"],
                                "caption": media["caption"],
                                "channel_id": self.user.pubnub_channel_id,
                                "group_channel_id": self.user.group_channel_id,
                            }
                        ),
                        is_channel_group=True,
                    )

                elif media["media_type"] == CustomMessageType.VIDEO.value:
                    sent_media_count += 1

                    self.pubnub_handler.publish_media(
                        target=self.user.group_channel_id,
                        message_type=CustomMessageType.VIDEO.value,
                        message=json.dumps(
                            {
                                "role": Role.ASSISTANT,
                                "content": media["media_url"],
                                "caption": media["caption"],
                                "thumbnail": media["thumbnail"],
                                "channel_id": self.user.pubnub_channel_id,
                                "group_channel_id": self.user.group_channel_id,
                            }
                        ),
                        is_channel_group=True,
                    )

                else:
                    logger.error(f"Unsupported media type: {media['media_type']}")

            if sent_media_count > 0:
                # agent_group = f"{message_object.id}"
                # self.send_message_to_websocket.delay(
                #     group_name=agent_group,
                #     message=ai_messages,
                # )

                return f"{sent_media_count} media items has been sent to the buyer."
            else:
                return "No media items were sent to the buyer."
        except Exception as e:
            logger.error(f"Error sending media items to buyer: {str(e)}")
            return "Failed to send media items to buyer."

    @log_input_output
    def send_documents_to_buyer(self, document_data: List[Dict]):
        # TODO: Send document to buyer
        logger.info("Document sending functionality not yet implemented")
        pass

    # @log_input_output
    # def format_agent_portfolio(self, properties_under_agent_portfolio):
    #     """Format all property details into a clean, user-friendly summary."""
    #     summaries = []

    #     properties = Property.objects.filter(id__in=properties_under_agent_portfolio)

    #     for item in properties:
    #         # General Property Details
    #         summary = (
    #             f"🏡 **Property Overview**\n"
    #             f"- **Property ID:** {item.id}\n"
    #             f"- **Community:** {item.community}\n"
    #             f"- **Address:** {item.address}\n"
    #             # f"- **Building:** {item.building_name} (No: {item.building_number})\n"
    #             # f"- **Unit:** {item.unit_number}, Floor {item.floor_number}\n"
    #             # f"- **Type:** {item.property_type} ({item.property_unit_type})\n"
    #             f"- **Bedrooms:** {item.number_of_bedrooms}\n"
    #             f"- **Bathrooms:** {item.number_of_common_bathrooms + item.number_of_attached_bathrooms}\n"
    #             f"- **Powder Rooms:** {item.number_of_powder_rooms}\n"
    #             # f"- **Total Area:** {item.total_area} sq. ft.\n"
    #             # f"  - Carpet Area: {item.carpet_area} sq. ft.\n"
    #             # f"  - Balcony Area: {item.balcony_area} sq. ft.\n"
    #             # f"- **Distressed Deal:** {'Yes' if item.distressed_deal else 'No'}\n"
    #         )

    #         # Financial Details
    #         # summary += (
    #         #     f"\n💰 **Financial Details**\n"
    #         #     f"- **Original Price:** {item.property_currency_original_price} {item.property_currency_code}\n"
    #         #     f"- **Asking Price:** {item.property_currency_asking_price} {item.property_currency_code}\n"
    #         #     f"- **Rejection Price:** {item.rejection_price} {item.property_currency_code}\n"
    #         #     f"- **Original Price + Selling cost:** {item.original_price_plus_selling_cost} {item.property_currency_code}\n"
    #         #     f"- **Valuation:** {item.property_currency_valuation} {item.property_currency_code}\n"
    #         #     f"- **Annual Service Charges:** {item.property_currency_annual_service_charges}\n"
    #         #     f"- **Estimated Gains:** {item.gains} ({item.gains_in_percentage}%)\n"
    #         # )

    #         # Availability
    #         # summary += (
    #         #     f"\n📅 **Availability**\n"
    #         #     f"- **Status:** {item.status}\n"
    #         #     f"- **Occupancy:** {item.occupancy_status}\n"
    #         #     f"- **Owner Intent:** {item.owner_intent}\n"
    #         #     f"- **Enable Payment Plan:** {'Yes' if item.enable_payment_plan else 'No'}\n"
    #         # )

    #         # Parking Details
    #         summary += (
    #             f"\n📸 **Parking Details**\n"
    #             f"- **Parking Available:** {'Yes' if item.parking_available else 'No'}\n"
    #             f"- **Covered Parking Spaces:** {item.number_of_covered_parking}\n"
    #             f"- **Open Parking Spaces:** {item.number_of_open_parking}\n"
    #             f"- **Parking Numbers:** {', '.join(item.parking_number)}\n"
    #         )

    #         # Sales History
    #         # if item.sales_history:
    #         #     summary += "\n **Sales History**\n"
    #         #     for sale in item.sales_history:
    #         #         summary += f"  - Date: {sale['evidence_date']}, Price: {sale['total_sales_price']} AED, Recurrence: {sale['sale_recurrence']}, Evidence: {sale['evidence']}\n"

    #         # Rental History
    #         # if item.rental_history:
    #         #     summary += "\n **Rental History**\n"
    #         #     for rent in item.rental_history:
    #         #         summary += f"  - Start Date: {rent['start_date']}, End Date: {rent['end_date']}, Total Rent: {rent['total_rent']} AED, Recurrence: {rent['rent_recurrence']}\n"

    #         # Images and Sections
    #         # if item.unit_images:
    #         #     summary += "\n📸 **Images and Features**\n"
    #         #     for section in item.unit_images:
    #         #         section_name = section.get("other_section", section["section_type"])
    #         #         attached_balcony = "Yes" if section["attached_balcony"] else "No"
    #         #         images = [media["media_file"] for media in section["media"]]
    #         #         summary += (
    #         #             f"  - **Section:** {section_name}\n"
    #         #             f"    - Attached Balcony: {attached_balcony}\n"
    #         #             f"    - Images: {len(images)} available\n"
    #         #         )
    #         #         summary += "".join(
    #         #             f"      {idx}. {image}\n"
    #         #             for idx, image in enumerate(images, start=1)
    #         #         )

    #         # Owner and Additional Features
    #         # summary += (
    #         #     f"\n🧑‍💼 **Owner and Features**\n"
    #         #     f"- **Owner Verified:** {'Yes' if item.owner_verified else 'No'}\n"
    #         #     f"- **Furnished:** {'Yes' if item.furnished else 'No'}\n"
    #         #     f"- **Branded Building:** {'Yes' if item.branded_building else 'No'}\n"
    #         #     f"- **Premium View:** {'Yes' if item.premium_view else 'No'}\n"
    #         # )

    #         summaries.append(summary)

    #     return "\n\n".join(summaries)
    @log_input_output
    def get_agent_portfolio(self, agent_portfolio_id: str):
        agent_profile = get_agent_profile_object(agent_portfolio_id)

        agent_associated_properties = (
            AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=agent_profile,
                is_request_expired=False,
            )
            .order_by("-created_ts")
            .values_list("property_id", flat=True)
        )

        properties = (
            Property.objects.filter(
                id__in=agent_associated_properties,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .order_by("-created_ts")
        )

        properties = (
            properties.select_related("propertyfinancialdetails").annotate(
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )
        ).order_by("-created_ts")

        serializer = AIAgentPortfolioViewSerializer(
            properties, context={"agent_profile": agent_profile}, many=True
        )
        properties_under_agent_portfolio = serializer.data

        portfolio = str(properties_under_agent_portfolio)

        # print("##### PROPERTIES UNDER AGENT PORTFOLIO #####", portfolio)

        return portfolio

    def get_agent_portfolio_properties_for_given_user_query(
        self, agent_portfolio_id: str, relevent_context_for_query_on_user_query: str
    ):
        iris = self.iris

        if not iris:
            return "Iris is not initialized"

        agent_profile = get_agent_profile_object(agent_portfolio_id)

        agent_associated_properties = (
            AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=agent_profile,
                is_request_expired=False,
            )
            .order_by("-created_ts")
            .values_list("property_id", flat=True)
        )

        properties = (
            Property.objects.filter(
                id__in=agent_associated_properties,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .order_by("-created_ts")
        )

        short_query = properties.values("id")

        print("##### PROPERTIES #####", short_query.query)

        schema = """{"properties_property":{"id":{"type":"UUID","constraints":"Primary Key","description":"Unique identifier for the property."},"area_id":{"type":"ForeignKey","constraints":"References Area(id), Nullable","description":"Reference to the area where the property is located.","schema":{"id":"UUID","country_id":"ForeignKey References Country(id)","state_id":"ForeignKey References State(id), Nullable","city_id":"ForeignKey References City(id), Nullable","name":"CharField(64)","is_active":"BooleanField","is_deleted":"BooleanField"}},"community_id":{"type":"ForeignKey","constraints":"References Community(id), Nullable","description":"Reference to the community where the property is located.","schema":{"id":"UUID","state_id":"ForeignKey References State(id), Nullable","city_id":"ForeignKey References City(id), Nullable","area_id":"ForeignKey References Area(id), Nullable","property_monitor_location_id":"IntegerField, Nullable","name":"CharField(128)","sub_loc_1":"CharField(128), Nullable","sub_loc_2":"CharField(128), Nullable","sub_loc_3":"CharField(128), Nullable","sub_loc_4":"CharField(128), Nullable","sub_loc_5":"CharField(128), Nullable","is_active":"BooleanField","is_deleted":"BooleanField","added_by":"CharField(32)"}},"property_type":{"type":"CharField(32)","constraints":{"Apartment":"Apartment","Villa":"Villa","Townhouse":"Townhouse","Office Space":"Office Space","Co-working":"Co-working","Shop":"Shop","Showroom":"Showroom","Godown/Warehouse":"Godown/Warehouse","Industrial Shed":"Industrial Shed","Industrial Building":"Industrial Building","Hospital/Clinic":"Hospital/Clinic"},"description":"Type of property."},"property_unit_type":"CharField(32), Default: sqft","total_area":"FloatField, Nullable","carpet_area":"FloatField, Nullable","number_of_bedrooms":"IntegerField, Nullable","user_unit_preference":"CharField(8), Default: sqft","tenancy_start_date":"DateField, Nullable","tenancy_end_date":"DateField, Nullable","property_category":{"type":"IntegerField","constraints":{"0":"Residential","1":"Commercial"},"description":"Category of the property."},"total_floors":"IntegerField, Nullable","building_type":{"type":"IntegerField","constraints":{"0":"Independent House","1":"Business Park","2":"Mall","3":"Standalone Building","4":"Independent Shop"},"description":"Type of building structure."}}}"""

        #     6. You do not need to add escape characters like `\\"` keep the format exactly same as given main query and no standard improvements need to be done anywhere. I.e. `"properties_property"."id"` and NOT `\\"properties_property\\".\\"id\\"`
        # 7. You do not need to add inverted commas around the values for filtering, just use the values directly. I.e. `"properties_property"."property_type" = Villa` and NOT `"properties_property"."property_type" = \'Villa\'`

        for attempt in range(3):
            try:
                dict = iris.chat_completion(
                    model=Model.GPT_4o_mini,
                    messages=[
                        {
                            "role": "system",
                            "content": f"""You are a (dialect) expert.
Please help to generate a (dialect) query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions.
=== Property Table Schema
{schema}

=== Main Filter Query
This query filters the properties for the given user agent. Your job is to add additional WHERE filter if needed based on user query. You do not modify the original query just add the additional filter.
{short_query.query}

Response Guidelines
1. If the provided context is sufficient, please generate a valid query without any explanations for the question. The query should start with a comment containing the question being asked.
2. If the provided context is insufficient, please explain why it can't be generated.
3. Make sure the query is within the scope of provided table schema, if not then please explain why it can't be generated and return out_of_scope as true. For example, if the user asks about the price of a property, and if the table schema does not have a price column, then you should return out_of_scope as true. If user has provided some information about query that is in schema and some that isn't then find based on the information that is there in schema and generate the query.
4. Use the main query and then append the required filters to it. Format main query if required without changing its logic. Like adding inverted commas around the values say not for sale becomes 'not for sale'
5. Query should strictly return IDs of the properties only. Even if user asks for a count/sum etc, you fetch the property ids only and return them in the query.
6. Operator may not match the given name and argument types.
7. Explicitly cast every single value based on the schema provided above in the query to the correct type like but not limited to `CAST(U0."action_status" AS INTEGER) = 2`, `CAST("properties_property"."property_publish_status" AS INTEGER) = 1` or `CAST("properties_property"."property_type" AS VARCHAR) = 'Villa'` etc.
8. Please use the most relevant table(s).
9. Please format the query before responding.
10. Query should be parsable an should never include anything uncompatible with SQL like new line character, tabs or any other special characters
11. Please always respond with a valid well-formed JSON object with the following format
===Response Format
{{
"query": "A generated SQL query when context is sufficient.", "explanation": "An explanation of failing to generate the query.", "success_generating_query": "true or false", "out_of_scope": "true or false"
}}""",
                        },
                        {
                            "role": "user",
                            "content": f"Property Retrieval SQL Query For User Query: {relevent_context_for_query_on_user_query}",
                        },
                    ],
                    tools=[],
                    json_response=True,
                )

                print("##### DICT #####", dict)
                generated_query = dict["query"]

                if dict["out_of_scope"] == "true":
                    return "This query is out of scope for this tool, answer the question based on the context that you have as of now."

                if dict["success_generating_query"] == "true":
                    if generated_query:
                        print("##### GENERATED QUERY #####", generated_query)

                    break

            except Exception as e:
                print(
                    "##################################################### value of dict #####################################################\n\n\n\n\n",
                    dict,
                )
                print(f"Attempt {attempt + 1} failed: {e}")

        try:
            final_query = None
            if generated_query:
                final_query = generated_query
            else:
                final_query = short_query.query

            print(
                "##################################################### final_query #####################################################\n\n\n\n\n",
                final_query,
            )

            from django.db import connection

            cursor = connection.cursor()
            cursor.execute(final_query)
            filtered_properties_ids = [row[0] for row in cursor.fetchall()]

            result = [
                {"property_id": property_id, "meta_data": {}}
                for property_id in filtered_properties_ids
            ]
            print("FILTERED PROPERTY ID____s", result)
            return json.dumps(result)

            # filtered_properties_ids = cursor.fetchall()
            print(
                "##################################################### filtered_properties_ids #####################################################\n\n\n\n\n",
                filtered_properties_ids,
            )
            filtered_properties = Property.objects.filter(
                id__in=(filtered_properties_ids)
            )

            filtered_properties = filtered_properties.select_related(
                "propertyfinancialdetails"
            ).annotate(
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )

            print(
                "##################################################### filtered_properties #####################################################\n\n\n\n\n",
                filtered_properties,
            )
            if not generated_query:
                filtered_properties = filtered_properties[:10]

            serializer = AIAgentPortfolioViewSerializer(
                filtered_properties, context={"agent_profile": agent_profile}, many=True
            )
            properties_under_agent_portfolio = serializer.data

            print(
                "##################################################### properties_under_agent_portfolio #####################################################\n\n\n\n\n",
                properties_under_agent_portfolio,
            )

            portfolio = str(properties_under_agent_portfolio)

            return portfolio

        except Exception as e:
            print(
                "##################################################### value of dict #####################################################\n\n\n\n\n",
                dict,
            )
            print(
                f"There was an error while fetching the properties: {e} for query: {final_query}"
            )
            return "There was an error while fetching the properties"

    def get_properties_by_community_or_building(
        self, agent_portfolio_id: str, search_term: str
    ):
        iris = self.iris

        if not iris:
            return "Iris is not initialized"

        agent_profile = get_agent_profile_object(agent_portfolio_id)
        property_ids = []

        try:
            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_community(%s, %s)",
                    [agent_profile.id, search_term],
                )
                property_ids = [row[0] for row in cursor.fetchall()]

            result = [
                {"property_id": property_id, "meta_data": {}}
                for property_id in property_ids
            ]
            print("FILTERED PROPERTY ID____s", result)
            return json.dumps(result)

        except Exception as e:
            return "There was an error while fetching the properties"

    def format_property_details(self, property_id):
        try:
            item = Property.objects.get(id=property_id)
        except Property.DoesNotExist:
            return f"Property with ID {property_id} does not exist."

        def format_images(images):
            return "\n".join(
                [
                    f"- Section Type: {image['section_type']}, Balcony Attached: {image['attached_balcony']}, Section Name: {image.get('other_section', 'N/A')}\n  Images: "
                    + ", ".join([media["media_file"] for media in image["media"]])
                    for image in images
                ]
            )

        try:
            property_details = (
                f"Property ID: {item.id}\n"
                f"Community: {item.community}\n"
                f"Unit Number: {item.unit_number}\n"
                f"Building Number: {item.building_number}\n"
                f"Building Name: {item.building_name}\n"
                f"Property Type: {item.property_type}\n"
                f"Floor Number: {item.floor_number}\n"
                f"Owner Verified: {item.owner_verified}\n"
                f"Postal Code: {item.postal_code}\n"
                f"Unit Type: {item.property_unit_type}\n"
                f"Tenancy Type: {item.tenancy_type}\n\n"
                f"### Area Details ###\n"
                f"Total Area: {item.total_area} sq. ft.\n"
                f"Carpet Area: {item.carpet_area} sq. ft.\n"
                f"Balcony Area: {item.balcony_area} sq. ft.\n\n"
                f"### Room Details ###\n"
                f"Number of Bedrooms: {item.number_of_bedrooms}\n"
                f"Number of Bathrooms (Common): {item.number_of_common_bathrooms}\n"
                f"Number of Bathrooms (Attached): {item.number_of_attached_bathrooms}\n"
                f"Number of Powder Rooms: {item.number_of_powder_rooms}\n\n"
                f"### Parking Details ###\n"
                f"Parking Available: {item.parking_available}\n"
                f"Covered Parking Spaces: {item.number_of_covered_parking}\n"
                f"Open Parking Spaces: {item.number_of_open_parking}\n"
                f"Parking Numbers: {', '.join(item.parking_number)}\n"
                f"Address: {item.address}\n\n"
                f"### Property Features ###\n"
                f"Furnished: {item.furnished}\n"
                f"Premium View: {item.premium_view}\n"
                f"Distressed Deal: {item.distressed_deal}\n\n"
                f"### Availability ###\n"
                f"Status: {item.status}\n"
                f"Occupancy: {item.occupancy_status}\n"
                f"Enable Payment Plan: {item.enable_payment_plan}\n\n"
                f"### Financial Details ###\n"
                f"Original Price: {item.property_currency_original_price} {item.property_currency_code}\n"
                f"Asking Price: {item.property_currency_asking_price} {item.property_currency_code}\n"
                f"Valuation: {item.property_currency_valuation} {item.property_currency_code}\n"
                f"Annual Service Charges: {item.property_currency_annual_service_charges}\n"
                f"Gains: {item.gains} ({item.gains_in_percentage}%)\n\n"
                f"### Sales History ###\n"
                + "\n".join(
                    [
                        f"- Date: {sale['evidence_date']}, Price: {sale['total_sales_price']} AED, Recurrence: {sale['sale_recurrence']}, Evidence: {sale['evidence']}"
                        for sale in item.sales_history
                    ]
                )
                + "\n\n"
                f"### Rental History ###\n"
                + "\n".join(
                    [
                        f"- Start Date: {rent['start_date']}, End Date: {rent['end_date']}, Total Rent: {rent['total_rent']} AED, Recurrence: {rent['rent_recurrence']}"
                        for rent in item.rental_history
                    ]
                )
                + "\n\n"
                f"### Unit Images ###\n" + format_images(item.unit_images) + "\n"
            )

        except Exception as e:
            return f"Error in formatting property details: {e}"
        return property_details

    @log_input_output
    def get_property_details(self, properties):
        property_ids = list(map(lambda x: x["property_id"], properties))

        properties = Property.objects.filter(id__in=property_ids).order_by(
            "-created_ts"
        )

        properties = properties.select_related("propertyfinancialdetails").annotate(
            property_currency_code=F(
                "propertyfinancialdetails__property_currency_code"
            ),
            asking_price=F("propertyfinancialdetails__asking_price"),
            original_price=F("propertyfinancialdetails__original_price"),
            valuation=F("propertyfinancialdetails__valuation"),
            annual_rent=F("propertyfinancialdetails__annual_rent"),
        )

        serializer = AIPropertyDetailSerializer(properties, many=True)

        print("##### FETCHED PROPERTIES IDS #####", property_ids)

        return str(serializer.data)

    def get_preferences(self, conversation_id: int) -> str:
        with connection.cursor() as cursor:
            cursor.execute(
                """
                SELECT id as preference_id, preferences
                FROM assistant_preferences  
                WHERE messages_id = %s
                AND deleted_at IS NULL
                """,
                [conversation_id],
            )
            preferences_raw = cursor.fetchall()

        if preferences_raw:
            print("Raw preferences 1: ", preferences_raw)
            return f"User preferences are {preferences_raw}"

        print("No preferences found for this conversation_id")
        return "This user's preferences are not set yet"

    def create_preferences(self, conversation_id: str, preference_details: object):
        preference_object = Preferences.objects.filter(messages=conversation_id).first()
        message_object = Messages.objects.get(buyer=self.user)

        Preferences.objects.create(
            buyer=self.user,
            messages=message_object,
            preferences=preference_details,
        )
        return "Preferences updated successfully, act upon the new preferences."

    @log_input_output
    def update_preferences(self, preference_id: str, preference_details: dict):
        try:
            # Fetch the existing preference object
            preference_object = Preferences.objects.filter(id=preference_id).first()

            if preference_object:
                print("Existing preferences: ", preference_object.preferences)

                # Update the preferences field with the new preference_details
                preference_object.preferences = preference_details
                preference_object.save()  # Save the changes to the database

                return f"User preferences with id {preference_id} have been updated."
            else:
                print("No preferences found for this preference_id")
                return "This user's preferences are not set yet."

        except Exception as e:
            # Handle any exceptions that may occur
            print(f"An error occurred: {str(e)}")
            return f"An error occurred while updating preferences: {str(e)}"

    def delete_preferences(self, preference_id: int) -> str:
        with connection.cursor() as cursor:
            # First, fetch the preferences to check if they exist
            cursor.execute(
                """
                SELECT preferences
                FROM assistant_preferences  
                WHERE id = %s::bigint
                """,
                [preference_id],
            )

            preferences_raw = cursor.fetchone()

            if preferences_raw:
                print("Raw preferences 1: ", preferences_raw)

                # If preferences exist, update the deleted_at field
                cursor.execute(
                    """
                    UPDATE assistant_preferences
                    SET deleted_at = (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)::bigint
                    WHERE id = %s::bigint
                    """,
                    [preference_id],
                )

                connection.commit()  # Commit the transaction

                return f"User preferences with id {preference_id} have been marked as deleted."

            print("No preferences found for this preference_id")
            return "This user's preferences are not set yet"

    def get_lat_long_coordinates(self, address: str):
        """Get coordinates using Google Geocoding API"""

        error = "Couldn't be able to fetch the lat long for given address"
        try:
            geocode_result = self.gmap_client.geocode(address)
            if geocode_result:
                location = geocode_result[0]["geometry"]["location"]
                return f" lat: {location['lat']}, long: {location['lng']}"
            return error
        except Exception as e:
            print(f"Geocoding error for {address}: {str(e)}")
            return error

    def get_nearby_properties(
        self, agent_portfolio_id: str, lat: float, long: float, range: int = 5
    ) -> str:
        try:
            agent_profile = get_agent_profile_object(agent_portfolio_id)

            with connection.cursor() as cursor:
                cursor.execute(
                    "SELECT * FROM get_properties_by_agent_and_location(%s, %s, %s, %s)",
                    [agent_profile.id, long, lat, range],
                )
                columns = [col[0] for col in cursor.description]
                properties = [dict(zip(columns, row)) for row in cursor.fetchall()]

            result = [
                {
                    "property_id": prop["property_id"],
                    "meta_data": {"distance": round(float(prop["distance_km"]), 2)},
                }
                for prop in properties
            ]

            return json.dumps(result)

        except Exception:
            return json.dumps([])  # Return empty array on failure


def filter_conversation(data: List[Dict], actor: Actor) -> List[Dict]:
    filtered_data = []
    visible_to = [Actor.Buyer] if actor == Actor.Buyer else [Actor.Buyer, Actor.Agent]

    for item in data:
        if item["role"] == "user" and item["name"] in visible_to:
            filtered_item = {
                "role": item["role"],
                "name": item["name"],
                "content": item["content"],
            }
            filtered_data.append(filtered_item)
        elif (
            item["role"] == "assistant"
            and item.get("content")
            and "tool_calls" not in item
        ):
            try:
                assistant_content = json.loads(item.get("content"))
                if assistant_content["response_to"] in visible_to:
                    filtered_item = {
                        "role": item["role"],
                        "content": assistant_content["response"],
                    }
                    filtered_data.append(filtered_item)

            except json.decoder.JSONDecodeError:
                assistant_content = item.get("content")
                filtered_item = {"role": item["role"], "content": assistant_content}

                filtered_data.append(filtered_item)

    return filtered_data


def get_conversation_tone(engagement_level) -> dict:
    """
    Determine the appropriate conversation tone based on engagement level.

    :param engagement_level: An integer representing the engagement level (1-10)
    :return: A dictionary with tone instructions
    """
    if engagement_level < 3:
        return {
            "buyer_tone": BuyerTone.FRIENDLY_AND_PROFESSIONAL,
            "agent_tone": AgentTone.FORMAL_AND_DETAILED,
            "negotiation_style": NegotiationStyle.CAUTIOUS_AND_INFORMATIVE,
        }
    elif 3 <= engagement_level < 6:
        return {
            "buyer_tone": BuyerTone.MORE_DIRECT_AND_CASUAL,
            "agent_tone": AgentTone.STRAIGHTFORWARD,
            "negotiation_style": NegotiationStyle.BALANCED_AND_ASSERTIVE,
        }
    elif 6 <= engagement_level < 8:
        return {
            "buyer_tone": BuyerTone.FRANK_AND_TO_THE_POINT,
            "agent_tone": AgentTone.BLUNT_AND_QUESTIONING,
            "negotiation_style": NegotiationStyle.AGGRESSIVE_TOWARDS_BUYER,
        }
    else:
        return {
            "buyer_tone": BuyerTone.VERY_BLUNT_AND_FAMILIAR,
            "agent_tone": AgentTone.DEMANDING_AND_CRITICAL,
            "negotiation_style": NegotiationStyle.AGGRESSIVE_TOWARDS_BUYER,
        }
