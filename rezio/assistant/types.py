from enum import Enum


class ModelProvider:
    OPENAI = "open_ai"
    TOGETHERAI = "together_ai"
    LOCAL = "local"
    GROQ = "Groq"


class Model:
    GPT_35_TURBO = "gpt-3.5-turbo"
    GPT_4_Turbo = "gpt-4-turbo"
    GPT_4o = "gpt-4o"
    GPT_4o_mini = "gpt-4o-mini"
    GPT_4o_2024_05_13 = "gpt-4o-2024-05-13"
    GPT_4o_2024_08_06 = "gpt-4o-2024-08-06"


class Actor:
    Buyer = "Buyer"
    Agent = "Agent"
    Seller = "Seller"


class Situation:
    BENCHMARK = "benchmark"
    LOW_BALL_OFFER = "low_ball_offer"
    OFFER_FLOOR = "offer_floor"
    ABOVE_OR_SAME_ASKING_PRICE = "above_or_same_asking_price"
    BETWEEN_BASELINE_AND_ASKING_PRICE = "between_baseline_and_asking_price"
    REFUSED_OFFER = "refused_offer"
    SAME_OFFER = "same_offer"
    SAME_AS_BENCHMARK = "same_as_benchmark"
    CLOSE_TO_BENCHMARK = "close_to_benchmark"
    IN_CONSIDERATION = "in_consideration"


class BuyerTone:
    FRIENDLY_AND_PROFESSIONAL = "friendly and professional"
    MORE_DIRECT_AND_CASUAL = "more direct and casual"
    FRANK_AND_TO_THE_POINT = "frank and to-the-point"
    VERY_BLUNT_AND_FAMILIAR = "very blunt and familiar"


class AgentTone:
    FORMAL_AND_DETAILED = "formal and detailed"
    STRAIGHTFORWARD = "straightforward"
    BLUNT_AND_QUESTIONING = "blunt and questioning"
    DEMANDING_AND_CRITICAL = "demanding and critical"


class NegotiationStyle:
    CAUTIOUS_AND_INFORMATIVE = "cautious and informative"
    BALANCED_AND_ASSERTIVE = "balanced and assertive"
    AGGRESSIVE_TOWARDS_BUYER = "aggressive towards buyer"


class VisitEnquiryStage:
    VISIT_ENQUIRY = "Visit Enquiry"
    TIMESLOT_PROVIDED_BY_BUYER = "Timeslot Provided by Buyer"
    TIMESLOT_PROVIDED_BY_AGENT = "Timeslot Provided by Agent"
    TIMESLOT_REJECTED_BY_BUYER = "Timeslot Rejected by Buyer"
    TIMESLOT_REJECTED_BY_AGENT = "Timeslot Rejected by Agent"
    TIMESLOT_ACCEPTED_BY_BUYER = "Timeslot Accepted by Buyer"
    TIMESLOT_ACCEPTED_BY_AGENT = "Timeslot Accepted by Agent"


class ThoughtType:
    def __init__(self, thought_for: Actor, input: str, output: str, thought: str):
        self.thought_for = thought_for
        self.input = input
        self.output = output
        self.thought = thought

    def to_dict(self):
        return {
            "thought_for": self.thought_for,
            "input": self.input,
            "output": self.output,
            "thought": self.thought,
        }


class BackendActions:
    LOGIN = "login"
    DISPLAY_MESSAGES = "display_messages"
    HANDLE_CHAT_MESSAGE = "handle_chat_message"
    TOOL_USE = "tool_use"
    CAPTURE_CHEQUE_IMAGE = "capture_cheque_image"
    WHAT_IS_IN_THE_DOCUMENT = "what_is_in_the_document"
    DELETE_EXISTING_MESSAGES = "delete_existing_messages"
    OPEN_TIMESLOT_MODAL = "open_timeslot_modal"


class OfferGoal:
    MAXIMIZE_PROFIT = "Maximize Profit"
    MINIMISE_LOSS = "Minimise Loss"
    NEGOTIATE_TO_RECOVER_BASELINE = "Negotiate To Recover Baseline"


class OfferStage:
    IN_CONSIDERATION = "In Consideration"
    BUYER_WITHDRAWN = "Buyer Withdrawn"
    SELLER_ACCEPTED = "Seller Accepted"
    SELLER_REJECTED = "Seller Rejected"
    CHEQUE_GIVEN_BY_BUYER = "Cheque Given by Buyer"
    CHEQUE_ACCEPTED_BY_SELLER = "Cheque Accepted by Seller"
    CHEQUE_REJECTED_BY_SELLER = "Cheque Rejected by Seller"


class BuyerSeriousness:
    NOT_SERIOUS = "Not Serious"
    SERIOUS = "Serious"
    VERY_SERIOUS = "Very Serious"


class Positions:
    BELOW_ALL_THRESHOLDS = "Below All Thresholds"
    BETWEEN_LOW_BALL_OFFER_AND_ORIGINAL_PRICE = (
        "Between Low Ball Offer and Original Price"
    )
    BETWEEN_ORIGINAL_PRICE_AND_ORIGINAL_PRICE_PLUS_SELLING_COST = (
        "Between Original Price and Original Price + Selling Cost"
    )
    BETWEEN_ORIGINAL_PRICE_PLUS_SELLING_COST_AND_ASKING_PRICE = (
        "Between Original Price + Selling Cost and Asking Price"
    )
    ABOVE_ALL_THRESHOLDS = "Above All Thresholds"


class OfferVariables:
    REJECTION_PRICE = "Rejection Price"
    ORIGINAL_PRICE = "Original Price"
    ORIGINAL_PRICE_PLUS_SELLING_COST = "Original Price + Selling Cost"
    ASKING_PRICE = "Asking Price"
    BENCHMARK = "Benchmark"
    OFFER_FLOOR = "Offer Floor"
    BENCHMARK_CLOSENESS_THRESHOLD_PRICE = "Benchmark Closeness Threshold Price"


class Role:
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class WhatsAppMessageTemplate:
    REZIO_WELCOME_MESSAGE = "rezio_welcome_message"
    HELLO_WORLD = "hello_world"
    CLEAR_CONVERSATION = "I've reset everything. To clear the chat, tap the three dots in the top-right corner, go to *More*, and select *Clear Chat*."


class WhatsAppPayLoadType:
    TEXT = "text"
    IMAGE = "image"
    DOCUMENT = "document"


class AgentType:
    INPUT_GUARD_RAIL = "input_guard_rail"
    CONTEXT_BUILDER = "context_builder"
    ORCHESTRATOR = "orchestrator"
    PROPERTY_DETAILS_AGENT = "property_details_agent"
    SCHEDULE_VISIT_AGENT = "schedule_visit_agent"
    LAW_ASSISTANCE_AGENT = "law_assistance_agent"
    AGGREGATOR = "aggregator"
    REAL_ESTATE_AGENT = "real_estate_agent"
    OUTPUT_GUARD_RAIL = "output_guard_rail"
    REACT_AGENT = "react_agent"
    PREFERENCE_AGENT = "preference_agent"


class CometChatGroup:
    Public = "public"
    Private = "private"
    Password = "password"


class CometChatRole:
    Buyer = "buyer"
    Agent = "agent"
    Seller = "seller"
    Assistant = "assistant"


class CometChatReceiverType:
    User = "user"
    Group = "group"


class CometChatWebhookEvent:
    SOMEONE_SENDS_A_MESSAGE = "message_sent"
    USER_GOT_CONNECTED_OR_DISCONNECTED = "user_connection_status_changed"


class CometChatWebhookConnectionStatus:
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"


class CometChatMessageType:
    GROUP_MEMBER = "groupMember"
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    FILE = "file"
    CUSTOM = "custom"
