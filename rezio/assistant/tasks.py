import logging
import random
import traceback
from typing import Dict, Optional
from django.db import transaction
from celery import shared_task
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from django.conf import settings
from langchain_core.messages import ToolMessage, HumanMessage, AIMessage

from rezio.ai.single_agent_langgraph import Workflow
from rezio.assistant.error_messages import custom_error_message
from rezio.ai.constants import (
    Actor,
    BackendActions,
    Role,
    AgentType,
)
from rezio.assistant.models import (
    ChatHistory,
    Messages,
    Memory,
    PropertyInquiries,
    PropertyInquiriesMessages,
)
from rezio.assistant.services.iris_service import Iris
from rezio.assistant.services.system_prompts.reasoning_prompts import (
    IrisReasoningAgentPrompts,
)
from rezio.assistant.services.system_prompts.multi_agent_prompts import (
    MultiAgentPrompts,
)
import json
from rezio.properties.text_choices import (
    PropertyAgentType,
    PropertyAreaUnit
)
from rezio.properties.utils import convert_area_unit
from rezio.user.utils import create_db_object, get_or_create_user_with_role
from rezio.properties.models import (
    AgentAssociatedProperty,
    Area,
    City,
    Requirements,
    Community,
    Country,
    Property,
    PropertyAvailabilityAndStatus,
    PropertyCompletionState,
    PropertyFeatures,
    PropertyFinancialDetails,
    State,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyData,
    UserLevelPropertyFeatures,
    UserLevelPropertyFinancialDetails,
)
from rezio.user.models import AgentProfile
from rezio.assistant.models import PubNubChannel
from rezio.ai.utils import generate_slug, get_tool_state
from rezio.ai.agents.property_monitor.agent import property_inquiries_agent

# from rezio.assistant.services.reasoning_agent_service import IrisReasoningAgent
from rezio.assistant.services.multi_agent_service import MultiAgent
from rezio.pubnub.handler import PubNubHandler
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output, close_db_connection
from rezio.utils.text_choices import DataSource

logger = logging.getLogger(DJANGO_LOGGER_NAME)


@shared_task
def set_reminder_for_message(conversation_id: int, response_to: Actor, response: str):
    channel_layer = get_channel_layer()
    messages = Messages.objects.get(id=conversation_id)

    # Prepare the message
    message = {
        "role": "assistant",
        "content": response,
    }

    # Update messages with proper JSON formatting
    messages.messages.append(
        {
            "role": "assistant",
            "content": json.dumps({"response_to": response_to, "response": response}),
        }
    )
    messages.save()

    # Determine the group based on response_to
    if response_to == Actor.Buyer:
        group = f"{messages.property_agent.property.id}_{messages.buyer.id}_{messages.property_agent.agent.id}"
    elif response_to == Actor.Agent:
        group = f"{messages.id}_{messages.property_agent.agent.id}"

    # Send the message
    async_to_sync(channel_layer.group_send)(
        group,
        {
            "type": "send.message",
            "action": BackendActions.DISPLAY_MESSAGES,
            "message": [message],
        },
    )


@shared_task
@log_input_output
def create_multi_agent_memory(pubnub_channel_id: int):
    """
    This task creates a memory objects for each sub AI agents for the multi-agent AI system.
    Each memory object contains the system prompt for the agent, the memory, and the agent type.
    parameters:
        pubnub_channel_id: The id of the pubnub channel object
    """
    pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
    if not Messages.objects.filter(buyer=pubnub_channel).exists():
        messages = Messages.objects.create(buyer=pubnub_channel)
    else:
        messages = Messages.objects.get(buyer=pubnub_channel)
    main_message_object = messages
    agent_profile = AgentProfile.objects.get(user=pubnub_channel.receiver_user)
    agent_portfolio_id = agent_profile.user_id
    # multi_agent_prompts = IrisReasoningAgentPrompts()
    multi_agent_prompts = MultiAgentPrompts()

    # ------- Input Guard Rail -------
    Memory.objects.create(
        agent_type=AgentType.INPUT_GUARD_RAIL,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.input_guard_rail(
                    conversation_id=main_message_object.id,
                ),
            )
        ],
    )

    # ------- Context Builder -------
    Memory.objects.create(
        agent_type=AgentType.CONTEXT_BUILDER,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.context_builder(
                    conversation_id=main_message_object.id,
                    agent_portfolio_id=agent_portfolio_id,
                ),
            )
        ],
    )

    # ------- Orchestrator -------
    Memory.objects.create(
        agent_type=AgentType.ORCHESTRATOR,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.orchestrator(
                    conversation_id=main_message_object.id,
                    agents=[
                        AgentType.PROPERTY_DETAILS_AGENT,
                        AgentType.SCHEDULE_VISIT_AGENT,
                        AgentType.LAW_ASSISTANCE_AGENT,
                    ],
                ),
            )
        ],
    )

    # ------- Property Details Agent -------
    Memory.objects.create(
        agent_type=AgentType.PROPERTY_DETAILS_AGENT,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.property_details_agent(
                    conversation_id=main_message_object.id,
                    agent_portfolio_id=agent_portfolio_id,
                ),
            )
        ],
    )

    # ------- Schedule Visit Agent -------
    Memory.objects.create(
        agent_type=AgentType.SCHEDULE_VISIT_AGENT,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.schedule_visit_agent(
                    conversation_id=main_message_object.id
                ),
            )
        ],
    )

    # ------- Law Assistance Agent -------
    Memory.objects.create(
        agent_type=AgentType.LAW_ASSISTANCE_AGENT,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.law_assistance_agent(
                    conversation_id=main_message_object.id
                ),
            )
        ],
    )

    # ------- Preference Agent -------
    Memory.objects.create(
        agent_type=AgentType.PREFERENCE_AGENT,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.preference_agent(
                    conversation_id=main_message_object.id
                ),
            )
        ],
    )

    # ------ Aggregator -------
    Memory.objects.create(
        agent_type=AgentType.AGGREGATOR,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.aggregator(
                    conversation_id=main_message_object.id,
                ),
            )
        ],
    )

    # ------- Real Estate Agent -------
    Memory.objects.create(
        agent_type=AgentType.REAL_ESTATE_AGENT,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.real_estate_agent(
                    conversation_id=main_message_object.id
                ),
            )
        ],
    )

    # ------- Output Guard Rail -------
    Memory.objects.create(
        agent_type=AgentType.OUTPUT_GUARD_RAIL,
        messages_id=main_message_object.id,
        memory=[
            Iris.create_message(
                role=Role.SYSTEM,
                content=multi_agent_prompts.output_guard_rail(
                    conversation_id=main_message_object.id
                ),
            )
        ],
    )


@shared_task
def run_rezio_ai_agent(pubnub_channel_id, message):
    try:
        pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
        logger.info(f"Fetched PubNubChannel: {pubnub_channel}")
        multi_agent = MultiAgent(
            pubnub_channel=pubnub_channel,
            agent_pubnub_uuid=pubnub_channel.receiver_user.id,
        )
        logger.info("MultiAgent instance created successfully")
        ai_response = multi_agent.generate_response(query=message)
        logger.info(f"AI Response generated: {ai_response}")
        # reasoning_agent = IrisReasoningAgent(pubnub_channel=pubnub_channel)
        # ai_response = reasoning_agent.reason(message)
        # Send the message to pubnub channel
        logger.info(f"AI response: {ai_response}")
        PubNubHandler(
            agent_pubnub_uuid=pubnub_channel.receiver_user.id
        ).publish_message(
            target=pubnub_channel.group_channel_id,
            message=json.dumps(
                {
                    "role": "assistant",
                    "content": ai_response,
                    "channel_id": pubnub_channel.pubnub_channel_id,
                    "group_channel_id": pubnub_channel.group_channel_id,
                }
            ),
            is_channel_group=True,
        )

    except Exception as error:
        traceback.print_exc()
        logger.error(f"Error running Rezio AI agent: %s {error}")
        return custom_error_message


@shared_task(max_retry=0)
def run_rezio_langgraph_ai_agent(
    uuid, agent_portfolio_id, pubnub_channel_id, message, message_id
):
    try:
        pubnub_channel = PubNubChannel.objects.get(id=pubnub_channel_id)
        logger.info(f"Fetched PubNubChannel: {pubnub_channel}")

        agent_portfolio_id = pubnub_channel.group_channel_id.split("_")[1]

        def publishMessage(ai_response, message_type="text"):
            PubNubHandler(
                agent_pubnub_uuid=pubnub_channel.receiver_user.id
            ).publish_message(
                target=pubnub_channel.group_channel_id,
                message_type=message_type,
                message=json.dumps(
                    {
                        "role": "assistant",
                        "content": ai_response,
                        "message_type": message_type,
                        "channel_id": pubnub_channel.pubnub_channel_id,
                        "group_channel_id": pubnub_channel.group_channel_id,
                    }
                ),
                is_channel_group=True,
            )

        graph = Workflow().graph

        logger.info(
            "Graph workflow instance created",
            uuid,
            agent_portfolio_id,
            pubnub_channel_id,
            message,
        )

        payload = {
            "conversation_id": uuid,
            "agent_portfolio_id": agent_portfolio_id,
            "message_id": message_id,
        }

        checkpoint_config = {
            "recursion_limit": 15,
            "configurable": {
                "thread_id": str(uuid),
                "checkpoint_ns": "rezio",
                "checkpoint_id": str(uuid),
            },
        }

        state_output = {"messages": [], "agent_responses": {}}

        print(f"Payload: {payload}, Message: {message}")

        initial_state = {
            "user_query": message,
            "payload": payload,
            "messages": [],
            "property_agent_messages": [],
            "preference_agent_messages": [],
            "law_agent_messages": [],
            "real_estate_agent_messages": [],
            "wait": False,
            "schedule_agent_messages": [],
            "agent_responses": {},
        }

        show_steps = True

        # [IMPORTANT] Step messages must be in more human like messages
        step_messages = [
            "Working on it...",
            "Hold tight, I'm on it...",
            "Please wait, I'm working on it...",
            "Just a moment, I'm gathering information...",
        ]

        # Stream workflow state updates
        for event in graph.stream(initial_state, config=checkpoint_config):
            # Extract state updates
            if isinstance(event, dict):
                for node, state in event.items():
                    if isinstance(state, dict):
                        if "messages" in state:
                            state_output["messages"] = state["messages"]

                        if "property_agent_messages" in state:
                            state_output["messages"] = state["property_agent_messages"]

                        if "preference_agent_messages" in state:
                            state_output["messages"] = state[
                                "preference_agent_messages"
                            ]

                        if "law_agent_messages" in state:
                            state_output["messages"] = state["law_agent_messages"]

                        if "schedule_agent_messages" in state:
                            state_output["messages"] = state["schedule_agent_messages"]

                        if "real_estate_agent_messages" in state:
                            state_output["messages"] = state[
                                "real_estate_agent_messages"
                            ]

            # publishMessage("Processing...", "thinking")

            # Format the output for display
            display_text = ""

            if state_output["messages"]:
                display_text += "Messages:\n"
                for msg in state_output["messages"]:
                    if isinstance(msg, HumanMessage):
                        print(msg.content)
                        # publishMessage(msg.content, "thinking")
                        # display_text += f"- User: {msg.content}\n"

                    elif isinstance(msg, AIMessage):
                        tools = msg.tool_calls

                        if len(tools) > 0:
                            tool = tools[0]
                            tool_name = tool.get("name", "")
                            tool_args = " ".join(
                                str(value) for value in tool.get("args", {}).values()
                            )

                            print("TOOL CALL >>>>>")
                            print(msg.name, msg.content, tool.get("args", {}))

                            placeholder = get_tool_state(
                                tool_name, "pending", tool_args
                            )
                            # if show_steps is true, then publish the placeholder with a random step message
                            if show_steps:
                                publishMessage(random.choice(step_messages), "thinking")
                                show_steps = False

                        else:
                            skip_llm_logs = msg.additional_kwargs.get(
                                "skip_llm_logs", None
                            )

                            if skip_llm_logs:
                                print("")
                                # publishMessage("Processing...", "thinking")
                            else:
                                result = PubNubHandler(
                                    agent_pubnub_uuid=pubnub_channel.receiver_user.id
                                ).publish_message(
                                    target=pubnub_channel.group_channel_id,
                                    message=json.dumps(
                                        {
                                            "role": "assistant",
                                            "content": msg.content,
                                            "channel_id": pubnub_channel.pubnub_channel_id,
                                            "group_channel_id": pubnub_channel.group_channel_id,
                                        }
                                    ),
                                    is_channel_group=True,
                                )

                                if result:
                                    ChatHistory.objects.create(
                                        pubnub_channel=pubnub_channel,
                                        author=Actor.Assistant,
                                        message=msg.content,
                                        message_id=result[0],
                                    )
                                # display_text += f"- {msg.content}\n"

                    elif isinstance(msg, ToolMessage):
                        print("TOOL RESPONSE <<<<")
                        print(msg.name, msg.content)

                        # try:
                        #     data = json.loads(msg.content)
                        #     show_step = data.get("show_step", True)

                        #     if "message" in data and show_step:
                        #         publishMessage(data["message"], "thinking")
                        # except (json.JSONDecodeError, TypeError):
                        #     pass

                    else:
                        publishMessage(msg.content, "thinking")

                        # display_text += f"- {msg.content}\n"

            # publishMessage(display_text, "thinking")
            print("DISPLAY TEXT", display_text)
            if state_output["messages"] and isinstance(
                state_output["messages"][-1], HumanMessage
            ):
                full_response = state_output["messages"][-1].content
                # publishMessage(full_response)

    except Exception as error:
        traceback.print_exc()
        logger.error(f"Error running Rezio AI agent: %s {error}")
        return custom_error_message

def create_property_from_response(response, user, message_created=None):
    """
    Creates property objects from a response object.
    
    Args:
        response (dict): The response object containing property details
        user (User): The user creating the property
        message_created (PropertyInquiriesMessages, optional): Reference to the message that created this property
    
    Returns:
        tuple: (property_obj, user_level_property) if successful, None if failed
    """
    try:

        is_requirement = response.get("is_requirement", False)
        property_data = response.get("property", {})
        address_details = response.get("address", {})
        financial_data = response.get("financial_details", {})

        print("ADDRESS DETAILS", address_details)

        # if address has missing short country code lets add IN as default
        if 'country_short_name' not in address_details or not address_details['country_short_name']:
            address_details['country_short_name'] = 'IN'
            address_details['country'] = 'India'
        
        # Create location hierarchy 
        location_hierarchy = create_location_hierarchy(address_details)

        # set default null if not found
        country = location_hierarchy.get("country", None)
        state = location_hierarchy.get("state", None)
        city = location_hierarchy.get("city", None)
        area = location_hierarchy.get("area", None)
        community = location_hierarchy.get("community", None)

        if is_requirement:
            print("CREATING REQUIREMENT")
            requirement = Requirements.objects.create(
                country=country,
                state=state,
                city=city,
                area=area,
                community=community,
                property_type=property_data.get("property_type"),
                property_category=property_data.get("property_category"),
                building_type=property_data.get("building_type"),
                floor_number=property_data.get("floor_number"),
                total_floors=property_data.get("total_floors") or None,
                total_area=property_data.get("total_area") or None,
                property_unit_type=property_data.get("property_unit_type") or None,
                carpet_area=property_data.get("carpet_area") or None,
                number_of_bedrooms=property_data.get("number_of_bedrooms") or None,
                number_of_bathrooms=property_data.get("number_of_bathrooms") or None,
                property_currency_code=financial_data.get("property_currency_code"),
                annual_rent=financial_data.get("annual_rent") or None,
                price_negotiable=financial_data.get("price_negotiable", False),
                user=user,
                is_archived=property_data.get("is_archived", False),
                intent=response.get("intent"),
                source_ref=message_created,
                meta=response.get("meta"),
            )
            print("REQUIREMENT CREATED")
            return f"requirement created {requirement.id}"

        # Create main property object
        property_obj = Property.objects.create(
            **response["property"],
            country=country,
            property_publish_status="1",
            community=community,
            state=state,
            city=city,
            area=area,
            created_by_id=user.id,
            updated_by_id=user.id,
        )

        # Create agent association
        try:
            agent_profile = AgentProfile.objects.get(user=user)
            AgentAssociatedProperty.objects.create(
                agent_profile=agent_profile,
                property=property_obj,
                created_by=user,
                request_agent_type=PropertyAgentType.OPEN_TO_ALL,
            )
        except Exception as e:
            logger.error(f"Error creating agent association: {e}")

        # Create user level property data
        new_obj = {**response["property"]}
        keys_to_remove = [
            "building_number",
            "unit_number",
            "postal_code",
            "owner_intent",
            "default_image",
            "property_category",
            "country",
        ]
        for key in keys_to_remove:
            new_obj.pop(key, None)

        user_level_property = UserLevelPropertyData.objects.create(
            **new_obj,
            property=property_obj,
            property_publish_status="1",
            source="WHATS_APP",
            open_for_collaboration=True,
            created_by_id=user.id,
            updated_by_id=user.id,
            source_ref=message_created,
            meta={**response["meta"]},
        )

        print("USER LEVEL PROPERTY CREATED")
        create_db_object(
            PropertyFinancialDetails,
            **response["financial_details"],
            property=property_obj,
        )
        
        print("PROPERTY FINANCIAL DETAILS CREATED")
        create_db_object(
            UserLevelPropertyFinancialDetails,
            **response["financial_details"],
            property_level_data=user_level_property,
        )
        print("USER LEVEL PROPERTY FINANCIAL DETAILS CREATED")
        # Create property features
        create_db_object(
            PropertyFeatures,
            **response["property_features"],
            property=property_obj,
        )
        print("PROPERTY FEATURES CREATED")

        create_db_object(
            UserLevelPropertyFeatures,
            **response["property_features"],
            property_level_data=user_level_property,
        )
        print("USER LEVEL PROPERTY FEATURES CREATED")

        # Create availability and status
        availability_and_status, _ = PropertyAvailabilityAndStatus.objects.get_or_create(
            property=property_obj,
            defaults={
                "rent_available_start_date": response.get("rent_available_start_date", None),
            }
        )
        availability_and_status.created_by = user
        availability_and_status.save()
        print("AVAILABILITY AND STATUS CREATED")
        
        
        user_level_availability_status, _ = UserLevelPropertyAvailabilityAndStatus.objects.get_or_create(
            property_level_data=user_level_property,
            defaults={
                "rent_available_start_date": response.get("rent_available_start_date", None),
            }
        )
        user_level_availability_status.created_by = user
        user_level_availability_status.save()
        print("USER LEVEL AVAILABILITY STATUS CREATED")

        # Create completion states
        completion_states = [
            "property_specifications",
            "availability_and_status",
            "location_details",
            "community_and_building_info",
        ]
        
        for state in completion_states:
            PropertyCompletionState.objects.get_or_create(
                property=property_obj,
                created_by_id=user.id,
                updated_by_id=user.id,
                data_source="USER_ADDED",
                state=state,
            )
        print("COMPLETION STATES CREATED")
        return f"competed property creation {property_obj.id}"

    except Exception as error:
        logger.error(f"Error creating property: {error}")
        traceback.print_exc()
        return "error while creating property from message reader"


@shared_task
@close_db_connection
def message_reader(message, body, event, channel_id, retry=False):
    try:

        from_number = message.get("from", "")
        from_name = message.get("from_name", "")

        text_for_slug = body[:200]
        slug = generate_slug(text_for_slug)

        message_created, created = PropertyInquiriesMessages.objects.get_or_create(
            slug=slug,
            defaults={
                "raw_message": body,
                "from_name": from_name,
                "from_number": from_number,
                "channel_id": channel_id,
                "meta": {"message": message, "event": event, "channel_id": channel_id},
            }
        )
        
        print("PROPERTY INQUIRIES MESSAGE:", message_created, created)

        if created == False and retry == False:
            print("RETURNING: Message already exists:", message_created)
            return

        # Handle name splitting more robustly
        name_parts = from_name.strip().split() if from_name else []
        if len(name_parts) >= 2:
            fname = name_parts[0]
            lname = " ".join(name_parts[1:])  # Join remaining parts as last name
        else:
            fname = from_name.strip() if from_name else "Unknown"
            lname = ""

        [user, created, new_user_with_role_created] = get_or_create_user_with_role(
            phone_number=f"+{from_number}", 
            role_name="Agent",
            first_name=fname,
            last_name=lname,
        )

        print("USER CREATED:", user, created)

        # Ensure we have a valid name for the agent profile
        agent_name = from_name.strip() if from_name else f"{fname} {lname}".strip() or " "
        
        try:
            [agent_profile, agent_profile_created] = AgentProfile.objects.get_or_create(
                user=user,
                defaults={"name": agent_name},
            )
            print("AGENT PROFILE CREATED:", agent_profile, agent_profile_created)
        except Exception as e:
            logger.error(f"Error creating agent profile: {e}")
           
        chat_id = message.get("chat_id")
        response = property_inquiries_agent(body, chat_id)

        print("RESPONSE FROM LLM:", response)

        for property_response in response["properties"]:
            try:
                property_unit_type = property_response["property"].get("property_unit_type", None)
                total_area = property_response["property"].get("total_area", None)

                if total_area and property_unit_type:
                    converted_area = convert_area_unit(total_area, property_unit_type, PropertyAreaUnit.SQFT.value)
                    unit_label = str(dict(PropertyAreaUnit.choices)[property_unit_type])
                    property_response["property"]["user_unit_preference"] = unit_label
                    property_response["property"]["total_area"] = converted_area
                    property_response["property"]["property_unit_type"] = str(PropertyAreaUnit.SQFT.value)            
            except Exception as e:
                logger.error(f"Error converting area unit: {e}")
                traceback.print_exc()
                continue
                
        # create if not exists update if exists
        PropertyInquiries.objects.update_or_create(
            input=message_created,
            defaults={"output": response},
        )

        if "errors" in response:
            print("ERROR IN RESPONSE", response)
            return
        
        # Process each property in the response
        for property_response in response["properties"]:

            response = create_property_from_response(
                property_response, 
                user, 
                message_created
            )
    
            logger.info(f"PROPERTY CREATED SUCCESSFULLY {response}")

    except Exception as error:
        traceback.print_exc()
        return custom_error_message


def create_location_hierarchy(address_data: Dict[str, str], confidence_score: float = 0.8) -> Dict:

    if confidence_score < 0.7:
        raise Exception("Confidence score must be 0.7 or higher to create address")

    required_fields = ['country', 'country_short_name']
    missing_fields = [field for field in required_fields if field not in address_data]
    if missing_fields:
        raise Exception(f"Missing required fields: {', '.join(missing_fields)}")

    try:
        print("CREATING LOCATION HIERARCHY")
        
        with transaction.atomic():
            country, _ = Country.objects.get_or_create(
                short_name=address_data['country_short_name'].upper(),
                defaults={
                    'name': address_data['country'],
                    'phone_code': '',  # You might want to add phone code logic
                    'is_active': True
                }
            )

            state = None
            if 'state' in address_data and address_data['state']:
                state, _ = State.objects.get_or_create(
                    name=address_data['state'],
                    country=country,  # Required foreign key
                    defaults={'is_active': True}
                )

            # Create or get City (optional)
            city = None
            if 'city' in address_data and address_data['city']:
                city, _ = City.objects.get_or_create(
                    name=address_data['city'],
                    country=country,  # Required foreign key
                    state=state,      # Optional foreign key
                    defaults={'is_active': True}
                )

            # Create or get Area (optional)
            area = None
            if 'area' in address_data and address_data['area']:
                area, _ = Area.objects.get_or_create(
                    name=address_data['area'],
                    country=country,  # Required foreign key
                    state=state,      # Optional foreign key
                    city=city,        # Optional foreign key
                    defaults={'is_active': True}
                )

            # Create or get Community (required)
            community = None
            if 'community' in address_data and address_data['community']:
                community, _ = Community.objects.get_or_create(
                    name=address_data['community'],
                    defaults={
                        'state': state,          
                        'city': city,            
                        'area': area, 
                        'is_active': True,
                        'added_by': 'MANUAL'  # You might want to change this based on your needs
                    }
                )

            return {
                'country': country,
                'state': state,
                'city': city,
                'area': area,
                'community': community
            }

    except Exception as e:
        logger.error(f"Error creating location hierarchy: {e}")
        traceback.print_exc()
        # return empty dict
        return {}
