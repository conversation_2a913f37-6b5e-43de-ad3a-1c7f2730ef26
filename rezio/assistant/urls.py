from django.urls import path
from rezio.assistant.views import pubnub_views, ai_views

urlpatterns = [
    path(
        "message/",
        ai_views.AIAgentView.as_view(),
        name="message",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/pubnub_user/",
        pubnub_views.PubNubUserView.as_view(),
        name="pubnub-user",
    ),
    path(
        "agent-channels/<str:agent_id>/",
        pubnub_views.AgentChannelsView.as_view(),
        name="agent-channels",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/pubnub_update_user/",
        pubnub_views.PubNubUserUpdateView.as_view(),
        name="pubnub-update-user",
    ),
]
