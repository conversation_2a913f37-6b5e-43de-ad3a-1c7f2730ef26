from django.contrib import admin
from .models import (
    PubNubWebUser,
    PubNubChannel,
    Messages,
    Personas,
    Memory,
    Preferences,
)

admin.site.site_header = "Rezio AI"
admin.site.site_title = "Rezio AI"
admin.site.index_title = "Control Room"


class PubNubWebUserAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "name",
        "phone_number",
        "created_at",
    ]
    search_fields = ["user__username", "pubnub_uuid"]


class MessagesAdmin(admin.ModelAdmin):
    list_display = ["id", "buyer"]
    # search_fields = ["pubnub_channel_id"]
    readonly_fields = ["created_at"]


class PersonasAdmin(admin.ModelAdmin):
    def activate_personas(modeladmin, request, queryset):
        queryset.update(is_active=True)
        # Deactivate all other personas
        Personas.objects.exclude(id__in=[persona.id for persona in queryset]).update(
            is_active=False
        )

    activate_personas.short_description = "Activate selected personas"

    list_display = ["name", "is_active"]
    search_fields = ["name", "prompt_template__in"]
    actions = [activate_personas]


class MemoryAdmin(admin.ModelAdmin):
    list_display = ("id", "agent_type", "messages")
    search_fields = ("agent_type", "messages__id")


class PreferencesAdmin(admin.ModelAdmin):
    list_display = ("id", "buyer", "preferences")
    search_fields = ("buyer__username", "preferences")


admin.site.register(PubNubWebUser, PubNubWebUserAdmin)
admin.site.register(PubNubChannel)
admin.site.register(Messages, MessagesAdmin)
admin.site.register(Personas, PersonasAdmin)
admin.site.register(Memory, MemoryAdmin)
admin.site.register(Preferences, PreferencesAdmin)
