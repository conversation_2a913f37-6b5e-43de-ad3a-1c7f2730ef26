import os
import time
import googlemaps
import psycopg2
import environ

# Initialize environ.Env
env = environ.Env()

# Load environment variables from multiple .env files
env_files = [
    "./.envs/.local/.django",  # Path to .django file
    "./.envs/.local/.postgres",  # Path to .postgres file
]

for env_file in env_files:
    env.read_env(env_file)

API_KEY = env("GOOGLE_MAPS_API_KEY")
GMAPS = googlemaps.Client(key=API_KEY)

# Database connection configuration
DB_CONFIG = {
    "dbname": env("POSTGRES_DB"),
    "user": env("POSTGRES_USER"),
    "password": env("POSTGRES_PASSWORD"),
    "host": "localhost",
    "port": env("POSTGRES_PORT"),
}

print(DB_CONFIG)


def get_coordinates(property_name):
    """Get coordinates using Google Geocoding API"""
    try:
        geocode_result = GMAPS.geocode(property_name)
        if geocode_result:
            location = geocode_result[0]["geometry"]["location"]
            return location["lat"], location["lng"]
        return None, None
    except Exception as e:
        print(f"Geocoding error for {property_name}: {str(e)}")
        return None, None


def get_address(obj):
    """Generate an address string from a flat dictionary."""
    skip_loc = None
    # Check if any sub_loc fields exist (assuming a community-like structure)
    for loc_no in range(5, 0, -1):
        if obj.get(f"sub_loc_{loc_no}"):
            skip_loc = f"sub_loc_{loc_no}"
            break

    address_parts = [
        *[
            obj.get(f"sub_loc_{loc_no}")
            for loc_no in range(5, 0, -1)
            if f"sub_loc_{loc_no}" != skip_loc and obj.get(f"sub_loc_{loc_no}")
        ],
        obj.get("community_name") if skip_loc else None,
        obj.get("area_name"),
        obj.get("city_name"),
        obj.get("state_name"),
        obj.get("country_name"),
    ]

    return ", ".join(filter(None, address_parts))


def update_properties():
    """Batch update properties with coordinates"""
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Get all properties without coordinates
        cursor.execute("""
            SELECT
                p.id,
                c.sub_loc_1,
                c.sub_loc_2,
                c.sub_loc_3,
                c.sub_loc_4,
                c.sub_loc_5,
                c.name AS community_name,
                a.name AS area_name,
                city.name AS city_name,
                s.name AS state_name,
                country.name AS country_name,
                c.id AS community_id
            FROM properties_property p
            LEFT JOIN properties_community c ON p.community_id = c.id
            LEFT JOIN properties_area a ON p.area_id = a.id
            LEFT JOIN properties_city city ON a.city_id = city.id
            LEFT JOIN properties_state s ON a.state_id = s.id
            LEFT JOIN properties_country country ON a.country_id = country.id
        """)

        rows = cursor.fetchall()

        for row in rows:
            # print(row)
            obj = {
                "sub_loc_1": row[1],
                "sub_loc_2": row[2],
                "sub_loc_3": row[3],
                "sub_loc_4": row[4],
                "sub_loc_5": row[5],
                "community_name": row[6],
                "area_name": row[7],
                "city_name": row[8],
                "state_name": row[9],
                "country_name": row[10],
            }
            # print(obj)
            # prop_id, name = row
            name = get_address(obj=obj)
            print(name)
            lat, lng = get_coordinates(name)
            print("LAT: LONG: ", lat, lng)

            property_id = row[0]

            if lat and lng:
                # Update with point geometry
                cursor.execute(
                    """
                    UPDATE properties_property
                    SET geom = ST_SetSRID(ST_MakePoint(%s, %s), 4326)
                    WHERE id = %s
                """,
                    (lng, lat, property_id),
                )

                print(f"Updated {name} with {lat}, {lng}")

            time.sleep(0.1)  # Rate limiting

        # Commit the transaction
        conn.commit()

        if conn:
            cursor.close()
            conn.close()

    except Exception as e:
        print(f"Database error: {str(e)}")


def find_nearby_properties(user_lat, user_lng, radius_km):
    """Find properties within specified radius using PostGIS"""
    try:
        # Connect to the database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # Execute the query
        cursor.execute(
            """
            SELECT id,
                   ST_DistanceSphere(
                       geom, 
                       ST_MakePoint(%s, %s)
                   ) / 1000 AS distance_km
            FROM properties_property
            WHERE ST_DWithin(
                geom::geography,
                ST_MakePoint(%s, %s)::geography,
                %s
            )
            ORDER BY distance_km
        """,
            [user_lng, user_lat, user_lng, user_lat, radius_km * 1000],
        )

        # Fetch results
        columns = [col[0] for col in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        if conn:
            cursor.close()
            conn.close()

        return results

    except Exception as e:
        print(f"Database error: {str(e)}")
        return []


# update_properties()

# user_lat = 25.197194795387933
# user_lng =  55.274275614726655
# radius_km = 15  # 5 kilometer radius

# nearby_properties = find_nearby_properties(user_lat, user_lng, radius_km)
# for prop in nearby_properties:
#     print(f" - {prop['distance_km']:.2f} km away")
