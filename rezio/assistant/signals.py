from django.db.models.signals import post_save
from django.dispatch import receiver
from rezio.assistant.tasks import create_multi_agent_memory
from rezio.assistant.models import PubNubChannel


@receiver(post_save, sender=PubNubChannel)
def create_multi_agent_memory_on_channel_create(sender, instance, created, **kwargs):
    """
    Signal handler to create multi-agent memory when a new PubNub channel is created
    """
    if created:
        # TODO: add delay
        create_multi_agent_memory(pubnub_channel_id=instance.id)
