from django.db import models
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from rezio.ai.utils import generate_slug
from rezio.user.models import User, Role
from rezio.properties.models import Property

class PubNubWebUser(models.Model):
    name = models.CharField(max_length=128)
    phone_number = PhoneNumberField(unique=True, blank=False, null=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} - {self.phone_number}"


class PubNubChannel(models.Model):
    web_user = models.ForeignKey(
        PubNubWebUser, on_delete=models.CASCADE, related_name="channels"
    )
    ACTOR_CHOICES = [
        ("Agent", "Agent"),
        ("Buyer", "Buyer"),
        ("Seller", "Seller"),
    ]
    actor = models.CharField(
        max_length=100, choices=ACTOR_CHOICES, null=True, blank=True, default="Buyer"
    )
    pubnub_channel_id = models.CharField(
        max_length=500, unique=True, null=True, blank=True
    )
    group_channel_id = models.Char<PERSON>ield(
        max_length=500, unique=True, null=True, blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    receiver_user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="receiver_user",
    )
    receiver_user_role = models.ForeignKey(
        Role, on_delete=models.CASCADE, default=1, related_name="user_role"
    )

    def __str__(self):
        return f"{self.web_user.name} - {self.pubnub_channel_id}"


class Messages(models.Model):
    buyer = models.ForeignKey(
        PubNubChannel,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="buyer",
    )
    messages = models.JSONField(default=list, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Messages"


class Personas(models.Model):
    name = models.CharField(max_length=500, null=True, blank=True)
    prompt_template = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=False, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Personas"


class Memory(models.Model):
    agent_type = models.CharField(max_length=100, null=True, blank=True)
    messages = models.ForeignKey(
        Messages, on_delete=models.CASCADE, null=True, blank=True
    )
    memory = models.JSONField(default=list, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Memory"


class Preferences(models.Model):
    buyer = models.ForeignKey(
        PubNubWebUser, on_delete=models.CASCADE, null=True, blank=True
    )
    preferences = models.JSONField(default=dict, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.BigIntegerField(null=True)

    def __str__(self):
        return f"{self.buyer} - {self.preferences}"


class PropertyInquiriesMessages(models.Model):
    raw_message = models.TextField()
    slug = models.SlugField(max_length=300, unique=True)
    from_name = models.CharField(max_length=255, null=True, blank=True)
    from_number = models.CharField(max_length=20, null=True, blank=True)
    channel_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    meta = models.JSONField(default=dict, null=False, blank=False)
    def save(self, *args, **kwargs):
        if not self.slug:
            text_for_slug = self.raw_message[:200]
            self.slug = generate_slug(text_for_slug)
        super().save(*args, **kwargs)

class PropertyInquiries(models.Model):
    input = models.ForeignKey(
        PropertyInquiriesMessages,
        on_delete=models.CASCADE, 
        null=True, 
        blank=True
    )
    output = models.JSONField(
        default=dict, 
        null=False, 
        blank=False
    )
    expected_output = models.JSONField(
        default=dict, 
        null=True, 
        blank=True
    )
    valid = models.BooleanField(
        null=True, 
        blank=True
    )
    comment = models.TextField(
        null=True,
        blank=True
    )
    error = models.TextField(
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Inquiry from {self.input.from_name or self.input.from_number or 'Unknown'}"


class QuestionsForBroker(models.Model):
    pubnub_channel = models.ForeignKey(
        PubNubChannel, on_delete=models.CASCADE
    )
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, null=True, blank=True
    )
    query = models.TextField()
    agent_id = models.CharField(max_length=100)
    message_id = models.CharField(max_length=100)
    agent_response = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Questions for Broker"

    def __str__(self):
        return f"{self.agent_id} - {self.query}"


class ChatHistory(models.Model):
    pubnub_channel = models.ForeignKey(
        PubNubChannel, on_delete=models.CASCADE
    )
    author = models.CharField(max_length=100)
    message = models.TextField()
    message_id = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Chat History"

    def __str__(self):
        return f"{self.pubnub_channel} - {self.author} - {self.message}"

class ChatGroups(models.Model):
    id = models.CharField(max_length=100, primary_key=True)
    name = models.CharField(max_length=100, null=True, blank=True)
    meta = models.JSONField(default=dict, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Chat Groups"

    def __str__(self):
        return f"{self.name}"
