from rezio.assistant.types import <PERSON>, <PERSON><PERSON><PERSON><PERSON>, AgentTone, NegotiationStyle
from typing import List, Dict
import json


def filter_conversation(data: List[Dict], actor: Actor) -> List[Dict]:
    """
    Filter the conversation to only include the messages visible to the actor.
    """
    filtered_data = []
    visible_to = [Actor.Buyer] if actor == Actor.Buyer else [Actor.Buyer, Actor.Agent]

    for item in data:
        if item["role"] == "user" and item["name"] in visible_to:
            filtered_item = {
                "role": item["role"],
                "name": item["name"],
                "content": item["content"],
            }
            filtered_data.append(filtered_item)
        elif (
            item["role"] == "assistant"
            and item.get("content")
            and "tool_calls" not in item
        ):
            try:
                assistant_content = json.loads(item.get("content"))
                if assistant_content["response_to"] in visible_to:
                    filtered_item = {
                        "role": item["role"],
                        "content": assistant_content["response"],
                    }
                    filtered_data.append(filtered_item)

            except json.decoder.JSONDecodeError:
                assistant_content = item.get("content")
                filtered_item = {"role": item["role"], "content": assistant_content}

                filtered_data.append(filtered_item)

    return filtered_data


def get_conversation_tone(engagement_level) -> dict:
    """
    Determine the appropriate conversation tone based on engagement level.

    :param engagement_level: An integer representing the engagement level (1-10)
    :return: A dictionary with tone instructions
    """
    if engagement_level < 3:
        return {
            "buyer_tone": BuyerTone.FRIENDLY_AND_PROFESSIONAL,
            "agent_tone": AgentTone.FORMAL_AND_DETAILED,
            "negotiation_style": NegotiationStyle.CAUTIOUS_AND_INFORMATIVE,
        }
    elif 3 <= engagement_level < 6:
        return {
            "buyer_tone": BuyerTone.MORE_DIRECT_AND_CASUAL,
            "agent_tone": AgentTone.STRAIGHTFORWARD,
            "negotiation_style": NegotiationStyle.BALANCED_AND_ASSERTIVE,
        }
    elif 6 <= engagement_level < 8:
        return {
            "buyer_tone": BuyerTone.FRANK_AND_TO_THE_POINT,
            "agent_tone": AgentTone.BLUNT_AND_QUESTIONING,
            "negotiation_style": NegotiationStyle.AGGRESSIVE_TOWARDS_BUYER,
        }
    else:
        return {
            "buyer_tone": BuyerTone.VERY_BLUNT_AND_FAMILIAR,
            "agent_tone": AgentTone.DEMANDING_AND_CRITICAL,
            "negotiation_style": NegotiationStyle.AGGRESSIVE_TOWARDS_BUYER,
        }
