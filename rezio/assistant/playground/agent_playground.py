import chainlit as cl
from agent_pubnub_config import PubN<PERSON><PERSON><PERSON><PERSON>
from typing import Optional, Dict, List
from datetime import datetime
import requests
import json
import asyncio

from chainlit.types import ThreadDict
from pubnub_data_layer import PubNubDataLayer

""" Follow instruction from `rezio/chainlit.md` in the root dir of the repository to use this file."""

# Store the selected channel for the current session
selected_channel: Optional[str] = None

# Your container's API endpoint or other environment like development or production wherever you want to test the chat.
API_ENDPOINT = "http://0.0.0.0:8000/web/ai/message/"

# Store message queue and task
message_queue = asyncio.Queue()
process_task = None


# Register the custom data layer
@cl.data_layer
def get_data_layer():
    return PubNubDataLayer()


@cl.password_auth_callback
def auth_callback(agent_uuid: str, password: str):
    # Just return the user object with the agent UUID
    if password == "rezio@123":
        return cl.User(
            identifier=agent_uuid, metadata={"role": "agent", "provider": "credentials"}
        )
    else:
        return None


@cl.on_chat_start
async def on_chat_start():
    global process_task

    # Get agent UUID from the user session
    user = cl.user_session.get("user")
    agent_uuid = user.identifier if user else "default_agent"

    # Store it in the session
    cl.user_session.set("agent_uuid", agent_uuid)

    # Welcome message
    await cl.Message(
        content=f"Welcome, Agent {agent_uuid}! Please select a conversation from the sidebar to begin."
    ).send()

    # Initialize PubNub
    pubnub = PubNubManager.get_instance()
    # Update PubNub config with agent UUID if needed
    pubnub.config.user_id = agent_uuid

    # Store PubNub instance in user session
    cl.user_session.set("pubnub", pubnub)

    # Start message processing task
    process_task = asyncio.create_task(process_messages())


async def process_messages():
    """Process messages from the queue within Chainlit context"""
    while True:
        try:
            msg_data = await message_queue.get()

            await display_message(msg_data)

            print(f"Successfully displayed queued message from {msg_data}...")

            message_queue.task_done()
        except Exception as e:
            print(f"Error processing message from queue: {str(e)}")
        await asyncio.sleep(0.1)  # Small delay to prevent CPU overuse


async def setup_channel(channel_id: str, group_channel_id: str):
    global selected_channel

    pubnub = PubNubManager.get_instance()
    selected_channel = channel_id

    # Store PubNub instance and channel in user session
    cl.user_session.set("pubnub", pubnub)
    cl.user_session.set("pubnub_channel", channel_id)

    # Fetch message history
    await cl.Message(
        content=f"Fetching message history for channel: {channel_id}"
    ).send()

    # Get message history for both channels
    history_messages = []
    for channel in [channel_id]:
        print(f"Requesting history for channel: {channel}")
        messages = pubnub.get_message_history(channel)
        print(f"Received {len(messages)} messages for channel {channel}")
        if messages:
            history_messages.extend(messages)

    # Sort messages by timetoken (timestamp)
    if history_messages:
        history_messages.sort(key=lambda x: x.timetoken)
        print(f"Sorted {len(history_messages)} total messages by timestamp")

    # Display message history
    if history_messages:
        await cl.Message(
            content=f"Found {len(history_messages)} previous messages"
        ).send()

        # Process and display each message
        for msg in history_messages:
            await display_message(msg)
    else:
        await cl.Message(content="No message history found").send()
        print("No message history found for channels")

    # Subscribe to the channel
    async def message_callback(message):
        try:
            # Parse message if it's a string
            if isinstance(message, str):
                try:
                    message = json.loads(message)
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON message: {e}")
                    return

            # Only process messages for our channel
            if message.get("channel_id") != channel_id:
                print(
                    f"Skipping message - channel mismatch: {message.get('channel_id')} != {channel_id}"
                )
                return

            # Get message role and content
            msg_role = message.get("role")
            msg_content = message.get("content")

            print(f"Processing message - Role: {msg_role}, Content: {msg_content}")

            # Add message to queue instead of sending directly
            await message_queue.put(msg)
            print(f"Message queued for processing: {msg_content}")

        except Exception as e:
            print(f"Error in message_callback: {str(e)}")
            print(f"Message that caused error: {message}")

    # Subscribe to both channel IDs
    channels_to_subscribe = [channel_id, group_channel_id]
    print(f"Setting up socket subscription for channels: {channels_to_subscribe}")

    pubnub.subscribe_to_channel(channels_to_subscribe, message_callback)


@cl.on_chat_resume
async def on_chat_resume(thread: ThreadDict):
    global selected_channel

    print(f"=== on_chat_resume called for thread: {thread['id']} ===")

    # Store thread ID in context
    cl.user_session.set("thread_id", thread["id"])

    # Get channel ID from thread metadata
    if "metadata" in thread and "channel_id" in thread["metadata"]:
        channel_id = thread["metadata"]["channel_id"]
        group_channel_id = thread["metadata"].get("group_channel_id")

        print(f"Found channel_id in metadata: {channel_id}")

        # Unsubscribe from previous channel if different
        if selected_channel and selected_channel != channel_id:
            print(f"Unsubscribing from previous channel: {selected_channel}")
            pubnub = cl.user_session.get("pubnub")
            if pubnub:
                pubnub.unsubscribe_from_channel(selected_channel)

        # Get agent UUID from the user session or thread
        user = cl.user_session.get("user")
        agent_uuid = (
            user.identifier if user else thread.get("userIdentifier", "default_agent")
        )

        # Store channel IDs and agent UUID in session
        selected_channel = channel_id
        cl.user_session.set("selected_channel_id", channel_id)
        cl.user_session.set("agent_uuid", agent_uuid)
        if group_channel_id:
            cl.user_session.set("group_channel_id", group_channel_id)

        # Get PubNub instance from session or create new one
        pubnub = cl.user_session.get("pubnub")
        if not pubnub:
            pubnub = PubNubManager.get_instance()
            pubnub.config.user_id = agent_uuid
            cl.user_session.set("pubnub", pubnub)

        # Display welcome message
        await cl.Message(content=f"Connected to channel: {channel_id}").send()

        # Fetch message history
        await cl.Message(content=f"Fetching message history...").send()

        await setup_channel(channel_id, group_channel_id)


@cl.on_message
async def on_message(message: cl.Message):
    print(f"Received message: {message}")
    await cl.Message(
        content="User input is currently not supported in this playground."
    ).send()
    return


@cl.on_stop
async def on_stop():
    global selected_channel, process_task

    if selected_channel:
        # Unsubscribe from channel
        pubnub = cl.user_session.get("pubnub")
        if pubnub:
            pubnub.unsubscribe_from_channel(selected_channel)
        selected_channel = None

    # Cancel message processing task
    if process_task:
        process_task.cancel()
        try:
            await process_task
        except asyncio.CancelledError:
            pass


async def display_message(msg):
    try:
        # Try different attributes based on the actual message structure
        if hasattr(msg, "message"):
            message_data = msg.message
        elif hasattr(msg, "entry"):
            message_data = msg.entry
        else:
            print(f"Unknown message structure: {dir(msg)}")
            return

        print(f"Processing history message: {message_data}")

        # Parse message if it's a string
        if isinstance(message_data, str):
            try:
                message_data = json.loads(message_data)
                print(f"Parsed JSON message: {message_data}")
            except json.JSONDecodeError as e:
                print(f"Failed to parse message as JSON: {e}")
                # If not JSON, display as plain text from system
                await cl.Message(content=message_data, author="System").send()
                return

        print(f"Message from pubnub: {msg.__dict__}")
        print(f"Message has attributes: {hasattr(msg, 'custom_message_type')}")
        print(f"Message has attributes: {hasattr(msg, 'message_type')}")

        # Get message role and content
        msg_type = (
            msg.custom_message_type
            if hasattr(msg, "custom_message_type")
            and msg.custom_message_type is not None
            else print("No message type found")
        )
        "text"
        msg_role = message_data.get("role")
        msg_content = message_data.get("content")

        print(f"Message role: {msg_role}, content: {msg_content}")

        if not msg_content:
            print("Skipping message with no content")
            return

        # Determine the author based on role
        author = "Assistant" if msg_role == "assistant" else "User"

        # Display media message
        if msg_type == "media":
            if message_data.get("media_type") == "image":
                image = cl.Image(
                    path=msg_content, name="Property Image", display="inline"
                )

                print(f"Displaying message from {author}: {msg_content}")

                await cl.Message(
                    content=message_data.get("caption"), author=author, elements=[image]
                ).send()

            elif message_data.get("media_type") == "video":
                video = cl.Video(
                    path=msg_content, name="Property Video", display="inline"
                )

                print(f"Displaying message from {author}: {msg_content}")

                await cl.Message(
                    content=message_data.get("caption"), author=author, elements=[video]
                ).send()

        # Display text message
        else:
            # Display the message
            print(f"Displaying message from {author}: {msg_content}")
            await cl.Message(content=msg_content, author=author).send()

    except Exception as e:
        print(f"Error processing history message: {str(e)}")
        import traceback

        traceback.print_exc()
