import asyncio
import os
from pathlib import Path

import dotenv
from pubnub.callbacks import SubscribeCallback
from pubnub.enums import PNStatusCategory, PNReconnectionPolicy
from pubnub.models.consumer.history import PNH<PERSON>oryResult
from pubnub.pnconfiguration import PNConfiguration
from pubnub.pubnub import PubNub
from typing_extensions import Dict, List, Optional

# Get the project root directory
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent

# Load environment variables from the correct path
env_path = BASE_DIR / ".envs" / ".local" / ".django"
dotenv.load_dotenv(env_path)
# 35_Agent_8eoNMp33YuWbLuU0dJrm8itrcLQ2_75464fe333d145de904ccbb9691bdec9 // old history
# 68_Agent_8eoNMp33YuWbLuU0dJrm8itrcLQ2_aaa596d5b18d417db8e847b8ff9992d9 // with all memory
#101_Agent_8eoNMp33YuWbLuU0dJrm8itrcLQ2_7c78b84e178f4ef19bc3924c74201dff // just context builder
CHANNEL_ID = "534_Agent_8eoNMp33YuWbLuU0dJrm8itrcLQ2_7b5c0f8409954ff1a7fbdc0be6221023"
UUID, GROUP_CHANNEL_ID = CHANNEL_ID.split("_", 1)


class PubNubManager:
    _instance = None

    def __init__(self):
        self.config = PNConfiguration()

        # Get PubNub keys from environment
        subscribe_key = os.getenv("PUBNUB_SUBSCRIBE_KEY")
        publish_key = os.getenv("PUBNUB_PUBLISH_KEY")

        print(
            f"PubNub keys from environment: subscribe_key={subscribe_key}, publish_key={publish_key}"
        )

        if not subscribe_key or not publish_key:
            raise ValueError("PubNub keys not found in environment variables!")

        self.config.subscribe_key = subscribe_key
        self.config.publish_key = publish_key
        self.config.user_id = UUID

        # MAXIMIZED TIMEOUT SETTINGS
        self.config.connect_timeout = 60
        self.config.non_subscribe_request_timeout = 60
        self.config.subscribe_request_timeout = 600

        # logging
        self.config.log_verbosity = True

        # origin
        self.config.origin = "ps2.pndsn.com"

        # Enable storage/playback for history
        self.config.enable_subscribe = True

        print(f"Initializing PubNub config: {self.config}")

        self.pubnub = PubNub(self.config)
        self.active_channels: Dict[str, dict] = {}

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = PubNubManager()
        return cls._instance

    def detele_message_history(self, channel):
        try:
            end = 1746099143071 * 10**6  # Convert ms to ns

            # Make the fetch_messages request
            result = (
                self.pubnub.delete_messages().channel(channel).start(0).end(end).sync()
            )

            print("DONE")

        except Exception as e:
            print(f"Error fetching message history: {str(e)}")
            import traceback

            traceback.print_exc()
            return []

    def get_message_history(self, channel, count=100):
        """
        Fetch message history for a channel using fetch_messages API

        Args:
            channel (str): Channel ID to fetch history for
            count (int): Maximum number of messages to retrieve

        Returns:
            List of messages in chronological order (oldest first)
        """
        try:
            print(f"Fetching message history for channel: {channel}")

            # Make the fetch_messages request
            result = (
                self.pubnub.fetch_messages()
                .channels([channel])
                .maximum_per_channel(count)
                .include_meta(True)
                .include_uuid(True)
                .sync()
            )

            if not result:
                print("No history result returned")
                return []

            # Debug the result structure
            print(f"Result type: {type(result)}")
            print(f"Result attributes: {dir(result)}")

            # Try to access the messages based on the actual structure
            messages = []

            # Check if result has a 'result' attribute (newer SDK versions)
            if hasattr(result, "result"):
                print("Using result.result structure")
                if (
                    hasattr(result.result, "channels")
                    and channel in result.result.channels
                ):
                    messages = result.result.channels[channel]
            # Check if result directly has 'channels' attribute (older SDK versions)
            elif hasattr(result, "channels") and channel in result.channels:
                print("Using result.channels structure")
                messages = result.channels[channel]
            else:
                print(f"Could not find messages in result structure: {result}")

            print(
                f"Retrieved {len(messages)} messages from history for channel {channel}"
            )

            # Debug: Print first message if available
            if messages and len(messages) > 0:
                print(f"First message sample: {messages[0]}")
                print(f"First message attributes: {dir(messages[0])}")

                # Try to access message content based on actual structure
                if hasattr(messages[0], "message"):
                    print(f"Message content: {messages[0].message}")
                elif hasattr(messages[0], "entry"):
                    print(f"Message content: {messages[0].entry}")

            # Return messages in chronological order (oldest first)
            return messages

        except Exception as e:
            print(f"Error fetching message history: {str(e)}")
            import traceback

            traceback.print_exc()
            return []

    def subscribe_to_channel(self, channels, callback):
        """Subscribe to channels using socket-based subscription"""
        if isinstance(channels, str):
            channels = [channels]

        class SocketSubscribeHandler(SubscribeCallback):
            def presence(self, pubnub, presence):
                print(f"Presence event: {presence.event} for UUID: {presence.uuid}")

            def status(self, pubnub, status):
                if status.category == PNStatusCategory.PNUnexpectedDisconnectCategory:
                    print(f"Unexpected disconnection from PubNub: {status.category}")
                elif status.category == PNStatusCategory.PNConnectedCategory:
                    print(f"Connected to PubNub channels: {channels}")
                elif status.category == PNStatusCategory.PNReconnectedCategory:
                    print("Reconnected to PubNub")
                elif status.category == PNStatusCategory.PNDecryptionErrorCategory:
                    print("Decryption error")
                else:
                    print(f"PubNub status changed: {status.category}")

            def message(self, pubnub, message):
                """Handle incoming messages"""
                print(
                    f"Received message on channel {message.channel}: {message.message}"
                )
                # Create new event loop for async callback
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(callback(message.message))
                finally:
                    loop.close()

        # Create and add the listener
        listener = SocketSubscribeHandler()
        self.pubnub.add_listener(listener)

        # Subscribe to the channels
        self.pubnub.subscribe().channels(channels).execute()

        # Store active channels
        for channel in channels:
            self.active_channels[channel] = {"listener": listener}

    def publish_message(self, channel, message):
        self.pubnub.publish().channel(channel).message(message).sync()

    def unsubscribe_from_channel(self, channels):
        """Unsubscribe from channels"""
        if isinstance(channels, str):
            channels = [channels]

        # Unsubscribe from the channels
        self.pubnub.unsubscribe().channels(channels).execute()

        # Remove listeners and clean up
        for channel in channels:
            if channel in self.active_channels:
                listener = self.active_channels[channel].get("listener")
                if listener:
                    self.pubnub.remove_listener(listener)
                del self.active_channels[channel]

    def add_active_channel(self, channel_id: str, metadata: dict):
        """Add a channel to active channels list with metadata"""
        self.active_channels[channel_id] = metadata

    def remove_active_channel(self, channel_id: str):
        """Remove a channel from active channels list"""
        if channel_id in self.active_channels:
            del self.active_channels[channel_id]

    def get_active_channels(self) -> List[Dict]:
        """Get list of active channels"""
        return [
            {"channel_id": channel_id} for channel_id in self.active_channels.keys()
        ]

    def get_channel_metadata(self, channel_id: str) -> Optional[dict]:
        """Get metadata for a specific channel"""
        return self.active_channels.get(channel_id)
