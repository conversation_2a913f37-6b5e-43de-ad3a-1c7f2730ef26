import json
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from chainlit.data import BaseDataLayer
from chainlit.element import ElementDict
from chainlit.step import StepDict
from chainlit.user import PersistedUser
from chainlit.user import User
from chainlit.types import (
    ThreadDict,
    Pagination,
    ThreadFilter,
    PaginatedResponse,
    Feedback,
)
from agent_pubnub_config import PubNubManager


class PubNubDataLayer(BaseDataLayer):
    """Custom data layer that uses PubNub for message history with in-memory persistence"""

    # Class-level storage for threads (shared across instances)
    _threads_store = {}

    def __init__(self):
        super().__init__()
        # We'll initialize PubNub when needed
        self.pubnub = None

    # User management
    async def get_user(self, identifier: str) -> Optional[PersistedUser]:
        """Get a user by identifier"""
        # Create a basic user without persistence
        return PersistedUser(
            id=identifier,
            identifier=identifier,
            metadata={},
            createdAt=datetime.now().isoformat(),
        )

    async def create_user(self, user: User) -> Optional[PersistedUser]:
        """Create a new user"""
        # Just return a PersistedUser without storing it
        return PersistedUser(
            id=user.identifier,
            identifier=user.identifier,
            metadata=user.metadata,
            createdAt=datetime.now().isoformat(),
        )

    # Thread management
    async def get_thread(self, thread_id: str) -> Optional[ThreadDict]:
        """Get a thread by ID"""
        print(f"=== get_thread called for thread_id: {thread_id} ===")

        # Normalize thread ID
        original_thread_id = thread_id
        thread_id = self._normalize_thread_id(thread_id)

        print(f"Looking for thread in store with ID: {thread_id}")
        print(f"Current threads in store: {list(self._threads_store.keys())}")

        # Check if thread exists in our store
        if thread_id in self._threads_store:
            print(f"Found thread {thread_id} in store")
            thread = self._threads_store[thread_id]

            # If thread has no steps, fetch messages and add them
            # if not thread.get("steps") or len(thread.get("steps", [])) == 0:
            #     print(f"Thread has no steps, fetching messages...")
            #     thread = await self._fetch_messages_for_thread(thread)
            #     # Update the store with the thread including messages
            #     self._threads_store[thread_id] = thread

            print(f"Returning existing thread from store: {thread['id']}")
            self._debug_thread_store()
            return thread

        # If not in store, create a new thread
        print(f"Thread {thread_id} not found in store, creating new thread")

        # Extract channel ID from thread ID
        channel_id = thread_id.replace("thread_", "")
        print(f"Using channel_id: {channel_id} for thread: {thread_id}")

        # Get agent UUID from user identifier in thread_id if available
        agent_uuid = "default_agent"
        parts = channel_id.split("_")
        if len(parts) > 2:
            agent_uuid = parts[2]  # Assuming format like "42_Agent_UUID_..."
            print(f"Extracted agent UUID from channel ID: {agent_uuid}")

        # Create basic thread without messages
        thread_dict = {
            "id": thread_id,
            "createdAt": datetime.now().isoformat(),  # Use camelCase
            "updatedAt": datetime.now().isoformat(),  # Use camelCase
            "name": f"Channel: {channel_id}",
            "user_id": agent_uuid,  # Keep this for our internal use
            "userIdentifier": agent_uuid,  # Add this for Chainlit's resume_thread function
            "tags": [],
            "metadata": {
                "channel_id": channel_id,
                "group_channel_id": channel_id.split("_", 1)[1]
                if "_" in channel_id
                else channel_id,
            },
            "steps": [],
        }

        # Fetch messages and add them as steps
        # thread_dict = await self._fetch_messages_for_thread(thread_dict)

        # Store the thread for future requests
        # self._threads_store[thread_id] = thread_dict
        # print(f"Stored new thread {thread_id} with {len(thread_dict.get('steps', []))} steps")

        # self._debug_thread_store()
        return thread_dict

    # async def _fetch_messages_for_thread(self, thread_dict: ThreadDict) -> ThreadDict:
    #     """Fetch messages for a thread and add them as steps"""
    #     thread_id = thread_dict["id"]
    #     channel_id = thread_dict["metadata"]["channel_id"]
    #     agent_uuid = thread_dict["user_id"]
    #     group_channel_id = thread_dict["metadata"].get("group_channel_id")

    #     print(f"Fetching messages for thread: {thread_id}")
    #     print(f"Channel ID: {channel_id}")
    #     print(f"Agent UUID: {agent_uuid}")
    #     print(f"Group Channel ID: {group_channel_id}")

    #     # Get PubNub instance
    #     pubnub = PubNubManager.get_instance()
    #     pubnub.config.user_id = agent_uuid

    #     # Get messages from PubNub for this channel
    #     print(f"Fetching message history for channel: {channel_id}")
    #     messages = pubnub.get_message_history(channel_id)
    #     print(f"Retrieved {len(messages)} messages from history for channel {channel_id}")

    #     # If we have a group channel, get its messages too
    #     if group_channel_id and group_channel_id != channel_id:
    #         print(f"Fetching message history for group channel: {group_channel_id}")
    #         group_messages = pubnub.get_message_history(group_channel_id)
    #         print(f"Retrieved {len(group_messages)} messages from history for group channel {group_channel_id}")
    #         if group_messages:
    #             messages.extend(group_messages)

    #     # Sort messages by timetoken (timestamp) if available
    #     if messages:
    #         try:
    #             messages.sort(key=lambda x: getattr(x, 'timetoken', 0))
    #             print(f"Sorted {len(messages)} total messages by timestamp")
    #         except Exception as e:
    #             print(f"Error sorting messages: {str(e)}")

    #     # Convert messages to steps
    #     steps = []
    #     for idx, msg in enumerate(messages):
    #         try:
    #             # Extract message content
    #             if hasattr(msg, 'message'):
    #                 message_data = msg.message
    #             elif hasattr(msg, 'entry'):
    #                 message_data = msg.entry
    #             else:
    #                 print(f"Unknown message structure: {dir(msg)}")
    #                 continue

    #             # Parse message if it's a string
    #             if isinstance(message_data, str):
    #                 try:
    #                     message_data = json.loads(message_data)
    #                 except json.JSONDecodeError:
    #                     # If not JSON, create a system message
    #                     step = {
    #                         "id": f"{thread_id}_step_{idx}",
    #                         "threadId": thread_id,
    #                         "type": "user_message",
    #                         "output": message_data,  # Content goes in output
    #                         "name": "System",  # Author name
    #                         "streaming": False,
    #                         "createdAt": datetime.now().isoformat(),
    #                         "updatedAt": datetime.now().isoformat(),
    #                         "startTime": datetime.now().isoformat(),
    #                         "endTime": datetime.now().isoformat(),
    #                         "parentId": None,
    #                         "feedback": None,
    #                         # Add any other fields that Message.from_dict might expect
    #                         "metadata": {},
    #                         "language": None,
    #                         "command": None
    #                     }
    #                     steps.append(step)
    #                     continue

    #             # Get message role and content
    #             msg_role = message_data.get("role", "user")
    #             msg_content = message_data.get("content", "")

    #             if not msg_content:
    #                 print(f"Skipping message {idx} with no content")
    #                 continue

    #             # Create step with the exact format Chainlit expects for Message.from_dict
    #             step_type = "assistant_message" if msg_role == "assistant" else "user_message"
    #             author = "Assistant" if msg_role == "assistant" else "User"

    #             step = {
    #                 "id": f"{thread_id}_step_{idx}",
    #                 "threadId": thread_id,
    #                 "type": step_type,
    #                 "output": msg_content,  # This is what Message.from_dict expects for content
    #                 "name": author,  # This is what Message.from_dict uses for author
    #                 "streaming": False,
    #                 "createdAt": datetime.now().isoformat(),
    #                 "updatedAt": datetime.now().isoformat(),
    #                 "startTime": datetime.now().isoformat(),
    #                 "endTime": datetime.now().isoformat(),
    #                 "parentId": None,
    #                 "feedback": None,
    #                 # Add any other fields that Message.from_dict might expect
    #                 "metadata": {},
    #                 "language": None,
    #                 "command": None
    #             }
    #             steps.append(step)
    #             print(f"Added step {idx} with role {msg_role} and content: {msg_content[:50]}...")

    #         except Exception as e:
    #             print(f"Error processing message {idx}: {str(e)}")
    #             import traceback
    #             traceback.print_exc()

    #     print(f"Created {len(steps)} steps from messages")

    #     # Update thread with steps
    #     thread_dict["steps"] = steps
    #     thread_dict["updatedAt"] = datetime.now().isoformat()  # Use camelCase

    #     return thread_dict

    async def list_threads(
        self, pagination: Pagination, filters: ThreadFilter
    ) -> PaginatedResponse[ThreadDict]:
        """List threads with pagination and filtering"""
        try:
            # Get agent UUID from filters or use default
            agent_uuid = (
                filters.userId
                if hasattr(filters, "userId") and filters.userId
                else "default_agent"
            )
            print(f"Using agent UUID for thread listing: {agent_uuid}")

            # Use the API endpoint to get channels
            import requests

            # Construct the API URL
            api_url = f"http://0.0.0.0:8000/web/ai/agent-channels/{agent_uuid}/"
            print(f"Fetching channels from API: {api_url}")

            # Make the request
            response = requests.get(api_url)

            if response.status_code != 200:
                print(f"Error fetching channels from API: {response.status_code}")
                channels = []
            else:
                # Parse the response
                data = response.json()

                # Check for the correct response structure
                if "data" not in data or "channels" not in data["data"]:
                    print(f"Invalid response format from API: {data}")
                    channels = []
                else:
                    # Extract channels from the response
                    api_channels = data["data"]["channels"]
                    total_channels = data["data"].get(
                        "total_channels", len(api_channels)
                    )

                    # Format channels for the application
                    channels = []
                    for channel in api_channels:
                        # Format the name to include user details
                        name = f"{channel['web_user_name']} - {channel['web_user_phone']} - {channel['web_user_id']}"

                        channel_info = {
                            "id": channel["channel_id"],
                            "name": name,
                            "group_id": channel["group_channel_id"],
                            "custom": {
                                "web_user_id": channel["web_user_id"],
                                "actor": channel["actor"],
                                "created_at": channel["created_at"],
                            },
                        }
                        channels.append(channel_info)

                    print(
                        f"Retrieved {len(channels)} channels from API for agent {agent_uuid} (total: {total_channels})"
                    )

            # If no channels found, create a default one
            if not channels:
                default_channel = {
                    "id": f"{agent_uuid}_default",
                    "name": f"Default Channel for {agent_uuid}",
                    "group_id": f"{agent_uuid}_default_group",
                }
                channels = [default_channel]
                print(f"Created default channel: {default_channel['id']}")

            # Convert channels to threads
            threads = []
            for channel in channels:
                channel_id = channel.get("id")
                # Create thread ID with prefix - ensure it's consistent
                thread_id = f"thread_{channel_id}"

                # Print the exact thread ID we're creating
                print(f"Creating thread with ID: {thread_id}")

                # Check if thread already exists in store
                if thread_id in self._threads_store:
                    print(
                        f"Thread {thread_id} already exists in store, using stored version"
                    )
                    threads.append(self._threads_store[thread_id])
                    continue

                # Create new thread
                thread_dict = {
                    "id": thread_id,
                    "createdAt": datetime.now().isoformat(),  # Use camelCase
                    "updatedAt": datetime.now().isoformat(),  # Use camelCase
                    "name": channel.get("name", channel_id),
                    "user_id": agent_uuid,  # Keep this for our internal use
                    "userIdentifier": agent_uuid,  # Add this for Chainlit's resume_thread function
                    "tags": [],
                    "metadata": {
                        "channel_id": channel_id,
                        "group_channel_id": channel.get("group_id"),
                    },
                    "steps": [],  # We'll load steps only when getting a specific thread
                }

                # Store thread in our in-memory store
                self._threads_store[thread_id] = thread_dict
                print(f"Stored thread {thread_id} in memory store")

                threads.append(thread_dict)

            print(f"Created {len(threads)} thread objects from channels")

            # Apply pagination
            if hasattr(pagination, "offset"):
                start = pagination.offset
            elif hasattr(pagination, "skip"):
                start = pagination.skip
            else:
                start = 0

            if hasattr(pagination, "limit"):
                # Use a larger limit if provided limit is small
                limit = max(pagination.limit, 50)  # Show at least 50 threads
            else:
                # Default to a larger number
                limit = 100  # Show up to 100 threads by default

            end = start + limit
            paginated_threads = threads[start:end]

            print(
                f"Returning paginated threads: {len(paginated_threads)} (offset: {start}, limit: {limit})"
            )

            # Create a custom class that matches the expected interface
            class CustomPaginatedResponse:
                def __init__(self, data, pageInfo):
                    self.data = data
                    self.pageInfo = pageInfo

                def to_dict(self):
                    return {"data": self.data, "pageInfo": self.pageInfo}

            # Return a custom response object that has the to_dict method
            self._debug_thread_store()
            return CustomPaginatedResponse(
                data=paginated_threads,
                pageInfo={"total": len(threads), "offset": start, "limit": limit},
            )
        except Exception as e:
            print(f"Error in list_threads: {str(e)}")
            import traceback

            traceback.print_exc()

            # Return a minimal response with the required to_dict method
            class EmptyResponse:
                def __init__(self):
                    self.data = []
                    self.pageInfo = {"total": 0, "offset": 0, "limit": 10}

                def to_dict(self):
                    return {"data": self.data, "pageInfo": self.pageInfo}

            return EmptyResponse()

    # Thread operations
    async def update_thread(self, thread_id: str, **kwargs) -> None:
        """Update thread properties"""
        print(f"=== update_thread called for thread_id: {thread_id} ===")
        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Check if thread exists in our store
        if thread_id in self._threads_store:
            # Update thread properties
            for key, value in kwargs.items():
                self._threads_store[thread_id][key] = value

            # Update timestamp
            self._threads_store[thread_id]["updatedAt"] = datetime.now().isoformat()
            print(f"Updated thread {thread_id} in store")

    async def delete_thread(self, thread_id: str) -> None:
        """Delete a thread and all its steps"""
        print(f"=== delete_thread called for thread_id: {thread_id} ===")
        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Remove from store if exists
        if thread_id in self._threads_store:
            del self._threads_store[thread_id]
            print(f"Deleted thread {thread_id} from store")

    async def get_thread_author(self, thread_id: str) -> Optional[str]:
        """Get the author of a thread"""
        print(f"=== get_thread_author called for thread_id: {thread_id} ===")
        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Check if thread exists in our store
        if thread_id in self._threads_store:
            return self._threads_store[thread_id].get("user_id")

        # Extract channel ID from thread ID
        channel_id = thread_id.replace("thread_", "")

        # For simplicity, return the first part of the channel ID as the author
        parts = channel_id.split("_")
        if len(parts) > 1:
            return parts[0]
        return None

    # Step management
    async def create_step(self, step_dict: StepDict) -> None:
        """Create a new step"""
        print(f"=== create_step called with step_dict: {step_dict} ===")

        # Check if step_dict has threadId (camelCase)
        thread_id = step_dict.get("threadId")
        if not thread_id:
            print("Warning: No threadId in step_dict, trying to find from context")

            # Try to get thread_id from context
            try:
                from chainlit.context import get_context

                context = get_context()
                if context and hasattr(context, "thread_id"):
                    thread_id = context.thread_id
                    print(f"Found thread_id {thread_id} from context")
                    # Update step_dict with threadId
                    step_dict["threadId"] = thread_id
                else:
                    print("Error: Could not find thread_id in context")
                    return
            except Exception as e:
                print(f"Error getting thread_id from context: {str(e)}")
                return

        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Ensure thread exists in our store
        if thread_id not in self._threads_store:
            print(f"Thread {thread_id} not found in store, creating it")

            # Try to get agent UUID from context
            agent_uuid = "default_agent"
            try:
                from chainlit.context import get_context

                context = get_context()
                if context and hasattr(context, "user") and context.user:
                    agent_uuid = context.user.identifier
            except Exception:
                pass

            # Create a basic thread
            self._threads_store[thread_id] = {
                "id": thread_id,
                "createdAt": datetime.now().isoformat(),  # Use camelCase
                "updatedAt": datetime.now().isoformat(),  # Use camelCase
                "name": f"Thread {thread_id}",
                "user_id": agent_uuid,  # Keep this for our internal use
                "userIdentifier": agent_uuid,  # Add this for Chainlit's resume_thread function
                "tags": [],
                "metadata": {},
                "steps": [],
            }

        # Add step to thread
        if "steps" not in self._threads_store[thread_id]:
            self._threads_store[thread_id]["steps"] = []

        # Ensure step has an ID
        if "id" not in step_dict:
            step_dict["id"] = (
                f"{thread_id}_step_{len(self._threads_store[thread_id]['steps'])}"
            )

        # Add other required fields if missing
        if "createdAt" not in step_dict:
            step_dict["createdAt"] = datetime.now().isoformat()  # Use camelCase
        if "updatedAt" not in step_dict:
            step_dict["updatedAt"] = datetime.now().isoformat()  # Use camelCase
        if "startTime" not in step_dict:
            step_dict["startTime"] = datetime.now().isoformat()  # Use camelCase
        if "endTime" not in step_dict:
            step_dict["endTime"] = datetime.now().isoformat()  # Use camelCase

        # Make sure step has thread_id for our internal use
        step_dict["thread_id"] = thread_id

        # Ensure parentId is set (required for memory system)
        if "parentId" not in step_dict:
            step_dict["parentId"] = None

        self._threads_store[thread_id]["steps"].append(step_dict)
        # Update timestamp
        self._threads_store[thread_id]["updatedAt"] = (
            datetime.now().isoformat()
        )  # Use camelCase
        print(f"Added step {step_dict.get('id')} to thread {thread_id}")

    async def update_step(self, step_dict: StepDict) -> None:
        """Update an existing step"""
        print(f"=== update_step called for step_id: {step_dict.get('id')} ===")

        # Get thread_id from either threadId (camelCase) or thread_id (snake_case)
        thread_id = step_dict.get("threadId") or step_dict.get("thread_id")
        step_id = step_dict.get("id")

        if not thread_id or not step_id:
            print("Error: Missing threadId/thread_id or id in step_dict")
            return

        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Check if thread exists in our store
        if (
            thread_id in self._threads_store
            and "steps" in self._threads_store[thread_id]
        ):
            # Find and update step
            for i, step in enumerate(self._threads_store[thread_id]["steps"]):
                if step.get("id") == step_id:
                    # Make sure step has thread_id for our internal use
                    step_dict["thread_id"] = thread_id

                    self._threads_store[thread_id]["steps"][i] = step_dict
                    # Update timestamp
                    self._threads_store[thread_id]["updatedAt"] = (
                        datetime.now().isoformat()
                    )
                    print(f"Updated step {step_id} in thread {thread_id}")
                    break

    async def delete_step(self, step_id: str) -> None:
        """Delete a step"""
        print(f"=== delete_step called for step_id: {step_id} ===")
        # Find thread containing this step
        for thread_id, thread in self._threads_store.items():
            if "steps" in thread:
                # Find and remove step
                for i, step in enumerate(thread["steps"]):
                    if step.get("id") == step_id:
                        thread["steps"].pop(i)
                        # Update timestamp
                        thread["updatedAt"] = datetime.now().isoformat()
                        print(f"Deleted step {step_id} from thread {thread_id}")
                        break

    # Other required methods (minimal implementation)
    async def create_element(self, element_dict: ElementDict) -> None:
        """Create a new element"""
        # No-op since we're not storing elements
        pass

    async def get_element(
        self, thread_id: str, element_id: str
    ) -> Optional[ElementDict]:
        """Get an element by ID"""
        # No elements stored
        return None

    async def delete_element(self, element_id: str) -> None:
        """Delete an element"""
        # No-op since we're not storing elements
        pass

    async def upsert_feedback(self, feedback: Feedback) -> str:
        """Create or update feedback"""
        # Not implemented
        return str(uuid.uuid4())

    async def delete_feedback(self, feedback_id: str) -> bool:
        """Delete feedback"""
        # Not implemented
        return True

    async def delete_user_session(self, id: str) -> bool:
        """Delete a user session"""
        # Not implemented
        return True

    async def build_debug_url(self, thread_id: str) -> Optional[str]:
        """Build a debug URL for a thread"""
        return None

    async def thread_exists(self, thread_id: str) -> bool:
        """Check if a thread exists"""
        print(f"=== thread_exists called for thread_id: {thread_id} ===")
        # Normalize thread ID
        thread_id = self._normalize_thread_id(thread_id)

        # Check if thread exists in our store
        exists = thread_id in self._threads_store
        print(f"Thread {thread_id} exists: {exists}")
        return exists

    def _normalize_thread_id(self, thread_id: str) -> str:
        """Normalize thread ID to ensure consistency"""
        print(f"Normalizing thread ID: {thread_id}")
        # If it doesn't start with "thread_", add it
        if not thread_id.startswith("thread_"):
            normalized_id = f"thread_{thread_id}"
            print(f"Normalized thread ID from {thread_id} to {normalized_id}")
            return normalized_id
        return thread_id

    async def get_threads(self, thread_ids: List[str]) -> List[Optional[ThreadDict]]:
        """Get multiple threads by their IDs"""
        print(f"=== get_threads called for thread_ids: {thread_ids} ===")

        threads = []
        for thread_id in thread_ids:
            thread = await self.get_thread(thread_id)
            threads.append(thread)

        return threads

    def _debug_thread_store(self):
        """Print debug information about the thread store"""
        print(f"=== Thread Store Debug ===")
        print(f"Number of threads in store: {len(self._threads_store)}")
        for thread_id, thread in self._threads_store.items():
            print(f"Thread ID: {thread_id}")
            print(f"  Created: {thread.get('createdAt')}")
            print(f"  Updated: {thread.get('updatedAt')}")
            print(f"  Name: {thread.get('name')}")
            print(f"  Steps: {len(thread.get('steps', []))}")
            print(f"  Metadata: {thread.get('metadata')}")
            print("---")
