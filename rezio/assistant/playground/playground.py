import chainlit as cl
from pubnub_config import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CHANNEL_ID, GROUP_CHANNEL_ID, <PERSON><PERSON><PERSON>
from typing import Op<PERSON>
from datetime import datetime
import requests
import json
import asyncio

""" Follow instruction from `rezio/chainlit.md` in the root dir of the repository to use this file."""

# Store the selected channel for the current session
selected_channel: Optional[str] = None

# Your container's API endpoint or other enviroment like development or production wherever you want to test the chat.
API_ENDPOINT = "http://0.0.0.0:8000/v2/web/ai/message/"

# Store message queue and task
message_queue = asyncio.Queue()
process_task = None


@cl.on_chat_start
async def on_chat_start():
    global process_task
    # Set up the channel with fixed channel ID
    await setup_channel(CHANNEL_ID)

    # Start message processing task
    process_task = asyncio.create_task(process_messages())


async def process_messages():
    """Process messages from the queue within Chainlit context"""
    while True:
        try:
            msg_data = await message_queue.get()
            content = msg_data["content"]
            author = msg_data["author"]
            message_type = msg_data["message_type"]

            await cl.Message(content=content, author=author).send()
            # if message_type == "thinking":
            #     # Create and update a step manually
            #         step = cl.Step(name=content)
            #         step.output = content
            #         await step.send()
            #         # await asyncio.sleep(1)  # Show loading for minimum time
            #         # await step.update()
            #         # await step.remove()
            # else:
            #     await cl.Message(content=content, author=author).send()
            # print("PROCESSING MESSAGE >>>>>>>>>>>>>", msg_data)
            # await cl.Message(
            #     content=content,
            #     author=author
            # ).send()

            message_queue.task_done()
        except Exception as e:
            print(f"Error processing message from queue: {str(e)}")
        await asyncio.sleep(0.1)  # Small delay to prevent CPU overuse


async def setup_channel(channel_id: str):
    global selected_channel

    pubnub = PubNubManager.get_instance()
    selected_channel = channel_id

    # Store PubNub instance and channel in user session
    cl.user_session.set("pubnub", pubnub)
    cl.user_session.set("pubnub_channel", channel_id)

    # Fetch message history
    await cl.Message(
        content=f"Fetching message history for channel: {channel_id}"
    ).send()

    # Get message history for both channels
    history_messages = []
    for channel in [CHANNEL_ID]:
        messages = pubnub.get_message_history(channel)
        if messages:
            history_messages.extend(messages)

    # Sort messages by timetoken (timestamp)
    if history_messages:
        history_messages.sort(key=lambda x: x.timetoken)
        print(f"Sorted {len(history_messages)} total messages by timestamp")

    # Display message history
    if history_messages:
        await cl.Message(
            content=f"Found {len(history_messages)} previous messages"
        ).send()

        # Process and display each message
        for msg in history_messages:
            try:
                # Try different attributes based on the actual message structure
                if hasattr(msg, "message"):
                    message_data = msg.message
                elif hasattr(msg, "entry"):
                    message_data = msg.entry
                else:
                    print(f"Unknown message structure: {dir(msg)}")
                    continue

                # Parse message if it's a string
                if isinstance(message_data, str):
                    try:
                        message_data = json.loads(message_data)
                        print(f"Parsed JSON message: {message_data}")
                    except json.JSONDecodeError as e:
                        print(f"Failed to parse message as JSON: {e}")
                        # If not JSON, display as plain text from system
                        await cl.Message(content=message_data, author="System").send()
                        continue

                # Get message role and content
                msg_role = message_data.get("role")
                msg_content = message_data.get("content")

                print(f"Message {message_data}")

                if not msg_content:
                    continue

                # Determine the author based on role
                author = "Assistant" if msg_role == "assistant" else "User"

                # Display the message
                print(f"Displaying message from {author}: {msg_content}")
                await cl.Message(content=msg_content, author=author).send()

            except Exception as e:
                print(f"Error processing history message: {str(e)}")
                import traceback

                traceback.print_exc()
    else:
        await cl.Message(content="No message history found").send()
        print("No message history found for channels")

    # Subscribe to the channel
    async def message_callback(message):
        try:
            # Parse message if it's a string
            if isinstance(message, str):
                try:
                    message = json.loads(message)
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON message: {e}")
                    return

            # Only process messages for our channel
            if message.get("channel_id") != CHANNEL_ID:
                return

            # Get message role and content
            msg_role = message.get("role")
            msg_content = message.get("content")
            msg_type = message.get("message_type")

            if msg_role == "user":
                return

            print(f"Processing message - {message}")

            # Determine the author based on role
            author = "Assistant" if msg_role == "assistant" else "User"

            # Add message to queue instead of sending directly
            await message_queue.put(
                {"author": author, "content": msg_content, "message_type": msg_type}
            )

        except Exception as e:
            print(f"Error in message_callback: {str(e)}")
            print(f"Message that caused error: {message}")

    # Subscribe to both channel IDs
    channels_to_subscribe = [CHANNEL_ID, GROUP_CHANNEL_ID]

    pubnub.subscribe_to_channel(channels_to_subscribe, message_callback)


@cl.on_message
async def on_message(message: str):
    global selected_channel

    if not selected_channel:
        await cl.Message(
            content="Channel connection error. Please restart the chat."
        ).send()
        return

    # Extract message content if it's a Message object
    message_content = message.content if hasattr(message, "content") else str(message)

    # Prepare the message payload
    payload = {
        "pubnub_channel_id": CHANNEL_ID,
        "group_channel_id": GROUP_CHANNEL_ID,
        "content": message_content,
        "role": "user",
        "uuid": UUID,
        "type": "text",
    }

    # Send message to backend API
    try:
        response = requests.post(
            API_ENDPOINT, json=payload, headers={"Content-Type": "application/json"}
        )
        if response.status_code != 200:
            await cl.Message(
                content=f"Error sending message: {response.status_code}",
                author="System",
            ).send()
        else:
            # Log successful send
            print(
                f"Message sent successfully: {message_content}\nResponse: {response.text}"
            )
    except Exception as e:
        await cl.Message(
            content=f"Error sending message: {str(e)}", author="System"
        ).send()


@cl.on_stop
async def on_stop():
    global selected_channel, process_task

    if selected_channel:
        pubnub = cl.user_session.get("pubnub")
        pubnub.unsubscribe_from_channel(selected_channel)
        selected_channel = None

    # Cancel message processing task
    if process_task:
        process_task.cancel()
        try:
            await process_task
        except asyncio.CancelledError:
            pass
