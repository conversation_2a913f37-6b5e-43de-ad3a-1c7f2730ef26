from pubnub.pnconfiguration import PNConfiguration
from pubnub.pubnub import PubNub
from pubnub.callbacks import SubscribeCallback
from pubnub.enums import PNStatusCategory, PNReconnectionPolicy
from pubnub.models.consumer.objects_v2.common import MembershipIncludes
import os
from pathlib import Path
import dotenv
from typing import Dict, List, Optional
import asyncio
from pubnub.models.consumer.history import PNHistoryResult
import requests

# Get the project root directory
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent

# Load environment variables from the correct path
env_path = BASE_DIR / ".envs" / ".local" / ".django"
dotenv.load_dotenv(env_path)

CHANNEL_ID = "142_Agent_PdLs6YwQeEgVXBVlRkUFQ4tCkzr1_799de5e529e643b5b2323faee24c93ca"
UUID, GROUP_CHANNEL_ID = CHANNEL_ID.split("_", 1)
AGENT_ID = CHANNEL_ID.split("_")[2]


def update_ids(channel_id: str):
    global CHANNEL_ID, UUID, GROUP_CHANNEL_ID, AGENT_ID
    CHANNEL_ID = channel_id
    UUID, GROUP_CHANNEL_ID = CHANNEL_ID.split("_", 1)
    AGENT_ID = CHANNEL_ID.split("_")[2]


class PubNubManager:
    _instance = None

    def __init__(self):
        self.config = PNConfiguration()

        # Get PubNub keys from environment
        subscribe_key = os.getenv("PUBNUB_SUBSCRIBE_KEY")
        publish_key = os.getenv("PUBNUB_PUBLISH_KEY")

        print(
            f"PubNub keys from environment: subscribe_key={subscribe_key}, publish_key={publish_key}"
        )

        if not subscribe_key or not publish_key:
            raise ValueError("PubNub keys not found in environment variables!")

        self.config.subscribe_key = subscribe_key
        self.config.publish_key = publish_key
        self.config.user_id = UUID

        # Enable storage/playback for history
        self.config.enable_subscribe = True

        print(f"Initializing PubNub with subscribe_key: {subscribe_key}")

        self.pubnub = PubNub(self.config)
        self.active_channels: Dict[str, dict] = {}

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = PubNubManager()
        return cls._instance

    def get_agent_channels(self, agent_uuid):
        """
        Fetch all channels for an agent by UUID using the API endpoint

        Args:
            agent_uuid (str): The UUID of the agent

        Returns:
            List of channel objects
        """
        try:
            print(f"Fetching channels for agent: {agent_uuid}")

            # Use the API endpoint to get channels
            # Construct the API URL
            api_url = f"http://0.0.0.0:8000/web/ai/agent-channels/{agent_uuid}/"

            # Make the request
            response = requests.get(api_url)

            if response.status_code != 200:
                print(f"Error fetching channels: {response.status_code}")
                return []

            # Parse the response
            data = response.json()

            # Check for the correct response structure
            if "data" not in data or "channels" not in data["data"]:
                print(f"Invalid response format: {data}")
                return []

            # Extract channels from the response
            api_channels = data["data"]["channels"]

            # Format channels for the application
            channels = []
            for channel in api_channels:
                # Format the name to include user details
                name = f"{channel['web_user_name']} - {channel['web_user_phone']} - {channel['web_user_id']}"

                channel_info = {
                    "id": channel["channel_id"],
                    "name": name,
                    "group_id": channel["group_channel_id"],
                    "custom": {
                        "web_user_id": channel["web_user_id"],
                        "actor": channel["actor"],
                        "created_at": channel["created_at"],
                    },
                }
                channels.append(channel_info)

            print(f"Retrieved {len(channels)} channels for agent {agent_uuid}")
            return channels

        except Exception as e:
            print(f"Error fetching agent channels: {str(e)}")
            import traceback

            traceback.print_exc()
            return []

    def get_message_history(self, channel, count=100):
        """
        Fetch message history for a channel using fetch_messages API

        Args:
            channel (str): Channel ID to fetch history for
            count (int): Maximum number of messages to retrieve

        Returns:
            List of messages in chronological order (oldest first)
        """
        try:
            print(f"Fetching message history for channel: {channel}")

            # Make the fetch_messages request
            result = (
                self.pubnub.fetch_messages()
                .channels([channel])
                .maximum_per_channel(count)
                .include_meta(True)
                .include_uuid(True)
                .include_message_type(True)
                .include_custom_message_type(True)
                .sync()
            )

            if not result:
                print("No history result returned")
                return []

            # Debug the result structure
            print(f"Result type: {type(result)}")
            print(f"Result attributes: {dir(result)}")

            # Try to access the messages based on the actual structure
            messages = []

            # Check if result has a 'result' attribute (newer SDK versions)
            if hasattr(result, "result"):
                print("Using result.result structure")
                if (
                    hasattr(result.result, "channels")
                    and channel in result.result.channels
                ):
                    messages = result.result.channels[channel]
            # Check if result directly has 'channels' attribute (older SDK versions)
            elif hasattr(result, "channels") and channel in result.channels:
                print("Using result.channels structure")
                messages = result.channels[channel]
            else:
                print(f"Could not find messages in result structure: {result}")

            print(
                f"Retrieved {len(messages)} messages from history for channel {channel}"
            )

            # Debug: Print first message if available
            if messages and len(messages) > 0:
                print(f"First message sample: {messages[0]}")
                print(f"First message attributes: {dir(messages[0])}")

                # Try to access message content based on actual structure
                if hasattr(messages[0], "message"):
                    print(f"Message content: {messages[0].message}")
                elif hasattr(messages[0], "entry"):
                    print(f"Message content: {messages[0].entry}")

            # Return messages in chronological order (oldest first)
            return messages

        except Exception as e:
            print(f"Error fetching message history: {str(e)}")
            import traceback

            traceback.print_exc()
            return []

    def subscribe_to_channel(self, channels, callback):
        """Subscribe to channels using socket-based subscription"""
        if isinstance(channels, str):
            channels = [channels]

        class SocketSubscribeHandler(SubscribeCallback):
            def presence(self, pubnub, presence):
                print(f"Presence event: {presence.event} for UUID: {presence.uuid}")

            def status(self, pubnub, status):
                if status.category == PNStatusCategory.PNUnexpectedDisconnectCategory:
                    print(f"Unexpected disconnection from PubNub: {status.category}")
                elif status.category == PNStatusCategory.PNConnectedCategory:
                    print(f"Connected to PubNub channels: {channels}")
                elif status.category == PNStatusCategory.PNReconnectedCategory:
                    print("Reconnected to PubNub")
                elif status.category == PNStatusCategory.PNDecryptionErrorCategory:
                    print("Decryption error")
                else:
                    print(f"PubNub status changed: {status.category}")

            def message(self, pubnub, message):
                """Handle incoming messages"""
                print(
                    f"Received message on channel {message.channel}: {message.message}"
                )
                # Create new event loop for async callback
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(callback(message.message))
                finally:
                    loop.close()

        # Create and add the listener
        listener = SocketSubscribeHandler()
        self.pubnub.add_listener(listener)

        # Subscribe to the channels
        self.pubnub.subscribe().channels(channels).execute()

        # Store active channels
        for channel in channels:
            self.active_channels[channel] = {"listener": listener}

    def publish_message(self, channel, message):
        self.pubnub.publish().channel(channel).message(message).sync()

    def unsubscribe_from_channel(self, channels):
        """Unsubscribe from channels"""
        if isinstance(channels, str):
            channels = [channels]

        # Unsubscribe from the channels
        self.pubnub.unsubscribe().channels(channels).execute()

        # Remove listeners and clean up
        for channel in channels:
            if channel in self.active_channels:
                listener = self.active_channels[channel].get("listener")
                if listener:
                    self.pubnub.remove_listener(listener)
                del self.active_channels[channel]

    def add_active_channel(self, channel_id: str, metadata: dict):
        """Add a channel to active channels list with metadata"""
        self.active_channels[channel_id] = metadata

    def remove_active_channel(self, channel_id: str):
        """Remove a channel from active channels list"""
        if channel_id in self.active_channels:
            del self.active_channels[channel_id]

    def get_active_channels(self) -> List[Dict]:
        """Get list of active channels"""
        return [
            {"channel_id": channel_id} for channel_id in self.active_channels.keys()
        ]

    def get_channel_metadata(self, channel_id: str) -> Optional[dict]:
        """Get metadata for a specific channel"""
        return self.active_channels.get(channel_id)

    def get_channel_groups(self, user_id):
        """Get channel groups for a user"""
        try:
            # Get all channel groups the user is subscribed to
            result = (
                self.pubnub.objects()
                .get_all_channel_metadata()
                .include("custom", "channel", "channel.custom")
                .sync()
            )

            # Filter for groups that include the user ID
            groups = []
            if hasattr(result, "data"):
                for group in result.data:
                    if user_id in group.name:
                        groups.append(group.id)

            return groups
        except Exception as e:
            print(f"Error getting channel groups: {str(e)}")
            return []

    def get_channels_in_group(self, group_id):
        """Get channels in a channel group"""
        try:
            # Get all channels in the group
            result = (
                self.pubnub.channel_groups()
                .list_channels(channel_group=group_id)
                .sync()
            )

            channels = []
            if hasattr(result, "channels"):
                for channel in result.channels:
                    channels.append({"id": channel, "name": channel})

            return channels
        except Exception as e:
            print(f"Error getting channels in group: {str(e)}")
            return []
