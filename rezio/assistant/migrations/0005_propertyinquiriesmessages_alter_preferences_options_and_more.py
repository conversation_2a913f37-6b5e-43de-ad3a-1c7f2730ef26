# Generated by Django 4.1.7 on 2025-05-06 12:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0004_remove_preferences_messages_alter_preferences_buyer'),
    ]

    operations = [
        migrations.CreateModel(
            name='PropertyInquiriesMessages',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('raw_message', models.TextField()),
                ('slug', models.SlugField(max_length=300, unique=True)),
                ('from_name', models.CharField(blank=True, max_length=255, null=True)),
                ('from_number', models.CharField(blank=True, max_length=20, null=True)),
                ('channel_id', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
                ('meta', models.JSONField(default=dict)),
            ],
        ),
        migrations.AlterModelOptions(
            name='preferences',
            options={},
        ),
        migrations.CreateModel(
            name='PropertyInquiries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('intent', models.CharField(blank=True, max_length=255, null=True)),
                ('listing_type', models.CharField(blank=True, max_length=255, null=True)),
                ('data', models.JSONField(blank=True, default=dict, null=True)),
                ('property_type', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('inquiry_message', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='assistant.propertyinquiriesmessages')),
            ],
        ),
    ]
