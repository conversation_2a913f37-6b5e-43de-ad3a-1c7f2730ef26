# Generated by Django 4.1.7 on 2025-05-25 13:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0008_rename_data_propertyinquiries_expected_output_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatGroups',
            fields=[
                ('id', models.CharField(max_length=100, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('meta', models.JSONField(blank=True, default=dict, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Chat Groups',
            },
        ),
    ]
