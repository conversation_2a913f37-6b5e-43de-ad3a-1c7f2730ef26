# Generated by Django 4.1.7 on 2025-05-08 17:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0005_propertyinquiriesmessages_alter_preferences_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='QuestionsForBroker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.TextField()),
                ('agent_id', models.CharField(max_length=100)),
                ('message_id', models.CharField(max_length=100)),
                ('agent_response', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('property', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='properties.property')),
                ('pubnub_channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assistant.pubnubchannel')),
            ],
            options={
                'verbose_name_plural': 'Questions for Broker',
            },
        ),
    ]
