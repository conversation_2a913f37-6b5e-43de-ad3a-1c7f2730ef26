# Generated by Django 4.1.7 on 2025-05-20 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0007_chathistory'),
    ]

    operations = [
        migrations.RenameField(
            model_name='propertyinquiries',
            old_name='data',
            new_name='expected_output',
        ),
        migrations.RenameField(
            model_name='propertyinquiries',
            old_name='inquiry_message',
            new_name='input',
        ),
        migrations.RemoveField(
            model_name='propertyinquiries',
            name='intent',
        ),
        migrations.RemoveField(
            model_name='propertyinquiries',
            name='listing_type',
        ),
        migrations.RemoveField(
            model_name='propertyinquiries',
            name='property_type',
        ),
        migrations.AddField(
            model_name='propertyinquiries',
            name='comment',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='propertyinquiries',
            name='error',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name='propertyinquiries',
            name='output',
            field=models.<PERSON><PERSON><PERSON>ield(default=dict),
        ),
        migrations.AddField(
            model_name='propertyinquiries',
            name='valid',
            field=models.<PERSON>oleanField(blank=True, null=True),
        ),
    ]
