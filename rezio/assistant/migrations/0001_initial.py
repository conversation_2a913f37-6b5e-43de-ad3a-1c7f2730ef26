# Generated by Django 4.1.7 on 2025-02-27 12:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("user", "0054_temp_stag_migration"),
    ]

    operations = [
        migrations.CreateModel(
            name="Messages",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("messages", models.JSONField(blank=True, default=list, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "pubnub_channel_id",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "Messages",
            },
        ),
        migrations.CreateModel(
            name="Personas",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(blank=True, max_length=500, null=True)),
                ("prompt_template", models.TextField(blank=True, null=True)),
                (
                    "is_active",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "Personas",
            },
        ),
        migrations.CreateModel(
            name="PubNubWebUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=128)),
                (
                    "phone_number",
                    phonenumber_field.modelfields.PhoneNumberField(
                        max_length=128, region=None, unique=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="PubNubChannel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "actor",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Agent", "Agent"),
                            ("Buyer", "Buyer"),
                            ("Seller", "Seller"),
                        ],
                        default="Buyer",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "pubnub_channel_id",
                    models.CharField(
                        blank=True, max_length=500, null=True, unique=True
                    ),
                ),
                (
                    "group_channel_id",
                    models.CharField(
                        blank=True, max_length=500, null=True, unique=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "receiver_user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="receiver_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "receiver_user_role",
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_role",
                        to="user.role",
                    ),
                ),
                (
                    "web_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="channels",
                        to="assistant.pubnubwebuser",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Preferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("preferences", models.JSONField(blank=True, default=dict, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "buyer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="assistant.pubnubchannel",
                    ),
                ),
                (
                    "messages",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="assistant.messages",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Preferences",
            },
        ),
        migrations.AddField(
            model_name="messages",
            name="buyer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="buyer",
                to="assistant.pubnubchannel",
            ),
        ),
        migrations.CreateModel(
            name="Memory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("agent_type", models.CharField(blank=True, max_length=100, null=True)),
                ("memory", models.JSONField(blank=True, default=list, null=True)),
                (
                    "messages",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="assistant.messages",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Memory",
            },
        ),
    ]
