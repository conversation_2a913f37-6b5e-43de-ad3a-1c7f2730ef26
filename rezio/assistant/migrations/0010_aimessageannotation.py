# Generated by Django 4.1.7 on 2025-05-28 18:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0009_chatgroups'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIMessageAnnotation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('inquiry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='assistant.propertyinquiriesmessages')),
            ],
            options={
                'verbose_name_plural': 'AIMessageAnnotation',
            },
        ),
    ]
