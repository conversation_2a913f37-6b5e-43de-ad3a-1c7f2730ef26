# Generated by Django 4.1.7 on 2025-05-09 11:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('assistant', '0006_questionsforbroker'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('author', models.Char<PERSON>ield(max_length=100)),
                ('message', models.TextField()),
                ('message_id', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pubnub_channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='assistant.pubnubchannel')),
            ],
            options={
                'verbose_name_plural': 'Chat History',
            },
        ),
    ]
