from django.core.management.base import BaseCommand
import subprocess
import os
import sys


class Command(BaseCommand):
    help = "Runs the Chainlit server"

    def handle(self, *args, **options):
        port = os.getenv("CHAINLIT_PORT", "8005")
        chainlit_path = os.path.join(
            os.getcwd(), "rezio/assistant/playground/playground.py"
        )

        self.stdout.write(f"Current directory: {os.getcwd()}")
        self.stdout.write(f"Chainlit path: {chainlit_path}")
        self.stdout.write(f"Starting Chainlit server on port {port}...")

        try:
            # Get the virtual environment's Python executable
            python_executable = sys.executable
            chainlit_cmd = [
                python_executable,
                "-m",
                "chainlit",
                "run",
                chainlit_path,
                "--host",
                "0.0.0.0",
                "--port",
                port,
            ]

            self.stdout.write(f'Running command: {" ".join(chainlit_cmd)}')

            # Run with environment variables
            env = os.environ.copy()
            env["PYTHONPATH"] = os.getcwd()

            process = subprocess.Popen(
                chainlit_cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
            )

            # Print output in real-time
            while True:
                output = process.stdout.readline()
                if output == "" and process.poll() is not None:
                    break
                if output:
                    self.stdout.write(output.strip())

            # Print any errors
            stderr = process.stderr.read()
            if stderr:
                self.stderr.write(f"Errors: {stderr}")

            if process.returncode != 0:
                raise subprocess.CalledProcessError(process.returncode, chainlit_cmd)

        except Exception as e:
            self.stderr.write(f"Error running Chainlit: {str(e)}")
            self.stderr.write(f"Python executable: {sys.executable}")
            self.stderr.write(f'PATH: {os.environ.get("PATH", "Not set")}')
            raise
