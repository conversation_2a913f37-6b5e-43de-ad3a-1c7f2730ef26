# Generated by Django 4.1.7 on 2024-11-30 12:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("notifications", "0014_notification_related_property_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomFCMDevice",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Name"
                    ),
                ),
                (
                    "active",
                    models.BooleanField(
                        default=True,
                        help_text="Inactive devices will not be sent notifications",
                        verbose_name="Is active",
                    ),
                ),
                (
                    "date_created",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="Creation date"
                    ),
                ),
                (
                    "device_id",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        help_text="Unique device identifier",
                        max_length=255,
                        null=True,
                        verbose_name="Device ID",
                    ),
                ),
                (
                    "registration_id",
                    models.TextField(unique=True, verbose_name="Registration token"),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("ios", "ios"),
                            ("android", "android"),
                            ("web", "web"),
                        ],
                        max_length=10,
                    ),
                ),
                (
                    "device_model",
                    models.CharField(
                        blank=True,
                        help_text="Model name of the device (e.g., iPhone 12, Samsung Galaxy S21)",
                        max_length=100,
                    ),
                ),
                (
                    "os_version",
                    models.CharField(
                        blank=True,
                        help_text="Operating system version (e.g., iOS 15.0, Android 12)",
                        max_length=50,
                    ),
                ),
                (
                    "app_version",
                    models.CharField(
                        blank=True,
                        help_text="Version of the Rezio app installed",
                        max_length=50,
                    ),
                ),
                (
                    "app_build_number",
                    models.CharField(
                        blank=True,
                        help_text="Build number of the app for more precise version tracking",
                        max_length=50,
                    ),
                ),
                (
                    "supports_notification_channels",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the device supports notification channels (Android 8.0+)",
                    ),
                ),
                (
                    "notification_permission_status",
                    models.CharField(
                        choices=[
                            ("authorized", "Authorized"),
                            ("denied", "Denied"),
                            ("not_determined", "Not Determined"),
                        ],
                        default="not_determined",
                        help_text="Status of notification permissions on the device",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "last_active_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time the device was active",
                        null=True,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_query_name="fcmdevice",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "FCM device",
                "abstract": False,
            },
        ),
        migrations.AddIndex(
            model_name="customfcmdevice",
            index=models.Index(
                fields=["registration_id", "user"],
                name="notificatio_registr_1ba259_idx",
            ),
        ),
    ]
