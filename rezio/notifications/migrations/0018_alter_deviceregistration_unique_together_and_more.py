# Generated by Django 4.1.7 on 2024-12-03 09:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0039_menusettingsprivacypolicy"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("notifications", "0017_alter_customfcmdevice_options_and_more"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="deviceregistration",
            unique_together=set(),
        ),
        migrations.AddField(
            model_name="deviceregistration",
            name="role",
            field=models.ForeignKey(
                default=1, on_delete=django.db.models.deletion.CASCADE, to="user.role"
            ),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name="deviceregistration",
            unique_together={("user", "device_token", "role")},
        ),
    ]
