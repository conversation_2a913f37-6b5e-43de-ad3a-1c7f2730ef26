# Generated by Django 4.1.7 on 2024-12-03 09:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0039_menusettingsprivacypolicy"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("notifications", "0016_notification_push_notification_data"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="customfcmdevice",
            options={},
        ),
        migrations.RemoveIndex(
            model_name="customfcmdevice",
            name="notificatio_registr_1ba259_idx",
        ),
        migrations.AddField(
            model_name="customfcmdevice",
            name="role",
            field=models.ForeignKey(
                default=1, on_delete=django.db.models.deletion.CASCADE, to="user.role"
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="notification",
            name="sent_push_ts",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterUniqueTogether(
            name="customfcmdevice",
            unique_together={("user", "registration_id", "role")},
        ),
    ]
