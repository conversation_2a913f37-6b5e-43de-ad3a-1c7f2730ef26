# Generated by Django 4.1.7 on 2025-02-13 17:01

from django.db import migrations

from rezio.utils.constants import DJANGO_LOGGER_NAME

from django.db.models import F
from django.db import transaction
from rezio.notifications.models import NotificationType

import logging

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def add_notification_related_user(apps, schema_editor):
    Notification = apps.get_model("notifications", "Notification")
    # Reset all related_user fields
    Notification.objects.all().update(related_user=None)

    # First set of conditions (user_role_notifications)
    for notification in Notification.objects.filter(
        notification_type__in=[
            NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value,
            NotificationType.INVESTOR_ACCEPTED_REQUEST.value,
            NotificationType.CO_OWNER_REQUEST_ACCEPTED.value,
        ]
    ):
        notification.related_user = notification.user
        notification.related_user_role = notification.role
        notification.save()

    # Second set of conditions (property_creator_notifications)
    for notification in Notification.objects.filter(
        notification_type__in=[
            NotificationType.EXCLUSIVE_AGENT_INVITATION.value,
            NotificationType.SELECTIVE_AGENT_INVITATION.value,
            NotificationType.INVESTOR_REQUEST_ACCESS.value,
            NotificationType.AGENT_ACCEPTED_INVITATION.value,
            NotificationType.AGENT_DECLINED_INVITATION.value,
            NotificationType.INVESTOR_DECLINED_REQUEST.value,
            NotificationType.INVESTOR_ACCEPTED_REQUEST.value,
            NotificationType.PROPERTY_AGENT_REMOVED.value,
            NotificationType.COOWNER_INVITATION.value,
            NotificationType.CO_OWNER_REQUEST_ACCESS.value,
            NotificationType.COOWNER_ACCEPTED_INVITATION.value,
            NotificationType.COOWNER_DECLINED_INVITATION.value,
            NotificationType.CO_OWNER_REQUEST_DECLINED.value,
            NotificationType.COOWNER_REMOVED_FROM_PROPERTY.value,
        ]
    ):
        if notification.related_property:
            notification.related_user = notification.related_property.created_by
            notification.related_user_role = (
                notification.related_property.created_by_role
            )
            notification.save()

    # Handle PROPERTY_STATUS_CHANGE notifications
    for notification in Notification.objects.filter(
        notification_type=NotificationType.PROPERTY_STATUS_CHANGE.value
    ):
        if notification.role.name == "Investor":
            notification.related_user = notification.user
            notification.related_user_role = notification.role
        elif notification.related_property:
            notification.related_user = notification.related_property.created_by
            notification.related_user_role = (
                notification.related_property.created_by_role
            )
        notification.save()

    logger.info("Successfully updated notification related users and roles")


class Migration(migrations.Migration):
    dependencies = [
        ("notifications", "0021_alter_notification_notification_type"),
    ]

    operations = [
        migrations.RunPython(add_notification_related_user),
    ]
