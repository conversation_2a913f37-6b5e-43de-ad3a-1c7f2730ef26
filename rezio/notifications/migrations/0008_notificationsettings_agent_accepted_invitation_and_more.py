# Generated by Django 4.1.7 on 2024-11-27 08:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("notifications", "0007_alter_notification_notification_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="notificationsettings",
            name="agent_accepted_invitation",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="agent_declined_invitation",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="agent_invitation",
            field=models.<PERSON><PERSON>anField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="agent_removed_from_property",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="completion_state_change",
            field=models.BooleanField(default=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="notificationsettings",
            name="coowner_accepted_invitation",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="coowner_declined_invitation",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="coowner_invitation",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="coowner_removed_from_property",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="financial_update",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="property_added_to_portfolio",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="property_status_not_for_sale",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="notificationsettings",
            name="property_status_open_for_sale",
            field=models.BooleanField(default=True),
        ),
    ]
