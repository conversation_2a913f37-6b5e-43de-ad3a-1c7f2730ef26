# Generated by Django 4.1.7 on 2024-11-14 21:32

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DeviceRegistration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "device_token",
                    models.<PERSON>r<PERSON><PERSON>(
                        help_text="FCM token for the device", max_length=255
                    ),
                ),
                (
                    "platform",
                    models.CharField(
                        choices=[
                            ("ios", "iOS"),
                            ("android", "Android"),
                            ("web", "Web"),
                        ],
                        help_text="Operating system platform of the device",
                        max_length=10,
                    ),
                ),
                (
                    "device_model",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Model name of the device (e.g., iPhone 12, Samsung Galaxy S21)",
                        max_length=100,
                    ),
                ),
                (
                    "os_version",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True,
                        help_text="Operating system version (e.g., iOS 15.0, Android 12)",
                        max_length=50,
                    ),
                ),
                (
                    "app_version",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True,
                        help_text="Version of the Rezio app installed",
                        max_length=50,
                    ),
                ),
                (
                    "app_build_number",
                    models.Char<PERSON>ield(
                        blank=True,
                        help_text="Build number of the app for more precise version tracking",
                        max_length=50,
                    ),
                ),
                (
                    "supports_notification_channels",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the device supports notification channels (Android 8.0+)",
                    ),
                ),
                (
                    "notification_permission_status",
                    models.CharField(
                        choices=[
                            ("authorized", "Authorized"),
                            ("denied", "Denied"),
                            ("not_determined", "Not Determined"),
                        ],
                        default="not_determined",
                        help_text="Status of notification permissions on the device",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "last_active_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Last time the device was active",
                        null=True,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this device registration is currently active",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[("AGENT", "Agent"), ("INVESTOR", "Investor")],
                        help_text="Role for which this notification is intended",
                        max_length=10,
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("SECURITY_ALERT", "Security Alert"),
                            ("PROPERTY_APPROVAL", "Property Approval"),
                            ("CHAT_NOTIFICATION", "Chat Notification"),
                            ("BRN_EXPIRY", "BRN Expiry"),
                            ("PROPERTY_VIEWS", "Property Views"),
                            ("NEW_PROPERTY_ALERT", "New Property Alert"),
                            ("COLLABORATION_REQUEST", "Collaboration Request"),
                            ("TRANSACTION_STATUS", "Transaction Status"),
                            ("PROFILE_COMPLETION", "Profile Completion"),
                            ("WELCOME", "Welcome"),
                            ("OFFER_RECEIVED", "Offer Received"),
                            ("OFFER_ACCEPTED", "Offer Accepted"),
                            ("OFFER_REJECTED", "Offer Rejected"),
                            ("ASSISTANCE_NEEDED", "Assistance Needed"),
                            ("AI_TIMEOUT", "AI Timeout"),
                            ("FOLLOW", "Follow Notification"),
                            ("PROPERTY_LISTING", "Property Listing"),
                            ("UNREAD_MESSAGE", "Unread Message"),
                            ("PROPERTY_STATUS_CHANGE", "Property Status Change"),
                            ("PROPERTY_COOWNER_ADDED", "Property Coowner Added"),
                            ("PROPERTY_COOWNER_REMOVED", "Property Coowner Removed"),
                            ("PROPERTY_AGENT_ASSIGNED", "Property Agent Assigned"),
                            ("PROPERTY_AGENT_REMOVED", "Property Agent Removed"),
                            ("PROPERTY_PRICE_UPDATE", "Property Price Update"),
                            ("PROPERTY_DOCS_UPDATE", "Property Docs Update"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("URGENT", "Urgent"),
                            ("HIGH", "High"),
                            ("MEDIUM", "Medium"),
                            ("LOW", "Low"),
                        ],
                        default="LOW",
                        max_length=10,
                    ),
                ),
                ("message", models.TextField()),
                ("title", models.CharField(max_length=255)),
                ("read_status", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
                ("action_taken", models.BooleanField(default=False)),
                (
                    "action_response",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("scheduled_time", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("response_deadline", models.DateTimeField(blank=True, null=True)),
                ("action_taken_at", models.DateTimeField(blank=True, null=True)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("deep_link", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "redirect_url",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("sent_email", models.BooleanField(default=False)),
                ("sent_push", models.BooleanField(default=False)),
                ("sent_sms", models.BooleanField(default=False)),
                ("metadata", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationAuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("action", models.CharField(max_length=50)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("details", models.JSONField(default=dict)),
            ],
            options={
                "ordering": ["-timestamp"],
            },
        ),
        migrations.CreateModel(
            name="NotificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("pause_all_notifications", models.BooleanField(default=False)),
                ("email_notifications", models.BooleanField(default=True)),
                ("push_notifications", models.BooleanField(default=True)),
                ("sms_notifications", models.BooleanField(default=False)),
                ("property_views", models.BooleanField(default=True)),
                ("chat_notifications", models.BooleanField(default=True)),
                ("profile_completion", models.BooleanField(default=True)),
                ("security_alerts", models.BooleanField(default=True)),
                ("collaboration_requests", models.BooleanField(default=True)),
                ("property_approval_status", models.BooleanField(default=True)),
                ("transaction_status", models.BooleanField(default=True)),
                ("new_property_alert", models.BooleanField(default=True)),
                ("offer_notifications", models.BooleanField(default=True)),
                ("quiet_hours_start", models.TimeField(blank=True, null=True)),
                ("quiet_hours_end", models.TimeField(blank=True, null=True)),
                (
                    "notification_frequency",
                    models.CharField(
                        choices=[
                            ("IMMEDIATE", "Immediate"),
                            ("HOURLY", "Hourly Digest"),
                            ("DAILY", "Daily Digest"),
                        ],
                        default="IMMEDIATE",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "verbose_name": "Notification Settings",
                "verbose_name_plural": "Notification Settings",
            },
        ),
    ]
