# Generated by Django 5.1.6 on 2025-04-15 06:03

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("notifications", "0023_alter_notification_notification_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="notification",
            name="notification_type",
            field=models.CharField(
                choices=[
                    ("SECURITY_ALERT", "Security Alert"),
                    ("PROPERTY_APPROVAL", "Property Approval"),
                    ("CHAT_NOTIFICATION", "Chat Notification"),
                    ("BRN_EXPIRY", "BRN Expiry"),
                    ("PROPERTY_VIEWS", "Property Views"),
                    ("NEW_PROPERTY_ALERT", "New Property Alert"),
                    ("COLLABORATION_REQUEST", "Collaboration Request"),
                    ("TRANSACTION_STATUS", "Transaction Status"),
                    ("PROFILE_COMPLETION", "Profile Completion"),
                    ("WELCOME", "Welcome"),
                    ("PROFILE_PHOTO_UPDATE", "Profile Photo Update"),
                    ("OFFER_RECEIVED", "Offer Received"),
                    ("OFFER_ACCEPTED", "Offer Accepted"),
                    ("OFFER_REJECTED", "Offer Rejected"),
                    ("ASSISTANCE_NEEDED", "Assistance Needed"),
                    ("AI_TIMEOUT", "AI Timeout"),
                    ("FOLLOW", "Follow Notification"),
                    ("PROPERTY_LISTING", "Property Listing"),
                    ("UNREAD_MESSAGE", "Unread Message"),
                    ("PROPERTY_STATUS_CHANGE", "Property Status Change"),
                    ("PROPERTY_COOWNER_ADDED", "Property Co-owner Added"),
                    ("PROPERTY_COOWNER_REMOVED", "Property Co-owner Removed"),
                    ("PROPERTY_AGENT_ASSIGNED", "Property Agent Assigned"),
                    ("PROPERTY_AGENT_REMOVED", "Property Agent Removed"),
                    ("PROPERTY_PRICE_UPDATE", "Property Price Update"),
                    ("PROPERTY_DOCS_UPDATE", "Property Documents Update"),
                    ("PROPERTY_ARCHIVE", "Property Archive"),
                    ("SELECTIVE_AGENT_INVITATION", "Selective Agent Invitation"),
                    ("EXCLUSIVE_AGENT_INVITATION", "Exclusive Agent Invitation"),
                    ("AGENT_ACCEPTED_INVITATION", "Agent Accepted Invitation"),
                    ("AGENT_DECLINED_INVITATION", "Agent Declined Invitation"),
                    ("INVESTOR_REQUEST_ACCESS", "Investor Request Access"),
                    ("INVESTOR_ACCEPTED_REQUEST", "Investor Accepted Request"),
                    ("INVESTOR_DECLINED_REQUEST", "Investor Declined Request"),
                    ("COOWNER_INVITATION", "Co-owner Invitation"),
                    ("COOWNER_ACCEPTED_INVITATION", "Co-owner Accepted Invitation"),
                    ("COOWNER_DECLINED_INVITATION", "Co-owner Declined Invitation"),
                    ("COOWNER_REQUEST_ACCESS", "Co-owner Request Access"),
                    ("CO_OWNER_REQUEST_ACCEPTED", "Co-owner Request Accepted"),
                    ("CO_OWNER_REQUEST_DECLINED", "Co-owner Request Declined"),
                    ("PROPERTY_STATUS_OPEN_FOR_SALE", "Property Open for Sale"),
                    ("PROPERTY_STATUS_NOT_FOR_SALE", "Property Not for Sale"),
                    ("AGENT_REMOVED_FROM_PROPERTY", "Agent Removed from Property"),
                    ("COOWNER_REMOVED_FROM_PROPERTY", "Co-owner Removed from Property"),
                    ("PROPERTY_ADDED_TO_PORTFOLIO", "Property Added to Portfolio"),
                    ("FINANCIAL_UPDATE", "Financial Update"),
                    ("COMPLETION_STATE_CHANGE", "Completion State Change"),
                    ("AGENT_SELF_REMOVE", "Agent Self Remove"),
                    ("SUBSCRIPTION_CANCELLATION", "Subscription Cancellation"),
                    ("SIMILAR_TRANSACTION_UPDATE", "Similar Transaction Update"),
                ],
                max_length=50,
            ),
        ),
    ]
