# Generated by Django 4.1.7 on 2024-11-28 20:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("notifications", "0012_alter_notificationsettings_unique_together"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="notificationsettings",
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name="notificationsettings",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notification_settings",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
