# Generated by Django 4.1.7 on 2024-11-14 21:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        ("notifications", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="notificationsettings",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notification_settings",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="notificationauditlog",
            name="notification",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="audit_logs",
                to="notifications.notification",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="content_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="related_user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="related_notifications",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="deviceregistration",
            name="user",
            field=models.ForeignKey(
                help_text="User associated with this device registration",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="device_registrations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["user", "role", "notification_type"],
                name="notificatio_user_id_30dbd1_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["created_at"], name="notificatio_created_46ad24_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["read_status"], name="notificatio_read_st_6b443c_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="notification",
            index=models.Index(
                fields=["priority"], name="notificatio_priorit_bf8ea0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="deviceregistration",
            index=models.Index(
                fields=["user", "is_active"], name="notificatio_user_id_12c7f2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="deviceregistration",
            index=models.Index(
                fields=["device_token"], name="notificatio_device__f7eae9_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="deviceregistration",
            unique_together={("user", "device_token")},
        ),
    ]
