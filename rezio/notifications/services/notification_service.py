from typing import List, Optional, Dict, Any
from django.utils import timezone
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Q, QuerySet

from rezio.notifications.models import Notification, NotificationSettings
from rezio.user.models import User, Role
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """
    Service class for handling notification operations.

    Handles:
    - Notification retrieval and pagination
    - Notification status updates
    - Settings management
    """

    @staticmethod
    def get_user_notifications(
        user: User, page: int = 1, page_size: int = 20
    ) -> QuerySet:
        """
        Get paginated notifications for a user.

        Args:
            user (User): Target user
            page (int): Page number (default: 1)
            page_size (int): Items per page (default: 20)

        Returns:
            QuerySet: Paginated notifications
        """
        try:
            notifications = (
                Notification.objects.filter(
                    Q(user=user)
                    & Q(is_deleted=False)
                    & Q(expires_at__gt=timezone.now())
                )
                .select_related("user")
                .order_by("-created_at")
            )

            paginator = Paginator(notifications, page_size)
            return paginator.get_page(page)

        except Exception as e:
            logger.error(f"Error retrieving notifications: {str(e)}", exc_info=True)
            return Notification.objects.none()

    @staticmethod
    def mark_as_read(notification_id: int, user: User) -> bool:
        """
        Mark a notification as read.

        Args:
            notification_id (int): Notification ID
            user (User): User making the request

        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                notification = Notification.objects.select_for_update().get(
                    id=notification_id, user=user
                )
                notification.read_status = True
                notification.read_at = timezone.now()
                notification.save(update_fields=["read_status", "read_at"])

                logger.info(
                    f"Marked notification {notification_id} as read for user {user.id}"
                )
                return True

        except Notification.DoesNotExist:
            logger.warning(
                f"Notification {notification_id} not found for user {user.id}"
            )
            return False
        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}", exc_info=True)
            return False

    @staticmethod
    def bulk_mark_as_read(notification_ids: List[int], user: User) -> int:
        """
        Mark multiple notifications as read.

        Args:
            notification_ids (List[int]): List of notification IDs
            user (User): User making the request

        Returns:
            int: Number of updated notifications
        """
        try:
            with transaction.atomic():
                updated = Notification.objects.filter(
                    id__in=notification_ids, user=user
                ).update(
                    read_status=True, read_at=timezone.now(), updated_at=timezone.now()
                )

                logger.info(
                    f"Marked {updated} notifications as read for user {user.id}"
                )
                return updated

        except Exception as e:
            logger.error(f"Error in bulk mark as read: {str(e)}", exc_info=True)
            return 0

    @staticmethod
    def soft_delete(notification_id: int, user: User) -> bool:
        """
        Soft delete a notification.

        Args:
            notification_id (int): Notification ID
            user (User): User making the request

        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                notification = Notification.objects.select_for_update().get(
                    id=notification_id, user=user
                )
                notification.is_deleted = True
                notification.save(update_fields=["is_deleted", "updated_at"])

                logger.info(
                    f"Soft deleted notification {notification_id} for user {user.id}"
                )
                return True

        except Notification.DoesNotExist:
            logger.warning(
                f"Notification {notification_id} not found for user {user.id}"
            )
            return False
        except Exception as e:
            logger.error(f"Error soft deleting notification: {str(e)}", exc_info=True)
            return False

    @staticmethod
    def bulk_soft_delete(notification_ids: List[int], user: User) -> int:
        """
        Soft delete multiple notifications.

        Args:
            notification_ids (List[int]): List of notification IDs
            user (User): User making the request

        Returns:
            int: Number of deleted notifications
        """
        try:
            with transaction.atomic():
                updated = Notification.objects.filter(
                    id__in=notification_ids, user=user
                ).update(is_deleted=True, updated_at=timezone.now())

                logger.info(f"Soft deleted {updated} notifications for user {user.id}")
                return updated

        except Exception as e:
            logger.error(f"Error in bulk soft delete: {str(e)}", exc_info=True)
            return 0

    @staticmethod
    def get_notification_settings(user: User) -> NotificationSettings:
        """
        Get or create notification settings for a user.

        Args:
            user (User): Target user

        Returns:
            NotificationSettings: User's notification settings
        """
        try:
            settings, created = NotificationSettings.objects.get_or_create(
                user=user,
                defaults={
                    "email_notifications": True,
                    "push_notifications": True,
                    "sms_notifications": False,
                    "pause_all_notifications": False,
                    # Add default settings for notification types if necessary
                },
            )

            if created:
                logger.info(f"Created new notification settings for user {user.id}")

            return settings

        except Exception as e:
            logger.error(
                f"Error getting notification settings: {str(e)}", exc_info=True
            )
            raise

    @staticmethod
    def update_notification_settings(
        user: User, settings_data: Dict[str, Any]
    ) -> NotificationSettings:
        """
        Update notification settings for a user.

        Args:
            user (User): Target user
            settings_data (Dict[str, Any]): Settings to update

        Returns:
            NotificationSettings: Updated settings
        """
        try:
            with transaction.atomic():
                settings = NotificationService.get_notification_settings(user)

                for key, value in settings_data.items():
                    if hasattr(settings, key):
                        setattr(settings, key, value)
                    else:
                        logger.warning(f"NotificationSettings has no attribute '{key}'")

                settings.save()
                logger.info(f"Updated notification settings for user {user.id}")
                return settings

        except Exception as e:
            logger.error(
                f"Error updating notification settings: {str(e)}", exc_info=True
            )
            raise
