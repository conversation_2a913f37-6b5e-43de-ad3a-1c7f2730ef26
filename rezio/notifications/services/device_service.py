import datetime
from typing import List, Optional
from django.db import transaction
from rezio.notifications.models import DeviceRegistration
from rezio.user.models import User
import logging

logger = logging.getLogger(__name__)


class DeviceRegistrationService:
    """
    Service class for handling device registration operations.

    Handles:
    - Device registration and token management
    - Device token retrieval
    """

    @staticmethod
    def register_device(
        user: User,
        device_token: str,
        device_info: dict = None
    ) -> Optional[DeviceRegistration]:
        """
        Register or update a device token for a user.

        Args:
            user (User): Device owner
            device_token (str): Firebase device token
            device_info (dict, optional): Additional device information

        Returns:
            Optional[DeviceRegistration]: Registered device or None if failed
        """
        try:
            with transaction.atomic():
                defaults = {
                    'is_active': True,
                    'last_active_at': datetime.datetime.now()
                }

                if device_info:
                    defaults.update(device_info)

                device, created = DeviceRegistration.objects.update_or_create(
                    user=user,
                    device_token=device_token,
                    defaults=defaults
                )

                action = "Created" if created else "Updated"
                logger.info(
                    f"{action} device registration for user {user.id} "
                    f"with token {device_token[:10]}..."
                )
                return device

        except Exception as e:
            logger.error(f"Error registering device: {str(e)}", exc_info=True)
            return None

    @staticmethod
    def get_device_tokens(user_id: int) -> List[str]:
        """
        Get all active device tokens for a user.

        Args:
            user_id (int): User ID

        Returns:
            List[str]: List of device tokens
        """
        try:
            devices = DeviceRegistration.objects.filter(
                user_id=user_id,
                is_active=True
            )

            return list(devices.values_list('device_token', flat=True))

        except Exception as e:
            logger.error(f"Error retrieving device tokens: {str(e)}", exc_info=True)
            return []

    @staticmethod
    def deactivate_device(user: User, device_token: str) -> bool:
        """
        Deactivate a device registration.

        Args:
            user (User): Device owner
            device_token (str): Device token to deactivate

        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                updated = DeviceRegistration.objects.filter(
                    user=user,
                    device_token=device_token
                ).update(is_active=False)

                if updated:
                    logger.info(f"Deactivated device for user {user.id}")
                    return True
                return False

        except Exception as e:
            logger.error(f"Error deactivating device: {str(e)}", exc_info=True)
            return False
