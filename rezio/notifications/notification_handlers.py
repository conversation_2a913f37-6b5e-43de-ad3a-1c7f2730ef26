# rezio/notifications/notification_handlers.py

import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List

import firebase_admin.messaging as fcm
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from rezio.notifications.models import (
    Notification,
    NotificationSettings,
    NotificationType,
    DeviceRegistration,
)
from rezio.properties.models import (
    Property,
    AgentAssociatedProperty,
    PropertyCoOwner,
)
from rezio.properties.text_choices import (
    PropertyAgentType,
    OwnerIntentForProperty,
    UserRequestActions,
)
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.helper import get_profile_object_by_role
from rezio.user.models import User, Role
from rezio.user.utils import (
    get_agent_profile_object,
    get_investor_profile_object,
    get_agent_role_object,
    get_investor_role_object,
    get_role_object,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output

# Import Firebase Admin SDK (assuming it's installed and configured)
# import firebase_admin
# from firebase_admin import messaging

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class NotificationHandler:
    """
    Notification handler for the Rezio platform.
    Handles all notifications including welcome, property-related, and user management notifications.
    """

    def __init__(self):
        # Initialize any required clients or services here
        # For example, initialize Firebase Admin SDK
        # firebase_admin.initialize_app()
        pass

    # General Helper Methods
    def _serialize_metadata(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively traverse the metadata dictionary and convert datetime objects to ISO-formatted strings.
        """

        def serialize(obj):
            if isinstance(obj, dict):
                return {k: serialize(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [serialize(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj

        return serialize(metadata) if metadata else {}

    def _get_recipients_excluding_owner(self, property_obj: Property):
        """
        Retrieve all stakeholders for the property excluding the owner.
        Stakeholders include co-owners and agents.
        """
        stakeholders = list()

        # Add co-owner(s)
        co_owners = PropertyCoOwner.objects.filter(
            property=property_obj, is_associated=True
        )
        investor_role = get_investor_role_object()
        for co_owner in co_owners:
            if co_owner.co_owner and co_owner.co_owner.user:
                stakeholders.append(
                    {"user": co_owner.co_owner.user, "role": investor_role}
                )

        # Add agents
        agents = AgentAssociatedProperty.objects.filter(
            property=property_obj, is_associated=True
        )
        agent_role = get_agent_role_object()
        for agent_assoc in agents:
            if agent_assoc.agent_profile and agent_assoc.agent_profile.user:
                stakeholders.append(
                    {"user": agent_assoc.agent_profile.user, "role": agent_role}
                )

        # Exclude the property owner
        # if property_obj.owner and property_obj.owner.user:
        #     stakeholders.discard(property_obj.owner.user)

        logger.info(
            f"Recipients excluding owner for property ID {property_obj.id}: {[user.get('user').id for user in stakeholders]}"
        )
        return stakeholders

    @log_input_output
    def _create_and_send_notification(
        self,
        user: User,
        role: Role,
        notification_type: str,
        title: str,
        message: str,
        metadata: Dict[str, Any] = None,
        priority: str = "LOW",
        send_push: bool = True,
        related_user: User = None,
        related_property: Property = None,
        related_user_role: Role = None,
        push_notification_data: Dict[str, str] = None,
    ) -> Optional[List[Notification]]:
        """
        Create and send notifications to a list of users.
        """
        try:
            with transaction.atomic():
                logger.info(f"Processing user ID: {user.id}")

                if not user or not user.id:
                    logger.warning("Invalid user: User is None or has no ID.")

                # Fetch the user's roles
                user_roles = list(user.roles.values_list("name", flat=True))
                logger.info(f"User ID {user.id} roles: {user_roles}")

                if not role:
                    logger.warning(
                        f"User {user.id} has no role assigned. Skipping notification."
                    )

                # Determine the appropriate role for this notification
                allowed_roles = NotificationType.get_role_mapping().get(
                    notification_type, []
                )
                logger.info(f"Allowed roles for '{notification_type}': {allowed_roles}")

                role_name = role.name

                logger.info(f"Assigned role for notification: {role_name}")

                if not role_name:
                    logger.error(
                        f"Notification type '{notification_type}' is not allowed for user '{user.id}' with roles {user_roles}."
                    )

                # Get or create NotificationSettings for the user
                settings, _ = NotificationSettings.objects.get_or_create(
                    user=user, role=role
                )
                logger.info(
                    f"Notification settings for user {user.id}: Pause All - {settings.pause_all_notifications}"
                )

                if settings.pause_all_notifications:
                    logger.info(f"User {user.id} has paused all notifications.")

                # Check if the notification type is allowed based on user settings
                if not settings.is_notification_allowed(notification_type):
                    logger.info(
                        f"User {user.id} has disabled notifications for type '{notification_type}'."
                    )

                # Check if current time is within user's quiet hours
                if settings.is_in_quiet_hours():
                    logger.info(
                        f"User {user.id} is within quiet hours. Skipping notification."
                    )

                # Create metadata with fallback
                base_metadata = {
                    "notification_category": self._get_notification_category(
                        notification_type
                    ),
                    "priority_level": priority,
                    "timestamp": timezone.now().isoformat(),
                    "source_type": (
                        metadata.get("source_type", "system") if metadata else "system"
                    ),
                    "action_required": self._action_required(notification_type),
                    "expiry_time": self._get_expiry_time(notification_type),
                    "delivery_channels": self._get_delivery_channels(settings),
                    "user_role": role_name,
                    "notification_version": "2.0",
                }
                if metadata:
                    base_metadata.update(metadata)

                logger.info(f"Base metadata for notification: {base_metadata}")

                # Serialize metadata to handle datetime objects
                serialized_metadata = self._serialize_metadata(base_metadata)
                logger.info(
                    f"Serialized metadata for notification: {serialized_metadata}"
                )

                # Create the notification
                notification = Notification.objects.create(
                    user=user,
                    role=role,
                    notification_type=notification_type,
                    title=title,
                    message=message,
                    metadata=serialized_metadata,
                    priority=priority,
                    expires_at=self._get_expiry_time(notification_type),
                    related_user=related_user,
                    related_user_role=related_user_role,
                    related_property=related_property,
                    push_notification_data=push_notification_data,
                )
                logger.info(
                    f"Created notification ID {notification.id} for user {user.id} with type '{notification_type}'"
                )

                # Send push notification if enabled
                if send_push and settings.push_notifications:
                    if self._send_push_notification(notification, user, role):
                        logger.info(
                            f"Push notification sent for notification ID {notification.id}"
                        )
                    else:
                        logger.error(
                            f"Push notification failed for notification ID {notification.id}"
                        )

                logger.info(
                    f"Notification {notification.id} created and sent to user {user.id}."
                )
            return notification

        except Exception as e:
            logger.error(
                f"Failed to create/send notification for user {user.id}: {str(e)}",
                exc_info=True,
                extra={"notification_type": notification_type, "user_id": user.id},
            )

    def _get_notification_category(self, notification_type: str) -> str:
        """Get the category for a notification type."""
        category_mapping = {
            NotificationType.WELCOME.value: "ONBOARDING",
            NotificationType.PROPERTY_STATUS_CHANGE.value: "PROPERTY",
            NotificationType.SELECTIVE_AGENT_INVITATION.value: "AGENT",
            NotificationType.EXCLUSIVE_AGENT_INVITATION.value: "AGENT",
            NotificationType.AGENT_ACCEPTED_INVITATION.value: "AGENT",
            NotificationType.AGENT_DECLINED_INVITATION.value: "AGENT",
            NotificationType.COOWNER_INVITATION.value: "OWNERSHIP",
            NotificationType.COOWNER_ACCEPTED_INVITATION.value: "OWNERSHIP",
            NotificationType.COOWNER_DECLINED_INVITATION.value: "OWNERSHIP",
            NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value: "PROPERTY",
            NotificationType.PROPERTY_AGENT_ASSIGNED.value: "AGENT",
            NotificationType.PROPERTY_ARCHIVE.value: "PROPERTY",
            NotificationType.PROPERTY_COOWNER_ADDED.value: "OWNERSHIP",
            NotificationType.PROPERTY_AGENT_REMOVED.value: "AGENT",
            NotificationType.COOWNER_REMOVED_FROM_PROPERTY.value: "OWNERSHIP",
            NotificationType.PROFILE_COMPLETION.value: "REMINDER",
            NotificationType.FINANCIAL_UPDATE.value: "PROPERTY",
            NotificationType.COMPLETION_STATE_CHANGE.value: "PROPERTY",
            NotificationType.FOLLOW.value: "USER_INTERACTION",
            # ... other mappings as needed
        }
        return category_mapping.get(notification_type, "GENERAL")

    def _action_required(self, notification_type: str) -> bool:
        """Determine if a notification type requires user action."""
        action_required_types = {
            NotificationType.SELECTIVE_AGENT_INVITATION.value,
            NotificationType.EXCLUSIVE_AGENT_INVITATION.value,
            NotificationType.COOWNER_INVITATION.value,
            # Add other types that require action
        }
        return notification_type in action_required_types

    def _get_expiry_time(self, notification_type: str) -> Optional[timezone.datetime]:
        """Get expiry time for a notification type if applicable."""
        expiry_mapping = {
            NotificationType.SELECTIVE_AGENT_INVITATION.value: timezone.now()
            + timezone.timedelta(days=7),
            NotificationType.EXCLUSIVE_AGENT_INVITATION.value: timezone.now()
            + timezone.timedelta(days=7),
            NotificationType.COOWNER_INVITATION.value: timezone.now()
            + timezone.timedelta(days=7),
            # Add other types and their expiry times
        }
        return expiry_mapping.get(notification_type)

    def _get_delivery_channels(self, settings: NotificationSettings) -> List[str]:
        """Get enabled delivery channels based on user settings."""
        channels = []
        if settings.push_notifications:
            channels.append("PUSH")
        if settings.email_notifications:
            channels.append("EMAIL")
        if settings.sms_notifications:
            channels.append("SMS")
        logger.info(f"Delivery channels for user {settings.user.id}: {channels}")
        return channels

    @staticmethod
    def get_address(obj: Property, sell_request=True):
        address_parts = [
            obj.unit_number if sell_request else None,
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
        ]

        return ", ".join(filter(None, address_parts))

    @log_input_output
    def _send_push_notification(
        self, notification: Notification, user: User, role: Role
    ) -> bool:
        """Send push notification using Firebase Cloud Messaging (FCM)."""
        try:
            # Get the user's FCM token
            devices = DeviceRegistration.objects.filter(
                user=user, role=role, is_active=True
            ).order_by("-created_at")
            if devices.exists():
                for device in devices:
                    push_notification_data = notification.push_notification_data
                    push_notification_data["notification_id"] = str(notification.id)
                    push_notification_data["notification_type"] = (
                        notification.notification_type
                    )
                    push_notification_data["related_user"] = (
                        notification.related_user.id
                        if notification.related_user
                        else ""
                    )
                    push_notification_data["related_user_role"] = (
                        notification.related_user_role.name
                        if notification.related_user_role
                        else ""
                    )

                    android = fcm.AndroidConfig(
                        notification=fcm.AndroidNotification(
                            sound="default", channel_id="High Importance Notifications"
                        )
                    )
                    ios = fcm.APNSConfig(
                        payload=fcm.APNSPayload(aps=fcm.Aps(sound="default"))
                    )
                    message = fcm.Message(
                        notification=fcm.Notification(
                            title=notification.title, body=notification.message
                        ),
                        android=android,
                        apns=ios,
                        token=device.device_token,
                        data=push_notification_data,
                    )
                    logger.info(
                        "Sending push notification to device token: %s", message
                    )
                    try:
                        response = fcm.send(message)
                        logger.info(
                            f"Successfully sent {response} messages out of {device.device_token}"
                        )
                    except Exception as error:
                        if "Requested entity was not found" in str(error):
                            logger.info(f"Device token not found for user {user.id}")
                            device.delete()
                        logger.error(f"Failed to send notitication: {error}")
                        traceback.print_exc()

                notification.sent_push_ts = datetime.now()
                notification.save()
                return True
            else:
                logger.info(
                    f"Device details not found for to user {notification.user.id}"
                )
            return False
        except Exception as e:
            logger.error(
                f"Failed to send push notification to user {notification.user.id}: {str(e)}",
                exc_info=True,
                extra={"notification_id": notification.id},
            )
            return False

    # Specific Event Handlers

    def handle_welcome_notification(
        self, user: User, role: Role
    ) -> Optional[List[Notification]]:
        """Handle welcome notification for new users."""
        logger.info(f"Handling welcome notification for user ID {user.id}")
        try:
            # Determine the role to use for the notification
            allowed_roles = NotificationType.get_role_mapping().get(
                NotificationType.WELCOME.value, []
            )
            role_name = role.name

            logger.info(f"Assigned role for welcome notification: {role_name}")

            if not role_name:
                logger.warning(
                    f"User {user.id} has roles {role_name} which are not allowed for WELCOME notification."
                )
                return None

            # Get welcome message based on role
            welcome_messages = {
                "Agent": "Let's get started on your real estate journey.",
                "Investor": "Let's get started on your real estate journey.",
            }
            message = welcome_messages.get(role_name, "Welcome to Rezio!")

            metadata = {
                "notification_category": "ONBOARDING",
                "priority_level": "HIGH",
                "user_role": role_name,
            }
            push_notification_data = {
                "notification_category": "ONBOARDING",
                "priority_level": "HIGH",
                "user_role": role_name,
                "user_id": user.id,
                "notification_type": NotificationType.WELCOME.value,
            }

            notification = self._create_and_send_notification(
                user=user,
                role=role,
                notification_type=NotificationType.WELCOME.value,
                title="Welcome to Rezio!",
                message=message,
                metadata=metadata,
                priority="HIGH",
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(f"Welcome notification created for user ID {user.id}")
            else:
                logger.info(
                    f"No notification created for welcome notification to user ID {user.id}"
                )

            return notification

        except Exception as e:
            logger.error(
                f"Failed to handle welcome notification for user {user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_property_added_to_portfolio(
        self, user: User, property_obj: Property, role: Role
    ) -> Optional[List[Notification]]:
        """Handle notification when a property is added to a user's portfolio."""
        logger.info(
            f"Handling property added to portfolio for user ID {user.id} and property ID {property_obj.id}"
        )
        address = self.get_address(property_obj)
        try:
            portfolio_message = {
                "Agent": f"Property {address} has been successfully added to your portfolio. Let’s match it with the perfect buyer!",
                "Investor": f"Your Property {address} has been successfully added to your portfolio. Let’s match it with the perfect agent!",
            }
            message = portfolio_message.get(role.name, "Welcome to Rezio!")
            metadata = {
                "property_id": str(property_obj.id),
                "property_unit_number": property_obj.unit_number,
                "notification_category": "PROPERTY",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "PROPERTY",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value,
            }
            notification = self._create_and_send_notification(
                user=user,
                role=role,
                notification_type=NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value,
                title="Congratulations!",
                message=message,
                metadata=metadata,
                priority="MEDIUM",
                related_property=property_obj,
                push_notification_data=push_notification_data,
                related_user=user,
                related_user_role=role,
            )

            if notification:
                logger.info(
                    f"Property added to portfolio notification created for user ID {user.id}"
                )
            else:
                logger.info(
                    f"No notification created for property added to portfolio for user ID {user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle property addition notification for user {user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_agent_invitation(
        self,
        agent_user: User,
        property_obj: Property,
        invited_by: User,
        invited_by_role_name: str,
        agent_association_object_id: int,
    ) -> Optional[List[Notification]]:
        """Handle sending an invitation notification to an agent."""
        logger.info(
            f"Handling agent invitation for user ID {agent_user.id} to property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj, True)
            metadata = {
                "property_id": str(property_obj.id),
                "invited_by": str(invited_by.id),
                "invited_by_role": invited_by_role_name,
                "agent_association_object_id": agent_association_object_id,
                "action_required": True,
                "notification_category": "AGENT",
                "address": address,
            }
            investor_profile = get_investor_profile_object(invited_by)
            if investor_profile:
                if property_obj.agent_type == PropertyAgentType.SELECTIVE_AGENTS:
                    title = "Selective Agent Request"
                    notification_title = (
                        NotificationType.SELECTIVE_AGENT_INVITATION.value
                    )
                    message = f"{investor_profile.name} has chosen you to be their agent for {address}"
                elif property_obj.agent_type == PropertyAgentType.EXCLUSIVE_AGENT:
                    title = "Exclusive Agent Request"
                    notification_title = (
                        NotificationType.EXCLUSIVE_AGENT_INVITATION.value
                    )
                    message = f"{investor_profile.name} has chosen you to be their exclusive agent for {address}"
                else:
                    title = "Property Management Invitation"
                    notification_title = "AGENT_INVITATION"
                    message = f"{investor_profile.name} has chosen you to be their exclusive agent for {address}"
                push_notification_data = {
                    "notification_category": "AGENT",
                    "priority_level": "HIGH",
                    "property_id": str(property_obj.id),
                    "notification_type": notification_title,
                }
                notification = self._create_and_send_notification(
                    user=agent_user,
                    role=get_agent_role_object(),
                    notification_type=notification_title,
                    title=title,
                    message=message,
                    metadata=metadata,
                    priority="HIGH",
                    related_property=property_obj,
                    related_user=invited_by,
                    related_user_role=get_investor_role_object(),
                    push_notification_data=push_notification_data,
                )

                if notification:
                    logger.info(
                        f"Agent invitation notification created for user ID {agent_user.id}"
                    )
                else:
                    logger.info(
                        f"No notification created for agent invitation to user ID {agent_user.id}"
                    )

                return notification
            else:
                logger.info(
                    f"No notification created for agent invitation to user ID {agent_user.id}"
                )

        except Exception as e:
            logger.error(
                f"Failed to handle agent invitation for user {agent_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_investor_request(
        self,
        agent_user: User,
        property_obj: Property,
        invited_by: User,
        invited_by_role_name: str,
        agent_association_object_id: int,
    ) -> Optional[List[Notification]]:
        """Handle sending an invitation notification to an agent."""
        logger.info(
            f"Handling investor request for user ID {invited_by.id} to property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "invited_by": str(invited_by.id),
                "invited_by_role": invited_by_role_name,
                "agent_association_object_id": agent_association_object_id,
                "action_required": True,
                "notification_category": "INVESTOR",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "INVESTOR",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.INVESTOR_REQUEST_ACCESS.value,
            }
            agent_profile = get_agent_profile_object(invited_by)

            if agent_profile:
                title = "Access Request Received"
                message = f"{agent_profile.name} has requested access to {address}"
                notification = self._create_and_send_notification(
                    user=property_obj.owner.user,
                    role=get_investor_role_object(),
                    notification_type=NotificationType.INVESTOR_REQUEST_ACCESS.value,
                    title=title,
                    message=message,
                    metadata=metadata,
                    priority="HIGH",
                    related_user=invited_by,
                    related_property=property_obj,
                    related_user_role=get_agent_role_object(),
                    push_notification_data=push_notification_data,
                )

                if notification:
                    logger.info(
                        f"Investor request notification created for user ID {invited_by.id}"
                    )
                else:
                    logger.info(
                        f"No notification created for investor request to user ID {invited_by.id}"
                    )

                return notification
            else:
                logger.info(
                    f"No notification created for investor request to user ID {invited_by.id}"
                )

        except Exception as e:
            logger.error(
                f"Failed to handle investor request for user {invited_by.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_agent_response(
        self, agent_user: User, property_obj: Property, response: str, owner_user: User
    ) -> Optional[bool]:
        """Handle agent's response to an invitation."""
        logger.info(
            f"Handling agent response '{response}' from user ID {agent_user.id} for property ID {property_obj.id}"
        )
        try:
            if response not in ["accepted", "declined"]:
                logger.error(f"Invalid response: {response}")
                return None

            notification_type = (
                NotificationType.AGENT_ACCEPTED_INVITATION.value
                if response == "accepted"
                else NotificationType.AGENT_DECLINED_INVITATION.value
            )
            address = self.get_address(property_obj, True)

            agent_role = get_agent_role_object()
            metadata = {
                "property_id": str(property_obj.id),
                "responded_by": str(agent_user.id),
                "responded_by_role": agent_role.name,
                "response": response,
                "notification_category": "AGENT",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "AGENT",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": notification_type,
            }
            investor_role = get_investor_role_object()
            # send acceptance notification to investor
            exclusive = None
            if property_obj.agent_type == PropertyAgentType.EXCLUSIVE_AGENT:
                exclusive = True

            agent_profile = get_agent_profile_object(agent_user)
            if response == "accepted":
                message = f"Congratulations! The agent {agent_profile.name} has accepted your request to work{' exclusively' if exclusive else ''} on your property {address}."
                title = "Request Approved"
            else:
                message = f"The agent {agent_profile.name} has declined your request to work{' exclusively' if exclusive else ''} on your property {address}."
                title = "Request Declined"
            investor_role = get_investor_role_object()
            notification = self._create_and_send_notification(
                user=owner_user,
                role=investor_role,
                notification_type=notification_type,
                title=title,
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=owner_user,
                related_property=property_obj,
                related_user_role=investor_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Agent response notification created for owner user ID {owner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for agent response to owner user ID {owner_user.id}"
                )

            if response == "accepted":
                metadata = {
                    "property_id": str(property_obj.id),
                    "responded_by": str(agent_user.id),
                    "responded_by_role": agent_role.name,
                    "response": response,
                    "notification_category": "AGENT",
                    "address": address,
                }
                push_notification_data = {
                    "notification_category": "AGENT",
                    "priority_level": "HIGH",
                    "property_id": str(property_obj.id),
                    "notification_type": NotificationType.PROPERTY_ADDED_TO_PORTFOLIO,
                }
                title = "Congratulations!"
                message = f"Property {address} has been successfully added to your portfolio. Let’s match it with the perfect buyer!"
                notification = self._create_and_send_notification(
                    user=agent_user,
                    role=agent_role,
                    notification_type=NotificationType.PROPERTY_ADDED_TO_PORTFOLIO,
                    title=title,
                    message=message,
                    metadata=metadata,
                    priority="HIGH",
                    related_user=agent_user,
                    related_property=property_obj,
                    related_user_role=agent_role,
                    push_notification_data=push_notification_data,
                )

            if notification:
                logger.info(
                    f"Agent response notification created for aget user ID {agent_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for agent response to agent user ID {agent_user.id}"
                )

            return True

        except Exception as e:
            logger.error(
                f"Failed to handle agent response from user {agent_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_investor_response(
        self, owner_user: User, property_obj: Property, response: str, agent_user: User
    ):
        """Handle agent's response to an invitation."""
        logger.info(
            f"Handling investor response '{response}' from user ID {owner_user.id} for property ID {property_obj.id}"
        )
        try:
            if response not in ["accepted", "declined"]:
                logger.error(f"Invalid response: {response}")
                return None

            notification_type = (
                NotificationType.INVESTOR_ACCEPTED_REQUEST.value
                if response == "accepted"
                else NotificationType.INVESTOR_DECLINED_REQUEST.value
            )
            accepted = None
            if response == "accepted":
                text = "approved"
                agent_tile = "Request Approved"
                accepted = True
            else:
                text = "declined"
                agent_tile = "Request Declined"
            investor_role = get_investor_role_object()
            agent_role = get_agent_role_object()
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "responded_by": str(owner_user.id),
                "responded_by_role": investor_role.name,
                "response": response,
                "notification_category": "AGENT",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "AGENT",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": notification_type,
            }
            investor_profile = get_investor_profile_object(owner_user)
            agent_profile = get_agent_profile_object(agent_user)

            message = f"{'Congratulations!' if accepted else ''}The owner {investor_profile.name} of {address} has {text} your access request."
            notification = self._create_and_send_notification(
                user=agent_user,
                role=agent_role,
                notification_type=notification_type,
                title=agent_tile,
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=agent_user,
                related_property=property_obj,
                related_user_role=agent_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Investor response notification created for agent user ID {agent_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for investor response to agent user ID {agent_user.id}"
                )

            if response == "accepted":
                metadata = {
                    "property_id": str(property_obj.id),
                    "responded_by": str(owner_user.id),
                    "responded_by_role": investor_role.name,
                    "response": response,
                    "notification_category": "INVESTOR",
                    "address": address,
                }
                push_notification_data = {
                    "notification_category": "INVESTOR",
                    "priority_level": "HIGH",
                    "property_id": str(property_obj.id),
                    "notification_type": notification_type,
                }
                message = f"Congratulations! The agent {agent_profile.name} is now associated with your property {address} "
                # to owner property
                notification = self._create_and_send_notification(
                    user=owner_user,
                    role=investor_role,
                    notification_type=NotificationType.INVESTOR_ACCEPTED_REQUEST.value,
                    title=f"New Agent Associated",
                    message=message,
                    metadata=metadata,
                    priority="HIGH",
                    related_user=owner_user,
                    related_property=property_obj,
                    related_user_role=investor_role,
                    push_notification_data=push_notification_data,
                )

            if notification:
                logger.info(
                    f"Investor response notification created for agent user ID {agent_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for investor response to agent user ID {agent_user.id}"
                )

            return True

        except Exception as e:
            logger.error(
                f"Failed to handle investor response from user {owner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_agent_removal(
        self, agent_user: User, property_obj: Property, removed_by: User
    ) -> Optional[List[Notification]]:
        """Handle notification when an agent is removed from a property."""
        logger.info(
            f"inside handle_agent_removal ********************** : {agent_user}, {property_obj}, {removed_by}"
        )
        logger.info(
            f"Handling agent removal for user ID {agent_user.id} from property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "removed_by": str(removed_by.id),
                "notification_category": "AGENT",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "AGENT",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.PROPERTY_AGENT_REMOVED.value,
            }
            investor_profile = get_investor_profile_object(removed_by)
            message = f"{investor_profile.name} has revoked your access to their property {address} and it has been removed from your portfolio"
            notification = self._create_and_send_notification(
                user=agent_user,
                role=get_agent_role_object(),
                notification_type=NotificationType.PROPERTY_AGENT_REMOVED.value,
                title="Property Access Revoked",
                message=message,
                metadata=metadata,
                priority="LOW",
                related_user=removed_by,
                related_property=property_obj,
                related_user_role=get_investor_role_object(),
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Agent removal notification created for user ID {agent_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for agent removal to user ID {agent_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle agent removal for user {agent_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_coowner_invitation(
        self,
        co_owner_user: User,
        property_obj: Property,
        invited_by: User,
        property_co_owner_object_id: int,
    ) -> Optional[List[Notification]]:
        """Handle sending an invitation notification to a co-owner."""
        logger.info(
            f"Handling co-owner invitation for user ID {co_owner_user.id} to property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj, True)
            metadata = {
                "property_id": str(property_obj.id),
                "invited_by": str(invited_by.id),
                "action_required": True,
                "notification_category": "OWNERSHIP",
                "property_co_owner_object_id": property_co_owner_object_id,
                "address": address,
            }
            push_notification_data = {
                "notification_category": "OWNERSHIP",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.COOWNER_INVITATION.value,
            }
            investor_profile = get_investor_profile_object(invited_by)
            message = f"{investor_profile.name} has added you as a Co-Owner for the property, {address}!"
            notification = self._create_and_send_notification(
                user=co_owner_user,
                role=get_investor_role_object(),
                notification_type=NotificationType.COOWNER_INVITATION.value,
                title="Co-owner Request",
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=invited_by,
                related_property=property_obj,
                related_user_role=get_investor_role_object(),
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Co-owner invitation notification created for user ID {co_owner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for co-owner invitation to user ID {co_owner_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle co-owner invitation for user {co_owner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_co_owner_request_access(
        self,
        co_owner_user: User,
        property_obj: Property,
        invited_by: User,
        property_co_owner_object_id: int,
    ) -> Optional[List[Notification]]:
        """Handle sending an invitation notification to a co-owner."""
        logger.info(
            f"Handling co-owner invitation for user ID {co_owner_user.id} to property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj, True)
            metadata = {
                "property_id": str(property_obj.id),
                "invited_by": str(invited_by.id),
                "action_required": True,
                "notification_category": "OWNERSHIP",
                "property_co_owner_object_id": property_co_owner_object_id,
                "address": address,
            }
            push_notification_data = {
                "notification_category": "OWNERSHIP",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.CO_OWNER_REQUEST_ACCESS.value,
            }
            co_owner_profile = get_investor_profile_object(co_owner_user)
            message = f"{co_owner_profile.name} has request Co-ownership, {address}!"
            notification = self._create_and_send_notification(
                user=property_obj.owner.user,
                role=get_investor_role_object(),
                notification_type=NotificationType.CO_OWNER_REQUEST_ACCESS.value,
                title="Co-owner Request",
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=invited_by,
                related_property=property_obj,
                related_user_role=get_investor_role_object(),
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Co-owner invitation notification created for user ID {co_owner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for co-owner invitation to user ID {co_owner_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle co-owner invitation for user {co_owner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_coowner_response(
        self,
        coowner_user: User,
        property_obj: Property,
        response: str,
        owner_user: User,
    ) -> Optional[List[Notification]]:
        """Handle co-owner's response to an invitation."""
        logger.info(
            f"Handling co-owner response '{response}' from user ID {coowner_user.id} for property ID {property_obj.id}"
        )
        try:
            if response not in ["accepted", "declined"]:
                logger.error(f"Invalid response: {response}")
                return None

            notification_type = (
                NotificationType.COOWNER_ACCEPTED_INVITATION.value
                if response == "accepted"
                else NotificationType.COOWNER_DECLINED_INVITATION.value
            )
            investor_role = get_investor_role_object()
            co_owner_profile = get_investor_profile_object(coowner_user)
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "responded_by": str(coowner_user.id),
                "responded_role_name": investor_role.name,
                "response": response,
                "notification_category": "OWNERSHIP",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "OWNERSHIP",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": notification_type,
            }
            if response == "accepted":
                message = f"{co_owner_profile.name} has accepted your request to be a co-owner of the property, {address}. You can now collaborate together!"
                title = "Co-owner Added"
            else:
                message = f"{co_owner_profile.name} has declined your request to be a co-owner of the property, {address}"
                title = "Request Decline"
            notification = self._create_and_send_notification(
                user=owner_user,
                role=investor_role,
                notification_type=notification_type,
                title=title,
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=owner_user,
                related_property=property_obj,
                related_user_role=investor_role,
                push_notification_data=push_notification_data,
            )

            if response == "accepted":
                metadata = {
                    "property_id": str(property_obj.id),
                    "responded_by": str(coowner_user.id),
                    "responded_role_name": investor_role.name,
                    "response": response,
                    "notification_category": "OWNERSHIP",
                    "address": address,
                }
                push_notification_data = {
                    "notification_category": "OWNERSHIP",
                    "priority_level": "HIGH",
                    "property_id": str(property_obj.id),
                    "notification_type": NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value,
                }
                notification = self._create_and_send_notification(
                    user=coowner_user,
                    role=investor_role,
                    notification_type=NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value,
                    title="Congratulations!",
                    message=f"Your Property {address} has been successfully added to your portfolio. Let’s match it with the perfect agent!",
                    metadata=metadata,
                    priority="HIGH",
                    related_user=coowner_user,
                    related_property=property_obj,
                    related_user_role=investor_role,
                    push_notification_data=push_notification_data,
                )

            if notification:
                logger.info(
                    f"Co-owner response notification created for owner user ID {owner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for co-owner response to owner user ID {owner_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle co-owner response from user {coowner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_owner_response(
        self,
        owner_user: User,
        property_obj: Property,
        response: str,
        coowner_user: User,
        ownership_percentage: Optional[float] = None,
    ) -> bool:
        """Handle owner's response to a co-owner request."""
        logger.info(
            f"Handling owner response '{response}' from user ID {owner_user.id} for property ID {property_obj.id}"
        )
        try:
            if response not in ["accepted", "declined"]:
                raise ValueError("Invalid response. Expected 'accepted' or 'declined'.")

            notification_type = (
                NotificationType.CO_OWNER_REQUEST_ACCEPTED.value
                if response == "accepted"
                else NotificationType.CO_OWNER_REQUEST_DECLINED.value
            )
            address = self.get_address(property_obj, True)
            owner_profile = get_investor_profile_object(owner_user)
            if response == "accepted" and ownership_percentage:
                message = f"{owner_profile.name} has {response} your co-ownership request for the property at {ownership_percentage}% co-ownership share, {address}. And this property has been added to your portfolio"
                title = "Co-Ownership Request Accepted"
            else:
                message = f"{owner_profile.name} has {response} your co-ownership request for the property, {address}"
                title = "Co-Ownership Request Declined"
            investor_role = get_investor_role_object()

            metadata = {
                "property_id": str(property_obj.id),
                "responded_by": str(coowner_user.id),
                "responded_role": investor_role.name,
                "response": response,
                "notification_category": "OWNERSHIP",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "OWNERSHIP",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": notification_type,
            }
            notification = self._create_and_send_notification(
                user=coowner_user,
                role=investor_role,
                notification_type=notification_type,
                title=title,
                message=message,
                metadata=metadata,
                priority="HIGH",
                related_user=coowner_user,
                related_property=property_obj,
                related_user_role=investor_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Owner response notification created for user ID {coowner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for owner response to user ID {coowner_user.id}"
                )

            return True

        except Exception as e:
            logger.error(
                f"Failed to handle owner response from user {owner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_coowner_removal(
        self, coowner_user: User, property_obj: Property, removed_by: User
    ) -> Optional[List[Notification]]:
        """Handle notification when a co-owner is removed from a property."""
        logger.info(
            f"Handling co-owner removal for user ID {coowner_user.id} from property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "removed_by": str(removed_by.id),
                "notification_category": "OWNERSHIP",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "OWNERSHIP",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.COOWNER_REMOVED_FROM_PROPERTY.value,
            }
            investor_role = get_investor_role_object()
            owner_profile = get_investor_profile_object(removed_by)
            notification = self._create_and_send_notification(
                user=coowner_user,
                role=investor_role,
                notification_type=NotificationType.COOWNER_REMOVED_FROM_PROPERTY.value,
                title="Property Access Revoked",
                message=f"{owner_profile.name} has removed you as the co-owner of  their property {address} and it has been removed from your portfolio",
                metadata=metadata,
                priority="LOW",
                related_user=removed_by,
                related_property=property_obj,
                related_user_role=investor_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Co-owner removal notification created for user ID {coowner_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for co-owner removal to user ID {coowner_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle co-owner removal for user {coowner_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_property_status_change(
        self, property_obj: Property, old_status: str, new_status: str, changed_by: User
    ):
        """Handle notifications when property status changes."""
        logger.info(
            f"Handling property status change for property ID {property_obj.id} from '{old_status}' to '{new_status}'"
        )
        try:
            stakeholders = self._get_recipients_excluding_owner(property_obj)
            address = self.get_address(property_obj)

            metadata = {
                "property_id": str(property_obj.id),
                "old_status": old_status,
                "new_status": new_status,
                "changed_by": str(changed_by.id),
                "notification_category": "PROPERTY",
                "address": address,
            }
            investor_profile = get_investor_profile_object(changed_by)
            if new_status == OwnerIntentForProperty.NOT_FOR_SALE:
                state_change_messages = {
                    "Agent": f"{investor_profile.name} has changed the status of their property, {address}, to ‘Not for Sale,’ and it has been removed from your portfolio.",
                    "Investor": f"The status of {address} has been changed to ‘Not for Sale’ by {investor_profile.name}",
                }
            else:
                state_change_messages = {
                    "Agent": f"{investor_profile.name} has made {address} available for sale again. The property has been automatically added back to your portfolio.",
                    "Investor": f"The status of {address} has been changed to ‘For Sale’ by {investor_profile.name}",
                }

            agent_role = get_agent_role_object()
            investor_role = get_investor_role_object()
            notification_list = list()
            agents_to_exclude = list()
            push_notification_data = {
                "notification_category": "PROPERTY",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.PROPERTY_STATUS_CHANGE.value,
            }
            # for not for sale redirect for co-owner own portfolio
            # for sale agent own portfolio
            # for sale co-owner own portfolio
            for associated_user in stakeholders:
                associated_user_role_name = associated_user.get("role").name
                message = state_change_messages.get(associated_user_role_name)
                if associated_user_role_name == AGENT:
                    agents_to_exclude.append(associated_user.get("user"))
                if new_status == OwnerIntentForProperty.NOT_FOR_SALE:
                    if associated_user_role_name == INVESTOR:
                        related_user = associated_user.get("user")
                        related_user_role = investor_role
                    else:
                        related_user = changed_by
                        related_user_role = investor_role
                else:
                    related_user = associated_user.get("user")
                    related_user_role = associated_user.get("role")
                notification = self._create_and_send_notification(
                    user=associated_user.get("user"),
                    role=associated_user.get("role"),
                    notification_type=NotificationType.PROPERTY_STATUS_CHANGE.value,
                    title="Property Status Update",
                    message=message,
                    metadata=metadata,
                    priority="MEDIUM",
                    related_user=related_user,
                    related_property=property_obj,
                    related_user_role=related_user_role,
                    push_notification_data=push_notification_data,
                )
                notification_list.append(notification)
                if notification:
                    logger.info(
                        f"Property status change notification created for property ID {property_obj.id}"
                    )
                else:
                    logger.info(
                        f"No notification created for property status change for property ID {property_obj.id}"
                    )

            if (
                new_status != old_status
                and new_status != OwnerIntentForProperty.NOT_FOR_SALE
                and property_obj.agent_type == PropertyAgentType.OPEN_TO_ALL
            ):
                open_to_all_stakeholders = (
                    investor_profile.followers.filter(from_user_role=agent_role)
                    .select_related("from_user")
                    .exclude(from_user__in=agents_to_exclude)
                )
                for agent_followers in open_to_all_stakeholders:
                    notification = self._create_and_send_notification(
                        user=agent_followers.from_user,
                        role=agent_role,
                        notification_type=NotificationType.PROPERTY_STATUS_CHANGE.value,
                        title="New Opportunity",
                        message=f"{investor_profile.name} has listed {address}. It’s open to all agents. ",
                        metadata=metadata,
                        priority="MEDIUM",
                        related_user=changed_by,
                        related_property=property_obj,
                        related_user_role=investor_role,
                        push_notification_data=push_notification_data,
                    )
                    notification_list.append(notification)
                    if notification:
                        logger.info(
                            f"Property status change notification created for property ID {property_obj.id}"
                        )
                    else:
                        logger.info(
                            f"No notification created for property status change for property ID {property_obj.id}"
                        )

            return notification_list

        except Exception as e:
            logger.error(
                f"Failed to handle property status change for property ID {property_obj.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_property_open_to_all_change(
        self, property_obj: Property, old_status: str, new_status: str, changed_by: User
    ):
        """Handle notifications when property status changes."""
        logger.info(
            f"Handling property agent type change for property ID {property_obj.id} from '{old_status}' to '{new_status}'"
        )
        try:
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "old_status": old_status,
                "new_status": new_status,
                "changed_by": str(changed_by.id),
                "notification_category": "PROPERTY",
                "address": address,
            }
            investor_profile = get_investor_profile_object(changed_by)
            associated_agents = AgentAssociatedProperty.objects.filter(
                Q(is_associated=True)
                | Q(is_request_expired=False, action_status=UserRequestActions.PENDING)
            ).values_list("agent_profile__user__id", flat=True)
            agent_role = get_agent_role_object()
            investor_role = get_investor_role_object()
            notification_list = list()
            push_notification_data = {
                "notification_category": "PROPERTY",
                "priority_level": "HIGH",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.PROPERTY_STATUS_CHANGE.value,
            }

            if property_obj.agent_type == PropertyAgentType.OPEN_TO_ALL:
                open_to_all_stakeholders = (
                    investor_profile.followers.filter(from_user_role=agent_role)
                    .select_related("from_user")
                    .exclude(from_user__in=associated_agents)
                )
                for agent_followers in open_to_all_stakeholders:
                    notification = self._create_and_send_notification(
                        user=agent_followers.from_user,
                        role=agent_role,
                        notification_type=NotificationType.PROPERTY_STATUS_CHANGE.value,
                        title="New Opportunity",
                        message=f"{investor_profile.name} has listed {address}. It’s open to all agents. ",
                        metadata=metadata,
                        priority="MEDIUM",
                        related_user=changed_by,
                        related_property=property_obj,
                        related_user_role=investor_role,
                        push_notification_data=push_notification_data,
                    )
                    notification_list.append(notification)
                    if notification:
                        logger.info(
                            f"Property status change notification created for property ID {property_obj.id}"
                        )
                    else:
                        logger.info(
                            f"No notification created for property status change for property ID {property_obj.id}"
                        )

            return notification_list

        except Exception as e:
            logger.error(
                f"Failed to handle property status change for property ID {property_obj.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_property_archive(
        self, property_obj: Property, is_archived: bool, reason: str, archived_by: User
    ):
        """Handle notifications for property archive/unarchive."""
        action = "ARCHIVE" if is_archived else "UNARCHIVE"
        logger.info(
            f"Handling property {action.lower()} for property ID {property_obj.id} by user ID {archived_by.id}"
        )
        try:
            stakeholders = self._get_recipients_excluding_owner(property_obj)
            title = "Property Archived" if is_archived else "Property Unarchived"
            message = f"Property '{property_obj.unit_number}' has been {'archived' if is_archived else 'unarchived'}. Reason: {reason}."

            metadata = {
                "property_id": str(property_obj.id),
                "action": action,
                "reason": reason,
                "archived_by": str(archived_by.id),
                "notification_category": "PROPERTY",
            }

            notification_list = []
            for associated_user in stakeholders:
                notification = self._create_and_send_notification(
                    user=associated_user.get("user"),
                    role=associated_user.get("role"),
                    notification_type=NotificationType.PROPERTY_ARCHIVE.value,
                    title=title,
                    message=message,
                    metadata=metadata,
                    priority="HIGH",
                )
                notification_list.append(notification)

                if notification:
                    logger.info(
                        f"Property archive/unarchive notification created for property ID {property_obj.id}"
                    )
                else:
                    logger.info(
                        f"No notification created for property archive/unarchive for property ID {property_obj.id}"
                    )

            return notification_list

        except Exception as e:
            logger.error(
                f"Failed to handle property {action.lower()} for property ID {property_obj.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_follow_notification(
        self,
        follower_user: User,
        follower_role: Role,
        followed_user: User,
        followed_role: Role,
    ) -> Optional[List[Notification]]:
        """Handle notification when a user is followed by another user."""
        logger.info(
            f"Handling follow notification from user ID {follower_user.id} - {follower_role.name} to user ID {followed_user.id} - {followed_role.name}"
        )
        follower_profile = get_profile_object_by_role(follower_user, follower_role)
        try:
            metadata = {
                "user_id": str(follower_user.id),
                "user_role": follower_role.name,
                "notification_category": "USER_INTERACTION",
            }
            push_notification_data = {
                "notification_category": "USER_INTERACTION",
                "priority_level": "HIGH",
                "user_id": follower_user.id,
                "user_role": follower_role.name,
            }

            if follower_role.name == AGENT:
                message = f"{follower_profile.name} (Agent) followed you."
            else:
                message = f"{follower_profile.name} followed you"

            notification = self._create_and_send_notification(
                user=followed_user,
                role=followed_role,
                notification_type=NotificationType.FOLLOW.value,
                title="New Follower",
                message=message,
                metadata=metadata,
                priority="LOW",
                related_user=follower_user,
                related_user_role=follower_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Follow notification created for user ID {followed_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for follow notification to user ID {followed_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle follow notification for user {follower_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    def handle_agent_self_remove(
        self, agent_user: User, property_obj: Property, investor_user: User
    ) -> Optional[List[Notification]]:
        """
        Handle notification when an agent do self remove from a property.
        """
        logger.info(
            f"Handling agent self removal for user ID {agent_user.id} from property ID {property_obj.id}"
        )
        try:
            address = self.get_address(property_obj)
            metadata = {
                "property_id": str(property_obj.id),
                "notification_category": "AGENT",
                "address": address,
            }
            push_notification_data = {
                "notification_category": "AGENT",
                "priority_level": "LOW",
                "property_id": str(property_obj.id),
                "notification_type": NotificationType.AGENT_SELF_REMOVE.value,
            }

            agent_profile = get_agent_profile_object(agent_user)
            message = (
                f"{agent_profile.name} has removed their access to {address} and is no longer associated with "
                f"this property for {property_obj.owner_intent}"
            )
            investor_role = get_investor_role_object()
            notification = self._create_and_send_notification(
                user=investor_user,
                role=investor_role,
                notification_type=NotificationType.AGENT_SELF_REMOVE.value,
                title="Agent Removed",
                message=message,
                metadata=metadata,
                priority="LOW",
                related_user=investor_user,
                related_property=property_obj,
                related_user_role=investor_role,
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Agent self removal notification created for user ID {agent_user.id}"
                )
            else:
                logger.info(
                    f"No notification created for agent self removal to user ID {agent_user.id}"
                )

            return notification
        except Exception as e:
            logger.error(
                f"Failed to handle agent self removal for user {agent_user.id}: {str(e)}",
                exc_info=True,
            )
            return None

    @log_input_output
    def handle_subscription_cancellation(
        self, user: User, role_name, expiry_date
    ) -> Optional[List[Notification]]:
        """Handle welcome notification for new users."""
        logger.info(
            f"Handling subscription cancellation notification for user ID {user.id}"
        )
        try:
            # Determine the role to use for the notification
            allowed_roles = NotificationType.get_role_mapping().get(
                NotificationType.SUBSCRIPTION_CANCELLATION.value, []
            )

            logger.info(
                f"Assigned role for subscription cancellation notification: {role_name}"
            )

            if not role_name:
                logger.warning(
                    f"User {user.id} has roles {role_name} which are not allowed for SUBSCRIPTION_CANCELLATION notification."
                )
                return None

            welcome_messages = {
                "Agent": f"Properties beyond the basic limit will be locked after {expiry_date}. "
                "Subscribe again to keep your recent properties active.",
            }
            message = welcome_messages.get(
                role_name, "Properties beyond the basic limit will be locked."
            )

            metadata = {
                "notification_category": "SUBSCRIPTION_CANCELLATION",
                "priority_level": "HIGH",
                "user_role": role_name,
            }
            push_notification_data = {
                "notification_category": "SUBSCRIPTION_CANCELLATION",
                "priority_level": "HIGH",
                "user_role": role_name,
                "user_id": user.id,
                "notification_type": NotificationType.SUBSCRIPTION_CANCELLATION.value,
            }

            notification = self._create_and_send_notification(
                user=user,
                role=get_role_object(role_name),
                notification_type=NotificationType.SUBSCRIPTION_CANCELLATION.value,
                title="Premium Plan Cancelled",
                message=message,
                metadata=metadata,
                priority="HIGH",
                push_notification_data=push_notification_data,
            )

            if notification:
                logger.info(
                    f"Subscription cancellation notification created for user ID {user.id}"
                )
            else:
                logger.info(
                    f"No notification created for subscription cancellation notification to user ID {user.id}"
                )

            return notification

        except Exception as e:
            logger.error(
                f"Failed to handle subscription cancellation notification for user {user.id}: {str(e)}",
                exc_info=True,
            )
            return None
