# rezio/notifications/signals.py
import logging
from threading import Thread

from django.contrib.auth import get_user_model
from django.db.models.query import QuerySet
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import Signal
from django.dispatch import receiver

from rezio.notifications.models import (
    Notification,
    NotificationAuditLog,
)  # Added NotificationType
from rezio.notifications.notification_handlers import NotificationHandler
from rezio.properties.models import (
    Property,
    AgentAssociatedProperty,
    PropertyCoOwner,
)
from rezio.properties.text_choices import (
    PropertyPublishStatus,
    UserRequestActions,
    RequestType,
    PropertyAgentType,
    OwnerIntentForProperty,
    CoOwnerRequestType,
)
from rezio.user.constants import INVESTOR, AGENT, CANCEL_SUBSCRIPTION_PLAN_EVENT
from rezio.user.models import InvestorProfile, AgentProfile, Follow
from rezio.user.text_choices import (
    AgentSubscriptionPlanChoices,
)
from rezio.user.utils import get_agent_role_object, get_investor_role_object
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import log_input_output

logger = logging.getLogger(DJANGO_LOGGER_NAME)

notification_handler = NotificationHandler()

# **User Creation and Role Assignment Signals**

User = get_user_model()
subscription_cancellation_signal = Signal()


def run_in_thread(func, *args, **kwargs):
    """Helper function to run a function in a separate thread."""
    Thread(target=func, args=args, kwargs=kwargs, daemon=True).start()


@receiver(post_save, sender=AgentProfile)
def handle_agent_user_creation(sender, instance, created, **kwargs):
    """Handle welcome notification for agents."""
    if created:
        run_in_thread(
            notification_handler.handle_welcome_notification,
            instance.user,
            get_agent_role_object(),
        )


@receiver(post_save, sender=InvestorProfile)
def handle_investor_user_creation(sender, instance, created, **kwargs):
    """Handle welcome notification for investors."""
    if created:
        run_in_thread(
            notification_handler.handle_welcome_notification,
            instance.user,
            get_investor_role_object(),
        )


# **Property Signals**


@receiver(pre_save, sender=Property)
def store_original_property_state(sender, instance, **kwargs):
    """Store original property state for comparison."""
    if instance.pk:
        try:
            original = Property.objects.get(pk=instance.pk)
            instance._original_owner_intent = original.owner_intent
            instance._original_is_archived = original.is_archived
            instance._original_property_publish_status = (
                original.property_publish_status
            )
            instance._original_agent_type = original.agent_type
        except Property.DoesNotExist:
            logger.error(
                f"Property with id {instance.pk} does not exist. Cannot store original state."
            )
            instance._original_owner_intent = None
            instance._original_is_archived = None
            instance._original_property_publish_status = None
            instance._original_agent_type = None


@receiver(post_save, sender=Property)
def handle_property_changes(sender, instance, created, **kwargs):
    """Handle property-related notifications."""
    if not created:
        # Check for owner intent changes (property status change)
        if (
            instance.property_publish_status == PropertyPublishStatus.ADDED_TO_PORTFOLIO
            and hasattr(instance, "_original_owner_intent")
            and (
                (
                    instance._original_owner_intent
                    == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                    or instance._original_owner_intent
                    == OwnerIntentForProperty.OPEN_TO_BIDS
                )
                and instance.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
            )
            or (
                instance._original_owner_intent
                in [
                    OwnerIntentForProperty.NOT_FOR_SALE,
                    OwnerIntentForProperty.AVAILABLE_FOR_RENT,
                ]
                and (
                    instance.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                    or instance.owner_intent == OwnerIntentForProperty.OPEN_TO_BIDS
                )
            )
        ):
            if instance.owner and instance.owner_verified:
                run_in_thread(
                    notification_handler.handle_property_status_change,
                    property_obj=instance,
                    old_status=instance._original_owner_intent,
                    new_status=instance.owner_intent,
                    changed_by=instance.owner.user,
                )
            else:
                logger.warning(
                    f"Property {instance.id} status changed but 'updated_by' is not set. Skipping notification."
                )

        if (
            hasattr(instance, "_original_property_publish_status")
            and instance._original_property_publish_status
            != instance.property_publish_status
        ):
            if (
                instance.updated_by
                and instance.property_publish_status
                == PropertyPublishStatus.ADDED_TO_PORTFOLIO
            ):
                run_in_thread(
                    notification_handler.handle_property_added_to_portfolio,
                    user=instance.updated_by,
                    role=instance.updated_by_role,
                    property_obj=instance,
                )
            else:
                logger.warning(
                    f"Property {instance.id} archive status changed but 'updated_by' is not set. Skipping notification."
                )

        if (
            hasattr(instance, "_original_owner_intent")
            and instance._original_owner_intent == instance.owner_intent
            and hasattr(instance, "_original_agent_type")
            and instance._original_agent_type != instance.agent_type
            and instance.agent_type == PropertyAgentType.OPEN_TO_ALL
        ):
            if instance.owner and instance.owner_verified:
                run_in_thread(
                    notification_handler.handle_property_open_to_all_change,
                    property_obj=instance,
                    old_status=instance._original_agent_type,
                    new_status=instance.agent_type,
                    changed_by=instance.owner.user,
                )
            else:
                logger.warning(
                    f"Property {instance.id} agent type changed but 'updated_by' is not set. Skipping notification."
                )


# **Agent Association Signals**


def associated_agent_pre_save_helper(instance):
    try:
        original = AgentAssociatedProperty.objects.get(id=instance.id)
        instance._original_action_status = original.action_status
        instance._original_is_associated = original.is_associated
        instance._original_is_request_expired = original.is_request_expired
    except AgentAssociatedProperty.DoesNotExist:
        logger.error(
            f"AgentAssociatedProperty with id {instance.id} does not exist. Cannot store original state."
        )
        instance._original_request_accepted = None
        instance._original_is_associated = None
        instance._original_is_request_expired = None


@receiver(pre_save, sender=AgentAssociatedProperty)
def store_original_agent_association_state(sender, instance, **kwargs):
    """Store original agent association state."""
    is_queryset = isinstance(instance, QuerySet)
    if is_queryset:
        for _ in instance:
            associated_agent_pre_save_helper(_)
    else:
        associated_agent_pre_save_helper(instance)


@receiver(post_save, sender=AgentAssociatedProperty)
def handle_agent_association_changes(sender, instance, created=None, **kwargs):
    """Handle agent association notifications."""
    if created:
        # if not instance.request_accepted:
        if instance.created_by:
            created_by_role_name = instance.created_by_role.name
            if created_by_role_name == INVESTOR:
                run_in_thread(
                    notification_handler.handle_agent_invitation,
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    invited_by_role_name=created_by_role_name,
                    agent_association_object_id=instance.id,
                )
            elif created_by_role_name == AGENT:
                run_in_thread(
                    notification_handler.handle_investor_request,
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    invited_by_role_name=created_by_role_name,
                    agent_association_object_id=instance.id,
                )
        else:
            logger.warning(
                f"AgentAssociation {instance.id} created without 'created_by'. Skipping agent invitation notification."
            )
    else:
        # Check for acceptance or declination
        if (
            hasattr(instance, "_original_action_status")
            and instance._original_action_status != instance.action_status
        ):
            response = None
            if instance.action_status == UserRequestActions.ACCEPTED:
                response = "accepted"
            elif instance.action_status == UserRequestActions.DECLINED:
                response = "declined"
            if response:
                if instance.request_type == RequestType.AGENT_INVITE:
                    run_in_thread(
                        notification_handler.handle_agent_response,
                        agent_user=instance.agent_profile.user,
                        property_obj=instance.property,
                        response=response,
                        owner_user=instance.property.owner.user,
                    )
                elif instance.request_type == RequestType.INVESTOR_REQUEST:
                    run_in_thread(
                        notification_handler.handle_investor_response,
                        owner_user=instance.property.owner.user,
                        property_obj=instance.property,
                        response=response,
                        agent_user=instance.agent_profile.user,
                    )
                else:
                    logger.warning(
                        f"AgentAssociation {instance.id} status changed but property owner is not set. Skipping agent response notification."
                    )


def associated_agent_post_save_helper(
    instance, manual_trigger, action_taken_by=None, **kwargs
):
    if (
        instance.agent_profile
        and instance.property.owner
        and not instance.is_associated
        and instance.action_status == UserRequestActions.ACCEPTED
        and instance.is_request_expired
        and manual_trigger
    ):
        if kwargs.get("is_self_remove"):
            logger.info(
                f"inside self remove, associated_agent_post_save_helper ********************** : {instance}"
            )
            investors = [instance.property.owner.user]
            for co_owner in instance.property.propertycoowner_set.all():
                investors.append(co_owner.co_owner.user)
            for investor in investors:
                run_in_thread(
                    notification_handler.handle_agent_self_remove,
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    investor_user=investor,
                )
        else:
            logger.info(
                f"inside else, associated_agent_post_save_helper ********************** : {instance}"
            )
            if not action_taken_by:
                action_taken_by = instance.property.owner.user
            run_in_thread(
                notification_handler.handle_agent_removal,
                agent_user=instance.agent_profile.user,
                property_obj=instance.property,
                removed_by=action_taken_by,
            )
    else:
        logger.warning(
            f"AgentAssociation {instance.id} deleted but agent_profile or user is not set. Skipping agent removal notification."
        )


@receiver(post_save, sender=AgentAssociatedProperty)
def handle_agent_association_deletion(
    sender, instance, manual_trigger=None, action_taken_by=None, **kwargs
):
    """Handle agent removal notifications."""
    logger.info(f"handle_agent_associated_deletion **********************")
    if manual_trigger:
        is_queryset = isinstance(instance, QuerySet)
        is_list = isinstance(instance, list)
        if is_queryset or is_list:
            for _ in instance:
                associated_agent_post_save_helper(
                    _, manual_trigger, action_taken_by, **kwargs
                )
        else:
            associated_agent_post_save_helper(
                instance, manual_trigger, action_taken_by, **kwargs
            )


# **Co-owner Signals**


@receiver(pre_save, sender=PropertyCoOwner)
def store_original_coowner_state(sender, instance, **kwargs):
    """Store original co-owner state."""
    if instance.pk:
        try:
            original = PropertyCoOwner.objects.get(id=instance.pk)
            instance._original_ownership_percentage = original.ownership_percentage
            instance._original_action_status = original.action_status
            instance._original_is_associated = original.is_associated
            instance._original_unregistered_co_owner = original.unregistered_co_owner
            instance._original_co_owner = original.co_owner
        except PropertyCoOwner.DoesNotExist:
            logger.error(
                f"PropertyCoOwner with id {instance.pk} does not exist. Cannot store original state."
            )
            instance._original_ownership_percentage = None
            instance._original_action_status = None
            instance._original_is_associated = None
            instance._original_unregistered_co_owner = None
            instance._original_co_owner = None


@receiver(post_save, sender=PropertyCoOwner)
def handle_coowner_changes(sender, instance, created, **kwargs):
    """Handle registered co-owner notifications."""
    if created:
        if instance.co_owner and instance.co_owner.user:
            if instance.request_type == CoOwnerRequestType.CO_OWNER_INVITE:
                run_in_thread(
                    notification_handler.handle_coowner_invitation,
                    co_owner_user=instance.co_owner.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    property_co_owner_object_id=instance.id,
                )
            elif instance.request_type == CoOwnerRequestType.REQUEST_TO_OWNER:
                run_in_thread(
                    notification_handler.handle_co_owner_request_access,
                    co_owner_user=instance.co_owner.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    property_co_owner_object_id=instance.id,
                )
    else:
        if (
            hasattr(instance, "_original_unregistered_co_owner")
            and instance._original_unregistered_co_owner
            and not instance.unregistered_co_owner
            and instance.co_owner
        ):
            run_in_thread(
                notification_handler.handle_coowner_invitation,
                co_owner_user=instance.co_owner.user,
                property_obj=instance.property,
                invited_by=instance.created_by,
                property_co_owner_object_id=instance.id,
            )
        elif (
            hasattr(instance, "_original_action_status")
            and instance._original_action_status != instance.action_status
        ):
            # Handle co-owner acceptance or updates if applicable
            response = None
            if instance.action_status == UserRequestActions.ACCEPTED:
                response = "accepted"
            elif instance.action_status == UserRequestActions.DECLINED:
                response = "declined"
            if response:
                if instance.request_type == CoOwnerRequestType.CO_OWNER_INVITE:
                    run_in_thread(
                        notification_handler.handle_coowner_response,
                        coowner_user=instance.co_owner.user,
                        property_obj=instance.property,
                        response=response,
                        owner_user=instance.property.owner.user,
                    )
                elif instance.request_type == CoOwnerRequestType.REQUEST_TO_OWNER:
                    run_in_thread(
                        notification_handler.handle_owner_response,
                        owner_user=instance.property.owner.user,
                        property_obj=instance.property,
                        response=response,
                        coowner_user=instance.co_owner.user,
                        ownership_percentage=instance.ownership_percentage,
                    )


@receiver(post_delete, sender=PropertyCoOwner)
def handle_coowner_deletion(sender, instance, **kwargs):
    """Handle co-owner removal notifications."""
    if instance.co_owner and instance.co_owner.user:
        if (
            instance.property.owner
            and instance.property.owner.user
            and instance.is_associated
        ):
            run_in_thread(
                notification_handler.handle_coowner_removal,
                coowner_user=instance.co_owner.user,
                property_obj=instance.property,
                removed_by=instance.property.owner.user,
            )
        else:
            logger.warning(
                f"PropertyCoOwner {instance.id} deleted but property owner is not set. Skipping co-owner removal notification."
            )
    else:
        logger.warning(
            f"PropertyCoOwner {instance.id} deleted but co_owner or user is not set. Skipping co-owner removal notification."
        )


@receiver(post_save, sender=Follow)
def handle_following_changes(sender, instance, created, **kwargs):
    """Handle follow notifications."""
    if created:
        run_in_thread(
            notification_handler.handle_follow_notification,
            follower_user=instance.from_user,
            follower_role=instance.from_user_role,
            followed_user=instance.to_user,
            followed_role=instance.to_user_role,
        )
    else:
        # Handle co-owner acceptance or updates if applicable
        pass  # Implement if needed


@receiver(post_save, sender=Notification)
def handle_notification_audit_log(sender, instance, created, **kwargs):
    """Create an audit log entry whenever a notification is created."""
    if created:
        try:
            NotificationAuditLog.objects.create(
                notification=instance,
                action="created",
                details={"message": instance.message},
            )
            logger.info(f"Audit log created for notification {instance.id}.")
        except Exception as e:
            logger.error(
                f"Failed to create audit log for notification {instance.id}: {str(e)}",
                exc_info=True,
            )


@log_input_output
@receiver(subscription_cancellation_signal)
def notify_subscription_cancellation(
    sender,
    instance,
    old_subscription_status,
    new_subscription_status,
    expiry_date,
    **kwargs,
):
    """Handle subscription cancellation notifications."""
    if (
        old_subscription_status == AgentSubscriptionPlanChoices.PREMIUM
        and new_subscription_status == CANCEL_SUBSCRIPTION_PLAN_EVENT
    ):
        run_in_thread(
            notification_handler.handle_subscription_cancellation,
            user=instance.user,
            role_name=AGENT,
            expiry_date=expiry_date,
        )
