# rezio/notifications/signals.py

from django.db.models.signals import post_save, post_delete, pre_save, m2m_changed
from django.dispatch import receiver
from rezio.properties.models import (
    Property, AgentAssociatedProperty, PropertyCoOwner,
    UnregisteredCoOwner, UnregisteredOwner,
    PropertyFinancialDetails, PropertyCompletionState
)
from rezio.properties.text_choices import PropertyPublishStatus
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.models import User, InvestorProfile, AgentProfile
from rezio.notifications.notification_handlers import NotificationHandler
from rezio.notifications.models import Notification, NotificationAuditLog, NotificationType  # Added NotificationType
from django.utils import timezone
import logging

from rezio.user.utils import get_agent_role_object, get_investor_role_object

logger = logging.getLogger(__name__)

notification_handler = NotificationHandler()

# **User Creation and Role Assignment Signals**


# @receiver(post_save, sender=User)
# def handle_user_creation(sender, instance, created, **kwargs):
#     """Handle welcome notification for new users."""
#     if created:
#         if instance.roles.exists():
#             notification_handler.handle_welcome_notification(instance)
#         else:
#             logger.warning(f"New user {instance.id} has no roles assigned. Skipping welcome notification.")


@receiver(post_save, sender=AgentProfile)
def handle_user_creation(sender, instance, created, **kwargs):
    """Handle welcome notification for agents."""
    if created:
        notification_handler.handle_welcome_notification(instance, get_agent_role_object())


@receiver(post_save, sender=InvestorProfile)
def handle_user_creation(sender, instance, created, **kwargs):
    """Handle welcome notification for investors."""
    if created:
        notification_handler.handle_welcome_notification(instance, get_investor_role_object())


# @receiver(post_save, sender=User)
# def handle_user_creation(sender, instance, created, **kwargs):
#     """Handle welcome notification for new users."""
#     if created:
#         if instance.roles.exists():
#             notification_handler.handle_welcome_notification(instance)
#         else:
#             logger.warning(f"New user {instance.id} has no roles assigned. Skipping welcome notification.")
#
#
# @receiver(m2m_changed, sender=User.roles.through)
# def handle_user_role_assignment(sender, instance, action, **kwargs):
#     """Handle welcome notification when roles are assigned to a user."""
#     if action == 'post_add':
#         # Check if a welcome notification has already been sent
#         if not Notification.objects.filter(user=instance, notification_type=NotificationType.WELCOME).exists():
#             notification_handler.handle_welcome_notification(instance)


# **Property Signals**


@receiver(pre_save, sender=Property)
def store_original_property_state(sender, instance, **kwargs):
    """Store original property state for comparison."""
    if instance.pk:
        try:
            original = Property.objects.get(pk=instance.pk)
            instance._original_owner_intent = original.owner_intent
            instance._original_is_archived = original.is_archived
            instance._original_property_publish_status = original.property_publish_status
        except Property.DoesNotExist:
            logger.error(f"Property with id {instance.pk} does not exist. Cannot store original state.")
            instance._original_owner_intent = None
            instance._original_is_archived = None
            instance._original_property_publish_status = None


@receiver(post_save, sender=Property)
def handle_property_changes(sender, instance, created, **kwargs):
    """Handle property-related notifications."""
    if not created:
        # Check for owner intent changes (property status change)
        if hasattr(instance, '_original_owner_intent') and instance._original_owner_intent != instance.owner_intent:
            if instance.updated_by:
                notification_handler.handle_property_status_change(
                    property_obj=instance,
                    old_status=instance._original_owner_intent,
                    new_status=instance.owner_intent,
                    changed_by=instance.updated_by
                )
            else:
                logger.warning(f"Property {instance.id} status changed but 'updated_by' is not set. Skipping notification.")

        # Check for archive status changes
        if hasattr(instance, '_original_is_archived') and instance._original_is_archived != instance.is_archived:
            reason = "Property archived" if instance.is_archived else "Property unarchived"
            if instance.updated_by:
                notification_handler.handle_property_archive(
                    property_obj=instance,
                    is_archived=instance.is_archived,
                    reason=reason,
                    archived_by=instance.updated_by
                )
            else:
                logger.warning(f"Property {instance.id} archive status changed but 'updated_by' is not set. Skipping notification.")

        if hasattr(instance, '_original_property_publish_status') and instance._original_property_publish_status != instance.property_publish_status:
            if instance.updated_by and instance.property_publish_status == PropertyPublishStatus.ADDED_TO_PORTFOLIO:
                notification_handler.handle_property_added_to_portfolio(
                    user=instance.updated_by,
                    role=instance.updated_by_role,
                    property_obj=instance
                )
            else:
                logger.warning(f"Property {instance.id} archive status changed but 'updated_by' is not set. Skipping notification.")

# **Agent Association Signals**


@receiver(pre_save, sender=AgentAssociatedProperty)
def store_original_agent_association_state(sender, instance, **kwargs):
    """Store original agent association state."""
    print("Agent pre save signal")
    print(instance.__dict__)
    if instance.pk:
        try:
            original = AgentAssociatedProperty.objects.get(pk=instance.pk)
            instance._original_request_accepted = original.request_accepted
        except AgentAssociatedProperty.DoesNotExist:
            logger.error(f"AgentAssociatedProperty with id {instance.pk} does not exist. Cannot store original state.")
            instance._original_request_accepted = None


@receiver(post_save, sender=AgentAssociatedProperty)
def handle_agent_association_changes(sender, instance, created, **kwargs):
    """Handle agent association notifications."""
    print("Agent post save signal")
    if created:
        # if not instance.request_accepted:
        if instance.created_by:
            created_by_role_name = instance.created_by_role.name
            if created_by_role_name == INVESTOR:
                notification_handler.handle_agent_invitation(
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    invited_by_role_name=created_by_role_name,
                    agent_association_object_id=instance.id
                )
            elif created_by_role_name == AGENT:
                notification_handler.handle_investor_request(
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by,
                    invited_by_role_name=created_by_role_name,
                    agent_association_object_id=instance.id
                )
        else:
            logger.warning(f"AgentAssociation {instance.id} created without 'created_by'. Skipping agent invitation notification.")
        # elif instance.request_accepted:
        #     # If an existing agent's request is accepted, notify them
        #     if instance.property.owner and instance.property.owner.user:
        #         notification_handler.handle_agent_assignment(
        #             property_obj=instance.property,
        #             new_agent_user=instance.agent_profile.user,
        #             assigned_by=instance.property.owner.user
        #         )
        #     else:
        #         logger.warning(f"AgentAssociation {instance.id} accepted but property owner is not set. Skipping agent assignment notification.")
    else:
        # Check for acceptance or declination
        if hasattr(instance, '_original_request_accepted') and instance._original_request_accepted != instance.request_accepted:
            response = 'accepted' if instance.request_accepted else 'declined'
            if instance.property.owner and instance.property.owner.user:
                notification_handler.handle_agent_response(
                    agent_user=instance.agent_profile.user,
                    property_obj=instance.property,
                    response=response,
                    owner_user=instance.property.owner.user
                )
            else:
                logger.warning(f"AgentAssociation {instance.id} status changed but property owner is not set. Skipping agent response notification.")

@receiver(post_delete, sender=AgentAssociatedProperty)
def handle_agent_association_deletion(sender, instance, **kwargs):
    """Handle agent removal notifications."""
    if instance.agent_profile and instance.agent_profile.user:
        if instance.property.owner and instance.property.owner.user:
            notification_handler.handle_agent_removal(
                agent_user=instance.agent_profile.user,
                property_obj=instance.property,
                removed_by=instance.property.owner.user
            )
        else:
            logger.warning(f"AgentAssociation {instance.id} deleted but property owner is not set. Skipping agent removal notification.")
    else:
        logger.warning(f"AgentAssociation {instance.id} deleted but agent_profile or user is not set. Skipping agent removal notification.")

# **Co-owner Signals**

@receiver(pre_save, sender=PropertyCoOwner)
def store_original_coowner_state(sender, instance, **kwargs):
    """Store original co-owner state."""
    if instance.pk:
        try:
            original = PropertyCoOwner.objects.get(pk=instance.pk)
            instance._original_ownership_percentage = original.ownership_percentage
        except PropertyCoOwner.DoesNotExist:
            logger.error(f"PropertyCoOwner with id {instance.pk} does not exist. Cannot store original state.")
            instance._original_ownership_percentage = None

@receiver(post_save, sender=PropertyCoOwner)
def handle_coowner_changes(sender, instance, created, **kwargs):
    """Handle registered co-owner notifications."""
    if created:
        if instance.co_owner and instance.co_owner.user:
            if instance.created_by:
                notification_handler.handle_coowner_invitation(
                    coowner_user=instance.co_owner.user,
                    property_obj=instance.property,
                    invited_by=instance.created_by
                )
            else:
                logger.warning(f"PropertyCoOwner {instance.id} created without 'created_by'. Skipping co-owner invitation notification.")
    else:
        # Handle co-owner acceptance or updates if applicable
        pass  # Implement if needed

@receiver(post_delete, sender=PropertyCoOwner)
def handle_coowner_deletion(sender, instance, **kwargs):
    """Handle co-owner removal notifications."""
    if instance.co_owner and instance.co_owner.user:
        if instance.property.owner and instance.property.owner.user:
            notification_handler.handle_coowner_removal(
                coowner_user=instance.co_owner.user,
                property_obj=instance.property,
                removed_by=instance.property.owner.user
            )
        else:
            logger.warning(f"PropertyCoOwner {instance.id} deleted but property owner is not set. Skipping co-owner removal notification.")
    else:
        logger.warning(f"PropertyCoOwner {instance.id} deleted but co_owner or user is not set. Skipping co-owner removal notification.")

# **Financial Details Signals**

@receiver(pre_save, sender=PropertyFinancialDetails)
def store_original_financial_state(sender, instance, **kwargs):
    """Store original financial state."""
    if instance.pk:
        try:
            instance._original_financial_state = PropertyFinancialDetails.objects.get(pk=instance.pk)
        except PropertyFinancialDetails.DoesNotExist:
            logger.error(f"PropertyFinancialDetails with id {instance.pk} does not exist. Cannot store original state.")
            instance._original_financial_state = None

# @receiver(post_save, sender=PropertyFinancialDetails)
# def handle_financial_changes(sender, instance, created, **kwargs):
#     """Handle property financial updates."""
#     if not created and hasattr(instance, '_original_financial_state'):
#         original = instance._original_financial_state
#         changes = {}
#
#         # Check for financial field changes
#         financial_fields = ['asking_price', 'valuation', 'annual_rent']
#         for field in financial_fields:
#             old_value = getattr(original, field)
#             new_value = getattr(instance, field)
#             if old_value != new_value:
#                 changes[field] = {
#                     'old': old_value,
#                     'new': new_value
#                 }
#
#         if changes:
#             if instance.updated_by:
#                 notification_handler.handle_financial_update(
#                     property_obj=instance.property,
#                     update_type='FINANCIAL_UPDATE',
#                     old_value={k: v['old'] for k, v in changes.items()},
#                     new_value={k: v['new'] for k, v in changes.items()},
#                     updated_by=instance.updated_by
#                 )
#             else:
#                 logger.warning(f"PropertyFinancialDetails {instance.id} updated but 'updated_by' is not set. Skipping financial update notification.")

# **Property Completion State Signals**

# @receiver(pre_save, sender=PropertyCompletionState)
# def store_original_completion_state(sender, instance, **kwargs):
#     """Store original completion state."""
#     if instance.pk:
#         try:
#             original = PropertyCompletionState.objects.get(pk=instance.pk)
#             instance._original_state = original.state
#         except PropertyCompletionState.DoesNotExist:
#             logger.error(f"PropertyCompletionState with id {instance.pk} does not exist. Cannot store original state.")
#             instance._original_state = None

# @receiver(post_save, sender=PropertyCompletionState)
# def handle_completion_state_changes(sender, instance, created, **kwargs):
#     """Handle property completion state changes."""
#     if not created and hasattr(instance, '_original_state') and instance._original_state != instance.state:
#         if instance.updated_by:
#             notification_handler.handle_completion_state_change(
#                 property_obj=instance.property,
#                 old_state=instance._original_state,
#                 new_state=instance.state,
#                 completion_data={},  # Populate if needed
#                 changed_by=instance.updated_by
#             )
#         else:
#             logger.warning(f"PropertyCompletionState {instance.id} changed but 'updated_by' is not set. Skipping completion state change notification.")

# **Audit Log Signal Handler**


@receiver(post_save, sender=Notification)
def handle_notification_audit_log(sender, instance, created, **kwargs):
    """Create an audit log entry whenever a notification is created."""
    if created:
        try:
            NotificationAuditLog.objects.create(
                notification=instance,
                action='created',
                details={'message': instance.message}
            )
            logger.debug(f"Audit log created for notification {instance.id}.")
        except Exception as e:
            logger.error(f"Failed to create audit log for notification {instance.id}: {str(e)}", exc_info=True)
