# rezio/notifications/urls.py

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from rezio.notifications.views.notification_viewset import NotificationViewSet
from rezio.notifications.views.settings_viewset import NotificationSettingsViewSet
from rezio.notifications.views.device_registration_api_view import (
    DeviceRegistrationViewSet,
)
from rezio.notifications.views.base import BaseNotificationView


# Initialize the router
router = DefaultRouter()

# Register ViewSets with the router
router.register(r"notifications", NotificationViewSet, basename="notifications")
router.register(
    r"device-registrations", DeviceRegistrationViewSet, basename="device-registrations"
)

urlpatterns = [
    # Notification Settings - Uses standard viewset actions
    # path(
    #     'notification-settings/',
    #     NotificationSettingsViewSet.as_view({
    #         'get': 'retrieve',
    #         'patch': 'update',
    #     }),
    #     name='notification-settings'
    # ),
    path(
        "notification_list/", BaseNotificationView.as_view(), name="notification-list"
    ),
    path(
        "<int:pk>/notification_action/",
        NotificationViewSet.as_view({"post": "notification_action"}),
        name="notification-action",
    ),
    path(
        "bulk_action/",
        NotificationViewSet.as_view({"post": "bulk_action"}),
        name="bulk-action",
    ),
    path(
        "unread_count/",
        NotificationViewSet.as_view({"get": "unread_count"}),
        name="unread-count",
    ),
    # Include router URLs for NotificationViewSet and DeviceRegistrationViewSet
    path("", include(router.urls)),
]

app_name = "notifications"
