from django.db import models, transaction
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
import logging
from datetime import datetime, timedelta
from fcm_django.models import AbstractFCMDevice

from rezio.user.models import Role
from rezio.properties.models import Property


logger = logging.getLogger(__name__)

# Assuming you have the Role model imported from your user app


class NotificationType(models.TextChoices):
    """Enumeration of all possible notification types in the system."""

    # Agent-specific notifications
    SECURITY_ALERT = "SECURITY_ALERT", "Security Alert"
    PROPERTY_APPROVAL = "PROPERTY_APPROVAL", "Property Approval"
    CHAT_NOTIFICATION = "CHAT_NOTIFICATION", "Chat Notification"
    BRN_EXPIRY = "BRN_EXPIRY", "BRN Expiry"
    # Investor-specific notifications
    PROPERTY_VIEWS = "PROPERTY_VIEWS", "Property Views"
    NEW_PROPERTY_ALERT = "NEW_PROPERTY_ALERT", "New Property Alert"
    COLLABORATION_REQUEST = "COLLABORATION_REQUEST", "Collaboration Request"
    # Common notifications
    TRANSACTION_STATUS = "TRANSACTION_STATUS", "Transaction Status"
    PROFILE_COMPLETION = "PROFILE_COMPLETION", "Profile Completion"
    WELCOME = "WELCOME", "Welcome"
    PROFILE_PHOTO_UPDATE = "PROFILE_PHOTO_UPDATE", "Profile Photo Update"
    # Additional notifications from user stories
    OFFER_RECEIVED = "OFFER_RECEIVED", "Offer Received"
    OFFER_ACCEPTED = "OFFER_ACCEPTED", "Offer Accepted"
    OFFER_REJECTED = "OFFER_REJECTED", "Offer Rejected"
    ASSISTANCE_NEEDED = "ASSISTANCE_NEEDED", "Assistance Needed"
    AI_TIMEOUT = "AI_TIMEOUT", "AI Timeout"
    FOLLOW = "FOLLOW", "Follow Notification"
    PROPERTY_LISTING = "PROPERTY_LISTING", "Property Listing"
    UNREAD_MESSAGE = "UNREAD_MESSAGE", "Unread Message"
    PROPERTY_STATUS_CHANGE = "PROPERTY_STATUS_CHANGE", "Property Status Change"
    PROPERTY_COOWNER_ADDED = "PROPERTY_COOWNER_ADDED", "Property Co-owner Added"
    PROPERTY_COOWNER_REMOVED = "PROPERTY_COOWNER_REMOVED", "Property Co-owner Removed"
    PROPERTY_AGENT_ASSIGNED = "PROPERTY_AGENT_ASSIGNED", "Property Agent Assigned"
    PROPERTY_AGENT_REMOVED = "PROPERTY_AGENT_REMOVED", "Property Agent Removed"
    PROPERTY_PRICE_UPDATE = "PROPERTY_PRICE_UPDATE", "Property Price Update"
    PROPERTY_DOCS_UPDATE = "PROPERTY_DOCS_UPDATE", "Property Documents Update"
    PROPERTY_ARCHIVE = "PROPERTY_ARCHIVE", "Property Archive"
    # New notification types based on user stories
    SELECTIVE_AGENT_INVITATION = (
        "SELECTIVE_AGENT_INVITATION",
        "Selective Agent Invitation",
    )
    EXCLUSIVE_AGENT_INVITATION = (
        "EXCLUSIVE_AGENT_INVITATION",
        "Exclusive Agent Invitation",
    )
    AGENT_ACCEPTED_INVITATION = "AGENT_ACCEPTED_INVITATION", "Agent Accepted Invitation"
    AGENT_DECLINED_INVITATION = "AGENT_DECLINED_INVITATION", "Agent Declined Invitation"
    INVESTOR_REQUEST_ACCESS = "INVESTOR_REQUEST_ACCESS", "Investor Request Access"
    INVESTOR_ACCEPTED_REQUEST = "INVESTOR_ACCEPTED_REQUEST", "Investor Accepted Request"
    INVESTOR_DECLINED_REQUEST = "INVESTOR_DECLINED_REQUEST", "Investor Declined Request"
    COOWNER_INVITATION = "COOWNER_INVITATION", "Co-owner Invitation"
    COOWNER_ACCEPTED_INVITATION = (
        "COOWNER_ACCEPTED_INVITATION",
        "Co-owner Accepted Invitation",
    )
    COOWNER_DECLINED_INVITATION = (
        "COOWNER_DECLINED_INVITATION",
        "Co-owner Declined Invitation",
    )
    CO_OWNER_REQUEST_ACCESS = "COOWNER_REQUEST_ACCESS", "Co-owner Request Access"
    CO_OWNER_REQUEST_ACCEPTED = "CO_OWNER_REQUEST_ACCEPTED", "Co-owner Request Accepted"
    CO_OWNER_REQUEST_DECLINED = "CO_OWNER_REQUEST_DECLINED", "Co-owner Request Declined"
    PROPERTY_STATUS_OPEN_FOR_SALE = (
        "PROPERTY_STATUS_OPEN_FOR_SALE",
        "Property Open for Sale",
    )
    PROPERTY_STATUS_NOT_FOR_SALE = (
        "PROPERTY_STATUS_NOT_FOR_SALE",
        "Property Not for Sale",
    )
    AGENT_REMOVED_FROM_PROPERTY = (
        "AGENT_REMOVED_FROM_PROPERTY",
        "Agent Removed from Property",
    )
    COOWNER_REMOVED_FROM_PROPERTY = (
        "COOWNER_REMOVED_FROM_PROPERTY",
        "Co-owner Removed from Property",
    )
    PROPERTY_ADDED_TO_PORTFOLIO = (
        "PROPERTY_ADDED_TO_PORTFOLIO",
        "Property Added to Portfolio",
    )
    FINANCIAL_UPDATE = "FINANCIAL_UPDATE", "Financial Update"
    COMPLETION_STATE_CHANGE = (
        "COMPLETION_STATE_CHANGE",
        "Completion State Change",
    )  # Added missing label
    AGENT_SELF_REMOVE = "AGENT_SELF_REMOVE", "Agent Self Remove"
    SUBSCRIPTION_CANCELLATION = "SUBSCRIPTION_CANCELLATION", "Subscription Cancellation"
    SIMILAR_TRANSACTION_UPDATE = (
        "SIMILAR_TRANSACTION_UPDATE",
        "Similar Transaction Update",
    )
    SIMILAR_PROPERTY_SOLD = "SIMILAR_PROPERTY_SOLD", "Similar Property Sold"
    SIMILAR_PROPERTY_RENTED = "SIMILAR_PROPERTY_RENTED", "Similar Property Rented"

    @classmethod
    def get_role_mapping(cls):
        return {
            # Agent-specific notifications
            cls.SECURITY_ALERT: ["Agent"],
            cls.PROPERTY_APPROVAL: ["Agent"],
            cls.CHAT_NOTIFICATION: ["Agent", "Investor"],
            cls.BRN_EXPIRY: ["Agent"],
            cls.SUBSCRIPTION_CANCELLATION: ["Agent"],
            # Investor-specific notifications
            cls.PROPERTY_VIEWS: ["Investor"],
            cls.NEW_PROPERTY_ALERT: ["Investor"],
            cls.COLLABORATION_REQUEST: ["Investor"],
            # Common notifications
            cls.TRANSACTION_STATUS: ["Agent", "Investor"],
            cls.PROFILE_COMPLETION: ["Agent", "Investor"],
            cls.WELCOME: ["Agent", "Investor"],
            cls.PROFILE_PHOTO_UPDATE: ["Agent", "Investor"],
            # Additional notifications
            cls.OFFER_RECEIVED: ["Agent", "Investor"],
            cls.OFFER_ACCEPTED: ["Agent", "Investor"],
            cls.OFFER_REJECTED: ["Agent", "Investor"],
            cls.ASSISTANCE_NEEDED: ["Agent"],
            cls.AI_TIMEOUT: ["Agent"],
            cls.FOLLOW: ["Agent", "Investor"],
            cls.PROPERTY_LISTING: ["Agent", "Investor"],
            cls.UNREAD_MESSAGE: ["Agent", "Investor"],
            cls.PROPERTY_STATUS_CHANGE: ["Agent", "Investor", "Co-owner"],
            cls.PROPERTY_COOWNER_ADDED: ["Agent", "Investor", "Co-owner"],
            cls.PROPERTY_COOWNER_REMOVED: ["Agent", "Investor", "Co-owner"],
            cls.PROPERTY_AGENT_ASSIGNED: ["Agent", "Investor"],
            cls.PROPERTY_AGENT_REMOVED: ["Agent", "Investor"],
            cls.PROPERTY_PRICE_UPDATE: ["Agent", "Investor", "Co-owner"],
            cls.PROPERTY_DOCS_UPDATE: ["Agent", "Investor", "Co-owner"],
            # New notification types based on user stories
            cls.SELECTIVE_AGENT_INVITATION: ["Agent"],
            cls.EXCLUSIVE_AGENT_INVITATION: ["Agent"],
            cls.AGENT_ACCEPTED_INVITATION: ["Agent", "Investor"],
            cls.AGENT_DECLINED_INVITATION: ["Agent", "Investor"],
            cls.COOWNER_INVITATION: ["Investor", "Co-owner"],
            cls.COOWNER_ACCEPTED_INVITATION: ["Investor", "Co-owner"],
            cls.COOWNER_DECLINED_INVITATION: ["Investor", "Co-owner"],
            cls.PROPERTY_STATUS_OPEN_FOR_SALE: ["Agent", "Investor"],
            cls.PROPERTY_STATUS_NOT_FOR_SALE: ["Agent", "Investor"],
            cls.AGENT_REMOVED_FROM_PROPERTY: ["Agent"],
            cls.COOWNER_REMOVED_FROM_PROPERTY: ["Investor", "Co-owner"],
            cls.PROPERTY_ADDED_TO_PORTFOLIO: ["Agent", "Investor", "Co-owner"],
            cls.FINANCIAL_UPDATE: ["Agent", "Investor", "Co-owner"],
            cls.COMPLETION_STATE_CHANGE: ["Agent", "Investor", "Co-owner"],
            cls.INVESTOR_REQUEST_ACCESS: ["Investor"],
            cls.INVESTOR_ACCEPTED_REQUEST: ["Agent", "Investor"],
            cls.INVESTOR_DECLINED_REQUEST: ["Agent", "Investor"],
            cls.AGENT_SELF_REMOVE: ["Investor", "Co-owner"],
            cls.SIMILAR_TRANSACTION_UPDATE: ["Agent", "Investor", "Co-owner"],
            cls.SIMILAR_PROPERTY_SOLD: ["Agent", "Investor", "Co-owner"],
            cls.SIMILAR_PROPERTY_RENTED: ["Agent", "Investor", "Co-owner"],
        }


class NotificationSettings(models.Model):
    """Settings for controlling notification preferences per user"""

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="notification_settings",
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
    )
    # Global Settings
    pause_all_notifications = models.BooleanField(default=False)
    # Channel preferences
    email_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    # Type preferences
    property_views = models.BooleanField(default=True)
    chat_notifications = models.BooleanField(default=True)
    profile_completion = models.BooleanField(default=True)
    security_alerts = models.BooleanField(default=True)
    collaboration_requests = models.BooleanField(default=True)
    property_approval_status = models.BooleanField(default=True)
    transaction_status = models.BooleanField(default=True)
    new_property_alert = models.BooleanField(default=True)
    offer_notifications = models.BooleanField(default=True)
    # New notification type preferences based on user stories
    agent_invitation = models.BooleanField(default=True)
    agent_accepted_invitation = models.BooleanField(default=True)
    agent_declined_invitation = models.BooleanField(default=True)
    coowner_invitation = models.BooleanField(default=True)
    coowner_accepted_invitation = models.BooleanField(default=True)
    coowner_declined_invitation = models.BooleanField(default=True)
    property_status_open_for_sale = models.BooleanField(default=True)
    property_status_not_for_sale = models.BooleanField(default=True)
    agent_removed_from_property = models.BooleanField(default=True)
    coowner_removed_from_property = models.BooleanField(default=True)
    property_added_to_portfolio = models.BooleanField(default=True)
    financial_update = models.BooleanField(default=True)
    completion_state_change = models.BooleanField(default=True)
    investor_request = models.BooleanField(default=True)
    investor_accepted_request = models.BooleanField(default=True)
    investor_declined_request = models.BooleanField(default=True)
    agent_self_remove = models.BooleanField(default=True)
    # Add other new notification types as fields here if needed
    subscription_cancellation = models.BooleanField(
        default=True,
        help_text="Receive notifications when your subscription is cancelled",
    )
    # Time Window Settings
    quiet_hours_start = models.TimeField(null=True, blank=True)
    quiet_hours_end = models.TimeField(null=True, blank=True)
    # Frequency Settings
    notification_frequency = models.CharField(
        max_length=20,
        choices=[
            ("IMMEDIATE", "Immediate"),
            ("HOURLY", "Hourly Digest"),
            ("DAILY", "Daily Digest"),
        ],
        default="IMMEDIATE",
    )

    class Meta:
        verbose_name = "Notification Settings"
        verbose_name_plural = "Notification Settings"

    def __str__(self):
        return f"Notification Settings for {self.user}"

    def clean(self):
        """Validate notification settings."""
        super().clean()
        if self.quiet_hours_start and self.quiet_hours_end:
            now = timezone.now()
            start_dt = timezone.make_aware(
                datetime.combine(now.date(), self.quiet_hours_start)
            )
            end_dt = timezone.make_aware(
                datetime.combine(now.date(), self.quiet_hours_end)
            )

            if end_dt < start_dt:
                end_dt = end_dt + timedelta(days=1)

            if (end_dt - start_dt).total_seconds() < 60:
                raise ValidationError(
                    {"quiet_hours_end": "Quiet hours end time must be after start time"}
                )

    def is_notification_allowed(self, notification_type: str) -> bool:
        """Check if a notification type is allowed based on settings."""
        if self.pause_all_notifications:
            return False

        type_mapping = {
            "PROPERTY_VIEWS": self.property_views,
            "CHAT_NOTIFICATION": self.chat_notifications,
            "PROFILE_COMPLETION": self.profile_completion,
            "SECURITY_ALERT": self.security_alerts,
            "COLLABORATION_REQUEST": self.collaboration_requests,
            "PROPERTY_APPROVAL": self.property_approval_status,
            "TRANSACTION_STATUS": self.transaction_status,
            "NEW_PROPERTY_ALERT": self.new_property_alert,
            "OFFER_RECEIVED": self.offer_notifications,
            "OFFER_ACCEPTED": self.offer_notifications,
            "OFFER_REJECTED": self.offer_notifications,
            # New notification type mappings
            "AGENT_INVITATION": self.agent_invitation,
            "AGENT_ACCEPTED_INVITATION": self.agent_accepted_invitation,
            "AGENT_DECLINED_INVITATION": self.agent_declined_invitation,
            "COOWNER_INVITATION": self.coowner_invitation,
            "COOWNER_ACCEPTED_INVITATION": self.coowner_accepted_invitation,
            "COOWNER_DECLINED_INVITATION": self.coowner_declined_invitation,
            "PROPERTY_STATUS_OPEN_FOR_SALE": self.property_status_open_for_sale,
            "PROPERTY_STATUS_NOT_FOR_SALE": self.property_status_not_for_sale,
            "AGENT_REMOVED_FROM_PROPERTY": self.agent_removed_from_property,
            "COOWNER_REMOVED_FROM_PROPERTY": self.coowner_removed_from_property,
            "PROPERTY_ADDED_TO_PORTFOLIO": self.property_added_to_portfolio,
            "FINANCIAL_UPDATE": self.financial_update,
            "COMPLETION_STATE_CHANGE": self.completion_state_change,
            "INVESTOR_REQUEST": self.investor_request,
            "INVESTOR_ACCEPTED_REQUEST": self.investor_accepted_request,
            "INVESTOR_DECLINED_REQUEST": self.investor_declined_request,
            "AGENT_SELF_REMOVE": self.agent_self_remove,
            "SUBSCRIPTION_CANCELLATION": self.subscription_cancellation,
            # Add mappings for any additional new notification types here
        }
        return type_mapping.get(notification_type, True)

    def is_in_quiet_hours(self) -> bool:
        """Check if current time is within quiet hours."""
        if not (self.quiet_hours_start and self.quiet_hours_end):
            return False

        now = timezone.localtime()
        current_time = now.time()

        if self.quiet_hours_end < self.quiet_hours_start:
            # Quiet hours span across midnight
            return (
                current_time >= self.quiet_hours_start
                or current_time <= self.quiet_hours_end
            )
        else:
            return self.quiet_hours_start <= current_time <= self.quiet_hours_end


class UserRole(models.TextChoices):
    AGENT = "Agent", "Agent"
    INVESTOR = "Investor", "Investor"
    COOWNER = "Co-owner", "Co-owner"


class Notification(models.Model):
    """Model to store user notifications"""

    PRIORITY_CHOICES = (
        ("URGENT", "Urgent"),
        ("HIGH", "High"),
        ("MEDIUM", "Medium"),
        ("LOW", "Low"),
    )

    ACTION_RESPONSE_CHOICES = [
        ("accepted", "Accepted"),
        ("declined", "Declined"),
    ]

    # Core Fields
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="notifications"
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
    )
    notification_type = models.CharField(
        max_length=50, choices=NotificationType.choices
    )
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default="LOW")
    message = models.TextField()
    title = models.CharField(max_length=255)

    # Status Fields
    read_status = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    action_taken = models.BooleanField(default=False)
    action_response = models.CharField(
        max_length=20, choices=ACTION_RESPONSE_CHOICES, null=True, blank=True
    )

    # Timing Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    scheduled_time = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    response_deadline = models.DateTimeField(null=True, blank=True)
    action_taken_at = models.DateTimeField(null=True, blank=True)

    # Generic Foreign Key for related objects
    content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE, null=True, blank=True
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey("content_type", "object_id")

    # Direct Foreign Keys for common relations
    related_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="related_notifications",
    )
    related_user_role = models.ForeignKey(
        Role,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="related_notifications_role",
    )

    related_property = models.ForeignKey(
        Property,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="related_notifications_property",
    )

    # Routing and Navigation
    deep_link = models.CharField(max_length=255, null=True, blank=True)
    redirect_url = models.CharField(max_length=255, null=True, blank=True)

    # Delivery Status
    sent_email = models.BooleanField(default=False)
    sent_push = models.BooleanField(default=False)
    sent_push_ts = models.DateTimeField(null=True, blank=True)
    sent_sms = models.BooleanField(default=False)

    # Additional Metadata
    metadata = models.JSONField(default=dict, blank=True)
    push_notification_data = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["user", "role", "notification_type"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["read_status"]),
            models.Index(fields=["priority"]),
        ]

    def __str__(self):
        return f"{self.id} - Notification for {self.user} ({self.role}): {self.message[:50]}..."

    def clean(self):
        """Validate notification."""
        super().clean()

        # Validate notification type and role
        allowed_roles = NotificationType.get_role_mapping().get(
            self.notification_type, []
        )
        if self.role not in allowed_roles:
            raise ValidationError(
                {
                    "role": f"Notification type '{self.notification_type}' is not valid for role '{self.role}'"
                }
            )

        # Check if user has the role
        if self.user_id:
            user_roles = self.user.roles.values_list("name", flat=True)
            if self.role not in user_roles:
                raise ValidationError(
                    {"role": f"User does not have the role: {self.role}"}
                )

        # Validate action_response if action_taken is True
        if self.action_taken and not self.action_response:
            raise ValidationError(
                {
                    "action_response": "Action response is required when action has been taken."
                }
            )

        # Validate deadline and expiry
        if self.response_deadline and self.expires_at:
            if self.response_deadline > self.expires_at:
                raise ValidationError(
                    {
                        "response_deadline": "Response deadline cannot be after expiry time"
                    }
                )

    @classmethod
    def get_notifications_for_role(cls, user, role):
        """Get all notifications for a user's specific role"""
        return cls.objects.filter(user=user, role=role, is_deleted=False).order_by(
            "-created_at"
        )

    def mark_as_read(self):
        """Mark notification as read and create audit log."""
        with transaction.atomic():
            self.read_status = True
            self.save(update_fields=["read_status"])

            NotificationAuditLog.objects.create(
                notification=self,
                action="marked_read",
                details={"marked_read_at": timezone.now().isoformat()},
            )

    def take_action(self, response):
        """Take action on notification with audit logging."""
        if response not in dict(self.ACTION_RESPONSE_CHOICES):
            raise ValidationError("Invalid action response.")

        with transaction.atomic():
            if self.action_taken:
                raise ValidationError(
                    "Action has already been taken on this notification"
                )

            self.action_taken = True
            self.action_response = response
            self.action_taken_at = timezone.now()
            self.save(
                update_fields=["action_taken", "action_response", "action_taken_at"]
            )

            NotificationAuditLog.objects.create(
                notification=self,
                action="action_taken",
                details={"response": response, "taken_at": timezone.now().isoformat()},
            )

    def delete(self, *args, **kwargs):
        """Override delete to create audit log."""
        with transaction.atomic():
            NotificationAuditLog.objects.create(
                notification=self,
                action="deleted",
                details={"deleted_at": timezone.now().isoformat()},
            )
            super().delete(*args, **kwargs)

    def is_expired(self):
        """Check if notification is expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def is_urgent(self):
        """Check if notification is urgent."""
        return self.priority in ["URGENT", "HIGH"]

    def requires_immediate_attention(self):
        """Check if notification requires immediate attention."""
        return self.is_urgent() and not self.read_status and not self.action_taken

    def send_notification(self) -> bool:
        """Send notification through configured channels."""
        try:
            settings = self.user.notification_settings

            if settings.is_in_quiet_hours():
                logger.debug(
                    f"User {self.user.id} is in quiet hours. Notification not sent."
                )
                return False

            if not settings.is_notification_allowed(self.notification_type):
                logger.debug(
                    f"User {self.user.id} has disabled notifications for type {self.notification_type}."
                )
                return False

            success = True
            if settings.push_notifications and not self.sent_push:
                push_success = self.send_push_notification()
                self.sent_push = push_success
                success = success and push_success

            if settings.email_notifications and not self.sent_email:
                email_success = self.send_email_notification()
                self.sent_email = email_success
                success = success and email_success

            if settings.sms_notifications and not self.sent_sms:
                sms_success = self.send_sms_notification()
                self.sent_sms = sms_success
                success = success and sms_success

            self.save(update_fields=["sent_push", "sent_email", "sent_sms"])
            return success

        except Exception as e:
            # Log the error
            logger.error(
                f"Failed to send notification ID {self.id}: {str(e)}", exc_info=True
            )
            return False

    def send_push_notification(self) -> bool:
        """Send push notification using Firebase Cloud Messaging (FCM)."""
        try:
            from firebase_admin import (
                messaging,
            )  # Ensure firebase_admin is initialized elsewhere

            device_registrations = self.user.device_registrations.filter(is_active=True)
            if not device_registrations.exists():
                logger.warning(
                    f"User {self.user.id} has no active device registrations for push notifications."
                )
                return False

            messages = []
            for device in device_registrations:
                message = messaging.Message(
                    notification=messaging.Notification(
                        title=self.title,
                        body=self.message,
                    ),
                    token=device.device_token,
                    data={
                        "notification_type": self.notification_type,
                        "notification_id": str(self.id),
                        # Include additional fields from metadata if necessary
                    },
                )
                messages.append(message)

            response = messaging.send_all(messages)
            logger.debug(
                f"Push notifications sent to user {self.user.id}: {response.success_count} successes."
            )
            return response.success_count > 0

        except Exception as e:
            logger.error(
                f"Failed to send push notification for notification ID {self.id}: {str(e)}",
                exc_info=True,
            )
            return False

    def send_email_notification(self) -> bool:
        """Send email notification."""
        try:
            from django.core.mail import send_mail
            from django.template.loader import render_to_string

            subject = self.title
            message = self.message
            recipient_list = [self.user.email]

            # Optionally, use a template for email content
            email_body = render_to_string(
                "notifications/email_template.html",
                {
                    "title": self.title,
                    "message": self.message,
                    "deep_link": self.deep_link,
                    "redirect_url": self.redirect_url,
                },
            )

            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=recipient_list,
                html_message=email_body,
                fail_silently=False,
            )
            logger.debug(f"Email notification sent to user {self.user.id}.")
            return True
        except Exception as e:
            logger.error(
                f"Failed to send email notification for notification ID {self.id}: {str(e)}",
                exc_info=True,
            )
            return False

    def send_sms_notification(self) -> bool:
        """Send SMS notification."""
        try:
            import requests  # Or use a dedicated SMS service SDK like Twilio

            sms_gateway_url = settings.SMS_GATEWAY_URL
            sms_api_key = settings.SMS_API_KEY
            phone_number = str(self.user.primary_phone_number)
            message = self.message

            payload = {
                "api_key": sms_api_key,
                "to": phone_number,
                "message": message,
            }

            response = requests.post(sms_gateway_url, data=payload)
            if response.status_code == 200:
                logger.debug(f"SMS notification sent to user {self.user.id}.")
                return True
            else:
                logger.error(
                    f"Failed to send SMS notification to user {self.user.id}: {response.text}"
                )
                return False

        except Exception as e:
            logger.error(
                f"Failed to send SMS notification for notification ID {self.id}: {str(e)}",
                exc_info=True,
            )
            return False


class NotificationAuditLog(models.Model):
    """Audit log for tracking notification lifecycle"""

    notification = models.ForeignKey(
        Notification,
        on_delete=models.SET_NULL,
        related_name="audit_logs",
        blank=True,
        null=True,
    )
    action = models.CharField(max_length=50)
    timestamp = models.DateTimeField(auto_now_add=True)
    details = models.JSONField(default=dict)

    class Meta:
        ordering = ["-timestamp"]


class DeviceRegistration(models.Model):
    """
    Stores device registration information for push notifications.
    This model maintains the mapping between users and their devices for sending
    targeted push notifications. It stores essential device information that can
    be useful for:
    - Device-specific notification handling
    - Analytics and tracking
    - Troubleshooting notification delivery issues
    - Supporting different app versions and OS-specific features
    """

    # Device Platform Choices
    PLATFORM_IOS = "ios"
    PLATFORM_ANDROID = "android"
    PLATFORM_WEB = "web"
    PLATFORM_CHOICES = [
        (PLATFORM_IOS, "iOS"),
        (PLATFORM_ANDROID, "Android"),
        (PLATFORM_WEB, "Web"),
    ]

    # Required Fields
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="device_registrations",
        help_text="User associated with this device registration",
    )
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    device_token = models.CharField(
        max_length=255, help_text="FCM token for the device"
    )
    platform = models.CharField(
        max_length=10,
        choices=PLATFORM_CHOICES,
        help_text="Operating system platform of the device",
    )

    # Device Information
    device_model = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model name of the device (e.g., iPhone 12, Samsung Galaxy S21)",
    )
    os_version = models.CharField(
        max_length=50,
        blank=True,
        help_text="Operating system version (e.g., iOS 15.0, Android 12)",
    )
    app_version = models.CharField(
        max_length=50, blank=True, help_text="Version of the Rezio app installed"
    )
    app_build_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Build number of the app for more precise version tracking",
    )

    # Device Capabilities
    supports_notification_channels = models.BooleanField(
        default=False,
        help_text="Whether the device supports notification channels (Android 8.0+)",
    )
    notification_permission_status = models.CharField(
        max_length=20,
        default="not_determined",
        choices=[
            ("authorized", "Authorized"),
            ("denied", "Denied"),
            ("not_determined", "Not Determined"),
        ],
        help_text="Status of notification permissions on the device",
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active_at = models.DateTimeField(
        null=True, blank=True, help_text="Last time the device was active"
    )
    is_active = models.BooleanField(
        default=True, help_text="Whether this device registration is currently active"
    )

    class Meta:
        unique_together = ("user", "device_token", "role")
        indexes = [
            models.Index(fields=["user", "is_active"]),
            models.Index(fields=["device_token"]),
        ]

    def __str__(self):
        return (
            f"{self.user.primary_phone_number} - {self.device_model} ({self.platform})"
        )

    def deactivate(self):
        """Deactivate this device registration"""
        self.is_active = False
        self.save()

    def update_last_active(self):
        """Update the last active timestamp"""
        self.last_active_at = timezone.now()
        self.save()


class CustomFCMDevice(AbstractFCMDevice):
    """
    Stores device registration information for push notifications.
    This model maintains the mapping between users and their devices for sending
    targeted push notifications. It stores essential device information that can
    be useful for:
    - Device-specific notification handling
    - Analytics and tracking
    - Troubleshooting notification delivery issues
    - Supporting different app versions and OS-specific features
    """

    # Device Platform Choices
    PLATFORM_IOS = "ios"
    PLATFORM_ANDROID = "android"
    PLATFORM_WEB = "web"
    PLATFORM_CHOICES = [
        (PLATFORM_IOS, "iOS"),
        (PLATFORM_ANDROID, "Android"),
        (PLATFORM_WEB, "Web"),
    ]
    device_model = models.CharField(
        max_length=100,
        blank=True,
        help_text="Model name of the device (e.g., iPhone 12, Samsung Galaxy S21)",
    )
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    os_version = models.CharField(
        max_length=50,
        blank=True,
        help_text="Operating system version (e.g., iOS 15.0, Android 12)",
    )
    app_version = models.CharField(
        max_length=50, blank=True, help_text="Version of the Rezio app installed"
    )
    app_build_number = models.CharField(
        max_length=50,
        blank=True,
        help_text="Build number of the app for more precise version tracking",
    )

    # Device Capabilities
    supports_notification_channels = models.BooleanField(
        default=False,
        help_text="Whether the device supports notification channels (Android 8.0+)",
    )
    notification_permission_status = models.CharField(
        max_length=20,
        default="not_determined",
        choices=[
            ("authorized", "Authorized"),
            ("denied", "Denied"),
            ("not_determined", "Not Determined"),
        ],
        help_text="Status of notification permissions on the device",
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active_at = models.DateTimeField(
        null=True, blank=True, help_text="Last time the device was active"
    )

    class Meta:
        unique_together = ("user", "registration_id", "role")
