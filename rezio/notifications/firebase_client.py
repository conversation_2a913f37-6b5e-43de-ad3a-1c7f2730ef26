# rezio/notifications/firebase_client.py

import firebase_admin
from firebase_admin import credentials, messaging
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class FirebaseClient:
    """
    Singleton client for interacting with Firebase services.
    """

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FirebaseClient, cls).__new__(cls)
            cls._instance.initialize_firebase()
        return cls._instance

    def initialize_firebase(self):
        if not firebase_admin._apps:
            try:
                cred = credentials.Certificate(
                    {
                        "type": "service_account",
                        "project_id": settings.env("FIREBASE_PROJECT_ID"),
                        "private_key_id": settings.env("FIREBASE_PRIVATE_KEY_ID"),
                        "private_key": settings.env("FIREBASE_PRIVATE_KEY").replace(
                            "\\n", "\n"
                        ),
                        "client_email": settings.env("FIREBASE_CLIENT_EMAIL"),
                        "client_id": settings.env("FIREBASE_CLIENT_ID"),
                        "auth_uri": settings.env("FIREBASE_AUTH_URI"),
                        "token_uri": settings.env("FIREBASE_TOKEN_URI"),
                        "auth_provider_x509_cert_url": settings.env(
                            "FIREBASE_AUTH_PROVIDER_X509_CERT_URL"
                        ),
                        "client_x509_cert_url": settings.env(
                            "FIREBASE_CLIENT_X509_CERT_URL"
                        ),
                        "universe_domain": settings.env("FIREBASE_UNIVERSE_DOMAIN"),
                    }
                )
                firebase_admin.initialize_app(cred)
                logger.info("Firebase app initialized successfully")
            except Exception as e:
                logger.error(
                    f"Failed to initialize Firebase app: {str(e)}", exc_info=True
                )
                raise

    def send_bulk_notifications(self, tokens, title, body, data=None):
        """
        Send notifications to multiple devices.
        Args:
            tokens (list): List of device tokens
            title (str): Notification title
            body (str): Notification message
            data (dict): Additional data to send
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            message = messaging.MulticastMessage(
                notification=messaging.Notification(title=title, body=body),
                data=data or {},
                tokens=tokens,
            )
            response = messaging.send_multicast(message)
            success_count = response.success_count
            failure_count = response.failure_count
            logger.info(
                f"Sent notifications to {success_count} devices, failed for {failure_count} devices"
            )
            return response.success_count > 0
        except Exception as e:
            logger.error(f"Error sending bulk notifications: {str(e)}", exc_info=True)
            return False

    def send_single_notification(self, token, title, body, data=None):
        """
        Send a single notification to a device.
        Args:
            token (str): Device token
            title (str): Notification title
            body (str): Notification message
            data (dict): Additional data to send
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            message = messaging.Message(
                notification=messaging.Notification(title=title, body=body),
                data=data or {},
                token=token,
            )
            response = messaging.send(message)
            logger.info(f"Sent notification to {token}, response: {response}")
            return True
        except Exception as e:
            logger.error(
                f"Error sending notification to {token}: {str(e)}", exc_info=True
            )
            return False
