# rezio/notifications/views/base.py

import logging

from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.views import APIView

from rezio.notifications.models import Notification
from rezio.notifications.serializers.notification_serializer import (
    NotificationListSerializer,
)
from rezio.user.authentication import J<PERSON><PERSON><PERSON>entication, FirebaseAuthentication
from rezio.user.helper import build_profile_permission_classes
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination

logger = logging.getLogger(__name__)


class BaseNotificationView(APIView):
    """Base class for all notification views with common functionality"""

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = NotificationListSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["notification_type", "read_status", "priority"]
    ordering_fields = ["created_at"]
    ordering = ["-created_at"]
    pagination_class = StandardResultsSetPagination

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    def get_queryset(self):
        """Base queryset for notifications"""
        user_role = self.request.query_params.get("user_role")
        queryset = Notification.objects.filter(
            user=self.request.user,
            role__name=user_role,
            is_deleted=False,
        ).select_related("user")
        return queryset

    @general_exception_handler
    def get(self, request):
        queryset = self.get_queryset()

        # Apply filters and ordering manually
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)

        # Apply pagination
        paginator = self.pagination_class()
        paginated_queryset = paginator.paginate_queryset(queryset, request)

        # Serialize and return response
        serializer = self.serializer_class(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)
