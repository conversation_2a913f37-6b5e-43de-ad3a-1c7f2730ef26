# rezio/notifications/views/notification_viewset.py

import logging

from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from rezio.notifications.models import (
    NotificationAuditLog,
    NotificationType,
    Notification,
)
from rezio.notifications.serializers.notification_serializer import (
    NotificationListSerializer,
    NotificationDetailSerializer,
    NotificationActionSerializer,
)
from rezio.notifications.views.base import BaseNotificationView
from rezio.properties.models import (
    AgentAssociatedProperty,
    PropertyCoOwner,
    Property,
)
from rezio.properties.services.property_service import recalculate_unlocked_properties
from rezio.properties.text_choices import (
    UserRequestActions,
    OwnerIntentForProperty,
    PropertyAgentType,
)
from rezio.properties.utils import create_user_level_property_data
from rezio.user.authentication import JWTAuthentication, FirebaseAuthentication
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.helper import (
    raise_invalid_data_exception,
)
from rezio.user.utils import (
    get_agent_role_object,
    get_investor_role_object,
    get_agent_profile_object,
)
from rezio.utils.constants import KEY_MESSAGE, KEY_ERROR, KEY_PAYLOAD, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import InvalidSerializerDataException
from rezio.utils.decorators import general_exception_handler

logger = logging.getLogger(__name__)


class NotificationViewSet(BaseNotificationView, viewsets.ModelViewSet):
    """
    ViewSet for handling all notification operations
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = NotificationListSerializer  # Default serializer

    def get_serializer_class(self):
        if self.action == "retrieve":
            return NotificationDetailSerializer
        elif self.action in ["mark_as_read", "accept", "decline", "bulk_action"]:
            return NotificationActionSerializer
        return super().get_serializer_class()

    @action(detail=True, methods=["patch"], url_path="mark_as_read")
    def mark_as_read(self, request, pk=None):
        """
        Mark a single notification as read.
        """
        try:
            notification = self.get_object()
            with transaction.atomic():
                notification.read_status = True
                notification.read_at = timezone.now()
                notification.save(update_fields=["read_status", "read_at"])
                NotificationAuditLog.objects.create(
                    notification=notification,
                    action="marked_read",
                    details={"marked_by": request.user.id},
                )
            return Response(
                {"status": "Notification marked as read"}, status=status.HTTP_200_OK
            )
        except Exception as e:
            logger.error(f"Error in mark_as_read: {str(e)}", exc_info=True)
            return Response(
                {"detail": "Error marking notification as read"},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @general_exception_handler
    @action(detail=False, methods=["post"], url_path="bulk_action")
    def bulk_action(self, request):
        """
        Perform bulk actions on notifications. Supported actions: 'mark_read', 'delete'.
        """
        serializer = NotificationActionSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            user_role = request.query_params.get("user_role")
            notification_action = serializer.validated_data.get("action")
            notification_ids = serializer.validated_data.get("notification_ids")

            notifications = self.get_queryset().filter(id__in=notification_ids)
            if not notifications.exists():
                raise_invalid_data_exception("No notifications found for the given IDs")

            with transaction.atomic():
                if notification_action == "mark_read":
                    updated = notifications.update(
                        read_status=True,
                    )
                    audit_action = "marked_read"
                else:
                    raise_invalid_data_exception(
                        f"Unsupported action: {notification_action}"
                    )

                # Create audit logs
                audit_logs = [
                    NotificationAuditLog(
                        notification_id=notification.id,
                        action=audit_action,
                        details={"user_id": request.user.id, "bulk_action": True},
                    )
                    for notification in notifications
                ]
                NotificationAuditLog.objects.bulk_create(audit_logs)

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"Notifications marked as read successfully",
                    KEY_PAYLOAD: {"user_id": request.user.pk},
                    KEY_ERROR: {},
                },
            )
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )

    def _mark_notification_handled(self, notification, is_expired=False):
        """Helper to mark notification as handled"""
        notification.action_taken_at = timezone.now()
        notification.action_taken = True
        notification.read_status = True
        notification.save()

    def _handle_expired_request(self, notification, error_message):
        """Handle expired request cases"""
        self._mark_notification_handled(notification)
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: "Invite Expired",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error_message},
            }
        )

    def _handle_agent_invitation(
        self, notification, property_obj, user, user_role, accepted
    ):
        """Handle agent invitation logic"""
        agent_association_request = AgentAssociatedProperty.objects.filter(
            id=notification.metadata.get("agent_association_object_id")
        ).first()

        if (
            not agent_association_request
            or not agent_association_request.agent_profile
            or not (
                agent_association_request.agent_profile.user == user
                and user_role == AGENT
            )
            or (
                agent_association_request.is_associated
                or agent_association_request.action_status != UserRequestActions.PENDING
            )
        ):
            raise_invalid_data_exception("Agent request not found")

        if (
            property_obj.is_archived
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            agent_association_request.is_request_expired = True
            agent_association_request.save()

        if agent_association_request.is_request_expired:
            self._handle_expired_request(
                notification, "The owner has withdrawn their request."
            )

        if accepted:
            # if property_obj.agent_type == PropertyAgentType.SELECTIVE_AGENTS:
            #     count = AgentAssociatedProperty.objects.filter(
            #         property=property_obj, is_associated=True
            #     ).count()
            #     if count >= settings.ALLOWED_SELECTIVE_AGENT_COUNT:
            #         agent_association_request.is_request_expired = True
            #         agent_association_request.save()
            #         self._handle_expired_request(notification, "Maximum agents reached")

            if property_obj.agent_type == PropertyAgentType.EXCLUSIVE_AGENT:
                if (
                    AgentAssociatedProperty.objects.filter(
                        property=property_obj, is_associated=True
                    ).count()
                    == 1
                ):
                    agent_association_request.is_request_expired = True
                    agent_association_request.save()
                    self._handle_expired_request(
                        notification, "The owner has withdrawn their request."
                    )

        agent_association_request.is_associated = accepted
        agent_association_request.action_status = (
            UserRequestActions.ACCEPTED if accepted else UserRequestActions.DECLINED
        )
        agent_role = get_agent_role_object()
        if accepted:
            # create user level property data for the agent
            create_user_level_property_data(
                property_obj=property_obj,
                user=agent_association_request.agent_profile.user,
                role=agent_role,
                user_hierarchy="Agent",
            )
        if not accepted:
            agent_association_request.is_request_expired = True
        agent_association_request.updated_by = user
        agent_association_request.updated_by_role = agent_role
        agent_association_request.save()

        # if the request is declined, set the property agent type to open to all
        if not accepted:
            property_agents = AgentAssociatedProperty.objects.filter(
                Q(is_associated=True) | Q(action_status=UserRequestActions.PENDING),
                property=property_obj,
            )
            if not property_agents.exists():
                property_obj.agent_type = PropertyAgentType.OPEN_TO_ALL
                property_obj.save()

        notification.action_response = "accepted" if accepted else "declined"

        if (
            agent_association_request
            and agent_association_request.agent_profile
            and agent_association_request.agent_profile.user
        ):
            agent_profile = get_agent_profile_object(
                agent_association_request.agent_profile.user
            )
            recalculate_unlocked_properties(agent_profile)

        # if accepted:
        #     # create user level property data for the agent
        #     create_user_level_property_data(
        #         property_obj=property_obj,
        #         user=agent_association_request.agent_profile.user,
        #         role=agent_role,
        #         user_hierarchy='Agent'
        #     )

    def _handle_investor_request(
        self, notification, property_obj, user, user_role, accepted
    ):
        """Handle investor request access logic"""
        agent_association_request = AgentAssociatedProperty.objects.filter(
            id=notification.metadata.get("agent_association_object_id")
        ).first()

        if (
            not agent_association_request
            or not agent_association_request.agent_profile
            or not (
                agent_association_request.property.owner.user.id == user.id
                and user_role == INVESTOR
            )
            or (
                agent_association_request.is_associated
                or agent_association_request.action_status != UserRequestActions.PENDING
            )
        ):
            raise_invalid_data_exception("Investor request not found")

        if property_obj.is_archived:
            agent_association_request.is_request_expired = True
            agent_association_request.save()
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Access Request Invalid",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "You have moved the property to archive. Request cannot be accepted"
                    },
                }
            )

        if agent_association_request.is_request_expired:
            self._handle_expired_request(notification, "The request has expired")

        if accepted:
            if property_obj.agent_type == PropertyAgentType.SELECTIVE_AGENTS:
                count = AgentAssociatedProperty.objects.filter(
                    property=property_obj, is_associated=True
                ).count()
                # if count >= settings.ALLOWED_SELECTIVE_AGENT_COUNT:
                #     agent_association_request.is_request_expired = True
                #     agent_association_request.save()
                #     self._handle_expired_request(notification, "Maximum agents reached")

            if property_obj.agent_type == PropertyAgentType.OPEN_TO_ALL:
                property_obj.agent_type = PropertyAgentType.SELECTIVE_AGENTS
                property_obj.save()

            # create user level property data for the investor
            create_user_level_property_data(
                property_obj=property_obj,
                user=agent_association_request.agent_profile.user,
                role=get_agent_role_object(),
                user_hierarchy="Agent",
            )

        agent_association_request.is_associated = accepted
        agent_association_request.action_status = (
            UserRequestActions.ACCEPTED if accepted else UserRequestActions.DECLINED
        )
        agent_association_request.updated_by = user
        agent_association_request.updated_by_role = get_investor_role_object()
        agent_association_request.save()

        if (
            agent_association_request
            and agent_association_request.agent_profile
            and agent_association_request.agent_profile.user
        ):
            agent_profile = get_agent_profile_object(
                agent_association_request.agent_profile.user
            )
            recalculate_unlocked_properties(agent_profile)

        # if accepted:
        #     # create user level property data for the investor
        #     create_user_level_property_data(
        #         property_obj=property_obj,
        #         user=agent_association_request.agent_profile.user,
        #         role=get_agent_role_object(),
        #         user_hierarchy='Agent'
        #     )
        notification.action_response = "accepted" if accepted else "declined"

    def _handle_coowner_invitation(
        self, notification, property_obj, user, user_role, accepted
    ):
        """Handle co-owner invitation logic"""
        property_co_owner = PropertyCoOwner.objects.filter(
            id=notification.metadata.get("property_co_owner_object_id")
        ).first()

        if (
            not property_co_owner
            or not (
                property_co_owner.co_owner.user.id == user.id and user_role == INVESTOR
            )
            or (
                property_co_owner.is_associated
                or property_co_owner.action_status != UserRequestActions.PENDING
            )
        ):
            raise_invalid_data_exception("Co-owner request not found")

        if property_obj.is_archived:
            property_co_owner.is_request_expired = True
            property_co_owner.save()
            self._handle_expired_request(
                notification, "The owner has withdrawn their request."
            )

        if property_co_owner.is_request_expired:
            self._handle_expired_request(
                notification, "The owner has withdrawn their request."
            )

        property_co_owner.is_associated = accepted
        property_co_owner.action_status = (
            UserRequestActions.ACCEPTED if accepted else UserRequestActions.DECLINED
        )
        property_co_owner.updated_by = user
        property_co_owner.updated_by_role = get_investor_role_object()
        property_co_owner.save()

        notification.action_response = "accepted" if accepted else "declined"

    def _handle_coowner_request(
        self, notification, property_obj, user, user_role, accepted, request
    ):
        """Handle co-owner request access logic"""
        property_co_owner = PropertyCoOwner.objects.filter(
            id=notification.metadata.get("property_co_owner_object_id")
        ).first()

        if (
            not property_co_owner
            or not (
                property_co_owner.property.owner.user.id == user.id
                and user_role == INVESTOR
            )
            or (
                property_co_owner.is_associated
                or property_co_owner.action_status != UserRequestActions.PENDING
            )
        ):
            raise_invalid_data_exception("Co-owner request not found")

        if property_obj.is_archived:
            property_co_owner.is_request_expired = True
            property_co_owner.save()
            self._handle_expired_request(
                notification, "The owner has withdrawn their request."
            )

        if property_co_owner.is_request_expired:
            self._handle_expired_request(
                notification, "The owner has withdrawn their request."
            )

        if accepted:
            ownership_percentage = request.data.get("ownership_percentage")
            if type(ownership_percentage) not in [int, float]:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Ownership percentage must be a number."
                        },
                    }
                )

            if (
                property_co_owner.property.lead_owner_percentage - ownership_percentage
                < 1
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "The ownership percentage for the co-owner exceeds the available lead owner percentage."
                        },
                    }
                )

            # Deduct the co-owner's percentage from lead owner
            property_instance = property_obj
            property_instance.lead_owner_percentage -= ownership_percentage
            property_instance.save()

            property_co_owner.ownership_percentage = ownership_percentage

        property_co_owner.is_associated = accepted
        property_co_owner.action_status = (
            UserRequestActions.ACCEPTED if accepted else UserRequestActions.DECLINED
        )
        property_co_owner.updated_by = user
        property_co_owner.updated_by_role = get_investor_role_object()
        property_co_owner.save()

        notification.action_response = "accepted" if accepted else "declined"

    @general_exception_handler
    @action(detail=True, methods=["post"], url_path="notification_action")
    def notification_action(self, request, pk=None):
        """Handle notification actions like accepting/declining invitations"""
        notification = Notification.objects.filter(id=pk).first()
        if not notification:
            raise_invalid_data_exception("Invalid notification")

        user_role = request.query_params.get("user_role")
        accepted = request.data.get("accepted")
        metadata = notification.metadata
        notification_type = notification.notification_type

        # Validate notification can be acted upon
        if not metadata.get("action_required") or notification.action_taken:
            raise_invalid_data_exception(
                "No action can be performed on this notification"
            )

        if not notification_type in [
            NotificationType.SELECTIVE_AGENT_INVITATION,
            NotificationType.EXCLUSIVE_AGENT_INVITATION,
            NotificationType.INVESTOR_REQUEST_ACCESS,
            NotificationType.COOWNER_INVITATION,
            NotificationType.CO_OWNER_REQUEST_ACCESS,
        ]:
            raise_invalid_data_exception(
                "No action can be performed on this notification"
            )

        # Get and validate property
        property_obj = Property.objects.filter(id=metadata.get("property_id")).first()
        if not property_obj:
            self._mark_notification_handled(notification)
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invite Expired",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        # Handle different notification types
        if notification_type in [
            NotificationType.SELECTIVE_AGENT_INVITATION.value,
            NotificationType.EXCLUSIVE_AGENT_INVITATION.value,
        ]:
            self._handle_agent_invitation(
                notification, property_obj, request.user, user_role, accepted
            )

        elif notification_type == NotificationType.INVESTOR_REQUEST_ACCESS.value:
            self._handle_investor_request(
                notification, property_obj, request.user, user_role, accepted
            )

        elif notification_type == NotificationType.COOWNER_INVITATION.value:
            self._handle_coowner_invitation(
                notification, property_obj, request.user, user_role, accepted
            )

        elif notification_type == NotificationType.CO_OWNER_REQUEST_ACCESS.value:
            self._handle_coowner_request(
                notification, property_obj, request.user, user_role, accepted, request
            )

        # Mark notification as handled
        self._mark_notification_handled(notification)
        NotificationAuditLog.objects.create(
            notification=notification,
            action="accepted",
            details={"accepted_by": request.user.id},
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Notification action has been registered successfully",
                KEY_PAYLOAD: {"user_id": request.user.pk},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @action(detail=False, methods=["get"], url_path="unread_count")
    def unread_count(self, request):
        """
        Get unread count of notifications
        """
        unread_count = self.get_queryset().filter(read_status=False).count()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Unread notifications count fetched successfully",
                KEY_PAYLOAD: {"unread_count": unread_count},
                KEY_ERROR: {},
            },
        )
