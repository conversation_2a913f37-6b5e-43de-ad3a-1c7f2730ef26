import datetime
import logging

from rest_framework import viewsets, status
from rest_framework.response import Response

from rezio.notifications.models import DeviceRegistration
from rezio.notifications.serializers.notification_serializer import (
    DeviceRegistrationSerializer,
)
from rezio.notifications.services.device_service import DeviceRegistrationService
from rezio.user.authentication import J<PERSON><PERSON><PERSON>entication, FirebaseAuthentication
from rezio.user.helper import build_profile_permission_classes
from rezio.user.utils import get_role_object
from rezio.utils.constants import KEY_MESSAGE, KEY_ERROR, KEY_ERROR_MESSAGE, KEY_PAYLOAD
from rezio.utils.custom_exceptions import InvalidSerializerDataException
from rezio.utils.decorators import general_exception_handler

logger = logging.getLogger(__name__)


class DeviceRegistrationViewSet(viewsets.GenericViewSet):
    """ViewSet for managing device registrations"""

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = DeviceRegistrationSerializer

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    @general_exception_handler
    def create(self, request, *args, **kwargs):
        """
        Register or update a device for push notifications.
        Expected payload:
        {
            "device_token": "string",
            "device_info": {
                "device_type": "iOS" or "Android",
                "app_version": "string",
                "os_version": "string",
                "device_model": "string"
            }
        }
        """
        user_role = request.query_params.get("user_role")
        role = get_role_object(user_role)
        serializer = self.get_serializer(
            data=request.data, context={"role": role, "request": request}
        )
        serializer.is_valid(raise_exception=True)
        device = serializer.save(role=role, user=request.user)
        device.last_active_at = datetime.datetime.now()
        # device.user = request.user
        # device.role = role
        device.save()
        if device:
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"Device has been registered successfully",
                    KEY_PAYLOAD: {"user_id": request.user.pk},
                    KEY_ERROR: {},
                },
            )
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Failed to register device",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Failed to register device"},
                }
            )

    @general_exception_handler
    def destroy(self, request, *args, **kwargs):
        """
        Deactivate a device registration.
        Expected URL parameter: device_token
        """
        device_token = request.query_params.get("device_token")
        if not device_token:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Device token is required",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Device token is required"},
                }
            )

        success = DeviceRegistrationService.deactivate_device(
            user=request.user, device_token=device_token
        )

        if success:
            return Response(
                status=status.HTTP_204_NO_CONTENT,
                data={
                    KEY_MESSAGE: f"Device has been deactivated successfully",
                    KEY_PAYLOAD: {"user_id": request.user.pk},
                    KEY_ERROR: {},
                },
            )
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Device not found or already deactivated",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Device not found or already deactivated"
                    },
                }
            )

    @general_exception_handler
    def get(self, request, *args, **kwargs):
        """
        Register or update a device for push notifications.
        Expected payload:
        {
            "device_token": "string",
            "device_info": {
                "device_type": "iOS" or "Android",
                "app_version": "string",
                "os_version": "string",
                "device_model": "string"
            }
        }
        """
        user_role = request.query_params.get("user_role")
        role = get_role_object(user_role)
        device = DeviceRegistration.objects.filter(
            user=request.user, is_active=True, role=role
        ).last()
        if not device:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Device not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Device not found"},
                }
            )
        serializer = self.get_serializer(device)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Device has been registered successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )
