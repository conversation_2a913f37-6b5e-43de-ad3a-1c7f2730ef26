from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from django.utils import timezone
from django.db import transaction
from rezio.notifications.models import Notification, NotificationSettings, NotificationAuditLog
from rezio.notifications.serializers.notification_serializer import (
    NotificationDetailSerializer,
    NotificationListSerializer,
    NotificationSettingsSerializer,
    NotificationActionSerializer,
    NotificationCountSerializer,
    NotificationBulkCreateSerializer
)
from rezio.user.models import Role
from rezio.user.permissions import IsAgent, IsInvestor
from rezio.notifications.permissions import InvestorNotificationPermission, AgentNotificationPermission
import logging
from ..notification_handlers import NotificationHandler
from properties.models import Property, PropertyAgent, PropertyOwner

class NotificationViewSet(viewsets.ModelViewSet):
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated]
    notification_handler = NotificationHandler()

    def get_queryset(self):
        return Notification.objects.filter(
            recipient=self.request.user
        ).order_by('-created_at')

    @action(detail=True, methods=['post'])
    def mark_read(self, request, pk=None):
        """Mark notification as read"""
        notification = self.get_object()
        notification.mark_as_read()
        return Response(status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def mark_all_read(self, request):
        """Mark all notifications as read"""
        self.get_queryset().update(is_read=True)
        return Response(status=status.HTTP_200_OK)

class PropertyNotificationViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]
    notification_handler = NotificationHandler()

    @action(detail=True, methods=['post'])
    def request_access(self, request, pk=None):
        """Handle property access request"""
        property_obj = Property.objects.get(pk=pk)
        self.notification_handler.handle_property_access_request(
            property_obj, request.user
        )
        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def respond_to_request(self, request, pk=None):
        """Handle response to property access request"""
        property_agent = PropertyAgent.objects.get(
            property_id=pk,
            agent=request.data.get('agent_id')
        )
        accepted = request.data.get('accepted', False)
        
        self.notification_handler.handle_agent_request_response(
            property_agent, accepted
        )
        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def add_coowner(self, request, pk=None):
        """Handle co-owner addition"""
        property_obj = Property.objects.get(pk=pk)
        co_owner = User.objects.get(pk=request.data.get('co_owner_id'))
        
        self.notification_handler.handle_coowner_request(
            property_obj, co_owner
        )
        return Response(status=status.HTTP_200_OK)

# Role-based notification types mapping
ROLE_NOTIFICATION_MAPPING = {
    'AGENT': [
        'SECURITY_ALERT', 'PROPERTY_APPROVAL', 'CHAT_NOTIFICATION',
        'TRANSACTION_STATUS', 'BRN_EXPIRY', 'OFFER_RECEIVED',
        'ASSISTANCE_NEEDED', 'AI_TIMEOUT', 'EXCLUSIVE_AGENT_REQUEST'
    ],
    'INVESTOR': [
        'PROPERTY_VIEWS', 'NEW_PROPERTY_ALERT', 'COLLABORATION_REQUEST',
        'TRANSACTION_STATUS', 'PROPERTY_LISTING', 'OFFER_ACCEPTED',
        'OFFER_REJECTED', 'FOLLOW'
    ],
}

def ensure_notification_settings(user):
    """Ensure notification settings exist for the user"""
    settings, created = NotificationSettings.objects.get_or_create(
        user=user,
        defaults={
            'pause_all_notifications': False,
            'email_notifications': True,
            'push_notifications': True,
            'chat_notifications': True
        }
    )
    if created:
        logger.info(f"Created default notification settings for user {user.id}")
    return settings

def validate_role(role_name, user):
    """Validate role and user's access"""
    role_name = role_name.upper()
    role = Role.objects.filter(name=role_name).first()
    if not role:
        logger.error(f"Role validation failed for role: {role_name}")
        raise ValidationError(f"Invalid role: {role_name}")
    if not user.roles.filter(name=role_name).exists():
        logger.error(f"User {user.id} does not have the role: {role_name}")
        raise ValidationError("User does not have the specified role")
    logger.info(f"Role {role.name} validated for user {user.id}")
    return role

class NotificationPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100

class NotificationViewSet(viewsets.ModelViewSet):
    pagination_class = NotificationPagination

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        role = self.kwargs.get('role', '').upper()
        if role == 'INVESTOR':
            permission_classes = [IsAuthenticated, InvestorNotificationPermission]
        elif role == 'AGENT':
            permission_classes = [IsAuthenticated, AgentNotificationPermission]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        if self.action == 'list':
            return NotificationListSerializer
        return NotificationDetailSerializer

    def get_queryset(self):
        try:
            role_name = self.kwargs.get("role", "").upper()
            if not role_name:
                raise ValidationError("Role parameter is required")
            
            role = validate_role(role_name, self.request.user)
            notification_types = ROLE_NOTIFICATION_MAPPING.get(role_name, [])
            
            queryset = Notification.objects.filter(
                user=self.request.user,
                role=role_name,
                notification_type__in=notification_types,
                is_deleted=False
            )

            # Apply filters
            filters = Q()
            # Scheduled time filter
            filters &= (
                Q(scheduled_time__isnull=True) |
                Q(scheduled_time__lte=timezone.now())
            )

            # Query params filters
            if self.request.query_params.get('type'):
                filters &= Q(notification_type=self.request.query_params['type'])
            if self.request.query_params.get('read') is not None:
                is_read = self.request.query_params['read'].lower() == 'true'
                filters &= Q(read_status=is_read)
            if self.request.query_params.get('priority'):
                filters &= Q(priority=self.request.query_params['priority'])

            queryset = queryset.filter(filters)
            # Ordering
            ordering = self.request.query_params.get('ordering', '-created_at')
            return queryset.order_by(ordering)
        except Exception as e:
            logger.error(f"Error in get_queryset: {str(e)}")
            raise

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            page = self.paginate_queryset(queryset)
            data = {
                'count': queryset.count(),
                'unread_count': queryset.filter(read_status=False).count(),
                'results': page if page is not None else queryset
            }
            serializer = NotificationListSerializer(data)
            if page is not None:
                return self.get_paginated_response(serializer.data)
            return Response(serializer.data)
        except ValidationError as e:
            logger.error(f"Validation error in list: {str(e)}")
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error in list notifications: {str(e)}")
            return Response(
                {'detail': 'Error retrieving notifications'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def perform_create(self, serializer):
        try:
            role_name = self.kwargs.get('role', '').upper()
            role = validate_role(role_name, self.request.user)
            with transaction.atomic():
                notification = serializer.save(
                    user=self.request.user,
                    role=role_name
                )
                NotificationAuditLog.objects.create(
                    notification=notification,
                    action='created',
                    details={'created_by': self.request.user.id}
                )
            logger.info(f"Created notification for user {self.request.user.id} with role {role.name}")
        except Exception as e:
            logger.error(f"Error in perform_create: {str(e)}")
            raise

    @action(detail=True, methods=['patch'])
    def mark_as_read(self, request, pk=None, role=None):
        try:
            notification = self.get_object()
            with transaction.atomic():
                notification.read_status = True
                notification.save()
                NotificationAuditLog.objects.create(
                    notification=notification,
                    action='marked_read',
                    details={'marked_by': request.user.id}
                )
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            logger.error(f"Error in mark_as_read: {str(e)}")
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def bulk_action(self, request, role=None):
        try:
            serializer = NotificationActionSerializer(
                data=request.data,
                context={'request': request}
            )
            serializer.is_valid(raise_exception=True)
            
            with transaction.atomic():
                notification_ids = serializer.validated_data['notification_ids']
                action = serializer.validated_data['action']
                notifications = self.get_queryset().filter(id__in=notification_ids)

                if action == 'mark_read':
                    updated = notifications.update(read_status=True)
                    audit_action = 'marked_read'
                elif action == 'delete':
                    updated = notifications.update(is_deleted=True)
                    audit_action = 'deleted'
                else:
                    updated = notifications.update(
                        action_taken=True,
                        action_response=action,
                        action_taken_at=timezone.now()
                    )
                    audit_action = f'action_{action}'

                # Create audit logs
                audit_logs = [
                    NotificationAuditLog(
                        notification_id=nid,
                        action=audit_action,
                        details={
                            'user_id': request.user.id,
                            'bulk_action': True
                        }
                    )
                    for nid in notification_ids
                ]
                NotificationAuditLog.objects.bulk_create(audit_logs)

            return Response(
                {'updated_count': updated},
                status=status.HTTP_200_OK
            )
        except ValidationError as e:
            logger.error(f"Validation error in bulk_action: {str(e)}")
            return Response(
                {'detail': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error in bulk_action: {str(e)}")
            return Response(
                {'detail': 'Error performing bulk action'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def handle_exception(self, exc):
        """Handle exceptions globally for the viewset"""
        if isinstance(exc, ValidationError):
            return Response(
                {'detail': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )
        logger.error(f"Unhandled exception: {str(exc)}")
        return super().handle_exception(exc)

class NotificationSettingsViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = NotificationSettingsSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        role = self.request.user.roles.first()
        if role and role.name.upper() == 'INVESTOR':
            permission_classes = [IsAuthenticated, InvestorNotificationPermission]
        elif role and role.name.upper() == 'AGENT':
            permission_classes = [IsAuthenticated, AgentNotificationPermission]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_object(self):
        return ensure_notification_settings(self.request.user)

    def retrieve(self, request, *args, **kwargs):
        try:
            settings = self.get_object()
            serializer = self.get_serializer(settings)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving notification settings: {str(e)}")
            return Response(
                {'detail': 'Error retrieving notification settings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        try:
            settings = self.get_object()
            serializer = self.get_serializer(
                settings,
                data=request.data,
                partial=True
            )
            if serializer.is_valid():
                serializer.save()
                logger.info(f"Updated notification settings for user {request.user.id}")
                return Response(serializer.data)
            logger.warning(
                f"Validation error updating settings for user {request.user.id}: "
                f"{serializer.errors}"
            )
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Error updating notification settings: {str(e)}")
            return Response(
                {'detail': 'Error updating notification settings'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )




class NotificationFilter(filters.FilterSet):
    """
    Filter class for Notifications
    """
    type = filters.ChoiceFilter(choices=NOTIFICATION_TYPES)
    start_date = filters.DateTimeFilter(field_name='created_at', lookup_expr='gte')
    end_date = filters.DateTimeFilter(field_name='created_at', lookup_expr='lte')
    is_read = filters.BooleanFilter()

    class Meta:
        model = Notification
        fields = ['type', 'is_read']

class NotificationListView(generics.ListAPIView):
    """
    API View to list user notifications with filtering and pagination
    
    Returns:
        - List of notifications for the authenticated user
        - Supports filtering by type, date range, and read status
        - Paginated results with cursor pagination
        
    Query Parameters:
        - type: Filter by notification type
        - start_date: Filter notifications from this date (ISO format)
        - end_date: Filter notifications until this date (ISO format)
        - is_read: Filter by read status (true/false)
        - cursor: Pagination cursor
        
    Response Format:
        {
            "count": total_count,
            "next": next_page_url,
            "previous": previous_page_url,
            "results": [
                {
                    "id": "notification_id",
                    "type": "notification_type",
                    "title": "notification_title",
                    "message": "notification_message",
                    "data": {},
                    "is_read": boolean,
                    "created_at": "timestamp",
                    ...
                }
            ]
        }
    """
    serializer_class = NotificationSerializer
    permission_classes = [IsAuthenticated, NotificationPermission]
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = NotificationFilter
    pagination_class = CursorPagination  # Assuming you have this configured

    def get_queryset(self):
        """
        Get notifications for the authenticated user
        Orders by created_at descending to show newest first
        """
        return Notification.objects.filter(
            user=self.request.user
        ).select_related(
            'user'  # Add other related fields if needed
        ).order_by('-created_at')

    def get(self, request, *args, **kwargs):
        """
        Override get method to include unread count in response
        """
        response = super().get(request, *args, **kwargs)
        unread_count = self.get_queryset().filter(is_read=False).count()
        response.data['unread_count'] = unread_count
        return response

class NotificationMarkReadView(generics.GenericAPIView):
    """
    API View to mark notifications as read
    
    Supports:
        - Marking single notification as read
        - Marking multiple notifications as read
        - Marking all notifications as read
    """
    permission_classes = [IsAuthenticated, NotificationPermission]
    serializer_class = NotificationSerializer

    def post(self, request, *args, **kwargs):
        notification_ids = request.data.get('notification_ids', [])
        mark_all = request.data.get('mark_all', False)

        if mark_all:
            Notification.objects.filter(
                user=request.user,
                is_read=False
            ).update(is_read=True)
        elif notification_ids:
            Notification.objects.filter(
                user=request.user,
                id__in=notification_ids,
                is_read=False
            ).update(is_read=True)
        else:
            return Response(
                {"error": "Please provide notification_ids or mark_all=true"},
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({"status": "success"}, status=status.HTTP_200_OK)



class NotificationFilter(filters.FilterSet):
    """Filter class for Notifications"""
    type = filters.ChoiceFilter(choices=NOTIFICATION_TYPES)
    date_range = filters.DateTimeFromToRangeFilter(field_name='created_at')
    is_read = filters.BooleanFilter()

    class Meta:
        model = Notification
        fields = ['type', 'is_read']

class BaseNotificationView(generics.GenericAPIView):
    """Base class for notification views with common functionality"""
    permission_classes = [IsAuthenticated, NotificationPermission]
    serializer_class = NotificationSerializer

    def get_queryset(self):
        """Get base queryset for notifications"""
        return Notification.objects.filter(
            user=self.request.user
        ).select_related(
            'user'
        ).order_by('-created_at')

class NotificationListView(BaseNotificationView, generics.ListAPIView):
    """
    List and filter notifications for the authenticated user
    
    Supports:
    - Filtering by type, date range, and read status
    - Cursor-based pagination
    - Unread count in response
    """
    filter_backends = (filters.DjangoFilterBackend,)
    filterset_class = NotificationFilter
    pagination_class = NotificationCursorPagination

    def get_response_data(self, response):
        """Enhance response with additional data"""
        response.data['unread_count'] = self.get_queryset().filter(
            is_read=False
        ).count()
        return response

    def list(self, request, *args, **kwargs):
        """Override list to include unread count"""
        response = super().list(request, *args, **kwargs)
        return self.get_response_data(response)

class NotificationActionView(BaseNotificationView):
    """
    Handle notification actions (mark read/unread)
    
    Supports:
    - Single notification
    - Multiple notifications
    - All notifications
    """
    def post(self, request, *args, **kwargs):
        """
        Mark notifications as read/unread
        
        Request Body:
        {
            "notification_ids": ["id1", "id2"],  # Optional
            "mark_all": true,                    # Optional
            "action": "read" | "unread"          # Required
        }
        """
        action = request.data.get('action')
        if action not in ['read', 'unread']:
            return Response(
                {"error": "Invalid action. Use 'read' or 'unread'"},
                status=status.HTTP_400_BAD_REQUEST
            )

        notification_ids = request.data.get('notification_ids', [])
        mark_all = request.data.get('mark_all', False)
        is_read = action == 'read'

        queryset = self.get_queryset()
        if mark_all:
            update_qs = queryset
        elif notification_ids:
            update_qs = queryset.filter(id__in=notification_ids)
        else:
            return Response(
                {"error": "Provide notification_ids or mark_all=true"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update notifications
        updated_count = update_qs.update(
            is_read=is_read,
            updated_at=timezone.now()
        )

        return Response({
            "status": "success",
            "updated_count": updated_count
        })
