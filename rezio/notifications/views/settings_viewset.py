# rezio/notifications/views/settings_viewset.py

import logging

from rest_framework import viewsets, mixins

from rezio.notifications.models import NotificationSettings
from rezio.notifications.serializers.notification_serializer import (
    NotificationSettingsSerializer,
)
from rezio.user.authentication import J<PERSON><PERSON><PERSON>entication, FirebaseAuthentication
from rezio.user.helper import build_profile_permission_classes
from rezio.user.utils import get_role_object

logger = logging.getLogger(__name__)


class NotificationSettingsViewSet(
    mixins.RetrieveModelMixin, mixins.UpdateModelMixin, viewsets.GenericViewSet
):
    """ViewSet for managing notification settings"""

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = NotificationSettingsSerializer

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    def get_object(self):
        user_role = self.request.query_params.get("user_role")
        role_obj = get_role_object(user_role)
        settings, created = NotificationSettings.objects.get_or_create(
            user=self.request.user,
            role=role_obj,
            defaults={
                "email_notifications": True,
                "push_notifications": True,
                "sms_notifications": False,
                "pause_all_notifications": False,
                # Add default settings for notification types if necessary
            },
        )
        if created:
            logger.info(
                f"Created default notification settings for user {self.request.user.id}"
            )
        return settings

    def perform_update(self, serializer):
        serializer.save()
        logger.info(f"Updated notification settings for user {self.request.user.id}")
