# rezio/notifications/notification_types.py

from enum import Enum


class NotificationType(Enum):
    WELCOME = "WELCOME"
    PROPERTY_STATUS_CHANGE = "PROPERTY_STATUS_CHANGE"
    AGENT_INVITATION = "AGENT_INVITATION"
    AGENT_ACCEPTED_INVITATION = "AGENT_ACCEPTED_INVITATION"
    AGENT_DECLINED_INVITATION = "AGENT_DECLINED_INVITATION"
    COOWNER_INVITATION = "COOWNER_INVITATION"
    COOWNER_ACCEPTED_INVITATION = "COOWNER_ACCEPTED_INVITATION"
    COOWNER_DECLINED_INVITATION = "COOWNER_DECLINED_INVITATION"
    PROPERTY_ADDED_TO_PORTFOLIO = "PROPERTY_ADDED_TO_PORTFOLIO"
    PROPERTY_AGENT_ASSIGNED = "PROPERTY_AGENT_ASSIGNED"
    PROPERTY_ARCHIVE = "PROPERTY_ARCHIVE"
    PROPERTY_COOWNER_ADDED = "PROPERTY_COOWNER_ADDED"
    PROPERTY_AGENT_REMOVED = "PROPERTY_AGENT_REMOVED"
    COOWNER_REMOVED_FROM_PROPERTY = "COOWNER_REMOVED_FROM_PROPERTY"
    PROFILE_COMPLETION = "PROFILE_COMPLETION"
    FINANCIAL_UPDATE = "FINANCIAL_UPDATE"  # Added Missing Notification Type
    COMPLETION_STATE_CHANGE = "COMPLETION_STATE_CHANGE"
    SUBSCRIPTION_CANCELLATION = "SUBSCRIPTION_CANCELLATION"
    # ... add other notification types as needed

    @staticmethod
    def get_role_mapping():
        return {
            NotificationType.WELCOME.value: ["Agent", "Investor"],
            NotificationType.PROPERTY_STATUS_CHANGE.value: ["Agent", "Investor"],
            NotificationType.AGENT_INVITATION.value: ["Agent"],
            NotificationType.AGENT_ACCEPTED_INVITATION.value: ["Agent"],
            NotificationType.AGENT_DECLINED_INVITATION.value: ["Agent"],
            NotificationType.COOWNER_INVITATION.value: ["Investor"],
            NotificationType.COOWNER_ACCEPTED_INVITATION.value: ["Investor"],
            NotificationType.COOWNER_DECLINED_INVITATION.value: ["Investor"],
            NotificationType.PROPERTY_ADDED_TO_PORTFOLIO.value: ["Investor"],
            NotificationType.PROPERTY_AGENT_ASSIGNED.value: ["Agent"],
            NotificationType.PROPERTY_ARCHIVE.value: ["Agent", "Investor"],
            NotificationType.PROPERTY_COOWNER_ADDED.value: ["Investor"],
            NotificationType.PROPERTY_AGENT_REMOVED.value: ["Agent"],
            NotificationType.COOWNER_REMOVED_FROM_PROPERTY.value: ["Investor"],
            NotificationType.PROFILE_COMPLETION.value: ["Agent", "Investor"],
            NotificationType.FINANCIAL_UPDATE.value: [
                "Agent",
                "Investor",
            ],  # Mapping for FINANCIAL_UPDATE
            NotificationType.COMPLETION_STATE_CHANGE.value: ["Agent", "Investor"],
            NotificationType.SUBSCRIPTION_CANCELLATION.value: ["Agent"],
            # ... add other mappings as needed
        }
