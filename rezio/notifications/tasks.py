from typing import List, Optional
from celery import shared_task
from django.utils import timezone
from django.db import transaction
from django.conf import settings
from django.db.models import Q

from rezio.notifications.firebase_client import FirebaseClient
from rezio.notifications.models import Notification, NotificationSettings, DeviceRegistration
from rezio.user.models import User
from rezio.notifications.notification_handlers import NotificationHandler

import logging
logger = logging.getLogger(__name__)

# Initialize the NotificationHandler
notification_handler = NotificationHandler()

# Notification Sending Tasks
# @shared_task(
#     name="rezio.notifications.send_scheduled_notifications",
#     bind=True,
#     max_retries=3,
#     default_retry_delay=300  # 5 minutes
# )
# def send_scheduled_notifications(self):
#     """
#     Process and send scheduled notifications that are due.
#
#     Handles:
#     - Email notifications
#     - Push notifications
#     - SMS notifications
#     - Notification status updates
#     """
#     try:
#         now = timezone.now()
#
#         # Get due notifications that have not yet been sent via all channels
#         notifications = Notification.objects.filter(
#             Q(scheduled_time__lte=now) | Q(scheduled_time__isnull=True),
#             is_deleted=False,
#             expires_at__gt=now,
#         ).exclude(
#             sent_email=True,
#             sent_push=True,
#             sent_sms=True
#         ).select_related('user').prefetch_related('user__notification_settings')
#
#         processed_count = 0
#
#         for notification in notifications:
#             try:
#                 with transaction.atomic():
#                     # Send the notification using its send_notification method
#                     success = notification.send_notification()
#                     if success:
#                         processed_count += 1
#
#             except Exception as e:
#                 logger.error(
#                     f"Error processing notification {notification.id}: {str(e)}",
#                     exc_info=True
#                 )
#                 continue
#
#         logger.info(f"Processed {processed_count} scheduled notifications")
#         return processed_count
#
#     except Exception as e:
#         logger.error(f"Error in send_scheduled_notifications: {str(e)}", exc_info=True)
#         raise self.retry(exc=e)
#
# @shared_task(
#     name="rezio.notifications.send_push_notification",
#     bind=True,
#     max_retries=3
# )
# def send_push_notification(self, notification_id: int):
#     """
#     Send push notification for a specific notification.
#     Args:
#         notification_id (int): ID of the notification to send
#     """
#     try:
#         with transaction.atomic():
#             notification = Notification.objects.select_related('user').get(
#                 id=notification_id
#             )
#
#             # Send push notification using the notification's send_notification method
#             success = notification.send_notification()
#             if success:
#                 logger.info(f"Push notification {notification_id} sent successfully")
#                 return True
#             else:
#                 logger.warning(f"Push notification {notification_id} failed or not allowed")
#                 return False
#
#     except Notification.DoesNotExist:
#         logger.warning(f"Notification {notification_id} not found")
#         return False
#     except Exception as e:
#         logger.error(f"Error sending push notification: {str(e)}", exc_info=True)
#         raise self.retry(exc=e)
#
# @shared_task(name="rezio.notifications.send_email_notification")
# def send_email_notification(notification_id: int):
#     """
#     Send email notification.
#
#     Args:
#         notification_id (int): Notification ID
#     """
#     try:
#         notification = Notification.objects.select_related('user').get(
#             id=notification_id
#         )
#
#         # Implement email sending logic here
#         # For example, using Django's send_mail function
#         from django.core.mail import send_mail
#
#         subject = notification.title
#         message = notification.message
#         recipient_list = [notification.user.email]
#         from_email = settings.DEFAULT_FROM_EMAIL
#
#         # Send the email
#         send_mail(subject, message, from_email, recipient_list)
#
#         # Update sent_email field
#         notification.sent_email = True
#         notification.save(update_fields=['sent_email'])
#
#         logger.info(f"Email notification {notification_id} sent successfully")
#         return True
#
#     except Notification.DoesNotExist:
#         logger.warning(f"Notification {notification_id} not found")
#         return False
#     except Exception as e:
#         logger.error(f"Error sending email notification: {str(e)}", exc_info=True)
#         raise
#
# # Maintenance Tasks
# @shared_task(name="rezio.notifications.clean_old_notifications")
# def clean_old_notifications():
#     """
#     Remove old notifications based on retention policy.
#
#     Deletes:
#     - Read notifications older than 6 months
#     - Unread notifications older than 1 year
#     """
#     try:
#         with transaction.atomic():
#             six_months_ago = timezone.now() - timezone.timedelta(days=180)
#             one_year_ago = timezone.now() - timezone.timedelta(days=365)
#
#             # Delete read notifications
#             read_notifications = Notification.objects.filter(
#                 read_status=True,
#                 created_at__lt=six_months_ago
#             )
#             read_count = read_notifications.count()
#             read_notifications.delete()
#
#             # Delete very old unread notifications
#             unread_notifications = Notification.objects.filter(
#                 read_status=False,
#                 created_at__lt=one_year_ago
#             )
#             unread_count = unread_notifications.count()
#             unread_notifications.delete()
#
#             logger.info(
#                 f"Cleaned notifications: {read_count} read, {unread_count} unread"
#             )
#             return read_count + unread_count
#
#     except Exception as e:
#         logger.error(f"Error cleaning old notifications: {str(e)}", exc_info=True)
#         raise
#
# # User Related Tasks
# @shared_task(name="rezio.notifications.create_profile_completion_reminder")
# def create_profile_completion_reminder(user_id: int):
#     """
#     Create reminder notification for profile completion.
#
#     Args:
#         user_id (int): User ID to remind
#     """
#     try:
#         user = User.objects.get(id=user_id)
#         notification_handler = NotificationHandler()
#         notification = notification_handler.handle_profile_completion_reminder(user)
#         if notification:
#             logger.info(f"Created profile completion reminder for user {user_id}")
#         else:
#             logger.warning(f"Failed to create profile completion reminder for user {user_id}")
#
#     except User.DoesNotExist:
#         logger.error(f"User {user_id} not found")
#     except Exception as e:
#         logger.error(
#             f"Error creating profile completion reminder: {str(e)}",
#             exc_info=True
#         )
#
# # Device Token Maintenance
# @shared_task(name="rezio.notifications.clean_inactive_devices")
# def clean_inactive_devices():
#     """Clean up inactive or invalid device registrations."""
#     try:
#         threshold = timezone.now() - timezone.timedelta(days=90)
#         inactive_devices = DeviceRegistration.objects.filter(
#             Q(last_active_at__lt=threshold) |
#             Q(is_active=False)
#         )
#
#         inactive_count = inactive_devices.count()
#         inactive_devices.delete()
#
#         logger.info(f"Cleaned {inactive_count} inactive device registrations")
#         return inactive_count
#
#     except Exception as e:
#         logger.error(f"Error cleaning inactive devices: {str(e)}", exc_info=True)
#         raise
