# rezio/notifications/serializers/notification_serializer.py

from rest_framework import serializers
from django.utils import timezone
from rezio.notifications.models import (
    Notification,
    NotificationSettings,
    NotificationAuditLog,
    DeviceRegistration,
    CustomFCMDevice,
)
from rezio.properties.utils import get_s3_object
from rezio.user.helper import raise_invalid_data_exception, get_profile_object_by_role
from rezio.user.models import User
from phonenumber_field.serializerfields import PhoneNumberField
import logging

logger = logging.getLogger(__name__)


class UserMinimalSerializer(serializers.ModelSerializer):
    """Minimal user serializer for notifications"""

    class Meta:
        model = User
        fields = ["id", "email"]
        read_only_fields = fields


class NotificationAuditLogSerializer(serializers.ModelSerializer):
    """Serializer for notification audit logs"""

    class Meta:
        model = NotificationAuditLog
        fields = ["action", "timestamp", "details"]


class NotificationDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for notifications with all related information
    """

    user = UserMinimalSerializer(read_only=True)
    metadata = serializers.JSONField()
    time_remaining = serializers.SerializerMethodField()
    requires_action = serializers.SerializerMethodField()
    delivery_status = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            "id",
            "user",
            "user_role",
            "notification_type",
            "priority",
            "title",
            "message",
            "read_status",
            "read_at",
            "is_deleted",
            "created_at",
            "updated_at",
            "expires_at",
            "metadata",
            "requires_action",
            "time_remaining",
            "delivery_status",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "delivery_status"]

    def get_time_remaining(self, obj):
        if obj.expires_at:
            remaining = obj.expires_at - timezone.now()
            return max(0, int(remaining.total_seconds()))
        return None

    def get_requires_action(self, obj):
        return obj.metadata.get("action_required", False)

    def get_delivery_status(self, obj):
        return {"email": obj.sent_email, "push": obj.sent_push, "sms": obj.sent_sms}

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Conditionally remove 'read_at' if not read
        if not instance.read_status:
            representation.pop("read_at", None)
        return representation


class NotificationListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for notification lists
    """

    requires_action = serializers.SerializerMethodField()
    role = serializers.StringRelatedField()
    related_user_profile_photo = serializers.SerializerMethodField()
    related_user = serializers.SerializerMethodField()
    related_user_role = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = [
            "id",
            "role",
            "notification_type",
            "priority",
            "title",
            "message",
            "read_status",
            "created_at",
            "requires_action",
            "metadata",
            "related_property_id",
            "related_user_profile_photo",
            "action_taken",
            "action_response",
            "related_user",
            "related_user_role",
        ]

    def get_requires_action(self, obj):
        return obj.metadata.get("action_required", False)

    def get_related_user_profile_photo(self, obj):
        if (
            obj.related_user
            and obj.related_user.roles.filter(name=obj.related_user_role.name).exists()
        ):
            user_profile = get_profile_object_by_role(
                obj.related_user, obj.related_user_role
            )
            if user_profile.profile_photo_key:
                return get_s3_object(user_profile.profile_photo_key)
        return None

    def get_related_user(self, obj):
        if obj.related_user:
            return obj.related_user.id
        return None

    def get_related_user_role(self, obj):
        if obj.related_user_role:
            return obj.related_user_role.name
        return None


class NotificationSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for notification settings with validation
    """

    class Meta:
        model = NotificationSettings
        fields = [
            "pause_all_notifications",
            "email_notifications",
            "push_notifications",
            "sms_notifications",
            # Add other fields as per your NotificationSettings model
        ]
        read_only_fields = ["user"]


class NotificationActionSerializer(serializers.Serializer):
    """
    Serializer for notification actions
    """

    notification_ids = serializers.ListField(
        child=serializers.IntegerField(),
        allow_empty=False,
        min_length=1,
        error_messages={
            "empty": "Please provide at least one notification ID.",
            "invalid": "Invalid notification ID format.",
        },
    )
    action = serializers.ChoiceField(
        choices=["mark_read", "delete", "archive", "restore"],  # Extended choices
        required=True,
    )

    def validate_notification_ids(self, value):
        request = self.context["request"]
        user = request.user
        user_role = request.query_params.get("user_role")
        existing_ids = set(
            Notification.objects.filter(
                id__in=value, user=user, role__name=user_role
            ).values_list("id", flat=True)
        )

        invalid_ids = set(value) - existing_ids
        if invalid_ids:
            raise_invalid_data_exception(
                f"Invalid notification IDs: {', '.join(map(str, invalid_ids))}"
            )
        return value


class DeviceRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for device registration
    """

    class Meta:
        model = DeviceRegistration
        fields = [
            "device_token",
            "platform",
            "device_model",
            "os_version",
            "app_version",
            "app_build_number",
            "notification_permission_status",
        ]
        read_only_fields = []

    def validate_platform(self, value):
        """
        Validate that the platform is one of the allowed choices
        """
        valid_platforms = dict(DeviceRegistration.PLATFORM_CHOICES).keys()
        if value not in valid_platforms:
            raise_invalid_data_exception(
                f"Invalid platform. Must be one of: {', '.join(valid_platforms)}"
            )
        return value

    def validate_device_token(self, value):
        """
        Ensure device_token follows a specific format or is unique per user
        """
        user = self.context["request"].user
        role = self.context["role"]
        if DeviceRegistration.objects.filter(
            user=user, device_token=value, role=role
        ).exists():
            raise_invalid_data_exception(
                "This device token is already registered for your account."
            )
        return value

    def create(self, validated_data):
        """
        Create or update device registration based on user and device_token
        """
        user = self.context["request"].user
        role = self.context["role"]
        device_token = validated_data.get("device_token")

        device_registration, created = DeviceRegistration.objects.update_or_create(
            user=user, role=role, device_token=device_token, defaults=validated_data
        )
        if created:
            logger.info(f"Registered new device for user {user.id}")
        else:
            logger.info(f"Updated device registration for user {user.id}")

        return device_registration


class FCMDeviceRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for device registration
    """

    device_token = serializers.CharField()
    platform = serializers.CharField()

    class Meta:
        model = CustomFCMDevice
        fields = [
            "device_token",
            "platform",
            "device_model",
            "os_version",
            "app_version",
            "app_build_number",
            "notification_permission_status",
        ]
        read_only_fields = []

    def validate_type(self, value):
        """
        Validate that the platform is one of the allowed choices
        """
        valid_platforms = dict(DeviceRegistration.PLATFORM_CHOICES).keys()
        if value not in valid_platforms:
            raise_invalid_data_exception(
                f"Invalid platform. Must be one of: {', '.join(valid_platforms)}"
            )
        return value

    def validate_device_token(self, value):
        """
        Ensure device_token follows a specific format or is unique per user
        """
        user = self.context["request"].user
        if DeviceRegistration.objects.filter(user=user, device_token=value).exists():
            raise_invalid_data_exception(
                "This device token is already registered for your account."
            )
        return value

    def create(self, validated_data):
        """
        Create or update device registration based on user and device_token
        """

        validated_data["registration_id"] = validated_data.pop("device_token")
        validated_data["type"] = validated_data.pop("platform")
        return super().create(validated_data)
        # user = self.context['request'].user
        # device_token = validated_data.get('device_token')
        #
        # device_registration, created = DeviceRegistration.objects.update_or_create(
        #     user=user,
        #     device_token=device_token,
        #     defaults=validated_data
        # )
        # if created:
        #     logger.info(f"Registered new device for user {user.id}")
        # else:
        #     logger.info(f"Updated device registration for user {user.id}")
        #
        # return device_registration
