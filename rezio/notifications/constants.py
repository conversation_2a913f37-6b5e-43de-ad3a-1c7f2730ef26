from django.db import models
from django.utils.translation import gettext_lazy as _


class NotificationTypes(models.TextChoices):
    """Text choices for notification types"""

    PROPERTY_LISTED = "PROPERTY_LISTED", _("Property Listed")
    PROPERTY_MATCHED = "PROPERTY_MATCHED", _("Property Matched")
    INQUIRY_RECEIVED = "INQUIRY_RECEIVED", _("Inquiry Received")
    PROFILE_UPDATE = "PROFILE_UPDATE", _("Profile Updated")
    SUBSCRIPTION_CANCELLATION = (
        "SUBSCRIPTION_CANCELLATION",
        _("Subscription Cancellation"),
    )


# Keep the old tuple for backwards compatibility if needed
NOTIFICATION_TYPES = (
    (NotificationTypes.PROPERTY_LISTED, "Property Listed"),
    (NotificationTypes.PROPERTY_MATCHED, "Property Matched"),
    (NotificationTypes.INQUIRY_RECEIVED, "Inquiry Received"),
    (NotificationTypes.PROFILE_UPDATE, "Profile Updated"),
    (NotificationTypes.SUBSCRIPTION_CANCELLATION, "Subscription Cancellation"),
)

# Constants for direct reference
PROPERTY_LISTED = NotificationTypes.PROPERTY_LISTED
PROPERTY_MATCHED = NotificationTypes.PROPERTY_MATCHED
INQUIRY_RECEIVED = NotificationTypes.INQUIRY_RECEIVED
PROFILE_UPDATE = NotificationTypes.PROFILE_UPDATE
SUBSCRIPTION_CANCELLATION = NotificationTypes.SUBSCRIPTION_CANCELLATION


ROLE_NOTIFICATION_MAPPING = {
    "Agent": [
        PROPERTY_LISTED,
        INQUIRY_RECEIVED,
        PROFILE_UPDATE,
        SUBSCRIPTION_CANCELLATION,
    ],
    "Investor": [
        PROPERTY_MATCHED,
        PROFILE_UPDATE,
    ],
    "Admin": [
        PROPERTY_LISTED,
        PROPERTY_MATCHED,
        INQUIRY_RECEIVED,
        PROFILE_UPDATE,
    ],
}
