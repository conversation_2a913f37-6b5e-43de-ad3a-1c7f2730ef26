# rezio/notifications/permissions.py

from rest_framework.permissions import Base<PERSON>ermission
from rezio.user.models import User


class RoleBasedPermission(BasePermission):
    """
    Custom permission to allow access based on user roles.
    """

    role_permission_map = {
        "Investor": "rezio.notifications.permissions.InvestorNotificationPermission",
        "Agent": "rezio.notifications.permissions.AgentNotificationPermission",
        # Add other roles and their corresponding permissions here
    }

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False

        user_roles = request.user.roles.values_list("name", flat=True)
        for role in user_roles:
            permission_class = self.role_permission_map.get(role)
            if permission_class:
                permission = permission_class()
                if permission.has_permission(request, view):
                    return True
        return False


class InvestorNotificationPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.user_type == User.INVESTOR


class AgentNotificationPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.user_type == User.AGENT


# Example for adding a new role
class CoOwnerNotificationPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.user_type == User.COOWNER
