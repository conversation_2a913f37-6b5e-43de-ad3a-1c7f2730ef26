from django.core.management.base import BaseCommand
from django.db import connection
from rezio.notifications.models import NotificationSettings

class Command(BaseCommand):
    help = 'Cleanup invalid notification settings'

    def handle(self, *args, **options):
        # Check if table exists
        with connection.cursor() as cursor:
            table_name = NotificationSettings._meta.db_table
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                );
            """, [table_name])
            table_exists = cursor.fetchone()[0]

        if not table_exists:
            self.stdout.write(
                self.style.WARNING('Notifications settings table does not exist. Skipping cleanup.')
            )
            return

        # Clean up notification settings with null users
        invalid_settings = NotificationSettings.objects.filter(user__isnull=True)
        count = invalid_settings.count()
        invalid_settings.delete()
        self.stdout.write(
            self.style.SUCCESS(f'Successfully deleted {count} invalid notification settings')
        )

        # Clean up orphaned notification settings
        orphaned_settings = NotificationSettings.objects.all()
        orphaned_count = 0
        for setting in orphaned_settings:
            if not setting.user_id:
                setting.delete()
                orphaned_count += 1
        
        if orphaned_count:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {orphaned_count} orphaned notification settings')
            )
