from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Prepare notifications app for testing by handling migrations and setup"

    def handle(self, *args, **options):
        try:
            self.stdout.write("Checking database connection...")
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")

            self.stdout.write("Making migrations for notifications app...")
            call_command("makemigrations", "notifications", verbosity=1)

            self.stdout.write("Applying all migrations...")
            call_command("migrate", verbosity=1)

            self.stdout.write("Verifying migrations...")
            call_command("showmigrations", "notifications", verbosity=1)

            self.stdout.write(
                self.style.SUCCESS(
                    "Successfully prepared notifications app for testing"
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error preparing notifications app: {str(e)}")
            )
            raise
