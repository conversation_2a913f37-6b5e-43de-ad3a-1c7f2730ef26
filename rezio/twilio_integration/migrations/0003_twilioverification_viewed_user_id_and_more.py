# Generated by Django 4.1.7 on 2025-03-11 05:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('twilio_integration', '0002_twilioverification_is_active'),
    ]

    operations = [
        migrations.AddField(
            model_name='twilioverification',
            name='viewed_user_id',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='twilioverification',
            unique_together={('phone_number', 'viewed_user_id')},
        ),
    ]
