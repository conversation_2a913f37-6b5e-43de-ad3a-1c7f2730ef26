import phonenumber_field
from django.db import migrations, connection


def remove_unique_constraint_if_exists(apps, schema_editor):
    """Check if the unique constraint exists before removing it."""
    table_name = "twilio_integration_twilioverification"
    constraint_name = (
        "twilio_integration_twilioverification_phone_number_viewed_user_id_id_uniq"
    )

    with connection.cursor() as cursor:
        # Check if constraint exists
        cursor.execute(
            f"""
            SELECT conname FROM pg_constraint
            WHERE conrelid = '{table_name}'::regclass
            AND conname = '{constraint_name}';
            """
        )
        constraint_exists = cursor.fetchone()

        # If constraint exists, drop it
        if constraint_exists:
            cursor.execute(
                f'ALTER TABLE "{table_name}" DROP CONSTRAINT "{constraint_name}";'
            )


class Migration(migrations.Migration):
    dependencies = [
        ("twilio_integration", "0003_twilioverification_viewed_user_id_and_more"),
    ]

    operations = [
        migrations.RunPython(
            remove_unique_constraint_if_exists, reverse_code=migrations.RunPython.noop
        ),
        migrations.AlterField(
            model_name="twilioverification",
            name="phone_number",
            field=phonenumber_field.modelfields.PhoneNumberField(
                max_length=128, region=None
            ),
        ),
    ]
