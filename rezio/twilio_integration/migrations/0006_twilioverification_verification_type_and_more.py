# Generated by Django 5.1.6 on 2025-04-21 12:20

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("twilio_integration", "0005_alter_twilioverification_unique_together"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="twilioverification",
            name="verification_type",
            field=models.CharField(
                choices=[("LOGIN", "Login"), ("CHAT", "Chat")],
                default="CHAT",
                max_length=20,
            ),
        ),
        migrations.AddIndex(
            model_name="twilioverification",
            index=models.Index(
                fields=["phone_number", "verification_type", "is_active"],
                name="twilio_inte_phone_n_c3e39e_idx",
            ),
        ),
    ]
