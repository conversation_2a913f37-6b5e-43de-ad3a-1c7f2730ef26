from django.contrib import admin
from rezio.twilio_integration.models import TwilioVerification


@admin.register(TwilioVerification)
class TwilioVerificationAdmin(admin.ModelAdmin):
    list_display = (
        "phone_number",
        "verification_sid",
        "attempts",
        "formatted_last_sent_at",
        "is_verified",
        "formatted_created_at",
        "is_active",
        "verification_type",
    )
    list_filter = ("is_verified", "is_active", "verification_type")
    search_fields = ("phone_number", "verification_sid")
    readonly_fields = ("created_at", "last_sent_at")
    ordering = ("-created_at",)

    def formatted_created_at(self, obj):
        return obj.created_at.strftime("%Y-%m-%d %H:%M")

    formatted_created_at.short_description = "Created At"
    formatted_created_at.admin_order_field = "created_at"

    def formatted_last_sent_at(self, obj):
        return obj.last_sent_at.strftime("%Y-%m-%d %H:%M")

    formatted_last_sent_at.short_description = "Last Sent At"
    formatted_last_sent_at.admin_order_field = "last_sent_at"
