import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from rezio.twilio_integration.text_choices import VerificationType
from rezio.twilio_integration.models import TwilioVerification
from rezio.twilio_integration.services.twilio_service import TwilioService
from rezio.user.utils import get_user_object
from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    DJANGO_LOGGER_NAME,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.custom_exceptions import InvalidSerializerDataException

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class SendOTPView(APIView):
    @general_exception_handler
    def post(self, request, viewed_user_id, viewed_user_role_name):
        """Send OTP to user"""
        phone_number = request.data.get("phone_number")
        name = request.data.get("name")
        if not all([phone_number, name]):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Missing required fields",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "phone_number and name are required"
                    },
                }
            )

        return self._send_otp(phone_number, name, viewed_user_id=viewed_user_id)

    @general_exception_handler
    def put(self, request, viewed_user_id, viewed_user_role_name):
        """Resend OTP"""
        phone_number = request.data.get("phone_number")
        name = request.data.get("name")

        if not all([phone_number, name]):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Missing required fields",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "phone_number and name are required"
                    },
                }
            )

        return self._send_otp(
            phone_number, name, is_resend=True, viewed_user_id=viewed_user_id
        )

    def _send_otp(
        self,
        phone_number: str,
        name: str,
        is_resend: bool = False,
        viewed_user_id: str = None,
    ) -> Response:
        """Helper method to send OTP with rate limiting"""
        # viewed_user = get_user_object(viewed_user_id)
        # verification = TwilioVerification.objects.filter(
        #     phone_number=phone_number, is_active=True, viewed_user_id=viewed_user, verification_type=VerificationType.CHAT
        # ).first()

        # if verification:
        #     # Check for rate limiting
        #     if verification.attempts >= 3:  # Max 3 attempts
        #         time_diff = timezone.now() - verification.last_sent_at
        #         if time_diff < timedelta(minutes=30):  # 30 minutes cooldown
        #             raise InvalidSerializerDataException(
        #                 {
        #                     KEY_MESSAGE: "OTP Limit exceeded. Please try again after some time.",
        #                     KEY_PAYLOAD: {},
        #                     KEY_ERROR: {
        #                         KEY_ERROR_MESSAGE: "OTP Limit exceeded. Please try again after some time."
        #                     },
        #                 }
        #             )

        # twilio_service = TwilioService()
        # result = twilio_service.send_verification(phone_number)

        # if result.get("error"):
        #     raise InvalidSerializerDataException(
        #         {
        #             KEY_MESSAGE: "Failed to send OTP",
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: result["error"],
        #         }
        #     )

        # if verification:
        #     verification.attempts += 1
        #     verification.last_sent_at = timezone.now()
        #     verification.save()
        # else:
        #     TwilioVerification.objects.create(
        #         phone_number=phone_number,
        #         verification_sid=result["data"].get("sid", ""),
        #     )

        # message = "OTP resent successfully" if is_resend else "OTP sent successfully"
        # logger.info(f"{message} to {phone_number}")

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "OTP sent successfully",
                KEY_PAYLOAD: {"phone_number": str(phone_number), "name": name},
                KEY_ERROR: {},
            },
        )
