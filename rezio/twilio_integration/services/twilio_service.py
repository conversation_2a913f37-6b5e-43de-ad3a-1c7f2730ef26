import logging
from typing import Dict

from django.conf import settings
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    <PERSON><PERSON><PERSON><PERSON>_LOGGER_NAME,
)
from rezio.utils.custom_exceptions import (
    TwilioException,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class TwilioService:
    """Service class to handle Twilio OTP operations"""

    def __init__(self):
        """Initialize Twilio client with credentials from settings"""
        try:
            self.client = Client(
                settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN
            )
            self.verify = self.client.verify.v2.services(
                settings.TWILIO_VERIFY_SERVICE_SID
            )
            logger.info("Twilio client initialized successfully")
        except Exception as error:
            logger.error(f"Error initializing Twilio client: {error}")
            raise TwilioException(
                {
                    KEY_MESSAGE: "Failed to initialize Twilio service",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: str(error)},
                }
            )

    def send_verification(self, phone_number: str) -> Dict:
        """
        Send OTP verification to the given phone number

        Args:
            phone_number (str): Phone number to send OTP to

        Returns:
            Dict: Response containing status and message
            {
                "message": str,
                "payload": {
                    "sid": str,
                    "status": str
                },
                "error": {}
            }

        Raises:
            InternalServerException: If there's an unexpected error
        """
        logger.info(f"Sending OTP verification to {phone_number}")
        try:
            verification = self.verify.verifications.create(
                to=phone_number, channel="sms"
            )
            logger.info(f"verification {verification}")
            logger.info(f"OTP sent successfully to {phone_number}")
            return {
                KEY_MESSAGE: "OTP sent successfully",
                KEY_PAYLOAD: {"sid": verification.sid},
                KEY_ERROR: {},
            }

        except TwilioRestException as error:
            logger.error(f"Twilio error sending OTP to {phone_number}: {error}")
            error_message = self._get_friendly_error_message(error)
            return {
                KEY_MESSAGE: "Failed to send OTP",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error_message},
            }
        except Exception as error:
            logger.error(f"Unexpected error sending OTP to {phone_number}: {error}")
            raise TwilioException(
                {
                    KEY_MESSAGE: "Internal server error while sending OTP",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: str(error)},
                }
            )

    def verify_otp(self, phone_number: str, otp: str) -> Dict:
        """
        Verify the OTP for given phone number

        Args:
            phone_number (str): Phone number to verify
            otp (str): OTP code to verify

        Returns:
            Dict: Response containing verification status
            {
                "message": str,
                "payload": {
                    "status": str
                },
                "error": {}
            }

        Raises:
            InternalServerException: If there's an unexpected error
        """
        logger.info(f"Verifying OTP for {phone_number}")
        try:
            verification_check = self.verify.verification_checks.create(
                to=phone_number, code=otp
            )

            if verification_check.status == "approved":
                logger.info(f"OTP verified successfully for {phone_number}")
                return {
                    KEY_MESSAGE: "OTP verified successfully",
                    KEY_PAYLOAD: {"status": verification_check.status},
                    KEY_ERROR: {},
                }
            else:
                logger.warning(f"Invalid OTP for {phone_number}")
                return {
                    KEY_MESSAGE: "Invalid OTP",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid OTP provided"},
                }

        except TwilioRestException as error:
            logger.error(f"Twilio error verifying OTP for {phone_number}: {error}")
            error_message = self._get_friendly_error_message(error)
            return {
                KEY_MESSAGE: "Failed to verify OTP",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error_message},
            }
        except Exception as error:
            logger.error(f"Unexpected error verifying OTP for {phone_number}: {error}")
            raise TwilioException(
                {
                    KEY_MESSAGE: "Internal server error while verifying OTP",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: str(error)},
                }
            )

    def _get_friendly_error_message(self, error: TwilioRestException) -> str:
        """
        Convert Twilio error codes to user-friendly messages

        Args:
            error (TwilioRestException): The Twilio exception

        Returns:
            str: User-friendly error message
        """
        error_codes = {
            60200: "Invalid phone number format",
            60203: "Maximum send attempts reached. Please try again later",
            60205: "Rate limit exceeded. Please try again later",
            20404: "Verification not found",
            20429: "Too many verification attempts. Please try again later",
            60202: "Max check attempts reached",
        }

        return error_codes.get(error.code, str(error))

    def validate_phone_number(self, phone_number) -> bool:
        if phone_number:
            try:
                response = self.client.lookups.v2.phone_numbers(phone_number).fetch()
                return response.valid
            except Exception as error:
                raise TwilioException(
                    {
                        KEY_MESSAGE: "Internal server error while verifying OTP",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: str(error)},
                    }
                )
        return False
