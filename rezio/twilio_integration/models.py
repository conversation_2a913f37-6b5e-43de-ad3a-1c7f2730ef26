from datetime import datetime, timedelta
from django.utils import timezone
from django.db import models
from django.conf import settings
from phonenumber_field.modelfields import PhoneNumberField

from rezio.user.models import User
from rezio.twilio_integration.text_choices import VerificationType


# Create your models here.
# Add this to existing models.py


class TwilioVerification(models.Model):
    phone_number = PhoneNumberField(blank=False, null=False)
    verification_sid = models.CharField(max_length=255)
    attempts = models.IntegerField(default=1)
    last_sent_at = models.DateTimeField(auto_now=True)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    viewed_user_id = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    verification_type = models.CharField(
        max_length=20, choices=VerificationType.choices, default=VerificationType.CHAT
    )

    class Meta:
        verbose_name_plural = "Twilio Verifications"
        indexes = [
            models.Index(fields=["phone_number", "verification_type", "is_active"]),
        ]

    def __str__(self):
        return (
            f"{self.phone_number} - {'Verified' if self.is_verified else 'Unverified'}"
        )

    def has_exceeded_rate_limit(self) -> bool:
        """Check if rate limit is exceeded based on verification type"""
        if self.verification_type == "LOGIN":
            max_attempts = settings.LOGIN_OTP_MAX_ATTEMPTS
            cooldown_minutes = settings.LOGIN_OTP_COOLDOWN_MINUTES

            time_diff = timezone.now() - self.last_sent_at
            if time_diff >= timedelta(minutes=cooldown_minutes):
                # Reset attempts if cooldown period has passed
                self.attempts = 1
                self.save()
                return False

            if self.attempts >= max_attempts:
                return True
        return False
