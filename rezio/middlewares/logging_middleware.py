import json
import logging
import threading
import uuid

from django.utils.deprecation import MiddlewareMixin

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)

local_storage = threading.local()


class APILoggingMiddleware(MiddlewareMixin):
    def process_request(self, request):
        local_storage.request_uuid = str(uuid.uuid4())
        logger.info(f"Request Method: {request.method}, Path: {request.path}")

        if request.method == "GET":
            logger.info("GET Params: %s", request.GET)

        elif request.method in ["POST", "PUT"]:
            try:
                logger.info("Params: %s", request.GET)
                body = request.body.decode("utf-8")
                logger.info("Request Body: %s", body)
            except Exception as e:
                logger.error("Could not read request body: %s", e)

    def process_response(self, request, response):
        try:
            if response.get("data"):
                # Access data directly from request.data
                response_data = response.get("data", {})
                logger.info(" this is response data: %s", response)
            else:
                response_data = response.content.decode(
                    "utf-8"
                )  # Assuming UTF-8 encoding

            logger.info(
                "Response Status: %s, Data: %s", response.status_code, response_data
            )
        except (ValueError, json.JSONDecodeError) as e:
            logger.warning("Could not decode response as JSON: %s", e)
            logger.info(
                "Response Status: %s, Content: %s",
                response.status_code,
                response.content,
            )
        except Exception as e:
            logger.info("Error processing response: %s", e)

        return response


class RequestUUIDFilter(logging.Filter):
    """Logging filter to add request UUID to log records"""

    def filter(self, record):
        record.request_uuid = getattr(local_storage, "request_uuid", None)
        return True


class RequestUserIDFilter(logging.Filter):
    """Logging filter to add user id to log records"""

    def filter(self, record):
        record.user_id = getattr(local_storage, "user_id", None)
        return True
