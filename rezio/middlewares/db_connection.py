from django.db import connection
import logging

logger = logging.getLogger("django")


class DBConnectionManagementMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            return self.get_response(request)
        finally:
            # Close database connections
            connection.close()
            logger.debug("Database connection closed after request")

    def process_exception(self, request, exception):
        # Ensure connections are closed even if an exception occurs
        connection.close()
        logger.warning(f"Database connection closed after exception: {str(exception)}")
        return None
