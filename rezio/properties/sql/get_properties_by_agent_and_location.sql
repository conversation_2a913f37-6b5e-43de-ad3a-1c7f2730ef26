CREATE OR REPLACE FUNCTION get_properties_by_agent_and_location(
    p_agent_id INTEGER DEFAULT NULL,
    p_user_lng DOUBLE PRECISION DEFAULT NULL,  -- Longitude
    p_user_lat DOUBLE PRECISION DEFAULT NULL,  -- Latitude
    p_radius_km DOUBLE PRECISION DEFAULT 5,    -- Radius in km, default 5 km
    p_action_status VARCHAR DEFAULT '2',
    p_is_associated BOOLEAN DEFAULT TRUE,
    p_is_request_expired BOOLEAN DEFAULT FALSE,
    p_is_archived BOOLEAN DEFAULT FALSE,
    p_property_publish_status VARCHAR DEFAULT '1',
    p_owner_intent VARCHAR DEFAULT 'not for sale'
) RETURNS TABLE (
    property_id BIGINT,
    distance_km DOUBLE PRECISION
) AS $$
BEGIN
    RETURN QUERY
        SELECT DISTINCT 
            p.id,
            ST_DistanceSphere(
                p.geom, 
                ST_MakePoint(p_user_lng, p_user_lat)
            ) / 1000 AS distance_km
        FROM properties_property p
        INNER JOIN properties_agentassociatedproperty aap 
            ON p.id = aap.property_id
        LEFT JOIN properties_deletedproperty dp 
            ON p.id = dp.property_id
        WHERE 
            -- Agent association conditions
            aap.agent_profile_id = p_agent_id
            AND aap.action_status = p_action_status
            AND aap.is_associated = p_is_associated
            AND aap.is_request_expired = p_is_request_expired

            -- Property status conditions
            AND dp.property_id IS NULL   
            AND p.is_archived = p_is_archived
            AND p.property_publish_status = p_property_publish_status
            AND p.owner_intent != p_owner_intent

             AND (p_user_lng IS NULL OR p_user_lat IS NULL OR (
                ST_DWithin(
                    p.geom::geography,
                    ST_MakePoint(p_user_lng, p_user_lat)::geography,
                    p_radius_km * 1000   
                )
            ))
        ORDER BY  distance_km;   

    IF NOT FOUND THEN
        RAISE NOTICE 'No properties found matching the criteria';
    END IF;

END;
$$ LANGUAGE plpgsql;