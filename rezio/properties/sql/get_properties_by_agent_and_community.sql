CREATE OR REPLACE FUNCTION get_properties_by_agent_and_community(
    p_agent_id INTEGER DEFAULT NULL,
    p_search_term VARCHAR DEFAULT NULL,
    p_action_status VARCHAR DEFAULT '2',
    p_is_associated BOOLEAN DEFAULT TRUE,
    p_is_request_expired BOOLEAN DEFAULT FALSE,
    p_is_archived BOOLEAN DEFAULT FALSE,
    p_property_publish_status VARCHAR DEFAULT '1',
    p_owner_intent VARCHAR DEFAULT 'not for sale'
) RETURNS TABLE (property_id BIGINT) AS $$
BEGIN
    RETURN QUERY
        SELECT DISTINCT p.id
        FROM properties_property p
        INNER JOIN properties_agentassociatedproperty aap 
            ON p.id = aap.property_id
        LEFT JOIN properties_deletedproperty dp 
            ON p.id = dp.property_id
        LEFT JOIN properties_community c 
            ON p.community_id = c.id
        WHERE 
            -- Agent association conditions
            aap.agent_profile_id = p_agent_id
            AND aap.action_status = p_action_status
            AND aap.is_associated = p_is_associated
            AND aap.is_request_expired = p_is_request_expired

            -- Property status conditions
            AND dp.property_id IS NULL
            AND p.is_archived = p_is_archived
            AND p.property_publish_status = p_property_publish_status
            AND p.owner_intent != p_owner_intent

            -- Community name condition (only applied if p_search_term is provided)
            AND (p_search_term IS NULL OR (
                COALESCE(
                    c.sub_loc_5,
                    c.sub_loc_4,
                    c.sub_loc_3,
                    c.sub_loc_2,
                    c.sub_loc_1,
                    c.name
                ) ILIKE '%' || p_search_term || '%'
                OR c.name ILIKE '%' || p_search_term || '%'
            ));

    -- Optional: Add a check if no rows were found
    IF NOT FOUND THEN
        RAISE NOTICE 'No properties found matching the criteria';
    END IF;

END;
$$ LANGUAGE plpgsql;