from django.urls import path
from rest_framework.routers import DefaultRouter

from rezio.properties import views

router = DefaultRouter()
router.register(r"property", views.PropertyViewSet, basename="property")
router.register(
    r"manual_save_property",
    views.ManualSavePropertyViewSet,
    basename="manual-save-property",
)
router.register(r"v1/property", views.PropertyViewSetV1, basename="property-v1")
router.register(
    r"v1/manual-property",
    views.ManualSavePropertyViewSetV1,
    basename="manual-property-v1",
)


urlpatterns = [
    # ... other urls ...
    path("community/", views.CommunityBuildingViewSet.as_view(), name="community"),
    path(
        "<int:pk>/specifications/",
        views.PropertyViewSet.as_view({"get": "retrieve_property_specifications"}),
        name="property-specifications",
    ),
    path(
        "unit/",
        views.UnitNoSearchViewSet.as_view(),
        name="unit",
    ),
    path(
        "save_community_info/",
        views.SaveCommunityUnitViewSet.as_view(),
        name="save-community-info",
    ),
    path(
        "manual_save_community_info/",
        views.ManualSaveCommunityUnitViewSet.as_view(),
        name="manual-save-community-info",
    ),
    path(
        "availability_and_status/",
        views.AvailabilityAndStatusViewSet.as_view(),
        name="availability-and-status",
    ),
    path(
        "property_documents/",
        views.PropertyDocumentsViewSet.as_view(),
        name="property-documents",
    ),
    path(
        "occupancy_status_choices/",
        views.OccupancyStatusViewSet.as_view(),
        name="occupancy-status-choices",
    ),
    path(
        "<int:property_id>/property_completion_state/",
        views.PropertyCompletionStateViewSet.as_view(),
        name="property-completion-state",
    ),
    path(
        "<int:property_id>/get_saved_community_and_building_info/",
        views.GetSavedCommunityAndBuildingInfo.as_view(),
        name="get-saved-community-and-building-info",
    ),
    # path(
    #     "<int:property_id>/get_property_details/",
    #     views.PropertyDetailsView.as_view(),
    #     name="get-property-details",
    # ),
    path(
        "<int:property_id>/get_saved_location_details/",
        views.GetSavedLocationDetails.as_view(),
        name="get-saved-location-details",
    ),
    path(
        "<int:property_id>/add_to_portfolio/",
        views.AddToPortfolioViewSet.as_view(),
        name="add-to-portfolio",
    ),
    path(
        "<int:property_id>/manual_save_property_specifications/",
        views.ManualSavePropertySpecifications.as_view(),
        name="manual-save-property-specifications",
    ),
    path(
        "<int:property_id>/get_saved_property_specifications/",
        views.GetSavedPropertySpecifications.as_view(),
        name="get-saved-property-specifications",
    ),
    path(
        "<int:property_id>/save_property_specifications/",
        views.SavePropertySpecifications.as_view(),
        name="save-property-specifications",
    ),
    path(
        "<int:property_id>/save_price/",
        views.SavePrice.as_view(),
        name="save-price",
    ),
    path(
        "<int:property_id>/edit_income_from_property/",
        views.SavePrice.as_view(),
        name="edit-income-from-property",
    ),
    # path(
    #     "<int:property_id>/get_property_financials/",
    #     views.GetPropertyFinancials.as_view(),
    #     name="get-property-financials",
    # ),
    path(
        "unit_images_section_types/",
        views.SectionTypeListAPIView.as_view(),
        name="section-type-list",
    ),
    path(
        "<int:property_id>/upload_unit_section_media/",
        views.PropertyMediaUploadAPIView.as_view(),
        name="upload-unit-section-media",
    ),
    path(
        "<int:property_id>/get_unit_section_media/",
        views.PropertyMediaRetrieveAPIView.as_view(),
        name="get-unit-section-media",
    ),
    path(
        "<int:property_id>/update_dewa_id/",
        views.UpdateDewaID.as_view(),
        name="update-dewa-id",
    ),
    path(
        "<int:property_id>/income_from_property/",
        views.EditIncomeFromPropertyView.as_view(),
        name="income-from-property",
    ),
    path(
        "<int:property_id>/edit_property_features/",
        views.EditPropertyFeaturesView.as_view(),
        name="edit-property-features",
    ),
    path(
        "<int:property_id>/add_property_co_owner/",
        views.PropertyCoOwnerView.as_view(),
        name="add-investor-user-as-co-owner",
    ),
    path(
        "<int:property_id>/manually_add_property_co_owner/",
        views.PropertyManuallyAddedCoOwnerView.as_view(),
        name="add-unregistered-investor-user-as-co-owner",
    ),
    path(
        "<int:property_id>/get_property_co_owner/",
        views.GetPropertyCoOwnersView.as_view(),
        name="get-all-investor-linked-as-co-owner",
    ),
    path(
        "<int:property_id>/add_property_agents/",
        views.PropertyAgentsViewSet.as_view(),
        name="add-property-agents",
    ),
    path(
        "<int:property_id>/delete_property_co_owner/",
        views.DeleteCoOwnerView.as_view(),
        name="delete-property-co-owner",
    ),
    path(
        "property_analytics/",
        views.PropertyAnalyticsViewSet.as_view(),
        name="property-analytics",
    ),
    path(
        "<int:property_id>/save_owner_intent/",
        views.PropertyIntentViewSet.as_view(),
        name="save-owner-intent",
    ),
    path(
        "<int:property_id>/associated_agents/",
        views.PropertyAssociatedAgentsViewSet.as_view(),
        name="associated-agents",
    ),
    path(
        "<int:property_id>/property_cost/",
        views.PropertyCostView.as_view(),
        name="property-cost",
    ),
    path(
        "<int:property_id>/add_property_owner/",
        views.PropertyOwnerView.as_view(),
        name="add-investor-user-as-owner",
    ),
    path(
        "<int:property_id>/manual_add_property_owner/",
        views.PropertyManuallyAddedOwnerView.as_view(),
        name="add-unregistered-investor-user-as-owner",
    ),
    path(
        "<int:property_id>/get_property_owner/",
        views.GetPropertyOwnerView.as_view(),
        name="get-property-owner",
    ),
    path(
        "<int:property_id>/remove_owner/",
        views.DeleteOwnerView.as_view(),
        name="remove-property-owner",
    ),
    path(
        "<int:property_id>/archive_property/",
        views.PropertyArchiveViewSet.as_view(),
        name="archive-property",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/archived_properties/",
        views.ListArchivedPropertyViewSet.as_view(),
        name="archived-properties",
    ),
    path(
        "<int:property_id>/agent_property_access/",
        views.AgentPropertyRequestViewSet.as_view(),
        name="agent-property-access",
    ),
    path(
        "<int:property_id>/delete/",
        views.DeletePropertyView.as_view(),
        name="delete-property",
    ),
    path(
        "upload_floor_plan/<int:property_id>/",
        views.UploadFloorPlanView.as_view(),
        name="upload-floor-plan",
    ),
    path(
        "upload_payment_plan/<int:property_id>/",
        views.UploadPaymentPlanView.as_view(),
        name="upload-payment-plan",
    ),
    path(
        "ai/get_property_details/<int:property_id>/",
        views.AIPropertyDetailsView.as_view(),
        name="get-property-details-for-ai",
    ),
    path(
        "v1/upload-external-media-image/<int:property_id>/",
        views.PropertyExternalMediaImageUploadView.as_view(),
        name="upload-external-media-image",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_details/<int:property_id>/",
        views.PropertyDetailsViewSet.as_view(),
        name="property-details",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_basic_details/<int:property_id>/",
        views.PropertyBasicDetailsViewSet.as_view(),
        name="property-basic-details",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_financial_details/<int:property_id>/",
        views.PropertyFinancialDetailsViewSet.as_view(),
        name="property-financial-details",
    ),
    path("countries/", views.CountryListView.as_view(), name="country-list"),
    path("states/<int:country_id>/", views.StateListView.as_view(), name="state-list"),
    path("cities/", views.CityListView.as_view(), name="city-list"),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/portfolio/",
        views.PropertyPortfolioViewSet.as_view(),
        name="user-portfolio",
    ),
    path(
        "edit_lease_conditions/<int:property_id>/",
        views.LeaseConditionsViewSet.as_view(),
        name="edit-lease-conditions",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_options/<int:property_id>/",
        views.PropertyOptionsAPIView.as_view(),
        name="property-options",
    ),
    path(
        "<int:property_id>/co_owner_property_access/",
        views.CoOwnerPropertyRequestViewSet.as_view(),
        name="co_owner-property-access",
    ),
    path(
        "<int:property_id>/direct_add_property_to_portfolio/",
        views.DirectAddPropertyToPortfolioViewSet.as_view(),
        name="direct-add-property-to-portfolio",
    ),
    path(
        "<int:property_id>/details/",
        views.PropertyDetailView.as_view(),
        name="property-details",
    ),
    path(
        "presigned_url/<int:property_id>/",
        views.PropertyMediaPresignedURLView.as_view(),
        name="property-media-presigned-url",
    ),
    path(
        "preview/<int:property_id>/<str:viewed_user_id>/<str:viewed_user_role>/",
        views.LinkPreview.as_view(),
        name="link_preview",
    ),
    path(
        "owner_unit_images/<int:property_id>/",
        views.OwnerUnitImagesView.as_view(),
        name="owner_unit_images",
    ),
    path(
        "agent_portfolio_analytics/",
        views.AgentPortfolioAnalyticsViewSet.as_view(),
        name="agent_portfolio_analytics",
    ),
    path(
        "compressed-image/",
        views.CompressedImageView.as_view(),
        name="compressed_image",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_share_options/<int:property_id>/",
        views.PropertyShareOptionsAPIView.as_view(),
        name="property-share-options",
    ),
    path(
        "trends_and_transactions/",
        views.PropertyTransactionsListingsView.as_view(),
        name="property_listings",
    ),
    path(
        "v1/property-save-unit-details/<int:property_id>/",
        views.PropertySaveUnitDetailsViewV1.as_view(),
        name="property-save-unit-details-v1",
    ),
    path(
        "v1/save-property-specifications/<int:property_id>/",
        views.SavePropertySpecificationsV1.as_view(),
        name="save-property-specifications-v1",
    ),
    path(
        "v1/manual-save-property-specifications/<int:property_id>/",
        views.ManualSavePropertySpecificationsV1.as_view(),
        name="manual-save-property-specifications-v1",
    ),
    path(
        "generate_similar_transactions_notifications/",
        views.GenerateSimilarTransactionsNotifications.as_view(),
        name="generate-similar-transactions-notifications",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_attributes/<int:property_id>/",
        views.PropertyAttributesViewSet.as_view(),
        name="property-attributes",
    ),
    path(
        "brn_update/",
        views.BRNUpdateTask.as_view(),
        name="brn_update",
    ),
    path(
        "v1/update-unit-number/<int:property_id>/",
        views.PropertyUpdateUnitNumberV1.as_view(),
        name="property-update-unit-number-v1",
    ),
    path(
        "v1/upload-external-media-document/<int:property_id>/",
        views.PropertyExternalMediaDocumentUploadView.as_view(),
        name="upload-external-media-document",
    ),
    path(
        "explore/",
        views.ExploreViewSet.as_view(),
        name="explore-properties",
    ),
    path(
        "explore_addresses_search/",
        views.PropertyAddressSearchView.as_view(),
        name="explore-addresses-search",
    ),
    path(
        "open-for-collaboration/<int:property_id>/",
        views.OpenForCollaborationUpdateView.as_view(),
        name="open-for-collaboration-update",
    ),
]


urlpatterns += router.urls
