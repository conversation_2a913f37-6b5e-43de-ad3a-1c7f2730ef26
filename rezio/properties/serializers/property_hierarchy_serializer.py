import json
import logging

from django.conf import settings
from rest_framework import serializers

from rezio.properties.constants import (
    portfolio_fields_to_check,
    DEFAULT_PROPERTY_IMAGES,
    EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES,
    EXPLORE_VISIBILITY_CONSTRAINT_LAND_ALLOWED_FIELDS,
    EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_ALLOWED_FIELDS,
    EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_TYPES,
)
from rezio.properties.models import (
    Property,
    PropertyFloorPlan,
    PropertyFeatures,
    AgentAssociatedProperty,
    PropertyUnitSections,
    PropertyAvailabilityAndStatus,
    PropertySalesUnitHistory,
    PropertyRentalUnitHistory,
    PropertyPaymentPlan,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyData,
    UserLevelPropertyFeatures,
    UserLevelPropertyFinancialDetails,
)
from rezio.properties.serializers import (
    PropertyFloorPlanSerializer,
    PropertySectionSerializer,
    PropertySalesUnitHistorySerializer,
    PropertyRentalUnitHistorySerializer,
    PropertyPaymentPlanSerializer,
)
from rezio.properties.text_choices import (
    PropertyAgentType,
    PropertyAvailabilityStatus,
    OwnerIntentForProperty,
    PropertyCategory,
    UserRequestActions,
    PropertyLockType,
    PropertyType,
)
from rezio.properties.utils import (
    build_property_address,
    create_user_level_filter,
    get_s3_object,
    get_property_building_name,
    build_return_values_for_attributes,
    build_return_values_for_scrollable_component_attributes,
    get_primary_number,
    get_primary_phone_code,
    build_return_values_portfolio_attributes,
    build_return_values_portfolio_specification_attributes,
    get_exchange_rates,
    get_property_gross_yield,
    build_property_address_explore,
)
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.models import InvestorProfile, AgentProfile
from rezio.user.serializers import AgentDetailSerializer
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


# Serializer for location_details_attributes
class LocationDetailsAttributesSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    country_code = serializers.SerializerMethodField()
    building_street = serializers.SerializerMethodField()
    unit_number = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    pin_code = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "country",
            "country_code",
            "building_street",
            "unit_number",
            "state",
            "city",
            "pin_code",
        ]

    def get_country(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.country:
            country_name = instance.country.name
        else:
            country_name = None
        return build_return_values_for_attributes(
            country_name,
            "country",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_country_code(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.country:
            country_code = instance.country.short_name
        else:
            country_code = None
        return build_return_values_for_attributes(
            country_code,
            "country",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_building_street(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        building_street = get_property_building_name(instance)
        return build_return_values_for_attributes(
            building_street,
            "building_street",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_unit_number(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        viewed_user = self.context.get("viewed_user")
        viewed_user_role = self.context.get("viewed_user_role")
        self_view = self.context.get("self_view")

        if (
            self_view
            and viewed_user == instance.created_by
            and viewed_user_role == instance.created_by_role
            and not instance.property_monitor_address_id
        ):
            make_non_editable = False
        else:
            make_non_editable = True

        return build_return_values_for_attributes(
            instance.unit_number,
            "unit_number",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_non_editable=make_non_editable,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_state(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.state:
            state_name = instance.state.name
        else:
            state_name = None
        return build_return_values_for_attributes(
            state_name,
            "state",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_city(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.city:
            city_name = instance.city.name
        else:
            city_name = None
        return build_return_values_for_attributes(
            city_name,
            "city",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_pin_code(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.postal_code,
            "pin_code",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )


class BedroomDataSerializer(serializers.ModelSerializer):
    number_of_bedrooms = serializers.IntegerField()
    number_of_master_bedrooms = serializers.IntegerField()
    number_of_other_bedrooms = serializers.IntegerField()
    number_of_maid_rooms = serializers.IntegerField()
    number_of_study_rooms = serializers.IntegerField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "number_of_bedrooms",
            "number_of_master_bedrooms",
            "number_of_other_bedrooms",
            "number_of_maid_rooms",
            "number_of_study_rooms",
        ]


class BathroomDataSerializer(serializers.ModelSerializer):
    number_of_bathrooms = serializers.IntegerField(default=0)
    number_of_common_bathrooms = serializers.IntegerField(default=0)
    number_of_attached_bathrooms = serializers.IntegerField(default=0)
    number_of_powder_rooms = serializers.IntegerField(default=0)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "number_of_bathrooms",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["number_of_bathrooms"] = data.get("number_of_bathrooms", 0)
        return data


class ParkingDataSerializer(serializers.ModelSerializer):
    parking_numbers = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_numbers",
        ]

    @staticmethod
    def get_parking_numbers(instance):
        return instance.get_parking_number()


class PropertySpecificationsAttributesSerializer(serializers.ModelSerializer):
    property_type = serializers.SerializerMethodField()
    total_area = serializers.SerializerMethodField()
    carpet_area = serializers.SerializerMethodField()
    balcony_area = serializers.SerializerMethodField()
    floor_number = serializers.SerializerMethodField()
    building_type = serializers.SerializerMethodField()
    total_floors = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "floor_number",
            "building_type",
            "total_floors",
        ]

    def get_property_type(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.property_type,
            "property_type",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_total_area(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.total_area,
            "total_area",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            unit_field=True,
            property_object=instance,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_carpet_area(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.carpet_area,
            "carpet_area",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            unit_field=True,
            property_object=instance,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_balcony_area(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.balcony_area,
            "balcony_area",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            unit_field=True,
            property_object=instance,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_floor_number(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.floor_number,
            "floor_number",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_building_type(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.building_type,
            "building_type",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_total_floors(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.total_floors,
            "total_floors",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )


class RestroomDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyFeatures
        fields = ["no_of_shared_restrooms", "no_of_private_restrooms"]


class LiftDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyFeatures
        fields = ["common_lift", "no_of_personal_lift"]


class CommercialParkingDataSerializer(serializers.ModelSerializer):
    reserved_parking_number = serializers.SerializerMethodField()

    class Meta:
        model = PropertyFeatures
        fields = ["public_parking", "no_of_reserved_parking", "reserved_parking_number"]

    @staticmethod
    def get_reserved_parking_number(instance):
        return instance.get_reserved_parking_number()


class PropertyUnitFeaturesAttributesSerializer(serializers.ModelSerializer):
    branded_building = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    water_storage_available = serializers.SerializerMethodField()
    security_available = serializers.SerializerMethodField()
    property_on_main_road = serializers.SerializerMethodField()
    corner_property = serializers.SerializerMethodField()
    ideal_for = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyFeatures
        fields = [
            "branded_building",
            "furnished",
            "premium_view",
            "water_storage_available",
            "security_available",
            "property_on_main_road",
            "corner_property",
            "ideal_for",
        ]

    def get_branded_building(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.branded_building
        return build_return_values_for_attributes(
            value,
            "branded_building",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_furnished(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.furnished
        return build_return_values_for_attributes(
            value,
            "furnished",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_premium_view(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.premium_view
        edit_access = self.context.get("edit_access", False)
        return build_return_values_for_attributes(
            value,
            "premium_view",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_water_storage_available(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.water_storage_available
        return build_return_values_for_attributes(
            value,
            "water_storage_available",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_security_available(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.security_available
        return build_return_values_for_attributes(
            value,
            "security_available",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_property_on_main_road(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.property_on_main_road
        return build_return_values_for_attributes(
            value,
            "property_on_main_road",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_corner_property(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.corner_property
        return build_return_values_for_attributes(
            value,
            "corner_property",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_ideal_for(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        make_invisible = self.context.get("make_invisible", False)
        value = None
        if instance:
            value = instance.get_tags()
        return build_return_values_for_attributes(
            value,
            "ideal_for",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )


class FloorPlanAttributesSerializer(serializers.ModelSerializer):
    floor_plan = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = ["floor_plan"]

    def get_floor_plan(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        is_common_view = self.context.get("is_common_view")

        filters = {"property": instance}
        if self.context.get("viewed_user_role").name == AGENT:
            filters.update(
                {
                    "created_by": self.context.get("viewed_user"),
                    "created_by_role": self.context.get("viewed_user_role"),
                }
            )
        else:
            filters.update({"created_by_role": self.context.get("viewed_user_role")})

        floor_plan = PropertyFloorPlan.objects.filter(**filters).order_by("order_no")
        value = PropertyFloorPlanSerializer(floor_plan, many=True).data
        floor_plan_response = build_return_values_for_attributes(
            value,
            "floor_plan",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=is_common_view,
        )

        floor_plan_response["share_icon"] = True
        if (
            not floor_plan_response.get("value")
            or instance.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
            or is_common_view
        ):
            floor_plan_response["share_icon"] = False
        return floor_plan_response


class OtherInformationAttributesSerializer(serializers.ModelSerializer):
    electricity_water_number = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = ["electricity_water_number"]

    def get_electricity_water_number(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.dewa_id,
            "electricity_water_number",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )


class PropertyOwnerDetailsSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(source="owner.user_id")
    name = serializers.CharField(source="owner.name")
    ownership_percentage = serializers.FloatField(source="lead_owner_percentage")
    profile_photo = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()
    phone_code = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "user_id",
            "name",
            "profile_photo",
            "ownership_percentage",
            "phone_number",
            "phone_code",
        ]

    @staticmethod
    def get_profile_photo(instance):
        owner = instance.owner
        if owner:
            if owner.profile_photo_key:
                return get_s3_object(owner.profile_photo_key)
        return None

    @staticmethod
    def get_phone_number(instance):
        return get_primary_number(instance.owner.user.primary_phone_number)

    @staticmethod
    def get_phone_code(instance):
        return get_primary_phone_code(instance.owner.user.primary_phone_number)


class InvestorDetailsSerializer(serializers.ModelSerializer):
    ownership_percentage = serializers.FloatField(default=None)
    profile_photo = serializers.SerializerMethodField()

    class Meta:
        model = InvestorProfile
        fields = ["user_id", "name", "profile_photo", "ownership_percentage"]

    @staticmethod
    def get_profile_photo(instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    @staticmethod
    def get_phone_number(instance):
        return get_primary_number(instance.user.primary_phone_number)

    @staticmethod
    def get_phone_code(instance):
        return get_primary_phone_code(instance.user.primary_phone_number)


class PropertyAgentsDetailsSerializer(serializers.ModelSerializer):
    user_id = serializers.CharField(source="agent_profile.user_id")
    name = serializers.CharField(source="agent_profile.name")
    agency = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField()
    license_verification_status = serializers.IntegerField(
        source="agent_profile.license_verification_status"
    )
    working_type = serializers.IntegerField(source="agent_profile.working_type")
    self_view = serializers.SerializerMethodField(default=False)
    action_status = serializers.IntegerField()
    phone_number = serializers.SerializerMethodField()
    phone_code = serializers.SerializerMethodField()
    agent_id = serializers.IntegerField(source="agent_profile.id")

    class Meta:
        model = AgentAssociatedProperty
        fields = [
            "user_id",
            "name",
            "profile_photo",
            "agency",
            "license_verification_status",
            "working_type",
            "self_view",
            "action_status",
            "phone_number",
            "phone_code",
            "agent_id",
        ]

    @staticmethod
    def get_profile_photo(instance):
        agent_profile = instance.agent_profile
        if agent_profile.profile_photo_key:
            return get_s3_object(agent_profile.profile_photo_key)
        return None

    @staticmethod
    def get_agency(instance):
        agency = instance.agent_profile.agency
        if agency:
            return agency.name
        return None

    def get_self_view(self, instance):
        viewer_role = self.context.get("viewer_role")
        return self.context.get("self_view", False) and viewer_role.name == AGENT

    @staticmethod
    def get_phone_number(instance):
        return get_primary_number(instance.agent_profile.user.primary_phone_number)

    @staticmethod
    def get_phone_code(instance):
        return get_primary_phone_code(instance.agent_profile.user.primary_phone_number)


class ScrollableComponentPropertySpecificationsAttributesSerializer(
    serializers.ModelSerializer
):
    property_type = serializers.SerializerMethodField()
    total_area = serializers.SerializerMethodField()
    carpet_area = serializers.SerializerMethodField()
    balcony_area = serializers.SerializerMethodField()
    floor_number = serializers.SerializerMethodField()
    number_of_bedrooms = serializers.SerializerMethodField()
    number_of_bathrooms = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "floor_number",
            "number_of_bedrooms",
            "number_of_bathrooms",
        ]

    def get_property_type(self, instance):
        return build_return_values_for_scrollable_component_attributes(
            instance.property_type,
            "property_type",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            property_object=instance,
            make_non_editable=True,
        )

    def get_total_area(self, instance):
        return build_return_values_for_scrollable_component_attributes(
            instance.total_area,
            "total_area",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            unit_field=True,
            is_common_view=self.context.get("is_common_view", False),
            property_object=instance,
            make_non_editable=True,
        )

    def get_carpet_area(self, instance):
        return build_return_values_for_scrollable_component_attributes(
            instance.carpet_area,
            "carpet_area",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            unit_field=True,
            property_object=instance,
            make_non_editable=True,
        )

    def get_balcony_area(self, instance):
        return build_return_values_for_scrollable_component_attributes(
            instance.balcony_area,
            "balcony_area",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            unit_field=True,
            property_object=instance,
            make_non_editable=True,
        )

    def get_floor_number(self, instance):
        return build_return_values_for_scrollable_component_attributes(
            instance.floor_number,
            "floor_number",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            property_object=instance,
            make_non_editable=True,
        )

    def get_number_of_bedrooms(self, instance):
        number_of_bedrooms = instance.number_of_bedrooms
        return build_return_values_for_scrollable_component_attributes(
            number_of_bedrooms,
            "number_of_bedrooms",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            property_object=instance,
            make_non_editable=True,
        )

    def get_number_of_bathrooms(self, instance):
        total_bathrooms = instance.number_of_bathrooms
        if total_bathrooms is not None and total_bathrooms == 0:
            total_bathrooms = None
        return build_return_values_for_scrollable_component_attributes(
            total_bathrooms,
            "bathroom_data",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            is_common_view=self.context.get("is_common_view", False),
            property_object=instance,
            make_non_editable=True,
        )


class AddressDetailsAttributesSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    building_street = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = ["country", "building_street", "address"]

    def get_country(self, instance):
        non_display_fields = self.context.get("non_display_fields", [])
        if instance.country:
            country_name = instance.country.name
        else:
            country_name = None
        return build_return_values_for_attributes(
            country_name, "country", non_display_fields
        )

    def get_building_street(self, instance):
        non_display_fields = self.context.get("non_display_fields", [])
        building_street = get_property_building_name(instance)
        return build_return_values_for_attributes(
            building_street, "building_street", non_display_fields
        )

    def get_address(self, instance):
        non_display_fields = self.context.get("non_display_fields", [])
        address = build_property_address(instance)
        return build_return_values_for_attributes(
            address, "address", non_display_fields
        )


class PropertyUnitImagesSerializer(serializers.ModelSerializer):
    unit_images = serializers.SerializerMethodField(read_only=True)
    default_image = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Property
        fields = ["default_image", "unit_images"]

    def get_unit_images(self, instance):
        filters = create_user_level_filter(
            instance,
            self.context.get("created_by", None),
            self.context.get("created_by_role", None),
        )
        unit_sections = PropertyUnitSections.objects.filter(**filters).order_by("id")

        data = PropertySectionSerializer(unit_sections, many=True).data
        return build_return_values_for_attributes(
            data,
            "unit_images",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            display_by_default=True,
        )

    def get_default_image(self, instance):
        return build_return_values_for_attributes(
            instance.default_image,
            "default_image",
            self.context.get("viewable_attributes", []),
            self.context.get("editable_attributes", []),
            self.context.get("property_verified_fields_and_values", {}),
            display_by_default=True,
        )


class PropertyAvailabilityAndStatusAttributesSerializer(serializers.ModelSerializer):
    property_status = serializers.SerializerMethodField()
    handover_date = serializers.SerializerMethodField()
    payment_plan_on_handover = serializers.SerializerMethodField()
    payment_during_construction = serializers.SerializerMethodField()
    payment_plan_document = serializers.SerializerMethodField()
    enable_post_handover = serializers.SerializerMethodField()
    post_handover_time_frame = serializers.SerializerMethodField()
    on_handover = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyAvailabilityAndStatus
        fields = [
            "property_status",
            "handover_date",
            "payment_plan_on_handover",
            "payment_during_construction",
            "payment_plan_document",
            "enable_post_handover",
            "post_handover_time_frame",
            "on_handover",
        ]

    def get_property_status(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            int(instance.status),
            "property_status",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_handover_date(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.handover_date,
            "handover_date",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_payment_plan_on_handover(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            not instance.enable_post_handover,
            "payment_plan_on_handover",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_payment_during_construction(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.during_construction,
            "payment_during_construction",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_enable_post_handover(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.enable_post_handover,
            "enable_post_handover",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_post_handover_time_frame(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.post_handover_time_frame,
            "post_handover_time_frame",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_on_handover(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.on_handover,
            "on_handover",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_payment_plan_document(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        is_common_view = self.context.get("is_common_view", False)
        filters = {"property": instance.property_level_data.property}
        if self.context.get("viewed_user_role").name == AGENT:
            filters.update(
                {
                    "created_by": self.context.get("viewed_user"),
                    "created_by_role": self.context.get("viewed_user_role"),
                }
            )
        else:
            filters.update({"created_by_role": self.context.get("viewed_user_role")})
        payment_plan_document = PropertyPaymentPlan.objects.filter(**filters).order_by(
            "order_no"
        )
        payment_plan_document = PropertyPaymentPlanSerializer(
            payment_plan_document, many=True
        ).data

        payment_plan_response = build_return_values_for_attributes(
            payment_plan_document,
            "payment_plan_document",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )
        payment_plan_response["share_icon"] = True
        if (
            not payment_plan_response.get("value")
            or instance.property_level_data.property.owner_intent
            == OwnerIntentForProperty.NOT_FOR_SALE
            or is_common_view
        ):
            payment_plan_response["share_icon"] = False
        return payment_plan_response


class RentContractDetailsSerializer(serializers.ModelSerializer):
    rent_contract_url = serializers.SerializerMethodField()

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "rent_contract_url",
            "rent_contract_file_name",
            "rent_contract_file_size",
        ]

    def get_rent_contract_url(self, instance):
        return get_s3_object(instance.rent_contract_key)


class IncomeFromPropertyAttributesSerializer(serializers.ModelSerializer):
    occupancy_status = serializers.SerializerMethodField()
    tenancy_type = serializers.SerializerMethodField()
    start_date = serializers.SerializerMethodField()
    end_date = serializers.SerializerMethodField()
    rent_contract = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "occupancy_status",
            "tenancy_type",
            "start_date",
            "end_date",
            "rent_contract",
        ]

    def get_occupancy_status(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        occupancy_status = self.context.get("occupancy_status", None)
        return build_return_values_for_attributes(
            occupancy_status,
            "occupancy_status",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_tenancy_type(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        occupancy_status = self.context.get("occupancy_status", None)
        make_invisible = False
        if occupancy_status == int(PropertyAvailabilityStatus.RENTED.value):
            tenancy_type = instance.tenancy_type
        else:
            if occupancy_status == int(PropertyAvailabilityStatus.OWNER_OCCUPIED.value):
                make_invisible = True
            tenancy_type = None
        return build_return_values_for_attributes(
            tenancy_type,
            "tenancy_type",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_start_date(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        occupancy_status = self.context.get("occupancy_status", None)
        make_invisible = False
        if occupancy_status == int(PropertyAvailabilityStatus.RENTED.value):
            tenancy_start_date = instance.tenancy_start_date
        else:
            if occupancy_status == int(PropertyAvailabilityStatus.OWNER_OCCUPIED.value):
                make_invisible = True
            tenancy_start_date = None
        return build_return_values_for_attributes(
            tenancy_start_date,
            "start_date",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_end_date(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        occupancy_status = self.context.get("occupancy_status", None)
        make_invisible = False
        if occupancy_status == int(PropertyAvailabilityStatus.RENTED.value):
            tenancy_end_date = instance.tenancy_end_date
        else:
            if occupancy_status == int(PropertyAvailabilityStatus.OWNER_OCCUPIED.value):
                make_invisible = True
            tenancy_end_date = None
        return build_return_values_for_attributes(
            tenancy_end_date,
            "end_date",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_rent_contract(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        rent_contract = None
        occupancy_status = self.context.get("occupancy_status", None)
        make_invisible = True
        if occupancy_status == int(PropertyAvailabilityStatus.RENTED.value):
            make_invisible = False
            availability_and_status = (
                instance.property_user_level_availability_and_status
            )
            if availability_and_status.rent_contract_key:
                rent_contract = RentContractDetailsSerializer(
                    availability_and_status
                ).data
        return build_return_values_for_attributes(
            rent_contract,
            "rent_contract",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.context.get("is_common_view", False),
        )


class PreviousTransactionsAttributesSerializer(serializers.Serializer):
    sales_history = serializers.SerializerMethodField()
    rental_history = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = ["sales_history", "rental_history"]

    def get_sales_history(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        sales_history = PropertySalesUnitHistory.objects.filter(
            property=instance
        ).order_by("-evidence_date")
        data = PropertySalesUnitHistorySerializer(sales_history, many=True).data
        return build_return_values_for_attributes(
            data,
            "sales_history",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
        )

    def get_rental_history(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        rental_history = PropertyRentalUnitHistory.objects.filter(
            property=instance
        ).order_by("-start_date")
        data = PropertyRentalUnitHistorySerializer(rental_history, many=True).data
        return build_return_values_for_attributes(
            data,
            "rental_history",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
        )


class InvestorCardDetailsSerializer(serializers.ModelSerializer):
    profile_photo = serializers.SerializerMethodField()
    self_view = serializers.SerializerMethodField()
    role = serializers.CharField(default="Investor")
    is_exclusive = serializers.BooleanField(default=False)

    class Meta:
        model = InvestorProfile
        fields = [
            "user_id",
            "name",
            "profile_photo",
            "self_view",
            "role",
            "is_exclusive",
        ]

    @staticmethod
    def get_profile_photo(instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_self_view(self, instance):
        return self.context.get("self_view", False)


class AgentCardDetailsSerializer(serializers.ModelSerializer):
    profile_photo = serializers.SerializerMethodField()
    self_view = serializers.SerializerMethodField()
    role = serializers.CharField(default="Agent")
    is_exclusive = serializers.SerializerMethodField()

    class Meta:
        model = AgentProfile
        fields = [
            "user_id",
            "name",
            "profile_photo",
            "self_view",
            "role",
            "is_exclusive",
        ]

    @staticmethod
    def get_profile_photo(instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_self_view(self, instance):
        return self.context.get("self_view", False)

    def get_is_exclusive(self, instance):
        property_obj = self.context.get("property_obj", None)
        if (
            property_obj
            and property_obj.agent_type == PropertyAgentType.EXCLUSIVE_AGENT
        ):
            return True
        return False


class UpperComponentLocationDetailsAttributesSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    country_code = serializers.SerializerMethodField()
    building_street = serializers.SerializerMethodField()
    unit_number = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "country",
            "country_code",
            "building_street",
            "unit_number",
            "address",
        ]

    def get_country(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.country:
            country_name = instance.country.name
        else:
            country_name = None
        return build_return_values_for_attributes(
            country_name,
            "country",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_country_code(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        if instance.country:
            country_code = instance.country.short_name
        else:
            country_code = None
        return build_return_values_for_attributes(
            country_code,
            "country",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_building_street(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        building_street = get_property_building_name(instance)
        return build_return_values_for_attributes(
            building_street,
            "building_street",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_unit_number(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        return build_return_values_for_attributes(
            instance.unit_number,
            "unit_number",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_address(self, instance):
        viewable_attributes = self.context.get("viewable_attributes", [])
        editable_attributes = self.context.get("editable_attributes", [])
        property_verified_fields_and_values = self.context.get(
            "property_verified_fields_and_values", {}
        )
        address = build_property_address(instance)
        return build_return_values_for_attributes(
            address,
            "address",
            viewable_attributes,
            editable_attributes,
            property_verified_fields_and_values,
            is_common_view=self.context.get("is_common_view", False),
        )


class ProfileCardSerializer(serializers.Serializer):
    profile_photo = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()

    def get_profile_photo(self, instance):
        profile_photo_key = self.context.get("profile_photo_key", None)
        if profile_photo_key:
            return get_s3_object(profile_photo_key)
        return None


class PortfolioPropertySpecificationsAttributesSerializer(serializers.ModelSerializer):
    property_type = serializers.SerializerMethodField()
    total_area = serializers.SerializerMethodField()
    carpet_area = serializers.SerializerMethodField()
    balcony_area = serializers.SerializerMethodField()
    floor_number = serializers.SerializerMethodField()
    number_of_bedrooms = serializers.SerializerMethodField()
    number_of_bathrooms = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "floor_number",
            "number_of_bedrooms",
            "number_of_bathrooms",
        ]

    def get_property_type(self, instance):
        manually_added_fields = instance.manually_added_fields
        return build_return_values_portfolio_specification_attributes(
            instance.property,
            instance.property_type,
            "property_type",
            manually_added_fields,
            portfolio_fields_to_check,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_total_area(self, instance):
        manually_added_fields = instance.manually_added_fields
        return build_return_values_portfolio_specification_attributes(
            instance,
            instance.total_area,
            "total_area",
            manually_added_fields,
            portfolio_fields_to_check,
            unit_field=True,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_carpet_area(self, instance):
        manually_added_fields = instance.manually_added_fields
        return build_return_values_portfolio_specification_attributes(
            instance,
            instance.carpet_area,
            "carpet_area",
            manually_added_fields,
            portfolio_fields_to_check,
            unit_field=True,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_balcony_area(self, instance):
        manually_added_fields = instance.manually_added_fields
        return build_return_values_portfolio_specification_attributes(
            instance,
            instance.balcony_area,
            "balcony_area",
            manually_added_fields,
            portfolio_fields_to_check,
            unit_field=True,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_floor_number(self, instance):
        manually_added_fields = instance.manually_added_fields
        self_view = self.context.get("self_view", False)
        return build_return_values_portfolio_specification_attributes(
            instance.property,
            instance.floor_number,
            "floor_number",
            manually_added_fields,
            portfolio_fields_to_check,
            self_view=self_view,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_number_of_bedrooms(self, instance):
        manually_added_fields = instance.manually_added_fields
        total_bedrooms = None
        make_not_visible = True
        if instance.property.property_category == PropertyCategory.RESIDENTIAL:
            make_not_visible = False
            total_bedrooms = instance.number_of_bedrooms

        return build_return_values_portfolio_specification_attributes(
            instance.property,
            total_bedrooms,
            "number_of_bedrooms",
            manually_added_fields,
            portfolio_fields_to_check,
            sub_text="bedrooms",
            make_not_visible=make_not_visible,
            is_common_view=self.context.get("is_common_view", False),
        )

    def get_number_of_bathrooms(self, instance):
        manually_added_fields = instance.manually_added_fields
        make_not_visible = True
        total_bathrooms = None
        if instance.property.property_category == PropertyCategory.RESIDENTIAL:
            make_not_visible = False
            total_bathrooms = instance.number_of_bathrooms
            if total_bathrooms is not None and total_bathrooms == 0:
                total_bathrooms = None
        return build_return_values_portfolio_specification_attributes(
            instance.property,
            total_bathrooms,
            "total_bathrooms",
            manually_added_fields,
            portfolio_fields_to_check,
            sub_text="bathrooms",
            make_not_visible=make_not_visible,
            is_common_view=self.context.get("is_common_view", False),
        )


class PropertyPortfolioLocationDetailsAttributesSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    country_code = serializers.SerializerMethodField()
    building_street = serializers.SerializerMethodField()
    unit_number = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "country",
            "country_code",
            "building_street",
            "unit_number",
            "address",
        ]

    @staticmethod
    def get_country(instance):
        if instance.country:
            country_name = instance.country.name
        else:
            country_name = None
        return build_return_values_portfolio_attributes(
            country_name, "country", portfolio_fields_to_check
        )

    @staticmethod
    def get_country_code(instance):
        if instance.country:
            country_code = instance.country.short_name
        else:
            country_code = None
        return build_return_values_portfolio_attributes(
            country_code, "country_code", portfolio_fields_to_check
        )

    @staticmethod
    def get_building_street(instance):
        building_street = get_property_building_name(instance)
        return build_return_values_portfolio_attributes(
            building_street, "building_street", portfolio_fields_to_check
        )

    def get_unit_number(self, instance):
        self_view = self.context.get("self_view", False)
        return build_return_values_portfolio_attributes(
            instance.unit_number,
            "unit_number",
            portfolio_fields_to_check,
            self_view=self_view,
        )

    @staticmethod
    def get_address(instance):
        address = build_property_address(instance)
        return build_return_values_portfolio_attributes(
            address, "address", portfolio_fields_to_check
        )


class PortfolioSerializer(serializers.ModelSerializer):
    property_information = serializers.SerializerMethodField()
    location_details = serializers.SerializerMethodField()
    unit_images_component = serializers.SerializerMethodField()
    property_specifications = serializers.SerializerMethodField()
    price_details = serializers.SerializerMethodField()
    property_intent = serializers.SerializerMethodField()
    profile_card = serializers.SerializerMethodField()
    locked = serializers.SerializerMethodField()
    lock_type = serializers.SerializerMethodField()
    share_icon = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_information",
            "property_specifications",
            "location_details",
            "unit_images_component",
            "price_details",
            "property_intent",
            "profile_card",
            "locked",
            "lock_type",
            "share_icon",
        ]

    @staticmethod
    def get_property_information(instance):
        property_category = instance.property.property_category
        property_category = {
            "value": property_category,
            "is_editable": False,
            "is_visible": False,
        }
        property_id = instance.property.id
        property_id = {"value": property_id, "is_editable": False, "is_visible": False}
        property_information = {
            "component_is_editable": False,
            "component_is_visible": False,
        }
        property_information.update({"property_category": property_category})
        property_information.update({"id": property_id})
        return property_information

    def get_property_specifications(self, instance):
        self_view = self.context.get("self_view", False)
        data = PortfolioPropertySpecificationsAttributesSerializer(
            instance,
            context={
                "self_view": self_view,
                "is_common_view": self.context.get("is_common_view", False),
            },
        ).data
        property_specifications = {
            "component_is_editable": False,
            "component_is_visible": True,
        }
        property_specifications.update(data)
        return property_specifications

    def get_location_details(self, instance):
        self_view = self.context.get("self_view", False)
        data = PropertyPortfolioLocationDetailsAttributesSerializer(
            instance.property, context={"self_view": self_view}
        ).data
        location_details = {
            "component_is_editable": False,
            "component_is_visible": True,
        }
        location_details.update(data)
        return location_details

    def get_unit_images_component(self, instance):
        viewed_user = self.context.get("viewed_user")
        viewed_role_object = self.context.get("viewed_role")

        data = PropertyUnitImagesSerializer(
            instance.property,
            context={
                "created_by": viewed_user,
                "created_by_role": viewed_role_object,
            },
        ).data
        unit_images = {"component_is_editable": False, "component_is_visible": True}
        unit_images.update(data)
        return unit_images

    def get_price_details(self, instance):
        self_view = self.context.get("self_view", False)
        viewer_role = self.context.get("viewer_role", None)
        viewed_profile = self.context.get("viewed_profile", None)
        is_common_view = self.context.get("is_common_view", None)
        financial_details = instance.property_user_level_financial_details
        availability_and_status = instance.property_user_level_availability_and_status
        property_currency_code = financial_details.property_currency_code
        preferred_currency_code = (
            instance.preferred_currency_code if not is_common_view else None
        )
        exchange_rate = 1
        is_valuation = False
        if (property_currency_code and preferred_currency_code) and (
            property_currency_code != preferred_currency_code
        ):
            exchange_rate = get_exchange_rates(
                property_currency_code, preferred_currency_code
            )
        if not preferred_currency_code:
            portfolio_fields_to_check.extend(
                [
                    "preferred_currency_code",
                    "exchange_rate",
                    "display_price_in_preferred_currency",
                    "expected_rent_in_preferred_currency",
                    "annual_rent_in_preferred_currency",
                ]
            )

        if instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            display_price_in_property_currency = financial_details.valuation
            is_valuation = True
        elif (
            instance.property.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
        ):
            display_price_in_property_currency = financial_details.expected_rent
        else:
            display_price_in_property_currency = financial_details.asking_price

        if exchange_rate and display_price_in_property_currency:
            display_price_in_preferred_currency = round(
                exchange_rate * display_price_in_property_currency, 3
            )

        else:
            display_price_in_preferred_currency = None

        property_currency_code = build_return_values_portfolio_attributes(
            property_currency_code, "property_currency_code", portfolio_fields_to_check
        )

        preferred_currency_code = build_return_values_portfolio_attributes(
            preferred_currency_code, "property_currency_code", portfolio_fields_to_check
        )

        display_price_in_property_currency = build_return_values_portfolio_attributes(
            display_price_in_property_currency,
            "property_currency_code",
            portfolio_fields_to_check,
        )

        display_price_in_preferred_currency = build_return_values_portfolio_attributes(
            display_price_in_preferred_currency,
            "property_currency_code",
            portfolio_fields_to_check,
        )

        asking_price_in_property_currency = financial_details.asking_price
        asking_price_in_preferred_currency = (
            round(exchange_rate * asking_price_in_property_currency, 3)
            if asking_price_in_property_currency and preferred_currency_code
            else None
        )

        asking_price_in_property_currency = build_return_values_portfolio_attributes(
            asking_price_in_property_currency,
            "asking_price_in_property_currency",
            portfolio_fields_to_check,
        )

        asking_price_in_preferred_currency = build_return_values_portfolio_attributes(
            asking_price_in_preferred_currency,
            "asking_price_in_preferred_currency",
            portfolio_fields_to_check,
        )

        is_valuation = build_return_values_portfolio_attributes(
            is_valuation, "property_currency_code", portfolio_fields_to_check
        )

        expected_rent_in_property_currency = None
        expected_rent_in_preferred_currency = None
        preferred_payment_frequency = None
        annual_rent_in_property_currency = None
        annual_rent_in_preferred_currency = None
        gross_yield = None
        make_rent_fields_not_visible = True
        make_rented_data_invisible = True
        if instance.property.owner_intent in [
            OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            OwnerIntentForProperty.AVAILABLE_FOR_SALE,
        ]:
            if (
                availability_and_status.occupancy_status
                == PropertyAvailabilityStatus.RENTED.value
            ):
                make_rented_data_invisible = False
                annual_rent_in_property_currency = financial_details.annual_rent
                if annual_rent_in_property_currency:
                    annual_rent_in_preferred_currency = round(
                        exchange_rate * annual_rent_in_property_currency, 3
                    )
                gross_yield = get_property_gross_yield(financial_details)

            expected_rent_in_property_currency = financial_details.expected_rent
            if expected_rent_in_property_currency:
                expected_rent_in_preferred_currency = round(
                    exchange_rate * expected_rent_in_property_currency, 3
                )
            preferred_payment_frequency = financial_details.preferred_payment_frequency
            make_rent_fields_not_visible = False

        tentative_commission_in_property_currency = None
        tentative_commission_in_preferred_currency = None
        make_commission_fields_not_visible = True
        if (
            instance.property.owner_intent
            in [
                OwnerIntentForProperty.AVAILABLE_FOR_SALE,
                OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            ]
            and self_view
            and viewer_role == AGENT
        ):
            commission_percentage = float(viewed_profile.commission_percentage)
            if (
                instance.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_SALE
            ):
                tentative_commission_in_property_currency = round(
                    financial_details.asking_price * commission_percentage / 100, 3
                )
                tentative_commission_in_preferred_currency = round(
                    exchange_rate * tentative_commission_in_property_currency, 3
                )
                make_commission_fields_not_visible = False
            if (
                instance.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                monthly_expected_rent = financial_details.expected_rent
                tentative_commission_in_property_currency = round(
                    monthly_expected_rent
                    * settings.AGENT_COMMISSION_PERCENTAGE_FOR_EXPECTED_RENT
                    / 100,
                    3,
                )
                tentative_commission_in_preferred_currency = round(
                    exchange_rate * tentative_commission_in_property_currency, 3
                )
                make_commission_fields_not_visible = False

        expected_rent_in_property_currency = build_return_values_portfolio_attributes(
            expected_rent_in_property_currency,
            "expected_rent_in_property_currency",
            portfolio_fields_to_check,
            make_not_visible=make_rent_fields_not_visible,
        )

        expected_rent_in_preferred_currency = build_return_values_portfolio_attributes(
            expected_rent_in_preferred_currency,
            "expected_rent_in_preferred_currency",
            portfolio_fields_to_check,
            make_not_visible=make_rent_fields_not_visible,
        )

        preferred_payment_frequency = build_return_values_portfolio_attributes(
            preferred_payment_frequency,
            "preferred_payment_frequency",
            portfolio_fields_to_check,
            make_not_visible=make_rent_fields_not_visible,
        )
        annual_rent_in_property_currency = build_return_values_portfolio_attributes(
            annual_rent_in_property_currency,
            "annual_rent_in_property_currency",
            portfolio_fields_to_check,
            make_not_visible=make_rented_data_invisible,
        )
        annual_rent_in_preferred_currency = build_return_values_portfolio_attributes(
            annual_rent_in_preferred_currency,
            "annual_rent_in_preferred_currency",
            portfolio_fields_to_check,
            make_not_visible=make_rented_data_invisible,
        )
        gross_yield = build_return_values_portfolio_attributes(
            gross_yield,
            "gross_yield",
            portfolio_fields_to_check,
            make_not_visible=make_rented_data_invisible,
        )

        tentative_commission_in_property_currency = (
            build_return_values_portfolio_attributes(
                tentative_commission_in_property_currency,
                "tentative_commission_in_property_currency",
                portfolio_fields_to_check,
                make_not_visible=make_commission_fields_not_visible,
            )
        )

        tentative_commission_in_preferred_currency = (
            build_return_values_portfolio_attributes(
                tentative_commission_in_preferred_currency,
                "tentative_commission_in_preferred_currency",
                portfolio_fields_to_check,
                make_not_visible=make_commission_fields_not_visible,
            )
        )

        exchange_rate = build_return_values_portfolio_attributes(
            exchange_rate, "property_currency_code", portfolio_fields_to_check
        )

        price_details = {"component_is_editable": False, "component_is_visible": True}
        price_details.update({"property_currency_code": property_currency_code})
        price_details.update({"preferred_currency_code": preferred_currency_code})
        price_details.update({"exchange_rate": exchange_rate})
        price_details.update(
            {"display_price_in_property_currency": display_price_in_property_currency}
        )
        price_details.update(
            {"display_price_in_preferred_currency": display_price_in_preferred_currency}
        )
        price_details.update(
            {"asking_price_in_property_currency": asking_price_in_property_currency}
        )
        price_details.update(
            {"asking_price_in_preferred_currency": asking_price_in_preferred_currency}
        )
        price_details.update({"is_valuation": is_valuation})
        price_details.update(
            {"expected_rent_in_property_currency": expected_rent_in_property_currency}
        )
        price_details.update(
            {"expected_rent_in_preferred_currency": expected_rent_in_preferred_currency}
        )
        price_details.update(
            {"preferred_payment_frequency": preferred_payment_frequency}
        )
        price_details.update(
            {"annual_rent_in_property_currency": annual_rent_in_property_currency}
        )
        price_details.update(
            {"annual_rent_in_preferred_currency": annual_rent_in_preferred_currency}
        )
        price_details.update({"gross_yield": gross_yield})
        price_details.update(
            {
                "tentative_commission_in_property_currency": tentative_commission_in_property_currency
            }
        )
        price_details.update(
            {
                "tentative_commission_in_preferred_currency": tentative_commission_in_preferred_currency
            }
        )
        return price_details

    @staticmethod
    def get_property_intent(instance):
        intent = instance.property.owner_intent
        intent = build_return_values_portfolio_attributes(
            intent, "intent", portfolio_fields_to_check
        )
        availability_and_status = instance.property_user_level_availability_and_status
        available_from = availability_and_status.rent_available_start_date
        available_from = build_return_values_portfolio_attributes(
            available_from, "available_from", portfolio_fields_to_check
        )

        distressed_deal = None
        if instance.property.owner_intent not in [
            OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            OwnerIntentForProperty.NOT_FOR_SALE,
        ]:
            financial_details = instance.property_user_level_financial_details
            distressed_deal = False
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                distressed_deal = True
        distressed_deal = build_return_values_portfolio_attributes(
            distressed_deal,
            "distressed_deal",
            [],
            make_not_visible=True if distressed_deal is None else False,
        )
        furnished = False
        premium_view = False
        branded_building = False
        property_features = UserLevelPropertyFeatures.objects.filter(
            property_level_data=instance
        ).first()
        if property_features:
            furnished = property_features.furnished
            premium_view = property_features.premium_view
            branded_building = property_features.branded_building
        furnished = build_return_values_portfolio_attributes(furnished, "furnished", [])
        premium_view = build_return_values_portfolio_attributes(
            premium_view, "premium_view", []
        )
        branded_building = build_return_values_portfolio_attributes(
            branded_building, "branded_building", []
        )
        property_intent = {"component_is_editable": False, "component_is_visible": True}
        property_intent.update({"intent": intent})
        property_intent.update({"available_from": available_from})
        property_intent.update({"distressed_deal": distressed_deal})
        property_intent.update({"furnished": furnished})
        property_intent.update({"premium_view": premium_view})
        property_intent.update({"branded_building": branded_building})
        return property_intent

    def get_profile_card(self, instance):
        self_view = self.context.get("self_view")
        viewer_role = self.context.get("viewer_role")
        viewed_role = self.context.get("viewed_role")
        viewed_profile = self.context.get("viewed_profile")
        viewer_profile = self.context.get("viewer_profile")

        if instance.property.owner_intent != OwnerIntentForProperty.NOT_FOR_SALE:
            property_agents = AgentAssociatedProperty.objects.filter(
                property=instance.property,
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
            )
        else:
            property_agents = None
        blur = False
        profile_card_data = None
        context = {"property_obj": instance.property}
        if viewed_role.name == AGENT:
            if self_view and instance.property.owner_verified:
                profile_card_data = InvestorCardDetailsSerializer(
                    [instance.property.owner], many=True
                ).data
            else:
                profile_card_data = AgentCardDetailsSerializer(
                    [viewed_profile], many=True, context=context
                ).data

        if viewed_role.name == INVESTOR:
            agent_profiles = None
            if property_agents and property_agents.exists():
                associated_agent_profile_ids = property_agents.values_list(
                    "agent_profile", flat=True
                )
                agent_profiles = AgentProfile.objects.filter(
                    id__in=associated_agent_profile_ids
                )
            if self_view:
                if agent_profiles:
                    profile_card_data = AgentCardDetailsSerializer(
                        agent_profiles, many=True, context=context
                    ).data
                else:
                    profile_card_data = InvestorCardDetailsSerializer(
                        [instance.property.owner], many=True
                    ).data
            else:
                if viewer_role == AGENT:
                    if agent_profiles:
                        profile_card_data = AgentCardDetailsSerializer(
                            agent_profiles, many=True, context=context
                        ).data
                        blur = True
                    else:
                        profile_card_data = InvestorCardDetailsSerializer(
                            [instance.property.owner], many=True
                        ).data

        profile_card = {"component_is_editable": False, "component_is_visible": True}
        profile_card_data = build_return_values_portfolio_attributes(
            profile_card_data, "profile_card_data", portfolio_fields_to_check
        )
        blur = build_return_values_portfolio_attributes(
            blur, "blur", portfolio_fields_to_check
        )
        profile_card.update({"profile_card_data": profile_card_data})
        profile_card.update({"display_blur": blur})
        return profile_card

    def get_locked(self, instance):
        # Use the is_locked annotation if available
        if hasattr(instance, "is_locked"):
            return instance.is_locked

        # Fallback to original logic if annotation is not present
        viewing_role = self.context.get("viewed_role")
        self_user = self.context.get("self_view")
        if viewing_role.name != AGENT:
            if (
                instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
                and not self_user
            ):
                return True
            return False

        is_basic_subscription = self.context.get("is_basic_subscription")

        if not is_basic_subscription:
            return False

        if not self_user:
            return False

        agent_profile = self.context.get("agent_profile")
        if agent_profile:
            return instance.property not in agent_profile.unlocked_properties.all()

        return False

    def get_lock_type(self, instance):
        viewing_role = self.context.get("viewed_role")
        self_user = self.context.get("self_view")
        if self.get_locked(instance):
            if viewing_role.name != AGENT:
                if (
                    instance.property.owner_intent
                    == OwnerIntentForProperty.NOT_FOR_SALE
                    and not self_user
                ):
                    return PropertyLockType.NOT_FOR_SALE_LOCK
            return PropertyLockType.SUBSCRIPTION_LOCK

    def get_share_icon(self, instance):
        if (
            instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
            or instance.property.is_archived
        ):
            return False
        return True


class PropertyForeignKeySerializer(serializers.ModelSerializer):
    """Base serializer for property objects with foreign key relationship"""

    def to_representation(self, instance):
        result = {}
        for field_name, field in self.fields.items():
            try:
                value = field.get_attribute(instance)
                is_visible = True

                # Check if value should be visible, commented for now as we want to show hypen in UI for null values
                # if (
                #     value is None
                #     or value == ""
                #     or value == 0
                #     or value == []
                #     or value == "null"
                # ):
                #     is_visible = False

                value_rep = field.to_representation(value) if value else None

                field_data = {
                    "value": value_rep,
                    "is_editable": False,
                    "is_visible": is_visible,
                }
                result[field_name] = field_data
            except (KeyError, AttributeError):
                continue
        return result


class BasePropertySerializer(PropertyForeignKeySerializer):
    class Meta:
        abstract = True

    def to_representation(self, instance, is_editable=False, is_visible=True):
        # Get the formatted representation from parent
        representation = super().to_representation(instance)

        # Add id field
        if hasattr(instance, "id"):
            representation["id"] = {
                "value": instance.id,
                "is_editable": is_editable,
                "is_visible": False,
            }

        return representation


class WhatsAppPropertyFeaturesSerializer(BasePropertySerializer):
    tags = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyFeatures
        exclude = [
            "property_level_data",
            "created_ts",
            "updated_ts",
            "created_by",
            "updated_by",
            "created_by_role",
            "updated_by_role",
        ]

    def get_tags(self, obj):
        tags = obj.get_tags()
        is_visible = True

        # Check if value should be visible
        if tags is None or tags == "" or tags == 0 or tags == []:
            is_visible = False
        return tags

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Add reserved parking numbers
        if hasattr(instance, "reserved_parking_number"):
            representation["reserved_parking_numbers"] = {
                "value": instance.get_reserved_parking_number(),
                "is_editable": False,
                "is_visible": True,
            }

        return representation


class WhatsAppPropertyAvailabilitySerializer(BasePropertySerializer):
    class Meta:
        model = UserLevelPropertyAvailabilityAndStatus
        exclude = [
            "property_level_data",
            "created_ts",
            "updated_ts",
            "created_by",
            "updated_by",
            "created_by_role",
            "updated_by_role",
        ]


class WhatsAppPropertyFinancialSerializer(BasePropertySerializer):
    yearly_expected_rent = serializers.SerializerMethodField()
    distressed_deal = serializers.SerializerMethodField()
    gross_yield = serializers.SerializerMethodField()
    display_price_in_property_currency = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyFinancialDetails
        exclude = [
            "property_level_data",
            "created_ts",
            "updated_ts",
            "created_by",
            "updated_by",
            "created_by_role",
            "updated_by_role",
        ]

    def get_yearly_expected_rent(self, obj):
        is_visible = True
        value = obj.get_yearly_expected_rent()
        # Check if value should be visible

        return value

    def get_distressed_deal(self, instance):
        try:
            if (
                instance.asking_price
                and instance.original_price
                and instance.asking_price < instance.original_price
            ):
                distressed = True
            else:
                distressed = False

            return distressed

        except Exception as e:
            return False

    def get_gross_yield(self, instance):
        try:
            gross_yield = get_property_gross_yield(instance)
            return gross_yield
        except Exception as error:
            return None

    def get_display_price_in_property_currency(self, instance):
        try:
            """Get display price in property currency"""
            if (
                instance.property_level_data.property.owner_intent
                == OwnerIntentForProperty.NOT_FOR_SALE
            ):
                return instance.valuation
            elif (
                instance.property_level_data.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                return instance.expected_rent
            else:
                return instance.asking_price
        except Exception as error:
            logger.error("Error in financial serializer: %s", str(error))
            raise


class PropertySerializer(BasePropertySerializer):
    address = serializers.SerializerMethodField()
    default_image = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "unit_number",
            "building_number",
            "address",
            "owner_intent",
            "community",
            "postal_code",
            "country",
            "state",
            "city",
            "area",
            "default_image",
            "unit_images",
        ]

    @staticmethod
    def get_address(instance):
        address = build_property_address_explore(instance)
        return address

    def get_unit_images(self, instance):
        """Get unit images with user and role filters from context"""
        try:
            filters = create_user_level_filter(
                instance,
                self.context.get("user_level_data_user", None),
                self.context.get("user_level_data_role", None),
            )
            unit_sections = PropertyUnitSections.objects.filter(**filters).order_by(
                "id"
            )

            data = PropertySectionSerializer(unit_sections, many=True).data
            logger.info("Fetched unit images for property %s", data)
            return data

        except Exception as e:
            logger.error(
                f"Error fetching unit images for property {instance.id}: {str(e)}"
            )
            return []

    def get_default_image(self, instance):
        if instance.default_image:
            return instance.default_image

        logger.info("Default image not set for property %s", instance.id)
        image_index = instance.id % 5
        if instance.property_type == PropertyType.APARTMENT:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                image_index
            ]
            # property_obj.default_image = default_image
        elif instance.property_type == PropertyType.VILLA:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.VILLA)[image_index]
            # property_obj.default_image = default_image
        elif instance.property_type == PropertyType.TOWNHOUSE:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.TOWNHOUSE)[
                image_index
            ]
            # property_obj.default_image = default_image
        # TODO: Need to add other images of new commercial property type
        else:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                image_index
            ]
        logger.info("Default image set for property %s", default_image)

        return default_image

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Add address information
        if hasattr(instance, "country") and instance.country:
            representation["country"] = {
                "value": instance.country.name,
                "is_editable": False,
                "is_visible": True if instance.country.name else False,
            }

        if hasattr(instance, "state") and instance.state:
            representation["state"] = {
                "value": instance.state.name,
                "is_editable": False,
                "is_visible": True if instance.state.name else False,
            }

        if hasattr(instance, "city") and instance.city:
            representation["city"] = {
                "value": instance.city.name,
                "is_editable": False,
                "is_visible": True if instance.city.name else False,
            }

        if hasattr(instance, "area") and instance.area:
            representation["area"] = {
                "value": instance.area.name,
                "is_editable": False,
                "is_visible": True if instance.area.name else False,
            }

        if hasattr(instance, "community") and instance.community:
            representation["community"] = {
                "value": instance.community.name,
                "is_editable": False,
                "is_visible": True if instance.community.name else False,
            }

        if hasattr(instance, "postal_code"):
            representation["postal_code"] = {
                "value": instance.postal_code,
                "is_editable": False,
                "is_visible": True if instance.postal_code else False,
            }

        if hasattr(instance, "owner_intent"):
            representation["owner_intent"] = {
                "value": instance.owner_intent,
                "is_editable": False,
                "is_visible": True if instance.owner_intent else False,
            }

        if hasattr(instance, "unit_number"):
            representation["unit_number"] = {
                "value": instance.unit_number,
                "is_editable": False,
                "is_visible": False,
            }

        if hasattr(instance, "building_number"):
            representation["building_number"] = {
                "value": instance.building_number,
                "is_editable": False,
                "is_visible": True if instance.building_number else False,
            }

        return representation


class WhatsAppPropertyDataSerializer(BasePropertySerializer):
    property = serializers.SerializerMethodField()
    property_unit_type = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        exclude = [
            "created_ts",
            "updated_ts",
            "created_by",
            "updated_by",
            "created_by_role",
            "updated_by_role",
        ]

    def get_property(self, instance):
        # Pass the user and role through context
        context = {
            **self.context,
            "user_level_data_user": instance.created_by,
            "user_level_data_role": instance.created_by_role,
        }
        return PropertySerializer(instance.property, context=context).data

    def get_property_unit_type(self, instance):
        property_unit_type = 0
        if instance.user_unit_preference:
            if instance.user_unit_preference == "sqft":
                property_unit_type = 0
            elif instance.user_unit_preference == "sqm":
                property_unit_type = 1
            elif instance.user_unit_preference == "sqyd":
                property_unit_type = 2
            elif instance.user_unit_preference == "acre":
                property_unit_type = 3
            elif instance.user_unit_preference == "hectare":
                property_unit_type = 4
            elif instance.user_unit_preference == "bigha":
                property_unit_type = 5

        return property_unit_type

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Add parking numbers
        if hasattr(instance, "parking_number"):
            value = instance.get_parking_number()
            representation["parking_numbers"] = {
                "value": value,
                "is_editable": False,
                "is_visible": True if value else False,
            }
        # Add bedroom count
        value = instance.get_total_bathroom_count()
        # Add bathroom count
        representation["total_bathroom_count"] = {
            "value": value,
            "is_editable": False,
            "is_visible": (
                True
                if value or instance.property_type == PropertyType.APARTMENT
                else False
            ),
        }

        if instance.property_type in EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES:
            for key in representation.keys():
                if key not in EXPLORE_VISIBILITY_CONSTRAINT_LAND_ALLOWED_FIELDS:
                    representation[key]["is_visible"] = False

        elif instance.property.property_category == PropertyCategory.COMMERCIAL:
            for key in representation.keys():
                if (
                    key not in EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_ALLOWED_FIELDS
                    and instance.property_type
                    not in EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_TYPES
                ):
                    representation[key]["is_visible"] = False

        return representation


class EnhancedWhatsAppPropertiesListSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer that includes agent details and WhatsApp contacts
    """

    property_information = serializers.SerializerMethodField()
    property_features = serializers.SerializerMethodField()
    property_availability = serializers.SerializerMethodField()
    property_financial_details = serializers.SerializerMethodField()
    agent_details = serializers.SerializerMethodField()
    contact_details = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_information",
            "property_features",
            "property_availability",
            "property_financial_details",
            "agent_details",
            "contact_details",
        ]

    def get_property_information(self, instance):
        property_data = WhatsAppPropertyDataSerializer(instance).data
        return {
            "component_is_editable": False,
            "component_is_visible": True,
            **property_data,
        }

    def get_property_features(self, instance):
        try:
            features = UserLevelPropertyFeatures.objects.get(
                property_level_data=instance
            )
            features_data = WhatsAppPropertyFeaturesSerializer(features).data
        except UserLevelPropertyFeatures.DoesNotExist:
            features_data = {}

        return {
            "component_is_editable": False,
            "component_is_visible": True,
            **features_data,
        }

    def get_property_availability(self, instance):
        try:
            availability = UserLevelPropertyAvailabilityAndStatus.objects.get(
                property_level_data=instance
            )
            availability_data = WhatsAppPropertyAvailabilitySerializer(
                availability
            ).data
        except UserLevelPropertyAvailabilityAndStatus.DoesNotExist:
            availability_data = {}

        return {
            "component_is_editable": False,
            "component_is_visible": True,
            **availability_data,
        }

    def get_property_financial_details(self, instance):
        try:
            financial = UserLevelPropertyFinancialDetails.objects.get(
                property_level_data=instance
            )
            logger.info("Financial details retrieved successfully {}".format(financial))
            financial_data = WhatsAppPropertyFinancialSerializer(financial).data
        except UserLevelPropertyFinancialDetails.DoesNotExist:
            financial_data = {}

        return {
            "component_is_editable": False,
            "component_is_visible": True,
            **financial_data,
        }

    def get_agent_details(self, obj):
        """Return agent details if available"""
        if obj.created_by and hasattr(obj.created_by, "agentprofile"):
            return AgentDetailSerializer(obj.created_by.agentprofile).data
        return None

    def get_contact_details(self, obj):
        """Extract WhatsApp contact details from meta field"""
        contacts = []
        print("this is meta", obj.meta)
        if obj.meta:
            try:
                meta_data = obj.meta
                if "contact" in meta_data and isinstance(meta_data["contact"], list):
                    for contact in meta_data["contact"]:
                        print("this is contact", contact)
                        if "phone" in contact and contact["phone"]:
                            contacts.append(
                                {
                                    "name": contact.get("name"),
                                    "phone": contact.get("phone"),
                                }
                            )
            except (json.JSONDecodeError, TypeError):
                # Handle invalid JSON
                pass
        return contacts
