import datetime
import logging
import traceback

from django.conf import settings
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, Q
from django.db.models.functions import Cast
from phonenumber_field.phonenumber import PhoneNumber
from rest_framework import serializers

from rezio.properties.helper import is_valid_image_or_video
from rezio.properties.models import (
    Property,
    PropertyFinancialDetails,
    UnitSectionMedia,
    PropertyUnitSections,
    PropertyAvailabilityAndStatus,
    PropertySalesUnitHistory,
    PropertyRentalUnitHistory,
    PropertyVerifiedDataFields,
    DeletedProperty,
    AgentAssociatedProperty,
    PropertyFeatures,
    UnregisteredCoOwner,
    PropertyCoOwner,
    PropertyCompletionState,
    UnregisteredOwner,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    PropertyTag,
    UserLevelPropertyData,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyFeatures,
)
from rezio.properties.services.property_service import handle_associated_agents
from rezio.properties.text_choices import (
    TenancyType,
    PropertyAvailabilityStatus,
    OwnerIntentForProperty,
    PropertyAgentType,
    PropertyCompletionStateChoices,
    UserRequestActions,
    RequestType,
    PreferredPaymentFrequency,
    PropertyMediaFeatures,
)
from rezio.properties.utils import (
    get_s3_object,
    get_primary_number,
    get_primary_phone_code,
    get_property_object,
    get_exchange_rates,
    build_property_address,
    get_property_building_name,
    create_user_level_filter,
)
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import check_file_validity
from rezio.user.models import AgentProfile, User, InvestorProfile
from rezio.user.serializers import PropertyAssociatedAgentsSerializer
from rezio.user.utils import (
    get_investor_profile_object,
    get_object_with_filters,
    get_agent_role_object,
    contains_special_characters,
    get_investor_role_object,
    get_list_of_object_with_filters,
    get_db_object,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
)
from rezio.utils.text_choices import DataSource

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PropertyDetailSerializer(serializers.ModelSerializer):
    is_deleted = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    role = serializers.CharField(source="created_by_role.name")
    community = serializers.CharField(source="community.name", allow_null=True)
    country = serializers.CharField(source="country.short_name", allow_null=True)
    state = serializers.CharField(source="state.name", allow_null=True)
    availability_status = serializers.IntegerField(
        source="propertyavailabilityandstatus.occupancy_status",
        read_only=True,
        allow_null=True,
    )
    # email = serializers.CharField(source='owner.email', allow_null=True)
    parking_number = serializers.SerializerMethodField()
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    manual_added_details = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    distressed_deal = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField()
    co_owner_list = serializers.SerializerMethodField()
    agents = serializers.SerializerMethodField()
    property_specification_type = serializers.SerializerMethodField()
    lead_owner_percentage = serializers.DecimalField(
        required=False, max_digits=5, decimal_places=2, min_value=0, allow_null=True
    )
    is_associated_agent = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    valuation_data_source = serializers.CharField()
    is_agent_action_status_pending = serializers.SerializerMethodField()
    floor_plan = serializers.SerializerMethodField()
    agent_request_type = serializers.SerializerMethodField()

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.owner:
            representation["owner"] = getattr(instance.owner, "name", None)
            representation["primary_phone_number"] = get_primary_number(
                instance.owner.user.primary_phone_number
            )
            representation["primary_phone_code"] = get_primary_phone_code(
                instance.owner.user.primary_phone_number
            )
            representation["gender"] = getattr(instance.owner, "gender", None)
            representation["investor_type"] = getattr(
                instance.owner.investor_type, "name", None
            )
            representation["email"] = getattr(instance.owner, "email", None)
        elif instance.unregistered_owner:
            representation["owner"] = getattr(instance.unregistered_owner, "name", None)
            representation["primary_phone_number"] = get_primary_number(
                instance.unregistered_owner.phone_number
            )
            representation["primary_phone_code"] = get_primary_phone_code(
                instance.unregistered_owner.phone_number
            )
            representation["gender"] = None
            representation["investor_type"] = None
            representation["email"] = getattr(
                instance.unregistered_owner, "email", None
            )
        else:
            representation["owner"] = None
            representation["primary_phone_number"] = None
            representation["primary_phone_code"] = None
            representation["gender"] = None
            representation["investor_type"] = None
            representation["email"] = None

        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == AGENT:
            if not AgentAssociatedProperty.objects.filter(
                property=instance,
                agent_profile__user=viewer,
                is_associated=True,
                action_status=UserRequestActions.ACCEPTED,
                is_request_expired=False,
            ).exists():
                representation["floor_number"] = None
        if viewer_role == INVESTOR:
            if (
                instance.owner and instance.owner.user == viewer
            ) or PropertyCoOwner.objects.filter(
                property=instance, co_owner__user=viewer
            ).exists():
                pass
            else:
                representation["floor_number"] = None

        return representation

    class Meta:
        model = Property
        fields = [
            "id",
            "role",
            "community",
            "unit_number",
            "building_number",
            "property_type",
            "floor_number",
            "owner_verified",
            "postal_code",
            "property_unit_type",
            "tenancy_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "manual_added_details",
            "address",
            "unit_images",
            "distressed_deal",
            "furnished",
            "branded_building",
            "premium_view",
            "availability_status",
            "owner_intent",
            "dewa_id",
            "asking_price",
            "currency_code",
            "profile_photo",
            "co_owner_list",
            "user_unit_preference",
            "lead_owner_percentage",
            "agent_type",
            "agents",
            "property_specification_type",
            "is_associated_agent",
            "building_name",
            "valuation_data_source",
            "default_image",
            "is_agent_action_status_pending",
            "is_deleted",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "manual_added_details",
            "address",
            "unit_images",
            "distressed_deal",
            "furnished",
            "branded_building",
            "premium_view",
            "availability_status",
            "owner_intent",
            "dewa_id",
            "asking_price",
            "currency_code",
            "profile_photo",
            "co_owner_list",
            "user_unit_preference",
            "lead_owner_percentage",
            "agent_type",
            "agents",
            "property_specification_type",
            "is_associated_agent",
            "building_name",
            "valuation_data_source",
            "default_image",
            "is_agent_action_status_pending",
            "floor_plan",
            "number_of_master_bedrooms",
            "number_of_other_bedrooms",
            "number_of_maid_rooms",
            "number_of_study_rooms",
            "country",
            "state",
            "agent_request_type",
        ]

    def get_parking_number(self, instance):
        return instance.get_parking_number()

    # TODO remove after FE implementation
    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    # TODO remove after FE implementation
    def get_currency_code(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.property_currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_manual_added_details(self, obj):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_profile_photo(self, instance):
        if instance.owner:
            investor_profile = get_investor_profile_object(instance.owner.user)
            if investor_profile.profile_photo_key:
                return get_s3_object(investor_profile.profile_photo_key)
        return None

    def get_co_owner_list(self, instance):
        property_co_owners = PropertyCoOwner.objects.filter(property=instance)

        co_owner_list = []
        for each_co_owner in property_co_owners:
            if each_co_owner.co_owner:
                if each_co_owner.co_owner.profile_photo_key:
                    profile_photo = get_s3_object(
                        each_co_owner.co_owner.profile_photo_key
                    )
                else:
                    profile_photo = None

                # If the co-owner is a registered user
                co_owner_list.append(
                    {
                        "user_id": each_co_owner.co_owner.id,
                        "name": each_co_owner.co_owner.name,
                        "email": each_co_owner.co_owner.user.email,
                        "primary_phone_number": get_primary_number(
                            each_co_owner.co_owner.user.primary_phone_number
                        ),
                        "primary_phone_code": get_primary_phone_code(
                            each_co_owner.co_owner.user.primary_phone_number
                        ),
                        "ownership_percentage": each_co_owner.ownership_percentage,
                        "profile_photo": profile_photo,
                        "is_manually_added": False,
                    }
                )
            elif each_co_owner.unregistered_co_owner:  # If the co-owner is unregistered
                co_owner_list.append(
                    {
                        "user_id": each_co_owner.unregistered_co_owner.id,
                        "name": each_co_owner.unregistered_co_owner.name,
                        "email": each_co_owner.unregistered_co_owner.email,
                        "primary_phone_number": get_primary_number(
                            each_co_owner.unregistered_co_owner.phone_number
                        ),
                        "primary_phone_code": get_primary_phone_code(
                            each_co_owner.unregistered_co_owner.phone_number
                        ),
                        "ownership_percentage": each_co_owner.ownership_percentage,
                        "profile_photo": None,
                        "is_manually_added": True,
                    }
                )
        return co_owner_list

    def get_property_specification_type(self, instance):
        property_specification_completion_state = (
            PropertyCompletionState.objects.filter(
                property=instance,
                state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            )
        )
        if property_specification_completion_state.exists():
            property_specification_completion_state = (
                property_specification_completion_state.first()
            )
            return property_specification_completion_state.data_source
        return None

    def get_agents(self, instance):
        associated_agents = AgentAssociatedProperty.objects.filter(
            (
                Q(action_status=UserRequestActions.PENDING)
                & Q(is_associated=False)
                & Q(request_type=RequestType.AGENT_INVITE)
            )
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True))
            | (
                Q(action_status=UserRequestActions.ACCEPTED)
                & Q(request_type=RequestType.INVESTOR_REQUEST)
            ),
            property=instance,
            is_request_expired=False,
        ).values_list("agent_profile_id", flat=True)
        agents = AgentProfile.objects.filter(id__in=associated_agents)
        return PropertyAssociatedAgentsSerializer(
            agents, many=True, context={"property_id": instance.id}
        ).data

    def get_is_associated_agent(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == AGENT:
            return AgentAssociatedProperty.objects.filter(
                property=instance,
                agent_profile__user=viewer,
                is_associated=True,
                action_status=UserRequestActions.ACCEPTED,
                is_request_expired=False,
            ).exists()

        return False

    def get_agent_request_type(self, instance):
        viewer = self.context.get("viewer")
        associated_agent = AgentAssociatedProperty.objects.filter(
            property=instance, agent_profile__user=viewer, is_request_expired=False
        ).first()
        if associated_agent:
            return associated_agent.request_type

    def get_is_agent_action_status_pending(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == AGENT:
            return AgentAssociatedProperty.objects.filter(
                property=instance,
                agent_profile__user=viewer,
                is_request_expired=False,
                action_status=UserRequestActions.PENDING,
            ).exists()

        return False

    def get_is_deleted(self, obj):
        return DeletedProperty.objects.filter(property=obj).exists()

    def get_floor_plan(self, instance):
        floor_plan = PropertyFloorPlan.objects.filter(property=instance).order_by(
            "order_no"
        )
        return PropertyFloorPlanSerializer(floor_plan, many=True).data


class PropertyFinancialDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLevelPropertyFinancialDetails
        fields = [
            "property_level_data",
            "property_currency_code",
            "original_price",
            "asking_price",
            "created_by",
            "updated_by",
        ]

    def validate_property_currency_code(self, value):
        # Ensure currency code is not empty
        if not value:
            raise serializers.ValidationError("Currency code is required.")
        return value

    def validate(self, data):
        asking_price = data.get("asking_price")
        original_price = data.get("original_price")
        currency_code = data.get("property_currency_code")

        if asking_price is None and original_price is None:
            raise serializers.ValidationError(
                "Either asking price or original price must be provided."
            )

        # Check if the PropertyFinancialDetails already exists for this property
        property_financial_details = UserLevelPropertyFinancialDetails.objects.filter(
            property_level_data=data["property_level_data"]
        ).first()

        if property_financial_details:
            # If financial details exist and one of the prices is being updated
            existing_asking_price = property_financial_details.asking_price
            existing_original_price = property_financial_details.original_price
            existing_currency_code = property_financial_details.property_currency_code

            # Ensure currency_code is the same as the existing one if one price already exists
            if (
                existing_asking_price or existing_original_price
            ) and existing_currency_code:
                if currency_code != existing_currency_code:
                    raise serializers.ValidationError(
                        f"Currency code should be {existing_currency_code}, as it is already set for this property."
                    )

        # Return the validated data
        return data


class PropertyPaymentPlanSerializer(serializers.ModelSerializer):
    """
    Serializer for property payment plan
    """

    media_id = serializers.SerializerMethodField()
    media_file = serializers.SerializerMethodField()
    media_type = serializers.SerializerMethodField()

    class Meta:
        model = PropertyPaymentPlan
        fields = [
            "media_id",
            "media_file",
            "order_no",
            "media_file_size",
            "media_file_name",
            "media_type",
        ]

    def get_media_file(self, instance):
        media_file_url = get_s3_object(instance.media_file_key)
        return media_file_url

    def get_media_id(self, instance):
        return instance.id

    def get_media_type(self, instance):
        return instance.media_file_content_type


class PropertyAvailabilityAndStatusSerializer(serializers.ModelSerializer):
    status = serializers.IntegerField()
    occupancy_status = serializers.IntegerField()
    during_construction = serializers.FloatField(allow_null=True)
    on_handover = serializers.FloatField(allow_null=True)
    payment_plan = serializers.SerializerMethodField()

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "status",
            "handover_date",
            "occupancy_status",
            "enable_payment_plan",
            "enable_post_handover",
            "during_construction",
            "on_handover",
            "post_handover_time_frame",
            "payment_plan",
        ]

    def get_payment_plan(self, instance):
        payment_plan = PropertyPaymentPlan.objects.filter(
            property=instance.property
        ).order_by("order_no")
        return PropertyPaymentPlanSerializer(payment_plan, many=True).data


class PropertyViewFinancialDetailsSerializer(serializers.ModelSerializer):
    gross_yield = serializers.SerializerMethodField()
    net_yield = serializers.SerializerMethodField()
    gains = serializers.SerializerMethodField()
    gains_in_percentage = serializers.SerializerMethodField()
    is_gains_upward = serializers.SerializerMethodField()
    property_currency_original_price = serializers.IntegerField(source="original_price")
    property_currency_asking_price = serializers.IntegerField(source="asking_price")
    property_currency_valuation = serializers.IntegerField(source="valuation")
    property_currency_annual_service_charges = serializers.IntegerField(
        source="annual_service_charges"
    )
    property_currency_annual_rent = serializers.IntegerField(source="annual_rent")
    property_currency_security_deposit = serializers.IntegerField(
        source="security_deposit"
    )
    property_currency_other_expenses = serializers.IntegerField(source="other_expenses")

    class Meta:
        model = PropertyFinancialDetails
        fields = [
            "property_currency_original_price",
            "property_currency_asking_price",
            "property_currency_valuation",
            "property_currency_annual_service_charges",
            "property_currency_annual_rent",
            "property_currency_security_deposit",
            "property_currency_other_expenses",
            "gross_yield",
            "net_yield",
            "gains",
            "gains_in_percentage",
            "property_currency_code",
            "is_gains_upward",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        exchange_rate = self.context.get("exchange_rate")

        fields_to_convert = [
            ("preferred_currency_original_price", "original_price"),
            ("preferred_currency_asking_price", "asking_price"),
            ("preferred_currency_valuation", "valuation"),
            ("preferred_currency_annual_service_charges", "annual_service_charges"),
            ("preferred_currency_annual_rent", "annual_rent"),
            ("preferred_currency_security_deposit", "security_deposit"),
            ("preferred_currency_other_expenses", "other_expenses"),
        ]

        for new_field, instance_field in fields_to_convert:
            value = getattr(instance, instance_field, None)
            data[new_field] = (
                round(value * exchange_rate, 3) if value and exchange_rate else None
            )
        data["preferred_currency_code"] = self.context.get("preferred_currency_code")
        data["exchange_rate"] = self.context.get("exchange_rate")
        return data

    def get_gross_yield(self, instance):
        gross_yield = None
        if instance.annual_rent and instance.original_price:
            gross_yield = (instance.annual_rent / instance.original_price) * 100
            gross_yield = round(gross_yield, 2)

        return gross_yield

    def get_net_yield(self, instance):
        net_yield = None

        if instance.original_price:
            purchase_cost = instance.original_price + instance.other_expenses

            if not instance.annual_service_charges:
                annual_service_charges = 0
            else:
                annual_service_charges = instance.annual_service_charges

            if not instance.annual_rent:
                annual_rent = 0
            else:
                annual_rent = instance.annual_rent

            net_annual_income = annual_rent - annual_service_charges
            if net_annual_income:
                net_yield = (net_annual_income / purchase_cost) * 100
                if net_yield:
                    net_yield = round(net_yield, 2)
                else:
                    net_yield = 0

        return net_yield

    def get_gains(self, instance):
        gain = None
        if instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            if instance.valuation and instance.original_price:
                gain = instance.valuation - instance.original_price
        else:
            if instance.asking_price and instance.original_price:
                gain = instance.asking_price - instance.original_price

        return gain

    def get_gains_in_percentage(self, instance):
        gain_percentage = None

        if instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            if instance.valuation and instance.original_price:
                gain_percentage = (
                    (instance.valuation - instance.original_price)
                    / instance.original_price
                ) * 100
                gain_percentage = round(gain_percentage, 2)
        else:
            if instance.asking_price and instance.original_price:
                gain_percentage = (
                    (instance.asking_price - instance.original_price)
                    / instance.original_price
                ) * 100
                gain_percentage = round(gain_percentage, 2)

        return gain_percentage

    def get_is_gains_upward(self, instance):
        if instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            if instance.valuation and instance.original_price:
                return instance.valuation > instance.original_price
        else:
            if instance.asking_price and instance.original_price:
                return instance.asking_price > instance.original_price

        return False


class PropertySalesUnitHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertySalesUnitHistory
        fields = [
            "evidence_date",
            "total_sales_price",
            "sale_recurrence",
            "sale_price_increase_percentage",
            "is_upward",
            "sales_price_sqft_unit",
            "sales_price_sqm_unit",
            "evidence",
        ]


class PropertyRentalUnitHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyRentalUnitHistory
        fields = [
            "start_date",
            "end_date",
            "total_rent",
            "rent_recurrence",
            "rent_increase_percentage",
            "is_upward",
        ]


class PropertyGetFinancialDetailsSerializer(serializers.ModelSerializer):
    availability_status = PropertyAvailabilityAndStatusSerializer(
        source="propertyavailabilityandstatus"
    )
    financial_details = PropertyViewFinancialDetailsSerializer(
        source="propertyfinancialdetails"
    )
    sales_history = PropertySalesUnitHistorySerializer(many=True)
    rental_history = PropertyRentalUnitHistorySerializer(many=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "availability_status",
            "financial_details",
            "sales_history",
            "rental_history",
            "tenancy_type",
            "tenancy_start_date",
            "tenancy_end_date",
        ]

    def __init__(self, *args, **kwargs):
        # Pass the context to the nested serializers
        super().__init__(*args, **kwargs)
        nested_serializers = [
            self.fields["financial_details"],
        ]

        for serializer in nested_serializers:
            serializer.context.update(self.context)


class MediaFileSerializer(serializers.Serializer):
    """
    Serializer for media files
    """

    media_id = serializers.IntegerField(required=False, allow_null=True)
    media_file_key = serializers.CharField(
        max_length=500, required=False, allow_null=True
    )
    media_type = serializers.CharField(max_length=50, required=False, allow_null=True)
    media_order = serializers.IntegerField(min_value=1, required=True)
    thumbnail_file_key = serializers.CharField(
        max_length=500, required=False, allow_null=True
    )
    media_url = serializers.URLField(required=False, allow_null=True)
    thumbnail_url = serializers.URLField(required=False, allow_null=True)
    media_file_name = serializers.CharField(
        max_length=128, required=False, allow_null=True
    )
    media_file_size = serializers.IntegerField(required=False, allow_null=True)
    media_mime_type = serializers.CharField(
        max_length=50, required=False, allow_null=True
    )

    def validate(self, attrs):
        if attrs.get("media_type") == "video" and not attrs.get("thumbnail_file_key"):
            raise serializers.ValidationError(
                "Thumbnail is required for video media type."
            )
        return attrs


class PropertyUnitSectionImageUploadSerializer(serializers.Serializer):
    """
    Serializer to upload property unit section image
    """

    section = serializers.CharField(required=True)
    attached_balcony = serializers.BooleanField(required=True)
    media_files = serializers.ListField(
        child=MediaFileSerializer(), required=False, allow_null=True
    )
    deleted_media = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_null=True
    )

    class Meta:
        fields = [
            "section",
            "attached_balcony",
            "media_files",
            "deleted_media",
        ]


class PropertyMediaSerializer(serializers.ModelSerializer):
    media_file = serializers.SerializerMethodField()
    media_id = serializers.SerializerMethodField()
    thumbnail_file = serializers.SerializerMethodField()

    class Meta:
        model = UnitSectionMedia
        fields = ["media_id", "media_type", "media_file", "order_no", "thumbnail_file"]

    def get_media_file(self, instance):
        return instance.media_url
        # media_file_url = get_s3_object(instance.media_file)
        # return media_file_url

    def get_media_id(self, instance):
        return instance.id

    def get_thumbnail_file(self, instance):
        return instance.thumbnail_url
        # if instance.thumbnail_file:
        #     thumbnail_file_url = get_s3_object(instance.thumbnail_file)
        #     return thumbnail_file_url

        # return instance.thumbnail_file


class PropertySectionSerializer(serializers.ModelSerializer):
    media = serializers.SerializerMethodField()

    class Meta:
        model = PropertyUnitSections
        fields = ["id", "section_type", "attached_balcony", "media"]

    def get_media(self, obj):
        # Fetch related media files ordered by order_no
        media_qs = UnitSectionMedia.objects.filter(section=obj).order_by("order_no")
        return PropertyMediaSerializer(media_qs, many=True).data


class PropertyDewaIDSerializer(serializers.ModelSerializer):
    dewa_id = serializers.CharField(required=True, allow_blank=True, max_length=32)

    class Meta:
        model = Property
        fields = ["dewa_id"]


class EditIncomeFromPropertySerializer(serializers.Serializer):
    TENANCY_TYPES_REQUIRING_DATES = [
        TenancyType.NEW,
        TenancyType.RENEWAL,
        TenancyType.VACANT,
        TenancyType.HOLIDAY_HOME,
    ]
    TENANCY_TYPES_REQUIRING_ANNUAL_RENT = [TenancyType.NEW, TenancyType.RENEWAL]
    TENANCY_TYPES_REQUIRING_DEPOSIT = [TenancyType.NEW, TenancyType.RENEWAL]
    # todo add validation to have correct occupancy status based on tenancy type
    occupancy_status = serializers.ChoiceField(
        choices=PropertyAvailabilityStatus.choices, allow_null=True
    )
    tenancy_type = serializers.ChoiceField(required=False, choices=TenancyType.choices)
    tenancy_start_date = serializers.DateField(required=False, allow_null=True)
    tenancy_end_date = serializers.DateField(required=False, allow_null=True)
    annual_rent = serializers.IntegerField(required=False, allow_null=True)
    annual_service_charges = serializers.IntegerField(required=False)
    security_deposit = serializers.IntegerField(required=False, allow_null=True)

    # Fields to check in PropertyVerifiedDataFields
    VERIFIED_FIELDS = [
        "tenancy_type",
        "tenancy_start_date",
        "tenancy_end_date",
        "annual_rent",
        "security_deposit",
        "occupancy_status",
    ]

    PROPERTY_LEVEL_FIELDS = [
        "tenancy_type",
        "tenancy_start_date",
        "tenancy_end_date",
    ]

    FINANCIAL_DETAILS_FIELDS = [
        "annual_rent",
        "annual_service_charges",
        "security_deposit",
    ]

    def validate(self, data):
        # Get the property instance from context
        user_level_property_obj = self.context.get("user_level_property_obj")
        property_financials = self.context.get("property_financials")
        if not user_level_property_obj or not property_financials:
            raise serializers.ValidationError(
                "Property instance is required in context"
            )

        # Check for verified fields that cannot be edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(
            property=user_level_property_obj.property,
            field_name__in=self.VERIFIED_FIELDS,
        )

        for verified_field in verified_fields:
            field_name = verified_field.field_name
            if field_name in data:
                if field_name in self.PROPERTY_LEVEL_FIELDS:
                    current_value = getattr(user_level_property_obj, field_name)
                elif field_name in self.FINANCIAL_DETAILS_FIELDS:
                    current_value = getattr(property_financials, field_name)
                else:
                    raise serializers.ValidationError(f"Invalid field: {field_name}")
                new_value = data[field_name]

                # Convert dates to string format for comparison if needed
                if isinstance(current_value, datetime.date):
                    current_value = current_value.isoformat()
                if isinstance(new_value, datetime.date):
                    new_value = new_value.isoformat()

                # Compare values and raise error if trying to change verified field
                if str(current_value) != str(new_value):
                    raise serializers.ValidationError(
                        f"Cannot modify verified field: {field_name}"
                    )

        tenancy_type = data.get("tenancy_type")
        tenancy_start_date = data.get("tenancy_start_date")
        tenancy_end_date = data.get("tenancy_end_date")
        annual_rent = data.get("annual_rent")
        security_deposit = data.get("security_deposit")
        occupancy_status = data.get("occupancy_status")

        # 1. Validate that start date is before the end date
        if tenancy_start_date and tenancy_end_date:
            if tenancy_start_date > tenancy_end_date:
                raise serializers.ValidationError(
                    "Tenancy start date cannot be after tenancy end date."
                )

        # 2. Validate based on tenancy type
        if tenancy_type in self.TENANCY_TYPES_REQUIRING_DATES:
            if not tenancy_start_date or not tenancy_end_date:
                raise serializers.ValidationError(
                    "Both start and end dates are required for this tenancy type."
                )

        if (
            tenancy_type in self.TENANCY_TYPES_REQUIRING_ANNUAL_RENT
            and occupancy_status
            and occupancy_status == PropertyAvailabilityStatus.RENTED
        ):
            if not annual_rent:
                raise serializers.ValidationError(
                    "Annual rent is required for this tenancy type."
                )

        if (
            tenancy_type in self.TENANCY_TYPES_REQUIRING_DEPOSIT
            and occupancy_status
            and occupancy_status == PropertyAvailabilityStatus.RENTED
        ):
            if not security_deposit:
                raise serializers.ValidationError(
                    "Security deposit is required for this tenancy type."
                )

        return data

    def update_property(self, user_level_property_obj, validated_data, user, role_obj):
        # Update tenancy data in Property model
        user_level_property_obj.tenancy_type = validated_data.get(
            "tenancy_type", user_level_property_obj.tenancy_type
        )
        user_level_property_obj.tenancy_start_date = validated_data.get(
            "tenancy_start_date", user_level_property_obj.tenancy_start_date
        )
        user_level_property_obj.tenancy_end_date = validated_data.get(
            "tenancy_end_date", user_level_property_obj.tenancy_end_date
        )
        user_level_property_obj.save()

        # Update or create PropertyFinancialDetails
        property_financials = self.context.get("property_financials")

        if validated_data.get("annual_rent"):
            property_financials.annual_rent = validated_data.get("annual_rent")
        property_financials.annual_service_charges = validated_data.get(
            "annual_service_charges"
        )
        property_financials.security_deposit = validated_data.get("security_deposit")
        property_financials.updated_by = user
        property_financials.updated_by_role = role_obj
        property_financials.save()

        if validated_data.get("occupancy_status"):
            (
                property_availability,
                created,
            ) = UserLevelPropertyAvailabilityAndStatus.objects.get_or_create(
                property_level_data=user_level_property_obj,
            )
            property_availability.occupancy_status = validated_data.get(
                "occupancy_status"
            )
            if created:
                property_availability.created_by = user
                property_availability.created_by_role = role_obj
            else:
                property_availability.updated_by = user
                property_availability.updated_by_role = role_obj
            property_availability.save()

        return user_level_property_obj


class PropertyFeaturesSerializer(serializers.ModelSerializer):
    """
    Serializer for property features
    """

    branded_building = serializers.BooleanField(required=False)
    furnished = serializers.IntegerField(required=False)
    premium_view = serializers.BooleanField(required=False)

    class Meta:
        model = UserLevelPropertyFeatures
        fields = ["branded_building", "furnished", "premium_view"]

    def update(self, instance, validated_data):
        super().update(instance, validated_data)

        if self.context.get("created"):
            instance.created_by = self.context.get("user")
            instance.created_by_role = self.context.get("role_obj")
        else:
            instance.updated_by = self.context.get("user")
            instance.updated_by_role = self.context.get("role_obj")
        instance.save()

        return instance


class CoOwnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyCoOwner
        fields = [
            "property",
            "co_owner",
            "ownership_percentage",
            "created_by",
            "updated_by",
        ]

    def create(self, validated_data):
        property_instance = validated_data["property"]
        co_owner = validated_data["co_owner"]

        if co_owner:
            if PropertyCoOwner.objects.filter(
                property=property_instance, co_owner=co_owner
            ).exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "This co-owner is already linked to the property."
                        },
                    }
                )

        # Ensure the lead_owner_percentage won't drop below 1
        if (
            property_instance.lead_owner_percentage
            - validated_data["ownership_percentage"]
            < 1
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "The ownership percentage for the co-owner exceeds the available lead owner percentage."
                    },
                }
            )

        # Deduct the co-owner's percentage from lead owner
        property_instance = validated_data["property"]
        property_instance.lead_owner_percentage -= validated_data[
            "ownership_percentage"
        ]
        property_instance.save()

        # Create the co-owner entry
        co_owner_instance = PropertyCoOwner.objects.create(
            created_by_role=get_investor_role_object(), **validated_data
        )
        return co_owner_instance

    def update(self, instance, validated_data):
        # Deduct the co-owner's percentage from lead owner
        property_instance = validated_data["property"]

        previous_percentage = instance.ownership_percentage
        property_instance.lead_owner_percentage += previous_percentage

        # Ensure the lead_owner_percentage won't drop below 1
        if (
            property_instance.lead_owner_percentage
            - validated_data["ownership_percentage"]
            < 1
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "The ownership percentage for the co-owner exceeds the available lead owner percentage."
                    },
                }
            )

        property_instance.lead_owner_percentage -= validated_data[
            "ownership_percentage"
        ]  # deduct new percentage
        property_instance.updated_by = validated_data["updated_by"]
        property_instance.save()

        # Create the co-owner entry
        instance.ownership_percentage = validated_data["ownership_percentage"]
        instance.updated_by = validated_data["updated_by"]
        instance.save()

        return instance


class UnregisteredCoOwnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnregisteredCoOwner
        fields = ["name", "email", "phone_number"]


class ManualAddedCoOwnerSerializer(serializers.ModelSerializer):
    unregistered_co_owner = UnregisteredCoOwnerSerializer()

    class Meta:
        model = PropertyCoOwner
        fields = [
            "id",
            "property",
            "unregistered_co_owner",
            "ownership_percentage",
            "created_by",
            "updated_by",
        ]

    @staticmethod
    def validate_email_phone_no(
        unregistered_co_owner_email, unregistered_co_owner_phone_no
    ):
        """
        Method to validate uniqueness of email and associated phone number

        :param unregistered_co_owner_email: Email of unregistered co-owner
        :param unregistered_co_owner_phone_no: Phone number of unregistered co-owner
        """
        is_user_email_exist = get_object_with_filters(
            model_name=User, email=unregistered_co_owner_email, roles__name=INVESTOR
        )
        is_user_phone_no_exist = get_object_with_filters(
            model_name=User,
            primary_phone_number=unregistered_co_owner_phone_no,
            roles__name=INVESTOR,
        )
        existing_unregistered_co_owner_email = get_object_with_filters(
            model_name=UnregisteredCoOwner, email=unregistered_co_owner_email
        )
        existing_unregistered_co_owner_phone = get_object_with_filters(
            model_name=UnregisteredCoOwner, phone_number=unregistered_co_owner_phone_no
        )

        if (
            is_user_email_exist
            or is_user_phone_no_exist
            or (
                existing_unregistered_co_owner_phone
                and not existing_unregistered_co_owner_email
            )
            or (
                existing_unregistered_co_owner_email
                and not existing_unregistered_co_owner_phone
            )
            or (
                existing_unregistered_co_owner_email
                and existing_unregistered_co_owner_phone
                and existing_unregistered_co_owner_email.id
                != existing_unregistered_co_owner_phone.id
            )
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "This email/ phone number already exists and is associated with another user"
                    },
                }
            )

    def create(self, validated_data):
        try:
            property_instance = validated_data["property"]
            unregistered_co_owner_data = validated_data.pop("unregistered_co_owner")
            unregistered_co_owner_email = unregistered_co_owner_data["email"]
            unregistered_co_owner_phone_no = unregistered_co_owner_data["phone_number"]

            self.validate_email_phone_no(
                unregistered_co_owner_email, unregistered_co_owner_phone_no
            )
            try:
                unregistered_co_owner = UnregisteredCoOwner.objects.get(
                    property=property_instance,
                    name=unregistered_co_owner_data["name"],
                    email=unregistered_co_owner_data["email"],
                    phone_number=unregistered_co_owner_data["phone_number"],
                )
            except UnregisteredCoOwner.DoesNotExist:
                # If it doesn't exist, create a new unregistered co-owner
                unregistered_co_owner_data["created_by"] = validated_data["created_by"]
                unregistered_co_owner = UnregisteredCoOwner.objects.create(
                    property=property_instance, **unregistered_co_owner_data
                )

            # Ensure the co-owner is not already linked to the property
            if PropertyCoOwner.objects.filter(
                unregistered_co_owner=unregistered_co_owner, property=property_instance
            ).exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "This co-owner is already linked to the property."
                        },
                    }
                )

            # Ensure the lead_owner_percentage won't drop below 1
            if (
                property_instance.lead_owner_percentage
                - validated_data["ownership_percentage"]
                < 1
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "The ownership percentage for the co-owner exceeds the available lead owner percentage."
                        },
                    }
                )

            # Deduct the co-owner's percentage from lead owner
            property_instance.lead_owner_percentage -= validated_data[
                "ownership_percentage"
            ]
            property_instance.save()

            # Create the PropertyCoOwner entry
            property_co_owner = PropertyCoOwner.objects.create(
                unregistered_co_owner=unregistered_co_owner, **validated_data
            )
            return property_co_owner

        except Exception as error:
            raise

    def update(self, instance, validated_data):
        try:
            property_instance = validated_data["property"]
            unregistered_co_owner_id = instance.unregistered_co_owner.id
            unregistered_co_owner_data = validated_data.pop("unregistered_co_owner")

            try:
                unregistered_co_owner = UnregisteredCoOwner.objects.get(
                    id=unregistered_co_owner_id
                )
            except UnregisteredCoOwner.DoesNotExist:
                # If it doesn't exist, create a new unregistered co-owner
                raise serializers.ValidationError(
                    "This co-owner does not exist as unregistered co-owner"
                )

            if validated_data["ownership_percentage"] != instance.ownership_percentage:
                previous_percentage = instance.ownership_percentage
                property_instance.lead_owner_percentage += previous_percentage

                # Ensure the lead_owner_percentage won't drop below 1
                if (
                    property_instance.lead_owner_percentage
                    - validated_data["ownership_percentage"]
                    < 1
                ):
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "The ownership percentage for the co-owner exceeds the available lead owner percentage."
                            },
                        }
                    )

                property_instance.lead_owner_percentage -= validated_data[
                    "ownership_percentage"
                ]  # deduct new percentage
                property_instance.updated_by = validated_data["updated_by"]
                property_instance.save()

                # update the co-owner entry

                instance.ownership_percentage = validated_data["ownership_percentage"]
                instance.updated_by = validated_data["updated_by"]
                instance.save()

            self.validate_email_phone_no(
                unregistered_co_owner_data["email"],
                unregistered_co_owner_data["phone_number"],
            )

            unregistered_co_owner.name = unregistered_co_owner_data["name"]
            unregistered_co_owner.email = unregistered_co_owner_data["email"]
            unregistered_co_owner.phone_number = unregistered_co_owner_data[
                "phone_number"
            ]
            unregistered_co_owner.updated_by = validated_data["updated_by"]
            unregistered_co_owner.save()

            return instance

        except Exception as error:
            traceback.print_exc()
            raise


class PropertyPortfolioViewSerializer(serializers.ModelSerializer):
    owner = serializers.SerializerMethodField()
    role = serializers.CharField(source="created_by_role.name")
    distressed_deal = serializers.SerializerMethodField()
    editable = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name")
    availability_status = serializers.IntegerField(
        source="propertyavailabilityandstatus.occupancy_status",
        read_only=True,
        allow_null=True,
    )
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    rent_data = serializers.SerializerMethodField()
    manual_added_details = serializers.SerializerMethodField()
    agents = serializers.SerializerMethodField()
    is_associated_agent = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()
    is_co_owner = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    property_specification_type = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    is_added_by_investor = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    preferred_currency_code = serializers.CharField()
    property_currency_code = serializers.CharField()
    locked = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "id",
            "owner",
            "community",
            "unit_number",
            "building_number",
            "property_type",
            "floor_number",
            "property_unit_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "property_currency_code",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "preferred_currency_code",
            "availability_status",
            "owner_intent",
            "asking_price",
            "currency_code",
            "rent_data",
            "unit_images",
            "editable",
            "distressed_deal",
            "manual_added_details",
            "user_unit_preference",
            "building_name",
            "is_co_owner",
            "agents",
            "is_associated_agent",
            "is_owner",
            "address",
            "property_specification_type",
            "is_archived",
            "furnished",
            "branded_building",
            "premium_view",
            "agent_type",
            "owner_verified",
            "is_added_by_investor",
            "default_image",
            "role",
            "locked",
        ]

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)

        # Access user and from context
        agent_profile = self.context.get("agent_profile")
        self_user = self.context.get("self_user")
        viewer_role = self.context.get("viewer_role")

        price_details_data = dict()
        property_currency_code = instance.property_currency_code
        preferred_currency_code = instance.preferred_currency_code
        exchange_rate = get_exchange_rates(
            property_currency_code, preferred_currency_code
        )
        asking_price = instance.asking_price
        valuation = instance.valuation
        preferred_currency_asking_price = None
        preferred_currency_valuation = None
        if exchange_rate:
            if asking_price:
                preferred_currency_asking_price = round(asking_price * exchange_rate, 3)
            else:
                preferred_currency_asking_price = None
            if valuation:
                preferred_currency_valuation = round(valuation * exchange_rate, 3)
            else:
                preferred_currency_valuation = None
        price_details_data["property_currency_asking_price"] = asking_price
        price_details_data["property_currency_valuation"] = valuation
        price_details_data["preferred_currency_asking_price"] = (
            preferred_currency_asking_price
        )
        price_details_data["preferred_currency_valuation"] = (
            preferred_currency_valuation
        )

        rent_details = dict()
        total_rent = instance.annual_rent
        original_price = instance.original_price
        rent_details["property_currency_total_rent"] = total_rent
        rent_details["preferred_currency_total_rent"] = (
            round(total_rent * exchange_rate, 3)
            if total_rent and exchange_rate
            else None
        )
        if (original_price and original_price > 0) and (total_rent and total_rent > 0):
            rent_increase_percentage = (total_rent / original_price) * 100
            rent_details["rent_increase_percentage"] = rent_increase_percentage
            rent_details["is_upward"] = True
        else:
            rent_details["rent_increase_percentage"] = None
            rent_details["is_upward"] = None

        data["price_details"] = price_details_data
        data["rent_details"] = rent_details

        # TODO remove tentative commission key when FE implements the changes
        data["tentative_commission"] = None
        commission_data = dict()
        if (
            asking_price
            and self_user
            and viewer_role == AGENT
            and agent_profile
            and agent_profile.commission_percentage
        ):
            commission_percentage = float(agent_profile.commission_percentage)
            if commission_percentage and preferred_currency_asking_price:
                commission = round(asking_price * commission_percentage / 100, 3)
                data["tentative_commission"] = commission
                if exchange_rate:
                    preferred_currency_tentative_commission = round(
                        commission * exchange_rate, 3
                    )
                else:
                    preferred_currency_tentative_commission = None

                commission_data["preferred_currency_tentative_commission"] = (
                    preferred_currency_tentative_commission
                )
                commission_data["property_currency_tentative_commission"] = commission
        else:
            commission_data = None
        data["commission_data"] = commission_data

        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        data["can_archive"] = False
        if viewer_role == AGENT:
            if not AgentAssociatedProperty.objects.filter(
                property=instance,
                agent_profile__user=viewer,
                is_associated=True,
                action_status=UserRequestActions.ACCEPTED,
                is_request_expired=False,
            ).exists():
                data["floor_number"] = None
            if (
                self_user
                and instance.created_by == viewer
                and instance.created_by_role.name == AGENT
            ):
                data["can_archive"] = True
        if viewer_role == INVESTOR:
            if (
                instance.owner and instance.owner.user == viewer
            ) or PropertyCoOwner.objects.filter(
                property=instance, co_owner__user=viewer
            ).exists():
                pass
            else:
                data["floor_number"] = None

            data["can_archive"] = (
                True
                if instance.owner
                and instance.owner.user == viewer
                and instance.owner_verified
                else False
            )

        return data

    def get_owner(self, instance):
        from rezio.user.serializers import BasicInvestorProfileInfoSerializer

        return BasicInvestorProfileInfoSerializer(instance.owner).data

    def get_agents(self, instance):
        from rezio.user.serializers import BasicAgentProfileInfoSerializer

        associated_agents = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            is_request_expired=False,
            property=instance,
        ).values_list("agent_profile_id", flat=True)
        agents = AgentProfile.objects.filter(id__in=associated_agents)
        return BasicAgentProfileInfoSerializer(agents, many=True).data

    def get_editable(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if (
            instance.owner
            and viewer_role == INVESTOR
            and instance.owner.user == viewer
            and instance.owner_verified
        ):
            return True
        return False

    # TODO remove after FE implements currency changes
    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    # TODO remove after FE implements currency changes
    def get_currency_code(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.property_currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_rent_data(self, instance):
        occupancy_status = instance.propertyavailabilityandstatus.occupancy_status
        if occupancy_status and occupancy_status == PropertyAvailabilityStatus.RENTED:
            financial_details = PropertyFinancialDetails.objects.filter(
                property=instance
            ).first()
            if financial_details:
                total_rent = financial_details.annual_rent
                original_price = financial_details.original_price
                data = dict()
                data["total_rent"] = total_rent
                if (original_price and original_price > 0) and (
                    total_rent and total_rent > 0
                ):
                    rent_increase_percentage = (total_rent / original_price) * 100
                    data["rent_increase_percentage"] = rent_increase_percentage
                    data["is_upward"] = True
                    return data
                else:
                    data["rent_increase_percentage"] = None
                    data["is_upward"] = None
                    return data
        return None

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_manual_added_details(self, instance):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=instance)
        return [field.field_name for field in verified_fields]

    def get_is_associated_agent(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == AGENT:
            return AgentAssociatedProperty.objects.filter(
                property=instance,
                agent_profile__user=viewer,
                is_associated=True,
                action_status=UserRequestActions.ACCEPTED,
                is_request_expired=False,
            ).exists()
        return False

    def get_is_owner(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == INVESTOR:
            if instance.owner and instance.owner.user == viewer:
                return True
            return PropertyCoOwner.objects.filter(
                property=instance, co_owner__user=viewer
            ).exists()
        return False

    def get_is_co_owner(self, instance):
        viewer = self.context.get("viewer")
        viewer_role = self.context.get("viewer_role")
        if viewer_role == INVESTOR:
            return PropertyCoOwner.objects.filter(
                property=instance, co_owner__user=viewer
            ).exists()
        return False

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_property_specification_type(self, instance):
        property_specification_completion_state = (
            PropertyCompletionState.objects.filter(
                property=instance,
                state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            )
        )
        if property_specification_completion_state.exists():
            property_specification_completion_state = (
                property_specification_completion_state.first()
            )
            return property_specification_completion_state.data_source
        return None

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_is_added_by_investor(self, instance):
        if instance.created_by_role.name == INVESTOR:
            return True
        return False

    def get_locked(self, instance):
        viewing_role = self.context.get("viewing_role")
        if viewing_role != AGENT:
            return False

        is_basic_subscription = self.context.get("is_basic_subscription")
        self_user = self.context.get("self_user")

        if not is_basic_subscription:
            return False

        if not self_user:
            return False

        agent_profile = AgentProfile.objects.filter(
            user=self.context.get("request").user
        ).first()
        if agent_profile:
            return instance not in agent_profile.unlocked_properties.all()

        return True


class BasicPropertyDetailSerializer(serializers.ModelSerializer):
    address = serializers.SerializerMethodField()
    community = serializers.SerializerMethodField()
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    valuation = serializers.SerializerMethodField()
    owner_intent = serializers.SerializerMethodField()
    expected_rent = serializers.SerializerMethodField()
    expected_security_deposit = serializers.SerializerMethodField()
    preferred_payment_frequency = serializers.SerializerMethodField()
    rent_available_start_date = serializers.SerializerMethodField()
    price_negotiable = serializers.SerializerMethodField()
    data_source = serializers.SerializerMethodField()
    number_of_total_bathrooms = serializers.SerializerMethodField()
    number_of_total_bedrooms = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    display_price_in_property_currency = serializers.SerializerMethodField()
    unit_number = serializers.SerializerMethodField()
    building_number = serializers.SerializerMethodField()
    property_category = serializers.SerializerMethodField()
    default_image = serializers.SerializerMethodField()
    id = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    country_short_name = serializers.SerializerMethodField()
    property_unit_type = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "id",
            "community",
            "unit_number",
            "building_number",
            "address",
            "unit_images",
            "asking_price",
            "currency_code",
            "valuation",
            "owner_intent",
            "expected_rent",
            "expected_security_deposit",
            "preferred_payment_frequency",
            "rent_available_start_date",
            "price_negotiable",
            "data_source",
            "number_of_total_bathrooms",
            "number_of_total_bedrooms",
            "total_area",
            "property_type",
            "property_unit_type",
            "building_type",
            "property_category",
            "default_image",
            "building_name",
            "display_price_in_property_currency",
            "furnished",
            "country_short_name",
            "open_for_collaboration",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.property_obj = self.instance.property if self.instance else None
        self.user = self.context.get("viewed_user")
        self.role_obj = self.context.get("viewed_user_role_obj")

    def get_id(self, instance):
        return self.property_obj.id

    def get_owner_intent(self, instance):
        return self.property_obj.owner_intent

    def get_default_image(self, instance):
        return self.property_obj.default_image

    def get_property_category(self, instance):
        return self.property_obj.property_category

    def get_unit_number(self, instance):
        return self.property_obj.unit_number

    def get_building_number(self, instance):
        return self.property_obj.building_number

    def get_community(self, instance):
        return self.property_obj.community.name

    def get_building_name(self, instance):
        # this is shown beside unit number
        community = self.property_obj.community
        if community:
            for loc_no in range(5, 0, -1):
                if getattr(community, f"sub_loc_{loc_no}", None):
                    return getattr(community, f"sub_loc_{loc_no}")
            else:
                return community.name

        return None

    def get_asking_price(self, instance):
        try:
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_SALE
            ):
                financial_details = instance.property_user_level_financial_details
                return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_currency_code(self, instance):
        try:
            financial_details = instance.property_user_level_financial_details
            return financial_details.property_currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_address(self, instance):
        property_obj = self.property_obj
        skip_loc = None
        if property_obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(property_obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(property_obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if property_obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(property_obj.community, f"sub_loc_{loc_no}", None)
            ],
            property_obj.community.name if property_obj.community else None,
            property_obj.area.name if property_obj.area else None,
            property_obj.city.name if property_obj.city else None,
            property_obj.state.name if property_obj.state else None,
            property_obj.country.name if property_obj.country else None,
        ]
        logger.info(address_parts)

        building_name = self.get_building_name(property_obj)
        if building_name and building_name in address_parts:
            address_parts.remove(building_name)

        return ", ".join(filter(None, address_parts))

    def get_valuation(self, instance):
        try:
            financial_details = instance.property_user_level_financial_details
            return financial_details.valuation
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_unit_images(self, instance):
        """
        Modify this function to get only first PropertyUnitSections object
        """
        user_level_filter = create_user_level_filter(
            self.property_obj, self.user, self.role_obj
        )
        first_unit_section = (
            PropertyUnitSections.objects.filter(**user_level_filter)
            .order_by("id")
            .first()
        )
        if first_unit_section:
            first_media = (
                UnitSectionMedia.objects.filter(section=first_unit_section)
                .order_by("order_no")
                .first()
            )
            if first_media:
                return PropertyMediaSerializer(
                    first_media
                ).data  # Return only one image

        return None

    def get_expected_rent(self, instance):
        try:
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                financial_details = instance.property_user_level_financial_details
                return financial_details.expected_rent
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_expected_security_deposit(self, instance):
        try:
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                financial_details = instance.property_user_level_financial_details
                return financial_details.expected_security_deposit
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_preferred_payment_frequency(self, instance):
        try:
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                financial_details = instance.property_user_level_financial_details
                return financial_details.preferred_payment_frequency
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_rent_available_start_date(self, instance):
        try:
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                availability_details = (
                    instance.property_user_level_availability_and_status
                )
                return availability_details.rent_available_start_date
        except PropertyAvailabilityAndStatus.DoesNotExist:
            return None

    def get_price_negotiable(self, instance):
        try:
            financial_details = instance.property_user_level_financial_details
            return financial_details.price_negotiable
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_data_source(self, instance):
        property_verified_fields = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyVerifiedDataFields.__name__,
            single_field_value_dict={
                "property": self.property_obj,
                "field_name": "valuation",
            },
        )

        if property_verified_fields.exists():
            return DataSource.PROPERTY_MONITOR
        else:
            return DataSource.USER_ADDED

    def get_number_of_total_bathrooms(self, instance):
        return instance.get_total_bathroom_count()

    def get_number_of_total_bedrooms(self, instance):
        number_of_bedrooms = instance.number_of_bedrooms or 0
        return number_of_bedrooms

    def get_display_price_in_property_currency(self, instance):
        financial_details = instance.property_user_level_financial_details
        if self.property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            display_price_in_property_currency = financial_details.valuation
        elif (
            self.property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
        ):
            display_price_in_property_currency = financial_details.expected_rent
        else:
            display_price_in_property_currency = financial_details.asking_price

        return display_price_in_property_currency

    def get_furnished(self, instance):
        if hasattr(instance, "property_user_level_features"):
            return instance.property_user_level_features.furnished
        else:
            return False

    def get_country_short_name(self, instance):
        return (
            self.property_obj.country.short_name if self.property_obj.country else None
        )

    def get_property_unit_type(self, instance):
        property_unit_type = 0
        if instance.user_unit_preference:
            if instance.user_unit_preference == "sqft":
                property_unit_type = 0
            elif instance.user_unit_preference == "sqm":
                property_unit_type = 1
            elif instance.user_unit_preference == "sqyd":
                property_unit_type = 2
            elif instance.user_unit_preference == "acre":
                property_unit_type = 3
            elif instance.user_unit_preference == "hectare":
                property_unit_type = 4
            elif instance.user_unit_preference == "bigha":
                property_unit_type = 5

        return property_unit_type


class PropertyListSerializer(serializers.ModelSerializer):
    address = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name")
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "id",
            "community",
            "unit_number",
            "building_number",
            "address",
            "unit_images",
            "asking_price",
            "currency_code",
        ]

    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_currency_code(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_address(self, obj):
        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(1, 6)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]
        logger.info(address_parts)
        return ", ".join(filter(None, address_parts))

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data


class OwnerIntentSerializer(serializers.Serializer):
    owner_intent = serializers.ChoiceField(
        choices=OwnerIntentForProperty.choices, required=True
    )
    asking_price = serializers.IntegerField(required=False, allow_null=True)
    valuation = serializers.IntegerField(required=False, allow_null=True)
    agent_type = serializers.ChoiceField(
        required=False, choices=PropertyAgentType.choices, allow_null=True
    )
    agents = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    agent_contract = serializers.FileField(allow_null=True, required=False)
    remove_exclusive_agent_contract = serializers.BooleanField(
        allow_null=True, default=False, required=False
    )
    expected_rent = serializers.IntegerField(required=False, allow_null=True)
    expected_security_deposit = serializers.IntegerField(
        required=False, allow_null=True
    )
    preferred_payment_frequency = serializers.ChoiceField(
        choices=PreferredPaymentFrequency.choices, required=False, allow_null=True
    )
    rent_available_start_date = serializers.DateField(required=False, allow_null=True)
    price_negotiable = serializers.BooleanField(required=True)

    def validate(self, data):
        user_role = self.context.get("user_role")
        property_intent = data.get("owner_intent", None)
        asking_price = data.get("asking_price", None)
        expected_rent = data.get("expected_rent")
        expected_security_deposit = data.get("expected_security_deposit")
        preferred_payment_frequency = data.get("preferred_payment_frequency")
        rent_available_start_date = data.get("rent_available_start_date")

        if user_role == INVESTOR:
            if data.get("owner_intent") != OwnerIntentForProperty.NOT_FOR_SALE:
                if (
                    property_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                    and not asking_price
                ) or (
                    asking_price
                    and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_SALE
                ):
                    raise serializers.ValidationError("Invalid data sent")

                if (
                    property_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
                    and not expected_rent
                    and not expected_security_deposit
                    and not preferred_payment_frequency
                    and not rent_available_start_date
                ) or (
                    (
                        expected_rent
                        or expected_security_deposit
                        or preferred_payment_frequency
                        or rent_available_start_date
                    )
                    and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_RENT
                ):
                    raise serializers.ValidationError("Invalid data sent")

                agent_type = data.get("agent_type")
                agents = data.get("agents", None)

                if agent_type == PropertyAgentType.OPEN_TO_ALL and (
                    agents or data.get("agent_contract")
                ):
                    raise serializers.ValidationError(
                        "Cannot add agents when selected as open to all"
                    )
                if not agent_type:
                    raise serializers.ValidationError("Agent type is required")
                if agent_type != PropertyAgentType.OPEN_TO_ALL:
                    if not agents:
                        raise serializers.ValidationError("No agent selected")
                    agents = [x.strip() for x in agents.split(",")]
                    # if (
                    #     agent_type == PropertyAgentType.SELECTIVE_AGENTS
                    #     and len(agents) > settings.ALLOWED_SELECTIVE_AGENT_COUNT
                    # ):
                    #     raise serializers.ValidationError("Agents limit reached")
                    if agent_type == PropertyAgentType.SELECTIVE_AGENTS and data.get(
                        "agent_contract"
                    ):
                        raise serializers.ValidationError(
                            "Cannot add agent contract for selective agents"
                        )
                    elif (
                        agent_type == PropertyAgentType.EXCLUSIVE_AGENT
                        and len(agents) > 1
                    ):
                        raise serializers.ValidationError(
                            "Cannot add more than one exclusive agent"
                        )

                    seen = set()
                    for agent in agents:
                        if agent in seen:
                            raise serializers.ValidationError("Duplicate users found")
                        else:
                            seen.add(agent)
                    agent_profiles = (
                        AgentProfile.objects.filter(id__in=agents)
                        .annotate(id_str=Cast("id", CharField()))
                        .values_list("id_str", flat=True)
                    )
                    missing_agents = set(agents) - set(agent_profiles)
                    if missing_agents:
                        raise serializers.ValidationError(
                            f"User not found {missing_agents}"
                        )
                if agent_type != PropertyAgentType.EXCLUSIVE_AGENT and data.get(
                    "agent_contract"
                ):
                    raise serializers.ValidationError(
                        "Agent contract is applicable only to exclusive agents"
                    )
            else:
                if (
                    data.get("agent_type")
                    or data.get("agents", None)
                    or data.get("agent_contract")
                ):
                    raise serializers.ValidationError(
                        "Cannot add agent data if intent is not for sale"
                    )
                if not data.get("valuation"):
                    raise serializers.ValidationError("Valuation is required")
                if (
                    data.get("agent_type")
                    or data.get("agents", None)
                    or data.get("agent_contract")
                ):
                    raise serializers.ValidationError(
                        "Cannot add agent data if intent is not for sale"
                    )
        elif user_role == AGENT:
            property_id = self.context.get("property_id")
            property_obj = get_property_object(property_id)

            if not property_obj.owner_verified and property_obj.owner is None:
                present_fields = [
                    field
                    for field in data
                    if field
                    not in [
                        "owner_intent",
                        "asking_price",
                        "remove_exclusive_agent_contract",
                        "valuation",
                        "expected_rent",
                        "expected_security_deposit",
                        "preferred_payment_frequency",
                        "rent_available_start_date",
                        "price_negotiable",
                    ]
                    and data[field] is not None
                ]
                if present_fields:
                    raise serializers.ValidationError("Invalid data sent")
                if data.get("owner_intent") not in [
                    OwnerIntentForProperty.AVAILABLE_FOR_SALE,
                    OwnerIntentForProperty.AVAILABLE_FOR_RENT,
                ]:
                    raise serializers.ValidationError("Invalid owner intent selected")

                if (
                    property_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                    and not asking_price
                ) or (
                    asking_price
                    and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_SALE
                ):
                    raise serializers.ValidationError("Invalid data sent")
                if (
                    property_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
                    and not expected_rent
                    and not expected_security_deposit
                    and not preferred_payment_frequency
                    and not rent_available_start_date
                ) or (
                    (
                        expected_rent
                        or expected_security_deposit
                        or preferred_payment_frequency
                        or rent_available_start_date
                    )
                    and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_RENT
                ):
                    raise serializers.ValidationError("Invalid data sent")

        return data

    def validate_agent_contract(self, agent_contract):
        if agent_contract:
            return check_file_validity(agent_contract)
        return agent_contract

    def update_user_level_data(
        self,
        validated_data,
        property_obj,
        user_level_property_obj,
        property_verified_fields,
        user_role,
        created_by,
        created_by_role,
    ):
        """
        Method to update user level and property level details
        """
        agent_profiles = validated_data.get("agents", None)
        if agent_profiles:
            agent_profiles = agent_profiles.split(",")
        agent_type = validated_data.get("agent_type", None)

        property_financials = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyFinancialDetails.__name__,
            single_field_value_dict={"property": property_obj},
        )

        user_level_property_financials = get_list_of_object_with_filters(
            app_name="properties",
            model_name=UserLevelPropertyFinancialDetails.__name__,
            multi_field_value_dict={"property_level_data": user_level_property_obj},
        )

        property_availability_and_status = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyAvailabilityAndStatus.__name__,
            single_field_value_dict={"property": property_obj},
        )

        user_level_availability_and_status = get_list_of_object_with_filters(
            app_name="properties",
            model_name=UserLevelPropertyAvailabilityAndStatus.__name__,
            multi_field_value_dict={"property_level_data": user_level_property_obj},
        )

        property_obj.owner_intent = validated_data.get("owner_intent")

        if agent_type and user_role == INVESTOR:
            property_obj.agent_type = agent_type
            user_level_property_obj.agent_type = agent_type

        if "valuation" in property_verified_fields:
            logger.info(
                f"Valuation {property_financials.first().valuation} is fetch from property monitor for property {property_obj.id}"
            )
            valuation = property_financials.first().valuation
        else:
            valuation = validated_data.get("valuation")

        asking_price = validated_data.get("asking_price")
        expected_rent = validated_data.get("expected_rent")
        expected_security_deposit = validated_data.get("expected_security_deposit")
        preferred_payment_frequency = validated_data.get("preferred_payment_frequency")
        price_negotiable = validated_data.get("price_negotiable")
        rent_available_start_date = validated_data.get("rent_available_start_date")

        property_financials.update(
            valuation=valuation,
            asking_price=asking_price,
            expected_rent=expected_rent,
            expected_security_deposit=expected_security_deposit,
            preferred_payment_frequency=preferred_payment_frequency,
            price_negotiable=price_negotiable,
            updated_by=created_by,
            updated_by_role=created_by_role,
        )

        user_level_property_financials.update(
            valuation=valuation,
            asking_price=asking_price,
            expected_rent=expected_rent,
            expected_security_deposit=expected_security_deposit,
            preferred_payment_frequency=preferred_payment_frequency,
            price_negotiable=price_negotiable,
            updated_by=created_by,
            updated_by_role=created_by_role,
        )

        property_availability_and_status.update(
            rent_available_start_date=rent_available_start_date,
            updated_by=created_by,
            updated_by_role=created_by_role,
        )

        user_level_availability_and_status.update(
            rent_available_start_date=rent_available_start_date,
            updated_by=created_by,
            updated_by_role=created_by_role,
        )

        if user_role == INVESTOR:
            handle_associated_agents(
                property_obj,
                agent_profiles,
                created_by,
                agent_type,
                validated_data,
                created_by_role,
            )

        property_obj.save()

    def create(self, validated_data):
        try:
            user_role = self.context.get("user_role")
            role_obj = self.context.get("role_obj")
            property_id = self.context.get("property_id")
            property_obj = get_property_object(property_id)
            created_by = self.context.get("user")
            user_level_property_obj = get_list_of_object_with_filters(
                app_name="properties",
                model_name=UserLevelPropertyData.__name__,
                single_field_value_dict={
                    "property": property_obj,
                },
            )
            property_verified_fields = get_list_of_object_with_filters(
                app_name="properties",
                model_name=PropertyVerifiedDataFields.__name__,
                single_field_value_dict={"property": property_obj},
            ).values_list("field_name", flat=True)

            if (
                user_role == AGENT
                and not property_obj.owner_verified
                and property_obj.owner is None
            ) or user_role == INVESTOR:
                with transaction.atomic():
                    owner_intent_changed = (
                        property_obj.owner_intent != validated_data["owner_intent"]
                    )
                    if owner_intent_changed:
                        self.update_user_level_data(
                            validated_data,
                            property_obj,
                            user_level_property_obj,
                            property_verified_fields,
                            user_role,
                            created_by,
                            role_obj,
                        )
                    else:
                        user_level_filters = create_user_level_filter(
                            property_obj, created_by, role_obj
                        )
                        user_level_property_obj = user_level_property_obj.filter(
                            **user_level_filters
                        )
                        self.update_user_level_data(
                            validated_data,
                            property_obj,
                            user_level_property_obj,
                            property_verified_fields,
                            user_role,
                            created_by,
                            role_obj,
                        )

            else:
                with transaction.atomic():
                    user_level_property_obj = user_level_property_obj.filter(
                        created_by=created_by,
                        created_by_role=role_obj,
                    ).first()

                    property_financials = get_db_object(
                        app_name="properties",
                        model_name=PropertyFinancialDetails.__name__,
                        single_field_value_dict={"property": property_obj},
                    )

                    user_level_property_financials = get_db_object(
                        app_name="properties",
                        model_name=UserLevelPropertyFinancialDetails.__name__,
                        single_field_value_dict={
                            "property_level_data": user_level_property_obj
                        },
                    )

                    if (
                        property_obj.owner_intent
                        == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                    ):
                        user_level_property_financials.asking_price = (
                            validated_data.get(
                                "asking_price", property_financials.asking_price
                            )
                        )
                    elif (
                        property_obj.owner_intent
                        == OwnerIntentForProperty.AVAILABLE_FOR_RENT
                    ):
                        user_level_property_financials.expected_rent = (
                            validated_data.get(
                                "expected_rent", property_financials.expected_rent
                            )
                        )

                    user_level_property_financials.price_negotiable = (
                        validated_data.get(
                            "price_negotiable", property_financials.price_negotiable
                        )
                    )
                    if "valuation" in property_verified_fields:
                        logger.info(
                            f"Valuation {property_financials.valuation} is fetch from property monitor for property "
                            f"{property_obj.id}"
                        )
                        pass
                    else:
                        user_level_property_financials.valuation = validated_data.get(
                            "valuation", property_financials.valuation
                        )
                    user_level_property_financials.expected_security_deposit = (
                        property_financials.expected_security_deposit
                    )
                    user_level_property_financials.save()

            return validated_data
        except Exception as error:
            raise InternalServerException(
                {
                    KEY_MESSAGE: "Error in adding agents to property",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyCostDetailsSerializer(serializers.ModelSerializer):
    dld_fee = serializers.SerializerMethodField()
    agent_fees = serializers.SerializerMethodField()
    transaction_price = serializers.SerializerMethodField()

    class Meta:
        model = PropertyFinancialDetails
        fields = [
            "asking_price",
            "original_price",
            "other_expenses",
            "dld_fee",
            "agent_fees",
            "transaction_price",
        ]

    def get_dld_fee(self, obj):
        dld_fees = settings.DLD_FEES_PERCENTAGE / 100
        return obj.asking_price * dld_fees if obj.asking_price else 0

    def get_agent_fees(self, obj):
        # this is agency_commission
        # Assuming a commission rate of 2%, you can modify this as needed
        commission_rate = settings.AGENT_COMMISSION_PERCENTAGE / 100
        return obj.asking_price * commission_rate if obj.asking_price else 0

    def get_transaction_price(self, obj):
        processing_fee = settings.BANK_PROCESSING_FEE_PERCENTAGE / 100
        # this is bank processing fee
        return obj.asking_price * processing_fee if obj.asking_price else 0

    # below are all added just for reference, will be changes once we have accurate values for all
    def get_trustee_office_fee(self, obj):
        return "4200-5800 AED"  # Static range

    def get_noc_fee(self, obj):
        return "±580 AED"  # Approximate

    def get_sales_progression_fee(self, obj):
        return "2000-10000 AED"  # Static range

    def get_valuation_fee(self, obj):
        return "1000-5000 AED"  # Static range


class AddOwnerSerializer(serializers.ModelSerializer):
    owner_id = serializers.IntegerField(required=True)

    class Meta:
        model = Property
        fields = ["id", "owner_id"]

    def validate_owner_id(self, owner_id):
        if not InvestorProfile.objects.filter(id=owner_id).exists():
            raise serializers.ValidationError("Invalid owner details")
        return owner_id

    def update(self, instance, validated_data):
        user = self.context.get("user")
        role_obj = self.context.get("role_obj")
        if instance.owner or instance.unregistered_owner:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Owner already exists"},
                }
            )

        instance.owner_id = validated_data["owner_id"]
        instance.updated_by = user
        instance.updated_by_role = role_obj
        instance.save()

        return instance


class UnregisteredOwnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = UnregisteredOwner
        fields = ["id", "name", "email", "phone_number"]


class ManualAddedOwnerSerializer(serializers.ModelSerializer):
    property_id = serializers.IntegerField(required=True, write_only=True)
    email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = UnregisteredOwner
        fields = ["id", "name", "email", "phone_number", "property_id"]

    def validate(self, data):
        phone_number = data.get("phone_number")
        email = data.get("email", None)
        if (
            phone_number
            and InvestorProfile.objects.filter(
                user__primary_phone_number=phone_number
            ).exists()
        ):
            raise serializers.ValidationError(
                "This phone number is already associated with another user"
            )
        if email and InvestorProfile.objects.filter(email=email).exists():
            raise serializers.ValidationError(
                "This email is already associated with another user"
            )
        return data

    def validate_name(self, name):
        if not contains_special_characters(name):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def validate_phone_number(self, phone_number):
        phone_number = PhoneNumber.from_string(phone_number)
        if not phone_number.is_valid():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"{phone_number} is invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                }
            )

        return phone_number

    def create(self, validated_data):
        try:
            user = self.context.get("user")
            agent_role = get_agent_role_object()
            property_obj = get_property_object(validated_data["property_id"])

            if property_obj.owner or property_obj.unregistered_owner:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Owner already exists"},
                    }
                )

            unregistered_owner = UnregisteredOwner.objects.create(
                name=validated_data["name"],
                email=validated_data["email"],
                phone_number=validated_data["phone_number"],
                created_by=user,
                created_by_role=agent_role,
            )

            property_obj.unregistered_owner = unregistered_owner
            property_obj.updated_by = user
            property_obj.updated_by_role = agent_role
            property_obj.save()

            return unregistered_owner

        except Exception as error:
            raise

    def update(self, instance, validated_data):
        try:
            user = self.context.get("user")
            agent_role = get_agent_role_object()
            property_obj = get_property_object(validated_data["property_id"])
            if instance.id != property_obj.unregistered_owner_id:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "unregistered_owner not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Unregistered owner details not found"
                        },
                    }
                )

            instance.name = validated_data["name"]
            instance.email = validated_data["email"]
            instance.phone_number = validated_data["phone_number"]
            instance.updated_by = user
            instance.updated_by_role = agent_role
            instance.save()

            return instance

        except Exception as error:
            traceback.print_exc()
            raise


class UploadFloorPlanSerializer(serializers.ModelSerializer):
    """
    Serializer to add/remove floor plan
    """

    document_media_file = serializers.FileField(required=False, allow_null=True)
    image_media_files = serializers.ListField(
        child=serializers.FileField(required=False, allow_null=True, default=None),
        max_length=10,
        required=False,
        allow_empty=True,
        default=[],
    )
    image_media_order = serializers.CharField(required=False, allow_null=True)
    is_deleted_document_media = serializers.BooleanField(
        required=False, allow_null=True, default=False
    )
    deleted_image_media = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = PropertyFloorPlan
        fields = [
            "document_media_file",
            "image_media_files",
            "image_media_order",
            "is_deleted_document_media",
            "deleted_image_media",
        ]

    def validate(self, data):
        document_media_file = data.get("document_media_file")
        image_media_files = data.get("image_media_files")
        image_media_order = data.get("image_media_order")
        is_deleted_document_media = data.get("is_deleted_document_media")

        if (
            (document_media_file and image_media_files)
            or (document_media_file and image_media_order)
            or (image_media_files and is_deleted_document_media)
        ):
            raise serializers.ValidationError(
                "Can't upload/ delete document and image together"
            )
        elif document_media_file and is_deleted_document_media:
            raise serializers.ValidationError(
                "Can't upload and remove document together"
            )
        elif image_media_files and not image_media_order:
            raise serializers.ValidationError("Media order is missing")

        return data

    def validate_document_media_file(self, media_file):
        if media_file:
            check_file_validity(media_file)

        return media_file

    def validate_image_media_files(self, media_files):
        logger.info(f"Media files {media_files}")

        if isinstance(media_files, list):
            # If it's an empty list, we return it as is (valid case)
            if not media_files:
                return media_files

        for each_media in media_files:
            is_valid_image_or_video(each_media, settings.ALLOWED_IMAGE_MIME_TYPES)

        return media_files


class PropertyFloorPlanSerializer(serializers.ModelSerializer):
    """
    Serializer for property floor plan
    """

    media_id = serializers.SerializerMethodField()
    media_file = serializers.SerializerMethodField()
    media_type = serializers.SerializerMethodField()

    class Meta:
        model = PropertyFloorPlan
        fields = [
            "media_id",
            "media_file",
            "order_no",
            "media_file_size",
            "media_file_name",
            "media_type",
        ]

    def get_media_file(self, instance):
        media_file_url = get_s3_object(instance.media_file_key)
        return media_file_url

    def get_media_id(self, instance):
        return instance.id

    def get_media_type(self, instance):
        return instance.media_file_content_type


class UploadPaymentPlanSerializer(serializers.ModelSerializer):
    """
    Serializer to add/remove payment plan
    """

    document_media_file = serializers.FileField(required=False, allow_null=True)
    image_media_files = serializers.ListField(
        child=serializers.FileField(required=False, allow_null=True, default=None),
        max_length=10,
        required=False,
        allow_empty=True,
        default=[],
    )
    image_media_order = serializers.CharField(required=False, allow_null=True)
    is_deleted_document_media = serializers.BooleanField(
        required=False, allow_null=True, default=False
    )
    deleted_image_media = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = PropertyPaymentPlan
        fields = [
            "document_media_file",
            "image_media_files",
            "image_media_order",
            "is_deleted_document_media",
            "deleted_image_media",
        ]

    def validate(self, data):
        document_media_file = data.get("document_media_file")
        image_media_files = data.get("image_media_files")
        image_media_order = data.get("image_media_order")
        is_deleted_document_media = data.get("is_deleted_document_media")

        if (
            (document_media_file and image_media_files)
            or (document_media_file and image_media_order)
            or (image_media_files and is_deleted_document_media)
        ):
            raise serializers.ValidationError(
                "Can't upload/ delete document and image together"
            )
        elif document_media_file and is_deleted_document_media:
            raise serializers.ValidationError(
                "Can't upload and remove document together"
            )
        elif image_media_files and not image_media_order:
            raise serializers.ValidationError("Media order is missing")

        return data

    def validate_document_media_file(self, media_file):
        if media_file:
            check_file_validity(media_file)

        return media_file

    def validate_image_media_files(self, media_files):
        logger.info(f"Media files {media_files}")

        if isinstance(media_files, list):
            # If it's an empty list, we return it as is (valid case)
            if not media_files:
                return media_files

        for each_media in media_files:
            is_valid_image_or_video(each_media, settings.ALLOWED_IMAGE_MIME_TYPES)

        return media_files


class AIAgentPortfolioViewSerializer(serializers.ModelSerializer):
    """
    Serializer to get agent portfolio for AI
    """

    distressed_deal = serializers.SerializerMethodField()
    property_unit_type = serializers.CharField(
        source="get_property_unit_type_display", read_only=True
    )
    # unit_images = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name")
    availability_status = serializers.SerializerMethodField()
    asking_price = serializers.IntegerField()
    rent_data = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    property_currency_code = serializers.CharField()
    valuation = serializers.IntegerField()

    class Meta:
        model = Property
        fields = [
            "id",
            "community",
            # "unit_number",
            # "building_number",
            "property_type",
            "floor_number",
            "property_unit_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "availability_status",
            "owner_intent",
            "asking_price",
            "rent_data",
            # "unit_images",
            "distressed_deal",
            "building_name",
            "address",
            "furnished",
            "branded_building",
            "premium_view",
            "property_currency_code",
            "owner_verified",
            "valuation",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        agent_profile = self.context.get("agent_profile")
        asking_price = instance.asking_price

        data["tentative_commission"] = None
        if asking_price and agent_profile and agent_profile.commission_percentage:
            commission = round(asking_price * agent_profile.commission_percentage / 100)
            data["tentative_commission"] = commission

        return data

    def get_availability_status(self, instance):
        if instance.propertyavailabilityandstatus:
            return instance.propertyavailabilityandstatus.get_occupancy_status_display()

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_rent_data(self, instance):
        occupancy_status = instance.propertyavailabilityandstatus.occupancy_status
        if occupancy_status and occupancy_status == PropertyAvailabilityStatus.RENTED:
            financial_details = PropertyFinancialDetails.objects.filter(
                property=instance
            ).first()
            if financial_details:
                total_rent = financial_details.annual_rent
                original_price = financial_details.original_price
                data = dict()
                data["total_rent"] = total_rent
                if (original_price and original_price > 0) and (
                    total_rent and total_rent > 0
                ):
                    rent_increase_percentage = (total_rent / original_price) * 100
                    data["rent_increase_percentage"] = rent_increase_percentage
                    data["is_upward"] = True
                    return data
                else:
                    data["rent_increase_percentage"] = None
                    data["is_upward"] = None
                    return data
        return None

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False


class AIPropertyAvailabilityAndStatusSerializer(serializers.ModelSerializer):
    """
    Serializer to get property availability and status details for AI
    """

    status = serializers.CharField(
        source="get_status_display", read_only=True, allow_null=True
    )
    occupancy_status = serializers.CharField(
        source="get_occupancy_status_display", read_only=True, allow_null=True
    )
    during_construction = serializers.FloatField(allow_null=True)
    on_handover = serializers.FloatField(allow_null=True)
    payment_plan = serializers.SerializerMethodField()

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "status",
            "handover_date",
            "occupancy_status",
            "enable_payment_plan",
            "enable_post_handover",
            "during_construction",
            "on_handover",
            "post_handover_time_frame",
            "payment_plan",
        ]

    def get_payment_plan(self, instance):
        payment_plan = PropertyPaymentPlan.objects.filter(
            property=instance.property
        ).order_by("order_no")
        return PropertyPaymentPlanSerializer(payment_plan, many=True).data


class AIPropertyViewFinancialDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer to get property financial details for AI
    """

    gross_yield = serializers.SerializerMethodField()
    net_yield = serializers.SerializerMethodField()
    gains = serializers.SerializerMethodField()
    gains_in_percentage = serializers.SerializerMethodField()
    is_gains_upward = serializers.SerializerMethodField()
    property_currency_original_price = serializers.IntegerField(source="original_price")
    property_currency_asking_price = serializers.IntegerField(source="asking_price")
    property_currency_valuation = serializers.IntegerField(source="valuation")
    property_currency_annual_service_charges = serializers.IntegerField(
        source="annual_service_charges"
    )
    property_currency_annual_rent = serializers.IntegerField(source="annual_rent")
    property_currency_security_deposit = serializers.IntegerField(
        source="security_deposit"
    )
    property_currency_other_expenses = serializers.IntegerField(source="other_expenses")

    class Meta:
        model = PropertyFinancialDetails
        fields = [
            "property_currency_original_price",
            "property_currency_asking_price",
            "property_currency_valuation",
            "property_currency_annual_service_charges",
            "property_currency_annual_rent",
            "property_currency_security_deposit",
            "property_currency_other_expenses",
            "gross_yield",
            "net_yield",
            "gains",
            "gains_in_percentage",
            "property_currency_code",
            "is_gains_upward",
        ]

    def get_gross_yield(self, instance):
        financial_serializer = PropertyViewFinancialDetailsSerializer()
        return financial_serializer.get_gross_yield(instance)

    def get_net_yield(self, instance):
        financial_serializer = PropertyViewFinancialDetailsSerializer()
        return financial_serializer.get_net_yield(instance)

    def get_gains(self, instance):
        financial_serializer = PropertyViewFinancialDetailsSerializer()
        return financial_serializer.get_gains(instance)

    def get_gains_in_percentage(self, instance):
        financial_serializer = PropertyViewFinancialDetailsSerializer()
        return financial_serializer.get_gains_in_percentage(instance)

    def get_is_gains_upward(self, instance):
        financial_serializer = PropertyViewFinancialDetailsSerializer()
        return financial_serializer.get_is_gains_upward(instance)


class AIPropertyDetailSerializer(serializers.ModelSerializer):
    """
    Serializer to get property details for AI
    """

    is_deleted = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name", allow_null=True)
    country = serializers.CharField(source="country.short_name", allow_null=True)
    state = serializers.CharField(source="state.name", allow_null=True)
    availability_status = AIPropertyAvailabilityAndStatusSerializer(
        source="propertyavailabilityandstatus"
    )
    unit_images = serializers.SerializerMethodField()
    parking_number = serializers.SerializerMethodField()
    distressed_deal = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    lead_owner_percentage = serializers.DecimalField(
        required=False, max_digits=5, decimal_places=2, min_value=0, allow_null=True
    )
    building_name = serializers.SerializerMethodField()
    floor_plan = serializers.SerializerMethodField()
    financial_details = AIPropertyViewFinancialDetailsSerializer(
        source="propertyfinancialdetails"
    )
    sales_history = PropertySalesUnitHistorySerializer(many=True)
    rental_history = PropertyRentalUnitHistorySerializer(many=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "community",
            # "unit_number",
            # "building_number",
            "property_type",
            "floor_number",
            "owner_verified",
            "postal_code",
            "property_unit_type",
            "tenancy_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "address",
            "unit_images",
            "distressed_deal",
            "furnished",
            "premium_view",
            "availability_status",
            "owner_intent",
            "dewa_id",
            "building_name",
            "is_deleted",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "branded_building",
            "lead_owner_percentage",
            "floor_plan",
            "number_of_master_bedrooms",
            "number_of_other_bedrooms",
            "number_of_maid_rooms",
            "number_of_study_rooms",
            "country",
            "state",
            "financial_details",
            "sales_history",
            "rental_history",
        ]

    def get_parking_number(self, instance):
        return instance.get_parking_number()

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_is_deleted(self, obj):
        return DeletedProperty.objects.filter(property=obj).exists()

    def get_floor_plan(self, instance):
        floor_plan = PropertyFloorPlan.objects.filter(property=instance).order_by(
            "order_no"
        )
        return PropertyFloorPlanSerializer(floor_plan, many=True).data


class PropertyExternalMediaImageSerializer(serializers.Serializer):
    """
    Serializer to upload image external media
    """

    title = serializers.CharField(required=True, allow_null=False, max_length=100)
    section_id = serializers.IntegerField(required=False, allow_null=True)
    media_files = serializers.ListField(
        child=MediaFileSerializer(), required=False, allow_null=True
    )
    deleted_media = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_null=True
    )

    class Meta:
        fields = [
            "title",
            "section_id",
            "media_files",
            "deleted_media",
        ]

    def validate(self, data):
        if data.get("deleted_media") and not data.get("section_id"):
            raise serializers.ValidationError("Section ID is required to delete media.")

        return data


class CommercialPropertyFeaturesSerializer(serializers.ModelSerializer):
    """
    Serializer for commercial property features
    """

    branded_building = serializers.BooleanField(required=False)
    furnished = serializers.IntegerField(required=False)
    premium_view = serializers.BooleanField(required=False)
    is_restroom_available = serializers.BooleanField(required=False, default=False)
    no_of_shared_restrooms = serializers.IntegerField(allow_null=True, required=False)
    no_of_private_restrooms = serializers.IntegerField(allow_null=True, required=False)
    is_parking_available = serializers.BooleanField(required=False, default=False)
    public_parking = serializers.BooleanField(required=False)
    no_of_reserved_parking = serializers.IntegerField(allow_null=True, required=False)
    reserved_parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    is_lift_available = serializers.BooleanField(required=False, default=False)
    common_lift = serializers.BooleanField(required=False)
    no_of_personal_lift = serializers.IntegerField(allow_null=True, required=False)
    security_available = serializers.BooleanField(required=False)
    water_storage_available = serializers.BooleanField(required=False)
    property_on_main_road = serializers.BooleanField(required=False)
    corner_property = serializers.BooleanField(required=False)
    tag_list = serializers.ListField(child=serializers.CharField(), required=False)

    class Meta:
        model = UserLevelPropertyFeatures
        fields = [
            "branded_building",
            "furnished",
            "premium_view",
            "is_restroom_available",
            "no_of_shared_restrooms",
            "no_of_private_restrooms",
            "is_parking_available",
            "public_parking",
            "no_of_reserved_parking",
            "reserved_parking_number",
            "is_lift_available",
            "common_lift",
            "no_of_personal_lift",
            "security_available",
            "water_storage_available",
            "property_on_main_road",
            "corner_property",
            "tag_list",
        ]

    def validate(self, data):
        if data.get("no_of_shared_restrooms", 0) + data.get(
            "no_of_private_restrooms", 0
        ) == 0 and data.get("is_restroom_available"):
            raise serializers.ValidationError(
                "No of shared and private restrooms cannot be 0 if restroom is available"
            )
        if (
            data.get("is_parking_available")
            and data.get("no_of_reserved_parking", 0) == 0
            and not data.get("public_parking", False)
        ):
            raise serializers.ValidationError(
                "No of reserved parking or public parking is required if parking is available"
            )
        if data.get("no_of_reserved_parking", 0) != len(
            data.get("reserved_parking_number", [])
        ):
            raise serializers.ValidationError(
                "No of reserved parking does not match with parking numbers"
            )

        if (
            data.get("is_lift_available")
            and data.get("no_of_personal_lift", 0) == 0
            and not data.get("common_lift", False)
        ):
            raise serializers.ValidationError(
                "No of personal lift or common lift is required if lift is available"
            )

        return data

    def update(self, instance, validated_data):
        tag_names = validated_data.pop("tag_list", [])
        reserved_parking_number = validated_data.pop("reserved_parking_number", [])
        reserved_parking_number = (
            ",".join(reserved_parking_number) if reserved_parking_number else None
        )

        tags_list = [
            PropertyTag.objects.get_or_create(name=tag)[0] for tag in tag_names
        ]

        super().update(instance, validated_data)
        instance.tags.set(tags_list)
        instance.reserved_parking_number = reserved_parking_number

        if self.context.get("created"):
            instance.created_by = self.context.get("user")
            instance.created_by_role = self.context.get("role_obj")
        else:
            instance.updated_by = self.context.get("user")
            instance.updated_by_role = self.context.get("role_obj")

        instance.save()

        return instance


class LeaseConditionsSerializer(serializers.ModelSerializer):
    expected_rent = serializers.IntegerField(required=True)
    expected_security_deposit = serializers.IntegerField(required=True)
    rent_available_start_date = serializers.DateField(required=True)
    preferred_payment_frequency = serializers.ChoiceField(
        choices=PreferredPaymentFrequency.choices, required=True
    )

    class Meta:
        model = UserLevelPropertyFinancialDetails
        fields = [
            "expected_rent",
            "expected_security_deposit",
            "preferred_payment_frequency",
            "rent_available_start_date",
        ]

    def update(self, instance, validated_data):
        instance.expected_rent = validated_data.get("expected_rent")
        instance.expected_security_deposit = validated_data.get(
            "expected_security_deposit"
        )
        instance.preferred_payment_frequency = validated_data.get(
            "preferred_payment_frequency"
        )
        instance.updated_by = self.context.get("user")
        instance.updated_by_role = self.context.get("role_obj")
        instance.save()
        availability_and_status = (
            instance.property_level_data.property_user_level_availability_and_status
        )
        availability_and_status.rent_available_start_date = validated_data.get(
            "rent_available_start_date"
        )
        availability_and_status.save()

        return instance


class ClaimPropertyBasicDetailSerializer(serializers.ModelSerializer):
    building_street = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    unit_number = serializers.SerializerMethodField()
    display_price = serializers.SerializerMethodField()
    property_currency_code = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    default_image = serializers.CharField(source="property.default_image")
    owner_intent = serializers.CharField(source="property.owner_intent")

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "building_street",
            "address",
            "unit_images",
            "display_price",
            "property_currency_code",
            "default_image",
            "owner_intent",
            "unit_number",
        ]

    @staticmethod
    def get_display_price(instance):
        try:
            financial_details = instance.property_user_level_financial_details
            if instance.property.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
                return financial_details.valuation
            elif (
                instance.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                return financial_details.expected_rent
            return financial_details.asking_price
        except UserLevelPropertyFinancialDetails.DoesNotExist:
            return None

    @staticmethod
    def get_property_currency_code(instance):
        try:
            financial_details = instance.property_user_level_financial_details
            return financial_details.property_currency_code
        except UserLevelPropertyFinancialDetails.DoesNotExist:
            return None

    @staticmethod
    def get_building_street(instance):
        building_name = get_property_building_name(instance.property)
        return building_name

    @staticmethod
    def get_address(instance):
        address = build_property_address(instance.property)
        return address

    @staticmethod
    def get_unit_images(instance):
        """
        Modify this function to get only first PropertyUnitSections object
        """
        filters = create_user_level_filter(
            instance.property, instance.created_by, instance.created_by_role
        )
        first_unit_section = (
            PropertyUnitSections.objects.filter(**filters).order_by("id").first()
        )
        if first_unit_section:
            first_media = (
                UnitSectionMedia.objects.filter(section=first_unit_section)
                .order_by("order_no")
                .first()
            )
            if first_media:
                return PropertyMediaSerializer(
                    first_media
                ).data  # Return only one image

        return None

    @staticmethod
    def get_unit_number(instance):
        return instance.property.unit_number


class FileDetailSerializer(serializers.Serializer):
    """Serializer for file details"""

    media_file_name = serializers.CharField(required=True)
    media_size = serializers.IntegerField(required=True)
    media_mime_type = serializers.CharField(required=True)
    thumbnail_file_name = serializers.CharField(required=False, allow_null=True)

    def validate(self, attrs):
        media_type = attrs.get("media_mime_type").split("/")[0]
        if media_type == "video" and not attrs.get("thumbnail_file_name"):
            raise serializers.ValidationError(
                "Thumbnail is required for video media type."
            )
        if (
            media_type == "image"
            and attrs.get("media_size") > settings.IMAGE_SIZE_LIMIT_IN_MB * 1024 * 1024
        ):
            raise serializers.ValidationError(
                f"Image size should not exceed {settings.IMAGE_SIZE_LIMIT_IN_MB} MB"
            )
        if (
            media_type == "video"
            and attrs.get("media_size") > settings.VIDEO_SIZE_LIMIT_IN_MB * 1024 * 1024
        ):
            raise serializers.ValidationError(
                f"Video size should not exceed {settings.VIDEO_SIZE_LIMIT_IN_MB} MB"
            )
        return attrs


class PresignedURLRequestSerializer(serializers.Serializer):
    """Serializer for presigned url request"""

    media_files = serializers.ListField(
        child=FileDetailSerializer(), required=True, allow_null=False
    )
    property_media_feature = serializers.ChoiceField(
        choices=PropertyMediaFeatures.choices,
        required=False,
        default=PropertyMediaFeatures.PROPERTY_UNIT_SECTION_MEDIA,
    )


class PropertySectionImagesSerializer(serializers.Serializer):
    sections = serializers.ListField(
        child=serializers.CharField(), source="section_types"
    )
    media_data = PropertySectionSerializer(source="sections", many=True, read_only=True)


class PropertyExternalMediaDocumentSerializer(serializers.Serializer):
    """
    Serializer to upload document external media
    """

    title = serializers.CharField(required=True, allow_null=False, max_length=100)
    section_id = serializers.IntegerField(required=False, allow_null=True)
    media_files = serializers.ListField(
        child=MediaFileSerializer(), required=False, allow_null=True
    )
    deleted_media = serializers.ListField(
        child=serializers.IntegerField(), required=False, allow_null=True
    )

    class Meta:
        fields = [
            "title",
            "section_id",
            "media_files",
            "deleted_media",
        ]

    def validate(self, data):
        if data.get("deleted_media") and not data.get("section_id"):
            raise serializers.ValidationError("Section ID is required to delete media.")

        return data


class OpenForCollaborationSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserLevelPropertyData
        fields = ["open_for_collaboration"]
