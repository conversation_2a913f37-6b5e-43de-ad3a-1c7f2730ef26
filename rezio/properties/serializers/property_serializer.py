import logging
import re
from datetime import datetime

from dateutil.relativedelta import relativedelta
from django.db import transaction
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import Cast
from rest_framework import serializers

from rezio.properties.constants import (
    COMMERCIAL_PROPERTY_TYPES,
    INDIA,
    PROPERTY_MONITOR_DETAILS_FIELD_MAPPING,
    REQUEST_BODY_DATA_MAPPING,
)
from rezio.properties.models import (
    Country,
    Property,
    PropertyAvailabilityAndStatus,
    PropertyCompletionState,
    PropertyVerifiedDataFields,
    PropertySalesUnitHistory,
    PropertyRentalUnitHistory,
    State,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyData,
    City,
)
from rezio.properties.services.property_service import handle_associated_agents
from rezio.properties.text_choices import (
    PropertyType,
    PropertyStatus,
    PropertyAgentType,
    OwnerIntentForProperty,
    PropertyBuildingType,
    PreferredPaymentFrequency,
    PropertyCategory,
)
from rezio.properties.utils import get_property_object
from rezio.rezio.custom_error_codes import INVALID_ADDRESS_ID
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.helper import check_file_validity
from rezio.user.models import AgentProfile
from rezio.user.utils import get_investor_role_object
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    KEY_ERROR_CODE,
)
from rezio.utils.custom_exceptions import (
    InternalServerException,
    InvalidSerializerDataException,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class GoogleLocationComponentsSerializer(serializers.Serializer):
    long_name = serializers.CharField(max_length=255)
    short_name = serializers.CharField(max_length=255)
    types = serializers.ListField(child=serializers.CharField(max_length=255))


class GoogleLocationSerializer(serializers.Serializer):
    address_components = serializers.ListField(
        child=GoogleLocationComponentsSerializer()
    )
    is_draft = serializers.BooleanField(required=True)

    def validate_is_draft(self, is_draft):
        if is_draft:
            raise serializers.ValidationError("Cannot save this section as draft")


class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = ["id", "name", "short_name"]


class StateSerializer(serializers.ModelSerializer):
    class Meta:
        model = State
        fields = ["id", "name"]


class CitySerializer(serializers.ModelSerializer):
    class Meta:
        model = City
        fields = ["id", "name", "is_active"]


class PropertyIDSerializer(serializers.ModelSerializer):
    property_id = serializers.IntegerField(source="id")

    class Meta:
        model = Property
        fields = ["property_id"]


class PropertyMonitorLocationDataSerializer(serializers.Serializer):
    location_id = serializers.IntegerField(source="id")
    name = serializers.CharField()
    emirate = serializers.CharField()
    master_development = serializers.CharField()
    sub_loc_1 = serializers.CharField(allow_blank=True)
    sub_loc_2 = serializers.CharField(allow_blank=True)
    sub_loc_3 = serializers.CharField(allow_blank=True)
    sub_loc_4 = serializers.CharField(allow_blank=True)
    building = serializers.CharField(allow_blank=True)
    community_string = serializers.CharField(allow_blank=True)


class PropertyMonitorLocationSerializer(serializers.Serializer):
    results = serializers.ListField(
        source="data", child=PropertyMonitorLocationDataSerializer()
    )
    # current_page = serializers.IntegerField(required=False)
    # last_page = serializers.IntegerField(required=False)
    # per_page = serializers.IntegerField(required=False)
    # total = serializers.IntegerField(required=False)


class PropertyMonitorUnitDataSerializer(serializers.Serializer):
    address_id = serializers.IntegerField()
    location_id = serializers.IntegerField()
    unit_number = serializers.CharField()
    building_number = serializers.CharField(allow_blank=True)
    name = serializers.CharField(allow_blank=True, source="location_name")
    master_development = serializers.CharField(source="master_community")
    sub_loc_1 = serializers.CharField(allow_blank=True)
    sub_loc_2 = serializers.CharField(allow_blank=True)
    sub_loc_3 = serializers.CharField(allow_blank=True)
    sub_loc_4 = serializers.CharField(allow_blank=True)


class PropertyMonitorUnitSerializer(serializers.Serializer):
    results = serializers.ListField(
        source="data", child=PropertyMonitorUnitDataSerializer()
    )
    count = serializers.IntegerField(required=False, source="total")
    next = serializers.IntegerField(required=False, default=None)
    previous = serializers.IntegerField(required=False, default=None)
    total_pages = serializers.IntegerField(required=False, source="last_page")
    current_page = serializers.IntegerField(required=False)
    page_size = serializers.IntegerField(required=False, source="per_page")


class CombinedPropertyMonitorSerializer(
    PropertyMonitorLocationDataSerializer, PropertyMonitorUnitDataSerializer
):
    is_draft = serializers.BooleanField(required=True)

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class ManualLocationDetails(serializers.Serializer):
    property_id = serializers.IntegerField(required=True)
    is_draft = serializers.BooleanField(required=True)
    community = serializers.CharField(required=True)
    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    unit_number = serializers.CharField(required=True)
    floor_number = serializers.CharField(required=False, allow_null=True)
    postal_code = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class UnitDetailSerializer(serializers.Serializer):
    property_type = serializers.ChoiceField(choices=PropertyType.choices)
    unit_bua_sqft = serializers.FloatField(required=False, allow_null=True)
    suite_area_sqft = serializers.FloatField(required=False, allow_null=True)
    balcony_size_sqft = serializers.FloatField(required=False, allow_null=True)
    no_beds = serializers.IntegerField(required=False, allow_null=True)
    parking = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    floor = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    location_id = serializers.IntegerField()
    master_community = serializers.CharField()
    sub_loc_1 = serializers.CharField(allow_blank=True)
    sub_loc_2 = serializers.CharField(allow_blank=True)
    sub_loc_3 = serializers.CharField(allow_blank=True)
    sub_loc_4 = serializers.CharField(allow_blank=True)


class PropertyMonitorResponseSerializer(serializers.ModelSerializer):
    community = serializers.CharField(source="community.name")
    country = serializers.CharField(source="country.name")
    state = serializers.CharField(source="state.name", allow_null=True)
    city = serializers.CharField(source="city.name", allow_null=True)
    area = serializers.CharField(source="area.name", allow_null=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "community",
            "building_number",
            "unit_number",
            "country",
            "state",
            "city",
            "area",
        ]


class UserAddedResponseSerializer(serializers.ModelSerializer):
    community = serializers.CharField(source="community.name")
    country = serializers.CharField(source="country.name")
    state = serializers.CharField(source="state.name", allow_null=True)
    city = serializers.CharField(source="city.name", allow_null=True)
    area = serializers.CharField(source="area.name", allow_null=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "community",
            "property_type",
            "postal_code",
            "unit_number",
            "floor_number",
            "country",
            "state",
            "city",
            "area",
        ]


class PropertyAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for property availability and status
    """

    property_id = serializers.IntegerField(required=True)
    is_draft = serializers.BooleanField(required=True, write_only=True)
    status = serializers.ChoiceField(choices=PropertyStatus.choices)
    handover_date = serializers.CharField(required=False, allow_null=True)
    enable_payment_plan = serializers.BooleanField(required=True)
    during_construction = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    on_handover = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    enable_post_handover = serializers.BooleanField(required=True)
    post_handover_time_frame = serializers.IntegerField(required=False, allow_null=True)
    property_intent = serializers.ChoiceField(
        choices=OwnerIntentForProperty.choices, required=False, allow_null=True
    )
    asking_price = serializers.IntegerField(required=False, allow_null=True)
    valuation = serializers.IntegerField(required=False, allow_null=True)
    edit_availability_status_data = serializers.BooleanField(
        required=True, write_only=True
    )
    expected_rent = serializers.IntegerField(required=False, allow_null=True)
    expected_security_deposit = serializers.IntegerField(
        required=False, allow_null=True
    )
    preferred_payment_frequency = serializers.ChoiceField(
        choices=PreferredPaymentFrequency.choices, required=False, allow_null=True
    )
    rent_available_start_date = serializers.DateField(required=False, allow_null=True)
    price_negotiable = serializers.BooleanField(required=False)

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "property_id",
            "is_draft",
            "status",
            "handover_date",
            "enable_payment_plan",
            "during_construction",
            "on_handover",
            "enable_post_handover",
            "post_handover_time_frame",
            "property_intent",
            "asking_price",
            "edit_availability_status_data",
            "valuation",
            "expected_rent",
            "expected_security_deposit",
            "preferred_payment_frequency",
            "rent_available_start_date",
            "price_negotiable",
        ]

    @staticmethod
    def check_date_format(date_string, property_status):
        # Regular expression pattern to match the format "month year - month year"
        pattern = (
            r"^((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{4}) - "
            r"((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{4})$"
        )

        match = re.match(pattern, date_string)
        # Check if the passed string matches the pattern
        if not match:
            return False
        # Extract the two dates from the matched groups
        start_date_str, end_date_str = match.group(1), match.group(2)
        # Convert to datetime objects for comparison
        start_date = datetime.strptime(start_date_str, "%b %Y")
        end_date = datetime.strptime(end_date_str, "%b %Y")
        if start_date > end_date:
            return False

        current_date = datetime.now()

        # Calculate the difference in months
        difference = relativedelta(end_date, start_date)
        if (
            property_status == PropertyStatus.UNDER_DEVELOPMENT
            and end_date
            < current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        ):
            return False

        # Check if the difference is exactly 3 months
        if difference.years == 0 and difference.months == 2:
            return True

        return False

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        # check handover date format
        if data.get("status") != PropertyStatus.READY:
            if not data.get("handover_date"):
                raise serializers.ValidationError("Handover date is required")

        # check all required fields for payment plan
        if data.get("enable_payment_plan"):
            if (
                data.get("during_construction") is None
                or data.get("on_handover") is None
            ):
                raise serializers.ValidationError("Payment percentage are required")

            if data.get("during_construction") + data.get("on_handover") != 100:
                raise serializers.ValidationError("Payment percentage do not add up")

            if data.get("post_handover_time_frame") and not data.get(
                "enable_post_handover"
            ):
                raise serializers.ValidationError("Post handover not enabled")

        if (
            data.get("during_construction")
            or data.get("on_handover")
            or data.get("enable_post_handover")
            or data.get("post_handover_time_frame")
        ) and not data.get("enable_payment_plan"):
            raise serializers.ValidationError("Payment plan not enabled")

        if data.get("handover_date") and not self.check_date_format(
            data.get("handover_date"), data.get("status")
        ):
            raise serializers.ValidationError("Invalid date")

        property_intent = data.get("property_intent", None)
        asking_price = data.get("asking_price", None)
        valuation = data.get("valuation", None)
        edit_availability_status_data = data.get("edit_availability_status_data")
        expected_rent = data.get("expected_rent")
        expected_security_deposit = data.get("expected_security_deposit")
        preferred_payment_frequency = data.get("preferred_payment_frequency")
        rent_available_start_date = data.get("rent_available_start_date")

        if self.context.get("user_role") == AGENT and not edit_availability_status_data:
            if property_intent not in [
                OwnerIntentForProperty.AVAILABLE_FOR_SALE,
                OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            ]:
                raise serializers.ValidationError("Invalid property intent selected")
            if (
                property_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE
                and not asking_price
            ) or (
                asking_price
                and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_SALE
            ):
                raise serializers.ValidationError("Invalid data sent")
            if (
                property_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
                and not expected_rent
                and not expected_security_deposit
                and not preferred_payment_frequency
                and not rent_available_start_date
            ) or (
                (
                    expected_rent
                    or expected_security_deposit
                    or preferred_payment_frequency
                    or rent_available_start_date
                )
                and property_intent != OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                raise serializers.ValidationError("Invalid data sent")

        if self.context.get("user_role") == INVESTOR:
            if not edit_availability_status_data:
                if not valuation:
                    raise serializers.ValidationError("Valuation is required")
                if property_intent or asking_price:
                    raise serializers.ValidationError("Invalid data sent")

        if edit_availability_status_data and (
            property_intent
            or asking_price
            or valuation
            or expected_rent
            or expected_security_deposit
            or preferred_payment_frequency
            or rent_available_start_date
        ):
            raise serializers.ValidationError("Invalid data sent")

        return data


class UserLevelPropertyAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for user level property availability and status
    """

    status = serializers.ChoiceField(choices=PropertyStatus.choices)
    handover_date = serializers.CharField(required=False, allow_null=True)
    enable_payment_plan = serializers.BooleanField(required=True)
    during_construction = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    on_handover = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    enable_post_handover = serializers.BooleanField(required=True)
    post_handover_time_frame = serializers.IntegerField(required=False, allow_null=True)
    property_intent = serializers.ChoiceField(
        choices=OwnerIntentForProperty.choices, required=False, allow_null=True
    )
    asking_price = serializers.IntegerField(required=False, allow_null=True)
    valuation = serializers.IntegerField(required=False, allow_null=True)
    edit_availability_status_data = serializers.BooleanField(
        required=True, write_only=True
    )
    expected_rent = serializers.IntegerField(required=False, allow_null=True)
    expected_security_deposit = serializers.IntegerField(
        required=False, allow_null=True
    )
    preferred_payment_frequency = serializers.ChoiceField(
        choices=PreferredPaymentFrequency.choices, required=False, allow_null=True
    )
    rent_available_start_date = serializers.DateField(required=False, allow_null=True)
    price_negotiable = serializers.BooleanField(required=False)

    class Meta:
        model = UserLevelPropertyAvailabilityAndStatus
        fields = [
            "status",
            "handover_date",
            "enable_payment_plan",
            "during_construction",
            "on_handover",
            "enable_post_handover",
            "post_handover_time_frame",
            "property_intent",
            "asking_price",
            "edit_availability_status_data",
            "valuation",
            "expected_rent",
            "expected_security_deposit",
            "preferred_payment_frequency",
            "rent_available_start_date",
            "price_negotiable",
        ]


class PropertyFileUploadSerializer(serializers.Serializer):
    property_id = serializers.IntegerField(required=True)
    is_rent_contract = serializers.BooleanField(required=True)
    is_ownership_proof = serializers.BooleanField(required=True)
    file = serializers.FileField(required=True)

    class Meta:
        fields = ["property_id", "file", "is_rent_contract", "is_ownership_proof"]

    def validate(self, data):
        if data.get("is_rent_contract") and data.get("is_ownership_proof"):
            raise serializers.ValidationError("Only one file type can be uploaded")
        elif not data.get("is_rent_contract") and not data.get("is_ownership_proof"):
            raise serializers.ValidationError("Need to upload one file type")
        return data

    def validate_file(self, file):
        return check_file_validity(file)


class PropertyFileDeleteSerializer(serializers.Serializer):
    property_id = serializers.IntegerField(required=True)
    is_rent_contract = serializers.BooleanField(required=True)
    is_ownership_proof = serializers.BooleanField(required=True)

    class Meta:
        fields = ["property_id", "is_rent_contract", "is_ownership_proof"]

    def validate(self, data):
        if data.get("is_rent_contract") and data.get("is_ownership_proof"):
            raise serializers.ValidationError("Only one file type can be uploaded")
        elif not data.get("is_rent_contract") and not data.get("is_ownership_proof"):
            raise serializers.ValidationError("Need to upload one file type")
        return data


class ChoiceFieldSerializer(serializers.Serializer):
    value = serializers.CharField()
    display_name = serializers.CharField()


class PropertyCompletionStateSerializer(serializers.ModelSerializer):
    property_id = serializers.IntegerField(source="property.id")

    class Meta:
        model = PropertyCompletionState
        fields = ["property_id", "state"]


class PropertyLocationDetailsSerializer(serializers.ModelSerializer):
    country = serializers.CharField(source="country.name")
    country_short_name = serializers.CharField(source="country.short_name")
    state = serializers.CharField(source="state.name")

    class Meta:
        model = Property
        fields = [
            "id",
            "country",
            "country_short_name",
            "state",
            "community",
            "unit_number",
            "property_category",
        ]


class ManualPropertySpecificationsSerializer(serializers.Serializer):
    is_draft = serializers.BooleanField(required=True, write_only=True)
    total_area = serializers.IntegerField(required=True)
    carpet_area = serializers.IntegerField(required=True)
    balcony_area = serializers.IntegerField(required=False, allow_null=True)
    number_of_bedrooms = serializers.IntegerField(required=False, allow_null=True)
    number_of_common_bathrooms = serializers.IntegerField(
        required=False, allow_null=True
    )
    parking_available = serializers.BooleanField(default=False)
    number_of_covered_parking = serializers.IntegerField(
        required=False, allow_null=True
    )
    number_of_open_parking = serializers.IntegerField(required=False, allow_null=True)
    parking_number = serializers.SerializerMethodField(required=False, allow_null=True)
    user_unit_preference = serializers.CharField(required=False, default="sqft")

    class Meta:
        model = Property
        fields = [
            "id",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "number_of_common_bathrooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "user_unit_preference",
        ]

    def get_parking_number(self, instance):
        # Only call get_parking_number if the instance is a Property object
        if isinstance(instance, Property):
            return instance.get_parking_number()
        return []


def validate_area(field):
    """
    Validate area fields

    :param field: Name of the field
    """

    def validator(value):
        if value is not None and value <= 0.0:
            raise serializers.ValidationError(f"{field} area should be greater than 0")

    return validator


class DraftPropertySpecificationSerializer(serializers.Serializer):
    """
    Serializer to save property specifications as draft
    """

    is_draft = serializers.BooleanField(required=True, write_only=True)
    address = serializers.SerializerMethodField(read_only=True)
    property_type = serializers.CharField(required=False, allow_null=True)
    unit_number = serializers.CharField(read_only=True)
    building_name = serializers.SerializerMethodField(read_only=True)
    total_area = serializers.FloatField(required=False, allow_null=True)
    carpet_area = serializers.FloatField(required=False, allow_null=True)
    balcony_area = serializers.FloatField(required=False, allow_null=True)
    bathroom_details = serializers.SerializerMethodField()
    parking_details = serializers.SerializerMethodField()
    floor_number = serializers.CharField(required=False, allow_null=True)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_available = serializers.BooleanField(default=False)
    manual_added_details = serializers.SerializerMethodField(read_only=True)
    bedroom_details = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = [
            "id",
            "is_draft",
            "address",
            "property_type",
            "total_area",
            "unit_number",
            "building_number",
            "carpet_area",
            "balcony_area",
            "bathroom_details",
            "parking_details",
            "floor_number",
            "manual_added_details",
            "user_unit_preference",
            "parking_available",
            "bedroom_details",
        ]

    def validate(self, data):
        if not data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(1, 6)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_manual_added_details(self, obj):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_bedroom_details(self, obj):
        return {
            "number_of_bedrooms": obj.number_of_bedrooms,
            "number_of_other_bedrooms": obj.number_of_other_bedrooms,
            "number_of_maid_rooms": obj.number_of_maid_rooms,
            "number_of_study_rooms": obj.number_of_study_rooms,
        }


class PropertySpecificationSerializer(serializers.Serializer):
    """
    Serializer to save property specifications
    """

    is_draft = serializers.BooleanField(required=True, write_only=True)
    address = serializers.SerializerMethodField(read_only=True)
    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    unit_number = serializers.CharField(read_only=True)
    building_name = serializers.SerializerMethodField(read_only=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    bathroom_details = serializers.SerializerMethodField()
    parking_details = serializers.SerializerMethodField()
    bedroom_details = serializers.SerializerMethodField()
    floor_number = serializers.CharField(required=False, allow_null=True)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_available = serializers.BooleanField(default=False)
    manual_added_details = serializers.SerializerMethodField(read_only=True)
    property_edit_fields = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "is_draft",
            "address",
            "property_type",
            "total_area",
            "unit_number",
            "building_number",
            "carpet_area",
            "balcony_area",
            "bathroom_details",
            "parking_details",
            "floor_number",
            "manual_added_details",
            "user_unit_preference",
            "parking_available",
            "bedroom_details",
            "property_edit_fields",
        ]

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        # Check for verified fields that cannot be edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(
            property=self.instance,
            field_name__in=PROPERTY_MONITOR_DETAILS_FIELD_MAPPING.keys(),
        )

        request_obj = self.context.get("request_obj")
        for verified_field in verified_fields:
            field_name = verified_field.field_name
            current_value = getattr(self.instance, field_name)
            new_value = None
            if field_name in ["number_of_bedrooms", "parking_number"]:
                field_details = request_obj.data.get(
                    REQUEST_BODY_DATA_MAPPING.get(field_name), None
                )
                if field_details:
                    if field_name == "parking_number":
                        new_value = ",".join(field_details.get(field_name))
                    else:
                        new_value = field_details.get(field_name)

            elif field_name in data:
                new_value = data[field_name]

            logger.info(
                f"Current value of {field_name} for {self.instance} is {current_value}"
            )
            logger.info(f"New value of {field_name} for {self.instance} is {new_value}")

            # Compare values and raise error if trying to change verified field
            if (current_value is not None or new_value is not None) and str(
                current_value
            ) != str(new_value):
                raise serializers.ValidationError(
                    f"Cannot modify verified field: {field_name}"
                )

        field_details = request_obj.data.get(
            REQUEST_BODY_DATA_MAPPING.get("number_of_bedrooms"), None
        )

        if (
            data.get("property_type") == "Villa"
            and field_details
            and field_details.get("number_of_bedrooms", 0) <= 0
        ):
            raise serializers.ValidationError(
                f"Property type Villa must have greater than 0 bedrooms"
            )

        return data

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break
        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_bathroom_details(self, obj):
        return {
            "common": obj.number_of_common_bathrooms or 0,
            "attached": obj.number_of_attached_bathrooms or 0,
            "powder_room": obj.number_of_powder_rooms or 0,
            "total_bathrooms": obj.get_total_bathroom_count(),
        }

    def get_parking_details(self, obj):
        return {
            "covered": obj.number_of_covered_parking or 0,
            "open": obj.number_of_open_parking or 0,
            "parking_number": obj.get_parking_number(),
        }

    def update(self, instance, validated_data):
        changes = {}
        user_level_property_obj = self.context.get("user_level_property_obj")
        for attr, value in validated_data.items():
            if attr != "is_draft" and getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
                setattr(user_level_property_obj, attr, value)
        instance.save()
        user_level_property_obj.save()
        return instance, changes

    def get_manual_added_details(self, obj):
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_bedroom_details(self, obj):
        return {
            "number_of_bedrooms": obj.number_of_bedrooms,
            "number_of_master_bedrooms": obj.number_of_master_bedrooms,
            "number_of_other_bedrooms": obj.number_of_other_bedrooms,
            "number_of_maid_rooms": obj.number_of_maid_rooms,
            "number_of_study_rooms": obj.number_of_study_rooms,
        }

    def get_property_edit_fields(self, obj):
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        verified_fields = [field.field_name for field in verified_fields]
        property_edit_fields = {}
        for field in PROPERTY_MONITOR_DETAILS_FIELD_MAPPING:
            property_edit_fields[field] = True
            if field in verified_fields:
                property_edit_fields[field] = False
        return property_edit_fields


class PropertyDraftDataSerializer(serializers.Serializer):
    draft_data = serializers.JSONField(required=True)


class DraftPropertyMonitorLocationDataSerializer(serializers.Serializer):
    location_id = serializers.IntegerField(allow_null=True)
    name = serializers.CharField(allow_null=True)
    emirate = serializers.CharField(allow_null=True)
    master_development = serializers.CharField(allow_null=True)
    sub_loc_1 = serializers.CharField(allow_blank=True, allow_null=True)
    sub_loc_2 = serializers.CharField(allow_blank=True, allow_null=True)
    sub_loc_3 = serializers.CharField(allow_blank=True, allow_null=True)
    sub_loc_4 = serializers.CharField(allow_blank=True, allow_null=True)
    sub_loc_5 = serializers.CharField(allow_blank=True, allow_null=True)
    building = serializers.CharField(allow_blank=True, allow_null=True)
    community_string = serializers.CharField(allow_blank=True, allow_null=True)


class DraftPropertyMonitorUnitDataSerializer(serializers.Serializer):
    address_id = serializers.IntegerField(allow_null=True)
    location_string = serializers.CharField(allow_null=True)
    unit_number = serializers.CharField(allow_null=True)
    building_number = serializers.CharField(allow_blank=True, allow_null=True)


class DraftCombinedPropertyMonitorSerializer(
    DraftPropertyMonitorLocationDataSerializer, DraftPropertyMonitorUnitDataSerializer
):
    property_id = serializers.IntegerField(required=True)
    is_draft = serializers.BooleanField(required=True)

    def validate(self, data):
        if not data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class DraftManualLocationDetails(serializers.Serializer):
    property_id = serializers.IntegerField(required=True)
    is_draft = serializers.BooleanField(required=True)
    community = serializers.CharField(required=True, allow_null=True)
    property_type = serializers.ChoiceField(
        choices=PropertyType.choices, required=True, allow_null=True
    )
    unit_number = serializers.CharField(required=True, allow_null=True)
    floor_number = serializers.CharField(required=False, allow_null=True)
    postal_code = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, data):
        if not data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class DraftPropertyAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for draft property availability and status
    """

    property_id = serializers.IntegerField(required=True)
    is_draft = serializers.BooleanField(required=True)
    status = serializers.ChoiceField(
        choices=PropertyStatus.choices, allow_null=True, required=False
    )
    handover_date = serializers.CharField(required=False, allow_null=True)
    enable_payment_plan = serializers.BooleanField(required=False, default=False)
    during_construction = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    on_handover = serializers.DecimalField(
        allow_null=True, max_digits=5, decimal_places=2, min_value=0, required=False
    )
    enable_post_handover = serializers.BooleanField(required=False)
    post_handover_time_frame = serializers.IntegerField(allow_null=True)
    property_intent = serializers.ChoiceField(
        choices=OwnerIntentForProperty.choices, required=False, allow_null=True
    )
    asking_price = serializers.IntegerField(required=False, allow_null=True)
    valuation = serializers.IntegerField(required=False, allow_null=True)
    price_negotiable = serializers.BooleanField(required=False, default=False)
    expected_rent = serializers.IntegerField(required=False, allow_null=True)
    expected_security_deposit = serializers.IntegerField(
        required=False, allow_null=True
    )
    preferred_payment_frequency = serializers.ChoiceField(
        choices=PreferredPaymentFrequency.choices, required=False, allow_null=True
    )
    rent_available_start_date = serializers.DateField(required=False, allow_null=True)

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "property_id",
            "is_draft",
            "status",
            "handover_date",
            "enable_payment_plan",
            "during_construction",
            "on_handover",
            "enable_post_handover",
            "post_handover_time_frame",
            "property_intent",
            "asking_price",
            "price_negotiable",
            "expected_rent",
            "expected_security_deposit",
            "preferred_payment_frequency",
            "rent_available_start_date",
            "valuation",
        ]

    def validate(self, data):
        if not data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        if data.get("during_construction"):
            data["during_construction"] = float(data["during_construction"])
        if data.get("enable_post_handover"):
            data["enable_post_handover"] = float(data["enable_post_handover"])

        property_intent = data.get("property_intent", None)
        asking_price = data.get("asking_price", None)
        if self.context.get("user_role") == INVESTOR:
            if property_intent or asking_price:
                raise serializers.ValidationError("Invalid data sent")

        return data


class PropertySalesUnitHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertySalesUnitHistory
        fields = "__all__"  # or specify fields as needed


class PropertyRentalUnitHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PropertyRentalUnitHistory
        fields = "__all__"  # or specify fields as needed


class SavePropertyAgentSerializer(serializers.Serializer):
    property_id = serializers.IntegerField(required=True)
    agent_type = serializers.ChoiceField(
        required=True, choices=PropertyAgentType.choices
    )
    agents = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    agent_contract = serializers.FileField(allow_null=True, required=False)
    remove_exclusive_agent_contract = serializers.BooleanField(
        allow_null=True, default=False, required=False
    )

    def validate(self, data):
        agent_type = data.get("agent_type")
        agents = data.get("agents", None)
        if agent_type == PropertyAgentType.OPEN_TO_ALL and (
            agents or data.get("agent_contract")
        ):
            raise serializers.ValidationError(
                "Cannot add agents when selected as open to all"
            )
        if agent_type != PropertyAgentType.OPEN_TO_ALL:
            if not agents:
                raise serializers.ValidationError("No agent selected")
            agents = [x.strip() for x in agents.split(",")]
            # Commenting out selective agent count validation
            # if (
            #     agent_type == PropertyAgentType.SELECTIVE_AGENTS
            #     and len(agents) > settings.ALLOWED_SELECTIVE_AGENT_COUNT
            # ):
            #     raise serializers.ValidationError("Agents limit reached")
            if agent_type == PropertyAgentType.SELECTIVE_AGENTS and data.get(
                "agent_contract"
            ):
                raise serializers.ValidationError(
                    "Cannot add agent contract for selective agents"
                )
            elif agent_type == PropertyAgentType.EXCLUSIVE_AGENT and len(agents) > 1:
                raise serializers.ValidationError(
                    "Cannot add more than one exclusive agent"
                )

            seen = set()
            for agent in agents:
                if agent in seen:
                    raise serializers.ValidationError("Duplicate users found")
                else:
                    seen.add(agent)
            agent_profiles = (
                AgentProfile.objects.filter(id__in=agents)
                .annotate(id_str=Cast("id", CharField()))
                .values_list("id_str", flat=True)
            )
            missing_agents = set(agents) - set(agent_profiles)
            if missing_agents:
                raise serializers.ValidationError(f"User not found {missing_agents}")
        if agent_type != PropertyAgentType.EXCLUSIVE_AGENT and data.get(
            "agent_contract"
        ):
            raise serializers.ValidationError(
                "Agent contract is applicable only to exclusive agents"
            )

        return data

    def validate_agent_contract(self, agent_contract):
        if agent_contract:
            return check_file_validity(agent_contract)
        return agent_contract

    def create(self, validated_data):
        try:
            with transaction.atomic():
                role_obj = get_investor_role_object()
                property_id = validated_data["property_id"]
                agent_profiles = validated_data.get("agents")
                if agent_profiles:
                    agent_profiles = agent_profiles.split(",")
                agent_type = validated_data.get("agent_type")
                created_by = self.context.get("user")

                property_obj = get_property_object(property_id)
                property_obj.agent_type = agent_type
                handle_associated_agents(
                    property_obj,
                    agent_profiles,
                    created_by,
                    agent_type,
                    validated_data,
                    role_obj,
                )
                property_obj.save()
            return validated_data
        except Exception as error:
            raise InternalServerException(
                {
                    KEY_MESSAGE: "Error in adding agents to property",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class ManualPropertySpecificationSerializer(serializers.Serializer):
    """
    Serializer to save property specifications manually
    """

    is_draft = serializers.BooleanField(required=True, write_only=True)
    address = serializers.SerializerMethodField(read_only=True)
    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    bathroom_details = serializers.SerializerMethodField()
    parking_details = serializers.SerializerMethodField()
    user_unit_preference = serializers.CharField(default="sqft")
    parking_available = serializers.BooleanField(default=False)
    manual_added_details = serializers.SerializerMethodField(read_only=True)
    bedroom_details = serializers.SerializerMethodField()
    floor_number = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "is_draft",
            "address",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "bathroom_details",
            "parking_details",
            "floor_number",
            "manual_added_details",
            "user_unit_preference",
            "parking_available",
            "bedroom_details",
        ]

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data

    def get_address(self, obj):
        address_parts = [
            obj.unit_number,
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(1, 6)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_bathroom_details(self, obj):
        return {
            "common": obj.number_of_common_bathrooms or 0,
            "attached": obj.number_of_attached_bathrooms or 0,
            "powder_room": obj.number_of_powder_rooms or 0,
            "total_bathrooms": obj.get_total_bathroom_count(),
        }

    def get_parking_details(self, obj):
        return {
            "covered": obj.number_of_covered_parking or 0,
            "open": obj.number_of_open_parking or 0,
            "parking_number": obj.get_parking_number(),
        }

    def update(self, instance, validated_data):
        changes = {}
        user_level_property_obj = self.context.get("user_level_property_obj")
        for attr, value in validated_data.items():
            if attr != "is_draft" and getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
                setattr(user_level_property_obj, attr, value)
        instance.save()
        user_level_property_obj.save()
        return instance, changes

    def get_manual_added_details(self, obj):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_bedroom_details(self, obj):
        return {
            "number_of_bedrooms": obj.number_of_bedrooms,
            "number_of_master_bedrooms": obj.number_of_master_bedrooms,
            "number_of_other_bedrooms": obj.number_of_other_bedrooms,
            "number_of_maid_rooms": obj.number_of_maid_rooms,
            "number_of_study_rooms": obj.number_of_study_rooms,
        }


class DraftManualPropertySpecificationSerializer(serializers.Serializer):
    """
    Serializer to save property specifications manually as draft
    """

    is_draft = serializers.BooleanField(required=True, write_only=True)
    address = serializers.SerializerMethodField(read_only=True)
    property_type = serializers.CharField(required=False, allow_null=True)
    total_area = serializers.FloatField(required=False, allow_null=True)
    carpet_area = serializers.FloatField(required=False, allow_null=True)
    balcony_area = serializers.FloatField(required=False, allow_null=True)
    bathroom_details = serializers.SerializerMethodField()
    parking_details = serializers.SerializerMethodField()
    user_unit_preference = serializers.CharField(default="sqft")
    parking_available = serializers.BooleanField(default=False)
    manual_added_details = serializers.SerializerMethodField(read_only=True)
    bedroom_details = serializers.SerializerMethodField()
    floor_number = serializers.CharField(required=False, allow_null=True)
    total_floors = serializers.IntegerField(required=False, allow_null=True)
    building_type = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "is_draft",
            "address",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "bathroom_details",
            "parking_details",
            "floor_number",
            "number_of_bedrooms",
            "manual_added_details",
            "user_unit_preference",
            "parking_available",
            "bedroom_details",
            "total_floors",
            "building_type",
        ]

    def validate(self, data):
        if not data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data

    def get_address(self, obj):
        address_parts = [
            obj.unit_number,
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(1, 6)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_manual_added_details(self, obj):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_bedroom_details(self, obj):
        return {
            "number_of_bedrooms": obj.number_of_bedrooms,
            "number_of_master_bedrooms": obj.number_of_master_bedrooms,
            "number_of_other_bedrooms": obj.number_of_other_bedrooms,
            "number_of_maid_rooms": obj.number_of_maid_rooms,
            "number_of_study_rooms": obj.number_of_study_rooms,
        }


class PropertySerializer(serializers.ModelSerializer):
    is_locked = serializers.SerializerMethodField()

    class Meta:
        model = Property
        fields = "__all__"  # or list fields as needed including 'is_locked'

    def get_is_locked(self, obj):
        request = self.context.get("request")
        if request and hasattr(request, "user"):
            agent_profile = AgentProfile.objects.filter(user=request.user).first()
            if agent_profile:
                return obj not in agent_profile.unlocked_properties.all()
        return False


class ManualAddLocationDetails(serializers.Serializer):
    is_draft = serializers.BooleanField(required=False, default=False)
    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=True)
    community = serializers.CharField(required=True)
    unit_number = serializers.CharField(required=True)
    property_category = serializers.ChoiceField(
        choices=PropertyCategory.choices, required=True
    )
    country_short_name = serializers.CharField(required=False)

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class PMAddLocationDetails(serializers.Serializer):
    is_draft = serializers.BooleanField(required=False, default=False)
    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=True)
    property_category = serializers.ChoiceField(
        choices=PropertyCategory.choices, required=True
    )
    country_short_name = serializers.CharField(required=False)

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        return data


class ManualCommercialPropertySpecificationSerializer(serializers.ModelSerializer):
    """
    Serializer to save commercial property specifications manually
    """

    is_draft = serializers.BooleanField(required=True, write_only=True)
    property_type = serializers.ChoiceField(
        choices=PropertyType.choices, required=True, allow_null=False
    )
    building_type = serializers.ChoiceField(
        choices=PropertyBuildingType.choices, required=False, allow_null=True
    )
    total_area = serializers.FloatField(
        required=True, allow_null=False, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    user_unit_preference = serializers.CharField(default="sqft")
    floor_number = serializers.CharField(required=False, allow_null=True)
    total_floors = serializers.IntegerField(required=False, allow_null=True)
    address = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Property
        fields = [
            "is_draft",
            "property_type",
            "building_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "user_unit_preference",
            "floor_number",
            "total_floors",
            "address",
        ]

    def validate(self, data):
        if data.get("is_draft"):
            raise serializers.ValidationError("Invalid data sent")

        if data.get("property_type") not in COMMERCIAL_PROPERTY_TYPES:
            raise serializers.ValidationError("Invalid property type")

        return data

    def update(self, instance, validated_data):
        changes = {}
        user_level_property_obj = self.context.get("user_level_property_obj")
        for attr, value in validated_data.items():
            if attr != "is_draft" and getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
                setattr(user_level_property_obj, attr, value)
        instance.save()
        user_level_property_obj.save()
        return instance, changes

    def get_address(self, obj):
        address_parts = [
            obj.unit_number,
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(1, 6)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))


class GetAvailabilityAndStatusSerializer(serializers.ModelSerializer):
    """
    Serializer for get availability & status
    """

    class Meta:
        model = PropertyAvailabilityAndStatus
        fields = [
            "property_id",
            "status",
            "handover_date",
            "enable_payment_plan",
            "during_construction",
            "on_handover",
            "enable_post_handover",
            "post_handover_time_frame",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        user_role = self.context.get("user_role")
        financial_details = self.context.get("financial_details")

        data["property_id"] = instance.property.id
        data["valuation"] = financial_details.valuation
        data["valuation_data_source"] = financial_details.valuation_data_source
        data["property_currency_code"] = financial_details.property_currency_code

        if user_role == AGENT:
            data["expected_rent"] = None
            data["expected_security_deposit"] = None
            data["preferred_payment_frequency"] = None
            data["rent_available_start_date"] = None
            data["asking_price"] = None
            data["property_intent"] = financial_details.property.owner_intent
            data["price_negotiable"] = financial_details.price_negotiable
            if (
                financial_details.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_SALE
            ):
                data["asking_price"] = financial_details.asking_price
            if (
                financial_details.property.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                data["expected_rent"] = financial_details.expected_rent
                data["expected_security_deposit"] = (
                    financial_details.expected_security_deposit
                )
                data["preferred_payment_frequency"] = (
                    financial_details.preferred_payment_frequency
                )
                data["rent_available_start_date"] = instance.rent_available_start_date

        return data


class PropertySpecificationsSerializer(serializers.ModelSerializer):
    total_bedrooms = serializers.SerializerMethodField()
    total_bathrooms = serializers.SerializerMethodField()
    property_verified_fields = serializers.SerializerMethodField()
    parking_number = serializers.SerializerMethodField()

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "floor_number",
            "building_type",
            "total_floors",
            "total_bedrooms",
            "total_bathrooms",
            "property_verified_fields",
            "user_unit_preference",
            "parking_available",
            "parking_number",
        ]

    def get_total_bedrooms(self, instance):
        number_of_bedrooms = instance.number_of_bedrooms or 0

        return number_of_bedrooms

    def get_total_bathrooms(self, instance):
        number_of_bathrooms = instance.number_of_bathrooms or 0

        return number_of_bathrooms

    def get_property_verified_fields(self, instance):
        verified_fields = list(
            PropertyVerifiedDataFields.objects.filter(
                property=instance.property
            ).values_list("field_name", flat=True)
        )
        fields_to_check = [
            "total_area",
            "carpet_area",
            "balcony_area",
            "floor_number",
            "property_type",
            "number_of_bedrooms",
        ]

        edit_icon = False
        for field in fields_to_check:
            if field not in verified_fields:
                edit_icon = True
                break

        if "number_of_bedrooms" in verified_fields:
            verified_fields.remove("number_of_bedrooms")
            verified_fields.append("total_bedrooms")

        verified_data = {"fields": verified_fields, "edit_icon": edit_icon}
        return verified_data

    def get_parking_number(self, instance):
        if isinstance(instance, UserLevelPropertyData):
            return instance.get_parking_number()
        return []


class PMAddLocationDetailsV1(serializers.Serializer):
    """
    Serializer to store PM property location details
    """

    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=False, allow_null=True)
    property_category = serializers.ChoiceField(
        choices=PropertyCategory.choices, required=True
    )
    country_short_name = serializers.CharField(required=False)
    location_id = serializers.IntegerField(required=True)
    master_development = serializers.CharField(required=False)
    sub_loc_1 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_2 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_3 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_4 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    state_name = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def validate(self, data):
        if data.get("location_id") <= 0:
            raise serializers.ValidationError("Invalid location ID")

        if not data.get("state_name") and not data.get("state"):
            raise serializers.ValidationError("State name or state id is required")
        elif data.get("state_name") and data.get("state"):
            raise serializers.ValidationError(
                "Only one of state name or state id is required"
            )

        return data


class PMUpdateLocationDetailsV1(serializers.Serializer):
    """
    Serializer to update PM property location details
    """

    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=False, allow_null=True)
    country_short_name = serializers.CharField(required=False)
    location_id = serializers.IntegerField(required=True)
    master_development = serializers.CharField(required=False)
    sub_loc_1 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_2 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_3 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    sub_loc_4 = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    state_name = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def validate(self, data):
        if data.get("location_id") <= 0:
            raise serializers.ValidationError("Invalid location ID")

        if not data.get("state_name") and not data.get("state"):
            raise serializers.ValidationError("State name or state id is required")
        elif data.get("state_name") and data.get("state"):
            raise serializers.ValidationError(
                "Only one of state name or state id is required"
            )

        return data


class ManualAddLocationDetailsV1(serializers.Serializer):
    """
    Serializer to store manual property location details
    """

    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=False, allow_null=True)
    community = serializers.CharField(required=True)
    property_category = serializers.ChoiceField(
        choices=PropertyCategory.choices, required=True
    )
    country_short_name = serializers.CharField(required=False)
    city = serializers.CharField(required=False, allow_null=True)
    postal_code = serializers.CharField(required=False, allow_null=True)
    state_name = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def validate(self, data):
        country = data.get("country")
        if country:
            country = country.capitalize()
        if country == INDIA and not data.get("city"):
            raise serializers.ValidationError(
                "City is required for properties in India"
            )

        if not data.get("state_name") and not data.get("state"):
            raise serializers.ValidationError("State name or state id is required")
        elif data.get("state_name") and data.get("state"):
            raise serializers.ValidationError(
                "Only one of state name or state id is required"
            )

        return data


class ManualUpdateLocationDetailsV1(serializers.Serializer):
    """
    Serializer to update manual property location details
    """

    country = serializers.CharField(required=True)
    state = serializers.IntegerField(required=False, allow_null=True)
    community = serializers.CharField(required=True)
    country_short_name = serializers.CharField(required=False)
    city = serializers.CharField(required=False, allow_null=True)
    postal_code = serializers.CharField(required=False, allow_null=True)
    state_name = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def validate(self, data):
        country = data.get("country")
        if country:
            country = country.capitalize()
        if country == INDIA and not data.get("city"):
            raise serializers.ValidationError(
                "City is required for properties in India"
            )

        if not data.get("state_name") and not data.get("state"):
            raise serializers.ValidationError("State name or state id is required")
        elif data.get("state_name") and data.get("state"):
            raise serializers.ValidationError(
                "Only one of state name or state id is required"
            )

        return data


class PropertySaveUnitDetailsV1Serializer(serializers.Serializer):
    """
    Serializer to store property unit details
    """

    address_id = serializers.IntegerField(required=True)

    def validate(self, data):
        if data.get("address_id") <= 0:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while fetching unit details from PM",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: INVALID_ADDRESS_ID.get("message"),
                        KEY_ERROR_CODE: INVALID_ADDRESS_ID.get("code"),
                    },
                }
            )
        return data


class BasePropertyTransactionSerializer(serializers.Serializer):
    address = serializers.SerializerMethodField()
    transaction_date = serializers.DateTimeField(source="evidence_date")
    number_of_bedrooms = serializers.CharField(source="no_beds")
    total_sales_price = serializers.FloatField()
    sales_price_sqft_unit = serializers.FloatField()
    sales_price_sqm_unit = serializers.FloatField()
    total_area = serializers.FloatField(source="unit_bua_sqft")
    total_area_sqm = serializers.FloatField(source="unit_bua_sqm")
    property_type = serializers.CharField()
    currency_code = serializers.CharField(default="AED")

    @staticmethod
    def get_address(data):
        location_string = ", ".join(
            filter(
                None,
                [
                    data["sub_loc_4"],
                    data["sub_loc_3"],
                    data["sub_loc_2"],
                    data["sub_loc_1"],
                    data["master_development"],
                ],
            )
        )
        return location_string


class PaginatedResponseSerializer(serializers.Serializer):
    results = serializers.ListField(
        source="data", child=BasePropertyTransactionSerializer()
    )
    count = serializers.IntegerField(required=False, source="total")
    next = serializers.SerializerMethodField()
    previous = serializers.SerializerMethodField()
    total_pages = serializers.IntegerField(required=False, source="last_page")
    current_page = serializers.IntegerField(required=False)
    page_size = serializers.IntegerField(required=False, source="per_page")

    @staticmethod
    def get_next(instance):
        total_pages = instance.get("last_page")
        current_page = instance.get("current_page")
        next_page = None
        if total_pages and current_page and total_pages > current_page:
            next_page = current_page + 1
        return next_page

    @staticmethod
    def get_previous(instance):
        current_page = instance.get("current_page")
        previous_page = None
        if current_page and current_page > 1:
            previous_page = current_page - 1
        return previous_page


class PropertySpecificationSerializerV1(serializers.Serializer):
    """
    Serializer to save property specifications
    """

    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_available = serializers.BooleanField(default=False)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    floor_number = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "id",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bathrooms",
            "parking_number",
            "floor_number",
            "user_unit_preference",
            "parking_available",
            "number_of_bedrooms",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.property_obj = self.instance.property if self.instance else None
        self.verified_fields = PropertyVerifiedDataFields.objects.filter(
            property=self.property_obj,
            field_name__in=PROPERTY_MONITOR_DETAILS_FIELD_MAPPING.keys(),
        )

    def validate(self, data):
        for verified_field in self.verified_fields:
            field_name = verified_field.field_name
            current_value = getattr(self.instance, field_name)
            new_value = data.get(field_name)

            logger.info(
                f"Current value of {field_name} for {self.instance} is {current_value}"
            )
            logger.info(f"New value of {field_name} for {self.instance} is {new_value}")

            # Compare values and raise error if trying to change verified field
            if current_value is not None or new_value is not None:
                if field_name == "parking_number":
                    current_value = [
                        parking_number.strip()
                        for parking_number in current_value.split(",")
                    ]
                    if sorted(current_value) != sorted(new_value):
                        raise serializers.ValidationError(
                            f"Cannot modify verified field: {field_name}"
                        )
                elif str(current_value) != str(new_value):
                    raise serializers.ValidationError(
                        f"Cannot modify verified field: {field_name}"
                    )

        if (
            data.get("property_type") == "Villa"
            and data.get("number_of_bedrooms", 0) <= 0
        ):
            raise serializers.ValidationError(
                f"Property type Villa must have greater than 0 bedrooms"
            )

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        property_obj = self.context.get("property_obj")
        for attr, value in validated_data.items():
            changes[attr] = {
                "old_value": getattr(instance, attr),
                "new_value": value,
            }
            setattr(instance, attr, value)
            setattr(property_obj, attr, value)

        instance.save()
        property_obj.save()
        return instance, changes

    def get_property_verified_fields(self, instance):
        return self.verified_fields


class ManualResidentialPropertySpecificationSerializerV1(serializers.Serializer):
    """
    Serializer to save residential property specifications manually
    """

    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_available = serializers.BooleanField(default=False)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    floor_number = serializers.CharField(required=False, allow_null=True)
    unit_number = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "id",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bathrooms",
            "parking_number",
            "floor_number",
            "user_unit_preference",
            "parking_available",
            "number_of_bedrooms",
            "unit_number",
        ]

    def validate(self, data):
        if (
            data.get("property_type") == "Villa"
            and data.get("number_of_bedrooms", 0) <= 0
        ):
            raise serializers.ValidationError(
                f"Property type Villa must have greater than 0 bedrooms"
            )

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        property_obj = self.context.get("property_obj")
        for attr, value in validated_data.items():
            if attr != "unit_number":
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
                setattr(property_obj, attr, value)
        instance.save()
        property_obj.save()
        return instance, changes


class ManualCommercialPropertySpecificationSerializerV1(serializers.ModelSerializer):
    """
    Serializer to save commercial property specifications manually
    """

    property_type = serializers.ChoiceField(
        choices=PropertyType.choices, required=True, allow_null=False
    )
    building_type = serializers.ChoiceField(
        choices=PropertyBuildingType.choices, required=False, allow_null=True
    )
    total_area = serializers.FloatField(
        required=True, allow_null=False, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    user_unit_preference = serializers.CharField(default="sqft")
    floor_number = serializers.CharField(required=False, allow_null=True)
    total_floors = serializers.IntegerField(required=False, allow_null=True)
    unit_number = serializers.CharField(required=False, allow_null=True)
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    parking_available = serializers.BooleanField(default=False)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "building_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "user_unit_preference",
            "floor_number",
            "total_floors",
            "unit_number",
            "number_of_bathrooms",
            "number_of_bedrooms",
            "parking_number",
            "parking_available",
        ]

    def validate(self, data):
        if data.get("property_type") not in COMMERCIAL_PROPERTY_TYPES:
            raise serializers.ValidationError("Invalid property type")

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        property_obj = self.context.get("property_obj")
        for attr, value in validated_data.items():
            if attr != "unit_number":
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
                setattr(property_obj, attr, value)
        instance.save()
        property_obj.save()
        return instance, changes


class EditPropertySpecificationSerializerV1(serializers.Serializer):
    """
    Serializer to edit property specifications
    """

    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_available = serializers.BooleanField(default=False)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    floor_number = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "id",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bathrooms",
            "parking_number",
            "floor_number",
            "user_unit_preference",
            "parking_available",
            "number_of_bedrooms",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.property_obj = self.instance.property if self.instance else None
        self.verified_fields = PropertyVerifiedDataFields.objects.filter(
            property=self.property_obj,
            field_name__in=PROPERTY_MONITOR_DETAILS_FIELD_MAPPING.keys(),
        )

    def validate(self, data):
        for verified_field in self.verified_fields:
            field_name = verified_field.field_name
            current_value = getattr(self.instance, field_name)
            new_value = data.get(field_name)

            logger.info(
                f"Current value of {field_name} for {self.instance} is {current_value}"
            )
            logger.info(f"New value of {field_name} for {self.instance} is {new_value}")

            # Compare values and raise error if trying to change verified field
            if current_value is not None or new_value is not None:
                if field_name == "parking_number":
                    current_value = [
                        parking_number.strip()
                        for parking_number in current_value.split(",")
                    ]
                    if sorted(current_value) != sorted(new_value):
                        raise serializers.ValidationError(
                            f"Cannot modify verified field: {field_name}"
                        )
                elif str(current_value) != str(new_value):
                    raise serializers.ValidationError(
                        f"Cannot modify verified field: {field_name}"
                    )
        if (
            data.get("property_type") == "Villa"
            and data.get("number_of_bedrooms", 0) <= 0
        ):
            raise serializers.ValidationError(
                f"Property type Villa must have greater than 0 bedrooms"
            )

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        for attr, value in validated_data.items():
            if getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)

        instance.save()
        return instance, changes


class EditManualResidentialPropertySpecificationSerializerV1(serializers.Serializer):
    """
    Serializer to edit residential property specifications manually
    """

    property_type = serializers.ChoiceField(choices=PropertyType.choices, required=True)
    total_area = serializers.FloatField(
        required=True, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_available = serializers.BooleanField(default=False)
    user_unit_preference = serializers.CharField(default="sqft")
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    floor_number = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "id",
            "property_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bathrooms",
            "parking_number",
            "floor_number",
            "user_unit_preference",
            "parking_available",
            "number_of_bedrooms",
        ]

    def validate(self, data):
        if (
            data.get("property_type") == "Villa"
            and data.get("number_of_bedrooms", 0) <= 0
        ):
            raise serializers.ValidationError(
                f"Property type Villa must have greater than 0 bedrooms"
            )

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        for attr, value in validated_data.items():
            if getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
        instance.save()
        return instance, changes


class EditManualCommercialPropertySpecificationSerializerV1(
    serializers.ModelSerializer
):
    """
    Serializer to edit commercial property specifications manually
    """

    property_type = serializers.ChoiceField(
        choices=PropertyType.choices, required=True, allow_null=False
    )
    building_type = serializers.ChoiceField(
        choices=PropertyBuildingType.choices, required=False, allow_null=True
    )
    total_area = serializers.FloatField(
        required=True, allow_null=False, validators=[validate_area("Total")]
    )
    carpet_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Carpet")]
    )
    balcony_area = serializers.FloatField(
        required=False, allow_null=True, validators=[validate_area("Balcony")]
    )
    user_unit_preference = serializers.CharField(default="sqft")
    floor_number = serializers.CharField(required=False, allow_null=True)
    total_floors = serializers.IntegerField(required=False, allow_null=True)
    number_of_bedrooms = serializers.IntegerField(min_value=0, required=False)
    number_of_bathrooms = serializers.IntegerField(min_value=0, required=False)
    parking_number = serializers.ListField(
        child=serializers.CharField(), required=False
    )
    parking_available = serializers.BooleanField(default=False)

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "property_type",
            "building_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "user_unit_preference",
            "floor_number",
            "total_floors",
            "number_of_bathrooms",
            "number_of_bedrooms",
            "parking_number",
            "parking_available",
        ]

    def validate(self, data):
        if data.get("property_type") not in COMMERCIAL_PROPERTY_TYPES:
            raise serializers.ValidationError("Invalid property type")

        data["parking_number"] = ",".join(data.get("parking_number", []))

        return data

    def update(self, instance, validated_data):
        changes = {}
        for attr, value in validated_data.items():
            if getattr(instance, attr) != value:
                changes[attr] = {
                    "old_value": getattr(instance, attr),
                    "new_value": value,
                }
                setattr(instance, attr, value)
        instance.save()
        return instance, changes


class UpdateUnitNumberSerializerV1(serializers.ModelSerializer):
    """
    Serializer to update unit number
    """

    unit_number = serializers.CharField(
        required=True, allow_null=False, allow_blank=False
    )

    class Meta:
        model = Property
        fields = ["unit_number"]

    def validate(self, data):
        user = self.context.get("user")
        role_obj = self.context.get("role_obj")

        if (
            self.instance.created_by != user
            or self.instance.created_by_role != role_obj
            or self.instance.property_monitor_address_id
        ):
            raise serializers.ValidationError(
                "You are not authorized to update unit number"
            )

        return data
