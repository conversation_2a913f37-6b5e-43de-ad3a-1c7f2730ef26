import datetime
import logging

from django.db.models import Q
from rest_framework import serializers
from rest_framework.serializers import ModelSerializer
from rezio.properties.models import (
    Property,
    AgentAssociatedProperty,
    PropertyFinancialDetails,
    PropertyUnitSections,
    PropertyRentalUnitHistory,
    PropertyVerifiedDataFields,
    PropertyCompletionState,
    PropertyFeatures,
    PropertyAvailabilityAndStatus,
    PropertyCoOwner,
)
from rezio.properties.serializers import (
    PropertySectionSerializer,
    PropertyRentalUnitHistorySerializer,
    PropertyViewFinancialDetailsSerializer,
    PropertyAvailabilityAndStatusSerializer,
    PropertySalesUnitHistorySerializer,
)
from rezio.properties.text_choices import (
    PropertyAvailabilityStatus,
    PropertyCompletionStateChoices,
    UserRequestActions,
)
from rezio.properties.utils import (
    get_s3_object,
    get_primary_number,
    get_primary_phone_code,
)
from rezio.user.constants import INVESTOR
from rezio.user.models import AgentProfile
from rezio.user.serializers import (
    BasicInvestorProfileInfoSerializer,
    BasicAgentProfileInfoSerializer,
    PropertyAssociatedAgentsSerializer,
)
from rezio.user.utils import get_investor_profile_object
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class WebPropertyPortfolioViewSerializer(ModelSerializer):
    """
    Property portfolio web view serializer
    """

    owner = serializers.SerializerMethodField()
    role = serializers.CharField(source="created_by_role.name")
    distressed_deal = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name")
    availability_status = serializers.IntegerField(
        source="propertyavailabilityandstatus.occupancy_status",
        read_only=True,
        allow_null=True,
    )
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    rent_data = serializers.SerializerMethodField()
    manual_added_details = serializers.SerializerMethodField()
    agents = serializers.SerializerMethodField()
    is_associated_agent = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()
    is_co_owner = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    property_specification_type = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    is_added_by_investor = serializers.SerializerMethodField()
    property_currency_code = serializers.CharField()
    preferred_currency_code = serializers.CharField()

    class Meta:
        model = Property
        fields = [
            "id",
            "owner",
            "community",
            "unit_number",
            "building_number",
            "property_type",
            "floor_number",
            "property_unit_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "property_currency_code",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "preferred_currency_code",
            "availability_status",
            "owner_intent",
            "asking_price",
            "currency_code",
            "rent_data",
            "unit_images",
            "distressed_deal",
            "manual_added_details",
            "user_unit_preference",
            "building_name",
            "is_co_owner",
            "agents",
            "is_associated_agent",
            "is_owner",
            "address",
            "property_specification_type",
            "is_archived",
            "furnished",
            "branded_building",
            "premium_view",
            "agent_type",
            "owner_verified",
            "is_added_by_investor",
            "default_image",
            "role",
        ]

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)

        price_details_data = dict()
        price_details_data["property_currency_asking_price"] = instance.asking_price
        price_details_data["property_currency_valuation"] = instance.valuation
        price_details_data["preferred_currency_asking_price"] = None
        price_details_data["preferred_currency_valuation"] = None

        rent_details = dict()
        total_rent = instance.annual_rent
        original_price = instance.original_price
        rent_details["property_currency_total_rent"] = total_rent
        rent_details["preferred_currency_total_rent"] = None
        if (original_price and original_price > 0) and (total_rent and total_rent > 0):
            rent_increase_percentage = (total_rent / original_price) * 100
            rent_details["rent_increase_percentage"] = rent_increase_percentage
            rent_details["is_upward"] = True
        else:
            rent_details["rent_increase_percentage"] = None
            rent_details["is_upward"] = None
        data["price_details"] = price_details_data
        data["rent_details"] = rent_details

        data["tentative_commission"] = None
        commission_data = dict()
        commission_data["preferred_currency_tentative_commission"] = None
        commission_data["property_currency_tentative_commission"] = None
        data["commission_data"] = commission_data
        data["can_archive"] = None
        return data

    def get_owner(self, instance):
        if instance.owner:
            return BasicInvestorProfileInfoSerializer(instance.owner).data

    def get_agents(self, instance):
        associated_agents = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            property=instance,
            is_request_expired=False,
        ).values_list("agent_profile_id", flat=True)
        agents = AgentProfile.objects.filter(id__in=associated_agents)
        return BasicAgentProfileInfoSerializer(agents, many=True).data

    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_currency_code(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.property_currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_rent_data(self, instance):
        occupancy_status = instance.propertyavailabilityandstatus.occupancy_status
        if occupancy_status and occupancy_status == PropertyAvailabilityStatus.RENTED:
            financial_details = PropertyFinancialDetails.objects.filter(
                property=instance
            ).first()
            if financial_details:
                total_rent = financial_details.annual_rent
                original_price = financial_details.original_price
                data = dict()
                data["total_rent"] = total_rent
                if (original_price and original_price > 0) and (
                    total_rent and total_rent > 0
                ):
                    rent_increase_percentage = (total_rent / original_price) * 100
                    data["rent_increase_percentage"] = rent_increase_percentage
                    data["is_upward"] = True
                    return data
                else:
                    data["rent_increase_percentage"] = None
                    data["is_upward"] = None
                    return data
        return None

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_manual_added_details(self, instance):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=instance)
        return [field.field_name for field in verified_fields]

    def get_is_associated_agent(self, instance):
        return None

    def get_is_owner(self, instance):
        return None

    def get_is_co_owner(self, instance):
        return None

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_property_specification_type(self, instance):
        property_specification_completion_state = (
            PropertyCompletionState.objects.filter(
                property=instance,
                state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            )
        )
        if property_specification_completion_state.exists():
            property_specification_completion_state = (
                property_specification_completion_state.first()
            )
            return property_specification_completion_state.data_source
        return None

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_is_added_by_investor(self, instance):
        if instance.created_by_role.name == INVESTOR:
            return True
        return False


class WebPropertyDetailSerializer(ModelSerializer):
    """
    Property detail web view serializer
    """

    address = serializers.SerializerMethodField()
    role = serializers.CharField(source="created_by_role.name")
    community = serializers.CharField(source="community.name")
    availability_status = serializers.IntegerField(
        source="propertyavailabilityandstatus.occupancy_status",
        read_only=True,
        allow_null=True,
    )
    building_name = serializers.SerializerMethodField()
    # community = serializers.CharField(source='community.name')
    parking_number = serializers.SerializerMethodField()
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    manual_added_details = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    distressed_deal = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField()
    co_owner_list = serializers.SerializerMethodField()
    agents = serializers.SerializerMethodField()
    property_specification_type = serializers.SerializerMethodField()
    lead_owner_percentage = serializers.DecimalField(
        required=False, max_digits=5, decimal_places=2, min_value=0, allow_null=True
    )
    is_associated_agent = serializers.SerializerMethodField()
    valuation_data_source = serializers.CharField()

    class Meta:
        model = Property
        fields = [
            "id",
            "role",
            "community",
            "unit_number",
            "building_number",
            "property_type",
            "floor_number",
            "owner_verified",
            "postal_code",
            "property_unit_type",
            "tenancy_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "parking_available",
            "number_of_covered_parking",
            "number_of_open_parking",
            "parking_number",
            "manual_added_details",
            "address",
            "unit_images",
            "distressed_deal",
            "furnished",
            "branded_building",
            "premium_view",
            "availability_status",
            "owner_intent",
            "dewa_id",
            "asking_price",
            "currency_code",
            "profile_photo",
            "co_owner_list",
            "user_unit_preference",
            "lead_owner_percentage",
            "agent_type",
            "agents",
            "property_specification_type",
            "is_associated_agent",
            "building_name",
            "valuation_data_source",
            "default_image",
        ]

    def get_parking_number(self, instance):
        return instance.get_parking_number()

    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_currency_code(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.property_currency_code
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_manual_added_details(self, obj):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=obj)
        return [field.field_name for field in verified_fields]

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_profile_photo(self, instance):
        if instance.owner:
            investor_profile = get_investor_profile_object(instance.owner.user)
            if investor_profile.profile_photo_key:
                return get_s3_object(investor_profile.profile_photo_key)
        return None

    def get_co_owner_list(self, instance):
        property_co_owners = PropertyCoOwner.objects.filter(property=instance)

        co_owner_list = []
        for each_co_owner in property_co_owners:
            if each_co_owner.co_owner:
                if each_co_owner.co_owner.profile_photo_key:
                    profile_photo = get_s3_object(
                        each_co_owner.co_owner.profile_photo_key
                    )
                else:
                    profile_photo = None

                # If the co-owner is a registered user
                co_owner_list.append(
                    {
                        "user_id": each_co_owner.co_owner.id,
                        "name": each_co_owner.co_owner.name,
                        "email": each_co_owner.co_owner.user.email,
                        "primary_phone_number": get_primary_number(
                            each_co_owner.co_owner.user.primary_phone_number
                        ),
                        "primary_phone_code": get_primary_phone_code(
                            each_co_owner.co_owner.user.primary_phone_number
                        ),
                        "ownership_percentage": each_co_owner.ownership_percentage,
                        "profile_photo": profile_photo,
                        "is_manually_added": False,
                    }
                )
            elif each_co_owner.unregistered_co_owner:  # If the co-owner is unregistered
                co_owner_list.append(
                    {
                        "user_id": each_co_owner.unregistered_co_owner.id,
                        "name": each_co_owner.unregistered_co_owner.name,
                        "email": each_co_owner.unregistered_co_owner.email,
                        "primary_phone_number": get_primary_number(
                            each_co_owner.unregistered_co_owner.phone_number
                        ),
                        "primary_phone_code": get_primary_phone_code(
                            each_co_owner.unregistered_co_owner.phone_number
                        ),
                        "ownership_percentage": each_co_owner.ownership_percentage,
                        "profile_photo": None,
                        "is_manually_added": True,
                    }
                )
        return co_owner_list

    def get_property_specification_type(self, instance):
        property_specification_completion_state = (
            PropertyCompletionState.objects.filter(
                property=instance,
                state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            )
        )
        if property_specification_completion_state.exists():
            property_specification_completion_state = (
                property_specification_completion_state.first()
            )
            return property_specification_completion_state.data_source
        return None

    def get_agents(self, instance):
        from rezio.user.serializers import BasicAgentProfileInfoSerializer

        associated_agents = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            property=instance,
            is_request_expired=False,
        ).values_list("agent_profile_id", flat=True)
        agents = AgentProfile.objects.filter(id__in=associated_agents)
        return PropertyAssociatedAgentsSerializer(
            agents, many=True, context={"property_id": instance.id}
        ).data

    def get_is_associated_agent(self, instance):
        return None


class WebPropertyFinancialSerializer(ModelSerializer):
    """
    Property financials web view serializer
    """

    availability_status = PropertyAvailabilityAndStatusSerializer(
        source="propertyavailabilityandstatus"
    )
    financial_details = PropertyViewFinancialDetailsSerializer(
        source="propertyfinancialdetails"
    )
    sales_history = PropertySalesUnitHistorySerializer(many=True)
    rental_history = PropertyRentalUnitHistorySerializer(many=True)

    class Meta:
        model = Property
        fields = [
            "id",
            "availability_status",
            "financial_details",
            "sales_history",
            "rental_history",
            "tenancy_type",
            "tenancy_start_date",
            "tenancy_end_date",
        ]

    def __init__(self, *args, **kwargs):
        # Pass the context to the nested serializers
        super().__init__(*args, **kwargs)
        nested_serializers = [
            self.fields["financial_details"],
        ]

        for serializer in nested_serializers:
            serializer.context.update(self.context)
