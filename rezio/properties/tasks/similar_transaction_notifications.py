from celery import shared_task


@shared_task(
    name="fetch_similar_transactions_and_notify",
    queue="default",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
)
def fetch_similar_transactions_and_notify():
    """
    Celery task to fetch similar transactions from Property Monitor API for properties
    and notify associated users about new transactions.
    """
    from rezio.properties.services.similar_transactions_notify_service import (
        SimilarTransactionNotifier,
    )

    notifier = SimilarTransactionNotifier()
    return notifier.execute()
