"""
Django signals for property scoring system.
Automatically recalculates property scores when related models are updated.
"""

import logging

from django.db import transaction
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

from rezio.properties.models import (
    Property,
    UserLevelPropertyData,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyFeatures,
    AgentAssociatedProperty,
)

logger = logging.getLogger(__name__)


def recalculate_user_level_property_score_async(user_level_property_data_id: int):
    """
    Asynchronously recalculate UserLevelPropertyData score using background tasks.
    Supports Celery, Django-RQ, or falls back to transaction.on_commit.
    """
    try:
        # Try Celery first
        try:
            # logger.info(
            #     f"Queued Celery task for UserLevelPropertyData {user_level_property_data_id}"
            # )
            # print(
            #     f"Queued Celery task for UserLevelPropertyData {user_level_property_data_id}"
            # )

            from rezio.properties.tasks import calculate_property_score_task

            transaction.on_commit(
                lambda: calculate_property_score_task.delay(user_level_property_data_id)
            )
            logger.info(
                f"Queued Celery task for UserLevelPropertyData {user_level_property_data_id}"
            )
            return
        except ImportError:
            pass

        # Fallback to direct execution
        # print(
        #     f"Scheduled direct execution for UserLevelPropertyData {user_level_property_data_id}"
        # )

        from rezio.properties.services.dynamic_property_scoring_service import (
            calculate_and_update_user_level_property_score_dynamic,
        )

        transaction.on_commit(
            lambda: calculate_and_update_user_level_property_score_dynamic(
                user_level_property_data_id
            )
        )
        logger.info(
            f"Scheduled direct execution for UserLevelPropertyData {user_level_property_data_id}"
        )

    except Exception as e:
        logger.error(
            f"Error scheduling property score recalculation for UserLevelPropertyData {user_level_property_data_id}: {str(e)}"
        )


@receiver(post_save, sender=UserLevelPropertyData)
def update_score_on_user_level_data_change(sender, instance, created, **kwargs):
    """
    Recalculate property score when UserLevelPropertyData is created or updated.
    """
    try:
        user_level_property_data_id = instance.id
        logger.info(
            f"UserLevelPropertyData {'created' if created else 'updated'} with ID {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(f"Error in UserLevelPropertyData signal handler: {str(e)}")


@receiver(post_delete, sender=UserLevelPropertyData)
def update_score_on_user_level_data_delete(sender, instance, **kwargs):
    """
    Recalculate property score when UserLevelPropertyData is deleted.
    Note: Since the instance is being deleted, we can't recalculate its score.
    This handler is kept for logging purposes.
    """
    try:
        user_level_property_data_id = instance.id
        logger.info(
            f"UserLevelPropertyData deleted with ID {user_level_property_data_id}"
        )
        # Note: We can't recalculate score for a deleted instance
    except Exception as e:
        logger.error(f"Error in UserLevelPropertyData delete signal handler: {str(e)}")


@receiver(post_save, sender=UserLevelPropertyAvailabilityAndStatus)
def update_score_on_availability_status_change(sender, instance, created, **kwargs):
    """
    Recalculate property score when UserLevelPropertyAvailabilityAndStatus is created or updated.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyAvailabilityAndStatus {'created' if created else 'updated'} for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(
            f"Error in UserLevelPropertyAvailabilityAndStatus signal handler: {str(e)}"
        )


@receiver(post_delete, sender=UserLevelPropertyAvailabilityAndStatus)
def update_score_on_availability_status_delete(sender, instance, **kwargs):
    """
    Recalculate property score when UserLevelPropertyAvailabilityAndStatus is deleted.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyAvailabilityAndStatus deleted for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(
            f"Error in UserLevelPropertyAvailabilityAndStatus delete signal handler: {str(e)}"
        )


@receiver(post_save, sender=UserLevelPropertyFinancialDetails)
def update_score_on_financial_details_change(sender, instance, created, **kwargs):
    """
    Recalculate property score when UserLevelPropertyFinancialDetails is created or updated.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyFinancialDetails {'created' if created else 'updated'} for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(
            f"Error in UserLevelPropertyFinancialDetails signal handler: {str(e)}"
        )


@receiver(post_delete, sender=UserLevelPropertyFinancialDetails)
def update_score_on_financial_details_delete(sender, instance, **kwargs):
    """
    Recalculate property score when UserLevelPropertyFinancialDetails is deleted.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyFinancialDetails deleted for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(
            f"Error in UserLevelPropertyFinancialDetails delete signal handler: {str(e)}"
        )


@receiver(post_save, sender=UserLevelPropertyFeatures)
def update_score_on_features_change(sender, instance, created, **kwargs):
    """
    Recalculate property score when UserLevelPropertyFeatures is created or updated.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyFeatures {'created' if created else 'updated'} for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(f"Error in UserLevelPropertyFeatures signal handler: {str(e)}")


@receiver(post_delete, sender=UserLevelPropertyFeatures)
def update_score_on_features_delete(sender, instance, **kwargs):
    """
    Recalculate property score when UserLevelPropertyFeatures is deleted.
    """
    try:
        user_level_property_data_id = instance.property_level_data.id
        logger.info(
            f"UserLevelPropertyFeatures deleted for UserLevelPropertyData {user_level_property_data_id}"
        )
        recalculate_user_level_property_score_async(user_level_property_data_id)
    except Exception as e:
        logger.error(
            f"Error in UserLevelPropertyFeatures delete signal handler: {str(e)}"
        )


def recalculate_scores_for_property_related_models(property_obj):
    """
    Helper function to recalculate scores for all UserLevelPropertyData instances related to a property.
    """
    try:
        user_level_data_objects = UserLevelPropertyData.objects.filter(
            property=property_obj
        )
        for user_level_data in user_level_data_objects:
            recalculate_user_level_property_score_async(user_level_data.id)
        logger.info(
            f"Scheduled score recalculation for {user_level_data_objects.count()} UserLevelPropertyData instances for property {property_obj.id}"
        )
    except Exception as e:
        logger.error(
            f"Error scheduling score recalculation for property {property_obj.id}: {str(e)}"
        )


# @receiver(post_save, sender=PropertyFloorPlan)
# def update_score_on_floor_plan_change(sender, instance, created, **kwargs):
#     """
#     Recalculate property score when PropertyFloorPlan is created or updated.
#     """
#     try:
#         property_obj = instance.property
#         logger.info(
#             f"PropertyFloorPlan {'created' if created else 'updated'} for property {property_obj.id}"
#         )
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in PropertyFloorPlan signal handler: {str(e)}")


# @receiver(post_delete, sender=PropertyFloorPlan)
# def update_score_on_floor_plan_delete(sender, instance, **kwargs):
#     """
#     Recalculate property score when PropertyFloorPlan is deleted.
#     """
#     try:
#         property_obj = instance.property
#         logger.info(f"PropertyFloorPlan deleted for property {property_obj.id}")
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in PropertyFloorPlan delete signal handler: {str(e)}")


# @receiver(post_save, sender=PropertyUnitSections)
# def update_score_on_unit_sections_change(sender, instance, created, **kwargs):
#     """
#     Recalculate property score when PropertyUnitSections is created or updated.
#     """
#     try:
#         property_obj = instance.property
#         logger.info(
#             f"PropertyUnitSections {'created' if created else 'updated'} for property {property_obj.id}"
#         )
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in PropertyUnitSections signal handler: {str(e)}")


# @receiver(post_delete, sender=PropertyUnitSections)
# def update_score_on_unit_sections_delete(sender, instance, **kwargs):
#     """
#     Recalculate property score when PropertyUnitSections is deleted.
#     """
#     try:
#         property_obj = instance.property
#         logger.info(f"PropertyUnitSections deleted for property {property_obj.id}")
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in PropertyUnitSections delete signal handler: {str(e)}")


# @receiver(post_save, sender=UnitSectionMedia)
# def update_score_on_unit_section_media_change(sender, instance, created, **kwargs):
#     """
#     Recalculate property score when UnitSectionMedia is created or updated.
#     """
#     try:
#         property_obj = instance.unit_section.property
#         logger.info(
#             f"UnitSectionMedia {'created' if created else 'updated'} for property {property_obj.id}"
#         )
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in UnitSectionMedia signal handler: {str(e)}")
#
#
# @receiver(post_delete, sender=UnitSectionMedia)
# def update_score_on_unit_section_media_delete(sender, instance, **kwargs):
#     """
#     Recalculate property score when UnitSectionMedia is deleted.
#     """
#     try:
#         property_obj = instance.unit_section.property
#         logger.info(f"UnitSectionMedia deleted for property {property_obj.id}")
#         recalculate_scores_for_property_related_models(property_obj)
#     except Exception as e:
#         logger.error(f"Error in UnitSectionMedia delete signal handler: {str(e)}")


@receiver(post_save, sender=AgentAssociatedProperty)
def update_score_on_agent_associated_property_change(
    sender, instance, created, **kwargs
):
    """
    Recalculate property score when AgentAssociatedProperty is created or updated.
    """
    try:
        property_obj = instance.property
        logger.info(
            f"AgentAssociatedProperty {'created' if created else 'updated'} for property {property_obj.id}"
        )
        recalculate_scores_for_property_related_models(property_obj)
    except Exception as e:
        logger.error(f"Error in AgentAssociatedProperty signal handler: {str(e)}")


@receiver(post_delete, sender=AgentAssociatedProperty)
def update_score_on_agent_associated_property_delete(sender, instance, **kwargs):
    """
    Recalculate property score when AgentAssociatedProperty is deleted.
    """
    try:
        property_obj = instance.property
        logger.info(f"AgentAssociatedProperty deleted for property {property_obj.id}")
        recalculate_scores_for_property_related_models(property_obj)
    except Exception as e:
        logger.error(
            f"Error in AgentAssociatedProperty delete signal handler: {str(e)}"
        )


@receiver(post_save, sender=Property)
def update_score_on_property_change(sender, instance, created, **kwargs):
    """
    Recalculate property score when Property owner or owner_verified fields are updated.
    """
    try:
        # Only trigger if owner or owner_verified fields might have changed
        if not created:  # Only for updates, not new properties
            logger.info(f"Property updated for property {instance.id}")
            recalculate_scores_for_property_related_models(instance)
    except Exception as e:
        logger.error(f"Error in Property signal handler: {str(e)}")
