"""
Property scoring weight constants for different property categories.
These weights are used to calculate property scores based on various attributes.
"""

# Weight mappings for residential properties
RESIDENTIAL_PROPERTY_SCORE_WEIGHTS = {
    # UserLevelPropertyData fields
    "user_level_data.property_type": 10,
    "user_level_data.total_area": 10,
    "user_level_data.carpet_area": 8,
    "user_level_data.balcony_area": 5,
    "user_level_data.number_of_bedrooms": 15,
    "user_level_data.number_of_bathrooms": 12,
    "user_level_data.number_of_common_bathrooms": 5,
    "user_level_data.number_of_attached_bathrooms": 8,
    "user_level_data.number_of_powder_rooms": 3,
    "user_level_data.parking_available": 8,
    "user_level_data.number_of_covered_parking": 6,
    "user_level_data.number_of_open_parking": 4,
    "user_level_data.floor_number": 5,
    "user_level_data.total_floors": 3,
    "user_level_data.building_type": 7,
    "user_level_data.number_of_master_bedrooms": 10,
    "user_level_data.number_of_other_bedrooms": 8,
    "user_level_data.number_of_maid_rooms": 4,
    "user_level_data.number_of_study_rooms": 6,
    
    # UserLevelPropertyAvailabilityAndStatus fields
    "availability_status.status": 12,
    "availability_status.occupancy_status": 10,
    "availability_status.handover_date": 8,
    "availability_status.rent_available_start_date": 6,
    "availability_status.enable_payment_plan": 5,
    "availability_status.during_construction": 4,
    "availability_status.on_handover": 4,
    "availability_status.enable_post_handover": 3,
    "availability_status.ownership_proof_key": 7,
    
    # UserLevelPropertyFinancialDetails fields
    "financial_details.original_price": 15,
    "financial_details.asking_price": 18,
    "financial_details.valuation": 12,
    "financial_details.annual_rent": 14,
    "financial_details.annual_service_charges": 6,
    "financial_details.security_deposit": 8,
    "financial_details.preferred_payment_frequency": 5,
    "financial_details.expected_rent": 12,
    "financial_details.expected_security_deposit": 6,
    
    # UserLevelPropertyFeatures fields
    "features.branded_building": 8,
    "features.furnished": 12,
    "features.premium_view": 10,
    "features.is_restroom_available": 6,
    "features.no_of_shared_restrooms": 4,
    "features.no_of_private_restrooms": 8,
    "features.is_parking_available": 10,
    "features.public_parking": 5,
    "features.no_of_reserved_parking": 8,
    "features.is_lift_available": 7,
    "features.common_lift": 4,
    "features.no_of_personal_lift": 9,
    "features.security_available": 8,
    "features.water_storage_available": 6,
    "features.property_on_main_road": 7,
    "features.corner_property": 6,
    "features.tags": 5,
    
    # PropertyFloorPlan fields
    "floor_plan.media_file_key": 6,
    
    # PropertyPaymentPlan fields
    "payment_plan.media_file_key": 5,
    
    # PropertyUnitSections fields
    "unit_sections.count": 8,
    "unit_sections.section_type": 6,
    "unit_sections.attached_balcony": 4,
}

# Weight mappings for commercial properties
COMMERCIAL_PROPERTY_SCORE_WEIGHTS = {
    # UserLevelPropertyData fields
    "user_level_data.property_type": 12,
    "user_level_data.total_area": 15,
    "user_level_data.carpet_area": 12,
    "user_level_data.balcony_area": 3,
    "user_level_data.number_of_bedrooms": 5,  # Less relevant for commercial
    "user_level_data.number_of_bathrooms": 8,
    "user_level_data.number_of_common_bathrooms": 6,
    "user_level_data.number_of_attached_bathrooms": 4,
    "user_level_data.number_of_powder_rooms": 5,
    "user_level_data.parking_available": 12,
    "user_level_data.number_of_covered_parking": 10,
    "user_level_data.number_of_open_parking": 8,
    "user_level_data.floor_number": 8,
    "user_level_data.total_floors": 6,
    "user_level_data.building_type": 10,
    "user_level_data.number_of_master_bedrooms": 3,  # Less relevant for commercial
    "user_level_data.number_of_other_bedrooms": 2,
    "user_level_data.number_of_maid_rooms": 2,
    "user_level_data.number_of_study_rooms": 4,
    
    # UserLevelPropertyAvailabilityAndStatus fields
    "availability_status.status": 15,
    "availability_status.occupancy_status": 12,
    "availability_status.handover_date": 10,
    "availability_status.rent_available_start_date": 8,
    "availability_status.enable_payment_plan": 7,
    "availability_status.during_construction": 6,
    "availability_status.on_handover": 6,
    "availability_status.enable_post_handover": 5,
    "availability_status.ownership_proof_key": 8,
    
    # UserLevelPropertyFinancialDetails fields
    "financial_details.original_price": 18,
    "financial_details.asking_price": 20,
    "financial_details.valuation": 15,
    "financial_details.annual_rent": 16,
    "financial_details.annual_service_charges": 8,
    "financial_details.security_deposit": 10,
    "financial_details.preferred_payment_frequency": 6,
    "financial_details.expected_rent": 14,
    "financial_details.expected_security_deposit": 8,
    
    # UserLevelPropertyFeatures fields
    "features.branded_building": 12,
    "features.furnished": 8,  # Less relevant for commercial
    "features.premium_view": 8,
    "features.is_restroom_available": 10,
    "features.no_of_shared_restrooms": 8,
    "features.no_of_private_restrooms": 6,
    "features.is_parking_available": 15,
    "features.public_parking": 8,
    "features.no_of_reserved_parking": 12,
    "features.is_lift_available": 12,
    "features.common_lift": 8,
    "features.no_of_personal_lift": 6,
    "features.security_available": 12,
    "features.water_storage_available": 8,
    "features.property_on_main_road": 10,
    "features.corner_property": 8,
    "features.tags": 6,
    
    # PropertyFloorPlan fields
    "floor_plan.media_file_key": 8,
    
    # PropertyPaymentPlan fields
    "payment_plan.media_file_key": 7,
    
    # PropertyUnitSections fields
    "unit_sections.count": 10,
    "unit_sections.section_type": 8,
    "unit_sections.attached_balcony": 3,
}

# Maximum possible score for normalization
MAX_RESIDENTIAL_SCORE = sum(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.values())
MAX_COMMERCIAL_SCORE = sum(COMMERCIAL_PROPERTY_SCORE_WEIGHTS.values())

def get_weight_mapping(property_category):
    """
    Get the appropriate weight mapping based on property category.
    
    Args:
        property_category (int): Property category (0 for Residential, 1 for Commercial)
        
    Returns:
        dict: Weight mapping for the given property category
    """
    from rezio.properties.text_choices import PropertyCategory
    
    if property_category == PropertyCategory.RESIDENTIAL:
        return RESIDENTIAL_PROPERTY_SCORE_WEIGHTS
    elif property_category == PropertyCategory.COMMERCIAL:
        return COMMERCIAL_PROPERTY_SCORE_WEIGHTS
    else:
        # Default to residential if category is unknown
        return RESIDENTIAL_PROPERTY_SCORE_WEIGHTS

def get_max_score(property_category):
    """
    Get the maximum possible score for a property category.
    
    Args:
        property_category (int): Property category (0 for Residential, 1 for Commercial)
        
    Returns:
        int: Maximum possible score for the given property category
    """
    from rezio.properties.text_choices import PropertyCategory
    
    if property_category == PropertyCategory.RESIDENTIAL:
        return MAX_RESIDENTIAL_SCORE
    elif property_category == PropertyCategory.COMMERCIAL:
        return MAX_COMMERCIAL_SCORE
    else:
        return MAX_RESIDENTIAL_SCORE
