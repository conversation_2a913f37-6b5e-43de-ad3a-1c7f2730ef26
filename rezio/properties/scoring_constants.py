"""
Property scoring weight constants for different property categories.
These weights are used to calculate property scores based on various attributes.
Format: "ModelName.field_name" for easy dynamic access.
"""

# Weight mappings for residential properties using ModelName.field_name format
RESIDENTIAL_PROPERTY_SCORE_WEIGHTS = {
    # Property location fields
    "Property.country": 2,
    "Property.state": 2,
    "Property.city": 3,
    "Property.community": 5,
    # UserLevelPropertyData fields
    "UserLevelPropertyData.property_type": 13,
    "UserLevelPropertyData.total_area": 13,
    "UserLevelPropertyData.carpet_area": 4,
    "UserLevelPropertyData.number_of_bedrooms": 5,
    "UserLevelPropertyData.floor_number": 3,
    # UserLevelPropertyAvailabilityAndStatus fields
    "UserLevelPropertyAvailabilityAndStatus.status": 3,
    "UserLevelPropertyAvailabilityAndStatus.occupancy_status": 4,
    "UserLevelPropertyAvailabilityAndStatus.handover_date": 1,
    # UserLevelPropertyFinancialDetails fields
    "UserLevelPropertyFinancialDetails.asking_price": 8,  # Used when owner_intent is AVAILABLE_FOR_SALE
    "UserLevelPropertyFinancialDetails.price_negotiable": 2,
    "UserLevelPropertyFinancialDetails.expected_rent": 8,  # Used when owner_intent is AVAILABLE_FOR_RENT
    # UserLevelPropertyFeatures fields
    "UserLevelPropertyFeatures.branded_building": 2,
    "UserLevelPropertyFeatures.furnished": 3,
    "UserLevelPropertyFeatures.premium_view": 3,
    # PropertyFloorPlan fields
    "PropertyFloorPlan.media_file_key": 5,
    # Conditional weights based on business logic
    "UnitSectionMedia.more_than_3_images": 5,  # If more than 3 unit images
    "Property.owner_details_present": 7,  # If owner and owner_verified are present
    "AgentAssociatedProperty.agent_mobile_present": 5,  # If agent mobile is present
    "AgentAssociatedProperty.agent_name_present": 2,  # If agent name is present
    "UserLevelPropertyData.meta_agent_mobile_present": 5,  # If agent mobile in meta
    "UserLevelPropertyData.meta_agent_name_present": 2,  # If agent name in meta
}

# Weight mappings for commercial properties using ModelName.field_name format
COMMERCIAL_PROPERTY_SCORE_WEIGHTS = {
    # Property location fields
    "Property.country": 10,
    "Property.state": 8,
    "Property.city": 12,
    "Property.community": 15,
    # UserLevelPropertyData fields
    "UserLevelPropertyData.property_type": 12,
    "UserLevelPropertyData.total_area": 15,
    "UserLevelPropertyData.carpet_area": 12,
    "UserLevelPropertyData.balcony_area": 3,
    "UserLevelPropertyData.number_of_bedrooms": 5,  # Less relevant for commercial
    "UserLevelPropertyData.number_of_bathrooms": 8,
    "UserLevelPropertyData.number_of_common_bathrooms": 6,
    "UserLevelPropertyData.number_of_attached_bathrooms": 4,
    "UserLevelPropertyData.number_of_powder_rooms": 5,
    "UserLevelPropertyData.parking_available": 12,
    "UserLevelPropertyData.number_of_covered_parking": 10,
    "UserLevelPropertyData.number_of_open_parking": 8,
    "UserLevelPropertyData.floor_number": 8,
    "UserLevelPropertyData.total_floors": 6,
    "UserLevelPropertyData.building_type": 10,
    "UserLevelPropertyData.number_of_master_bedrooms": 3,  # Less relevant for commercial
    "UserLevelPropertyData.number_of_other_bedrooms": 2,
    "UserLevelPropertyData.number_of_maid_rooms": 2,
    "UserLevelPropertyData.number_of_study_rooms": 4,
    # UserLevelPropertyAvailabilityAndStatus fields
    "UserLevelPropertyAvailabilityAndStatus.status": 15,
    "UserLevelPropertyAvailabilityAndStatus.occupancy_status": 12,
    "UserLevelPropertyAvailabilityAndStatus.handover_date": 10,
    "UserLevelPropertyAvailabilityAndStatus.rent_available_start_date": 8,
    "UserLevelPropertyAvailabilityAndStatus.enable_payment_plan": 7,
    "UserLevelPropertyAvailabilityAndStatus.during_construction": 6,
    "UserLevelPropertyAvailabilityAndStatus.on_handover": 6,
    "UserLevelPropertyAvailabilityAndStatus.enable_post_handover": 5,
    "UserLevelPropertyAvailabilityAndStatus.ownership_proof_key": 8,
    # UserLevelPropertyFinancialDetails fields
    "UserLevelPropertyFinancialDetails.original_price": 18,
    "UserLevelPropertyFinancialDetails.asking_price": 20,  # Used when owner_intent is AVAILABLE_FOR_SALE
    "UserLevelPropertyFinancialDetails.valuation": 15,
    "UserLevelPropertyFinancialDetails.annual_rent": 16,
    "UserLevelPropertyFinancialDetails.annual_service_charges": 8,
    "UserLevelPropertyFinancialDetails.security_deposit": 10,
    "UserLevelPropertyFinancialDetails.preferred_payment_frequency": 6,
    "UserLevelPropertyFinancialDetails.expected_rent": 18,  # Used when owner_intent is AVAILABLE_FOR_RENT
    "UserLevelPropertyFinancialDetails.expected_security_deposit": 8,
    # UserLevelPropertyFeatures fields
    "UserLevelPropertyFeatures.branded_building": 12,
    "UserLevelPropertyFeatures.furnished": 8,  # Less relevant for commercial
    "UserLevelPropertyFeatures.premium_view": 8,
    "UserLevelPropertyFeatures.is_restroom_available": 10,
    "UserLevelPropertyFeatures.no_of_shared_restrooms": 8,
    "UserLevelPropertyFeatures.no_of_private_restrooms": 6,
    "UserLevelPropertyFeatures.is_parking_available": 15,
    "UserLevelPropertyFeatures.public_parking": 8,
    "UserLevelPropertyFeatures.no_of_reserved_parking": 12,
    "UserLevelPropertyFeatures.is_lift_available": 12,
    "UserLevelPropertyFeatures.common_lift": 8,
    "UserLevelPropertyFeatures.no_of_personal_lift": 6,
    "UserLevelPropertyFeatures.security_available": 12,
    "UserLevelPropertyFeatures.water_storage_available": 8,
    "UserLevelPropertyFeatures.property_on_main_road": 10,
    "UserLevelPropertyFeatures.corner_property": 8,
    "UserLevelPropertyFeatures.tags": 6,
    # PropertyFloorPlan fields
    "PropertyFloorPlan.media_file_key": 8,
    # PropertyPaymentPlan fields
    "PropertyPaymentPlan.media_file_key": 7,
    # PropertyUnitSections fields
    "PropertyUnitSections.count": 10,
    "PropertyUnitSections.section_type": 8,
    "PropertyUnitSections.attached_balcony": 3,
    # Conditional weights based on business logic
    "UnitSectionMedia.more_than_3_images": 6,  # If more than 3 unit images (higher for commercial)
    "Property.owner_details_present": 8,  # If owner and owner_verified are present (higher for commercial)
    "AgentAssociatedProperty.agent_mobile_present": 6,  # If agent mobile is present (higher for commercial)
    "AgentAssociatedProperty.agent_name_present": 3,  # If agent name is present (higher for commercial)
    "UserLevelPropertyData.meta_agent_mobile_present": 6,  # If agent mobile in meta (higher for commercial)
    "UserLevelPropertyData.meta_agent_name_present": 3,  # If agent name in meta (higher for commercial)
}

# Maximum possible score for normalization
MAX_RESIDENTIAL_SCORE = 90  # sum(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.values())
MAX_COMMERCIAL_SCORE = sum(COMMERCIAL_PROPERTY_SCORE_WEIGHTS.values())


def get_weight_mapping(property_category):
    """
    Get the appropriate weight mapping based on property category.

    Args:
        property_category (int): Property category (0 for Residential, 1 for Commercial)

    Returns:
        dict: Weight mapping for the given property category
    """
    from rezio.properties.text_choices import PropertyCategory

    if property_category == PropertyCategory.RESIDENTIAL:
        return RESIDENTIAL_PROPERTY_SCORE_WEIGHTS
    elif property_category == PropertyCategory.COMMERCIAL:
        return COMMERCIAL_PROPERTY_SCORE_WEIGHTS
    else:
        # Default to residential if category is unknown
        return RESIDENTIAL_PROPERTY_SCORE_WEIGHTS


def get_max_score(property_category):
    """
    Get the maximum possible score for a property category.

    Args:
        property_category (int): Property category (0 for Residential, 1 for Commercial)

    Returns:
        int: Maximum possible score for the given property category
    """
    from rezio.properties.text_choices import PropertyCategory

    if property_category == PropertyCategory.RESIDENTIAL:
        return MAX_RESIDENTIAL_SCORE
    elif property_category == PropertyCategory.COMMERCIAL:
        return MAX_COMMERCIAL_SCORE
    else:
        return MAX_RESIDENTIAL_SCORE
