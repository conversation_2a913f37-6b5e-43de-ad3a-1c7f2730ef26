"""
Django management command to calculate property scores.
Usage: python manage.py calculate_property_scores [--property-ids 1,2,3] [--batch-size 100]
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone

from rezio.properties.models import Property
from rezio.properties.services.property_scoring_service import PropertyScoringService


class Command(BaseCommand):
    help = 'Calculate property scores for all properties or specific property IDs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--property-ids',
            type=str,
            help='Comma-separated list of property IDs to calculate scores for (e.g., "1,2,3")',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of properties to process in each batch (default: 100)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without actually updating the database',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        start_time = timezone.now()
        
        # Parse property IDs if provided
        property_ids = None
        if options['property_ids']:
            try:
                property_ids = [int(pid.strip()) for pid in options['property_ids'].split(',')]
                self.stdout.write(f"Processing specific property IDs: {property_ids}")
            except ValueError:
                raise CommandError("Invalid property IDs format. Use comma-separated integers (e.g., '1,2,3')")
        
        # Get properties to process
        if property_ids:
            properties = Property.objects.filter(id__in=property_ids)
            missing_ids = set(property_ids) - set(properties.values_list('id', flat=True))
            if missing_ids:
                self.stdout.write(
                    self.style.WARNING(f"Warning: Properties with IDs {missing_ids} not found")
                )
        else:
            properties = Property.objects.all()
        
        total_properties = properties.count()
        
        if total_properties == 0:
            self.stdout.write(self.style.WARNING("No properties found to process"))
            return
        
        self.stdout.write(f"Found {total_properties} properties to process")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be saved"))
        
        # Initialize scoring service
        scoring_service = PropertyScoringService()
        
        # Process properties in batches
        batch_size = options['batch_size']
        processed_count = 0
        updated_count = 0
        failed_count = 0
        
        for i in range(0, total_properties, batch_size):
            batch_properties = properties[i:i + batch_size]
            
            self.stdout.write(f"Processing batch {i//batch_size + 1} ({len(batch_properties)} properties)...")
            
            for property_obj in batch_properties:
                try:
                    if options['dry_run']:
                        # Just calculate the score without saving
                        score = scoring_service._calculate_score_for_property(property_obj)
                        if options['verbose']:
                            self.stdout.write(f"  Property {property_obj.id}: Score would be {score:.2f}")
                        updated_count += 1
                    else:
                        # Calculate and save the score
                        with transaction.atomic():
                            new_score = scoring_service._calculate_score_for_property(property_obj)
                            property_obj.property_score = new_score
                            property_obj.save(update_fields=['property_score'])
                            
                            if options['verbose']:
                                self.stdout.write(f"  Property {property_obj.id}: Updated score to {new_score:.2f}")
                            
                            updated_count += 1
                    
                    processed_count += 1
                    
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"  Failed to process property {property_obj.id}: {str(e)}")
                    )
                    
                    if options['verbose']:
                        import traceback
                        self.stdout.write(traceback.format_exc())
            
            # Progress update
            progress = (processed_count / total_properties) * 100
            self.stdout.write(f"Progress: {processed_count}/{total_properties} ({progress:.1f}%)")
        
        # Final summary
        end_time = timezone.now()
        duration = end_time - start_time
        
        self.stdout.write("\n" + "="*50)
        self.stdout.write("SUMMARY")
        self.stdout.write("="*50)
        self.stdout.write(f"Total properties processed: {processed_count}")
        self.stdout.write(f"Successfully updated: {updated_count}")
        self.stdout.write(f"Failed: {failed_count}")
        self.stdout.write(f"Duration: {duration}")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN COMPLETED - No changes were saved"))
        else:
            self.stdout.write(self.style.SUCCESS("Property score calculation completed successfully"))
        
        if failed_count > 0:
            self.stdout.write(
                self.style.WARNING(f"Warning: {failed_count} properties failed to process")
            )
