"""
Django management command to calculate property scores using dynamic scoring service.
Usage: python manage.py calculate_property_scores [--user-level-property-data-ids 1,2,3] [--batch-size 100]
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone

from rezio.properties.models import UserLevelPropertyData
from rezio.properties.services.dynamic_property_scoring_service import DynamicPropertyScoringService


class Command(BaseCommand):
    help = 'Calculate property scores for all UserLevelPropertyData or specific UserLevelPropertyData IDs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-level-property-data-ids',
            type=str,
            help='Comma-separated list of UserLevelPropertyData IDs to calculate scores for (e.g., "1,2,3")',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of UserLevelPropertyData objects to process in each batch (default: 100)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without actually updating the database',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        start_time = timezone.now()
        
        # Parse UserLevelPropertyData IDs if provided
        user_level_property_data_ids = None
        if options['user_level_property_data_ids']:
            try:
                user_level_property_data_ids = [int(pid.strip()) for pid in options['user_level_property_data_ids'].split(',')]
                self.stdout.write(f"Processing specific UserLevelPropertyData IDs: {user_level_property_data_ids}")
            except ValueError:
                raise CommandError("Invalid UserLevelPropertyData IDs format. Use comma-separated integers (e.g., '1,2,3')")
        
        # Get UserLevelPropertyData objects to process
        if user_level_property_data_ids:
            user_level_data_objects = UserLevelPropertyData.objects.filter(id__in=user_level_property_data_ids)
            missing_ids = set(user_level_property_data_ids) - set(user_level_data_objects.values_list('id', flat=True))
            if missing_ids:
                self.stdout.write(
                    self.style.WARNING(f"Warning: UserLevelPropertyData with IDs {missing_ids} not found")
                )
        else:
            user_level_data_objects = UserLevelPropertyData.objects.all()
        
        total_objects = user_level_data_objects.count()
        
        if total_objects == 0:
            self.stdout.write(self.style.WARNING("No UserLevelPropertyData objects found to process"))
            return
        
        self.stdout.write(f"Found {total_objects} UserLevelPropertyData objects to process")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No changes will be saved"))
        
        # Initialize dynamic scoring service
        scoring_service = DynamicPropertyScoringService()
        
        # Process UserLevelPropertyData objects in batches
        batch_size = options['batch_size']
        processed_count = 0
        updated_count = 0
        failed_count = 0
        
        for i in range(0, total_objects, batch_size):
            batch_objects = user_level_data_objects[i:i + batch_size]
            
            self.stdout.write(f"Processing batch {i//batch_size + 1} ({len(batch_objects)} UserLevelPropertyData objects)...")
            
            for user_level_data in batch_objects:
                try:
                    if options['dry_run']:
                        # Just calculate the score without saving
                        score = scoring_service._calculate_score_for_user_level_data(user_level_data)
                        if options['verbose']:
                            self.stdout.write(f"  UserLevelPropertyData {user_level_data.id}: Score would be {score:.2f}")
                        updated_count += 1
                    else:
                        # Calculate and save the score
                        with transaction.atomic():
                            new_score = scoring_service._calculate_score_for_user_level_data(user_level_data)
                            user_level_data.property_score = new_score
                            user_level_data.save(update_fields=['property_score'])
                            
                            if options['verbose']:
                                self.stdout.write(f"  UserLevelPropertyData {user_level_data.id}: Updated score to {new_score:.2f}")
                            
                            updated_count += 1
                    
                    processed_count += 1
                    
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"  Failed to process UserLevelPropertyData {user_level_data.id}: {str(e)}")
                    )
                    
                    if options['verbose']:
                        import traceback
                        self.stdout.write(traceback.format_exc())
            
            # Progress update
            progress = (processed_count / total_objects) * 100
            self.stdout.write(f"Progress: {processed_count}/{total_objects} ({progress:.1f}%)")
        
        # Final summary
        end_time = timezone.now()
        duration = end_time - start_time
        
        self.stdout.write("\n" + "="*50)
        self.stdout.write("SUMMARY")
        self.stdout.write("="*50)
        self.stdout.write(f"Total UserLevelPropertyData objects processed: {processed_count}")
        self.stdout.write(f"Successfully updated: {updated_count}")
        self.stdout.write(f"Failed: {failed_count}")
        self.stdout.write(f"Duration: {duration}")
        
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN COMPLETED - No changes were saved"))
        else:
            self.stdout.write(self.style.SUCCESS("Property score calculation completed successfully"))
        
        if failed_count > 0:
            self.stdout.write(
                self.style.WARNING(f"Warning: {failed_count} UserLevelPropertyData objects failed to process")
            )
