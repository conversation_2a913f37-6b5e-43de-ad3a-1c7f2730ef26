<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta property="og:title" content="{{ title }}">
    <meta property="og:description"
          content="{{ address }} | {{ currency_code }} {{ asking_price }} {% if is_rent %} /Yr {% endif %} |
          {{ total_area }} {{ property_unit_type }}
          {% if number_of_bedrooms and number_of_bedrooms != 0 %} | {{ number_of_bedrooms }} Bed {% endif %}
          {% if is_studio %} | Studio {% endif %}
          {% if number_of_bathrooms and number_of_bathrooms != 0 %} | {{ number_of_bathrooms }} Bath {% endif %}">
    <meta property="og:image"
          content="{{ project_env_url }}/properties/compressed-image/?image_url={{ image_url }}">
    <meta property="og:image:secure_url"
          content="{{ project_env_url }}/properties/compressed-image/?image_url={{ image_url }}">
    <meta property="og:image:type" content="image/jpeg">
    <meta property="og:type" content="website">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:url" content="{{ redirect_url }}">
    <meta property="og:site_name" content="Rezio Properties">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ title }}">
    <meta name="twitter:description"
          content="{{ address }} | {{ currency_code }} {{ asking_price }} {% if is_rent %} /Yr {% endif %} |
          {{ total_area }} {{ property_unit_type }}
          {% if number_of_bedrooms and number_of_bedrooms != 0 %} | {{ number_of_bedrooms }} Bed {% endif %}
          {% if is_studio %} | Studio {% endif %}
          {% if number_of_bathrooms and number_of_bathrooms != 0 %} | {{ number_of_bathrooms }} Bath {% endif %}">
    <meta name="twitter:image" content="{{ project_env_url }}/properties/compressed-image/?image_url={{ image_url }}">
    <meta name="twitter:url" content="{{ redirect_url }}">
    <title>Property Preview</title>
</head>
<body>
<script>
    window.location.href = "{{ redirect_url }}";
</script>
</body>
</html>