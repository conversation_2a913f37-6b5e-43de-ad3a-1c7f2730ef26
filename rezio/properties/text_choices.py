from django.db import models
from django.utils.translation import gettext_lazy as _


class ActorType(models.TextChoices):
    """Text choices for actor type"""

    SYSTEM = "System", _("System")
    USER = "User", _("User")


# TODO: Will remove thess choices and move it to DB
class PropertyType(models.TextChoices):
    """Text choices for property type"""

    # residential
    APARTMENT = "Apartment", _("Apartment")
    VILLA = "Villa", _("Villa")
    TOWNHOUSE = "Townhouse", _("Townhouse")
    RESIDENTIAL_PLOT = "Residential Plot", _("Residential Plot")
    RESIDENTIAL_LAND = "Residential Land", _("Residential Land")
    BUILDER_FLOOR = "Builder Floor", _("Builder Floor")

    # commerical
    OFFICE_SPACE = "Office Space", _("Office Space")
    CO_WORKING = "Co-working", _("Co-working")
    SHOP = "Shop", _("Shop")
    SHOWROOM = "Showroom", _("Showroom")
    GODOWN_WAREHOUSE = "Godown/Warehouse", _("Godown/Warehouse")
    INDUSTRIAL_SHED = "Industrial Shed", _("Industrial Shed")
    INDUSTRIAL_BUILDING = "Industrial Building", _("Industrial Building")
    HOSPITAL_CLINIC = "Hospital/Clinic", _("Hospital/Clinic")
    SCHOOL = "School", _("School")
    RETAIL_SPACE = "Retail Space", _("Retail Space")
    HOTEL = "Hotel", _("Hotel")
    GUEST_HOUSE = "Guest House", _("Guest House")
    SCO_PLOT = "S.C.O Plot", _("S.C.O Plot")  # it means shop cum office
    COMMERCIAL_PLOT = "Commercial Plot", _("Commercial Plot")
    COMMERCIAL_LAND = "Commercial Land", _("Commercial Land")
    FACTORY = "Factory", _("Factory")


class PropertyAvailabilityStatus(models.TextChoices):
    """Text choices for availability"""

    RENTED = 0, _("Rented")
    VACANT = 1, _("Vacant")
    OWNER_OCCUPIED = 2, _("Owner Occupied")
    HOLIDAY_HOME = 3, _("Holiday Home")


class PropertyStatus(models.TextChoices):
    """Text choices for property status"""

    READY = 0, _("Ready")
    UNDER_DEVELOPMENT = 1, _("Under Development/Off Plan")


class PropertyAreaUnit(models.TextChoices):
    SQFT = 0, _("sqft")
    SQM = 1, _("sqm")
    SQYD = 2, _("sqyd")
    ACRE = 3, _("acre")
    HECTARE = 4, _("hectare")
    BIGHA = 5, _("bigha")


class PropertyCompletionStateChoices(models.TextChoices):
    LOCATION_DETAILS = "location_details", _("Location Details")
    COMMUNITY_AND_BUILDING_INFO = (
        "community_and_building_info",
        _("Community & Building Info"),
    )
    PROPERTY_SPECIFICATIONS = "property_specifications", _("Property Specifications")
    AVAILABILITY_AND_STATUS = "availability_and_status", _("Availability & Status")


class PropertyPublishStatus(models.TextChoices):
    """Text choices for property published status"""

    DRAFT = 0, _("Draft")
    ADDED_TO_PORTFOLIO = 1, _("Added to portfolio")


class OwnerIntentForProperty(models.TextChoices):
    NOT_FOR_SALE = "not for sale", _("Not for Sale")
    OPEN_TO_BIDS = "open to bids", _("Open To Bids")
    AVAILABLE_FOR_SALE = "available for sale", _("Available for Sale")
    AVAILABLE_FOR_RENT = "available for rent", _("Available for Rent")


class TenancyType(models.TextChoices):
    NEW = "New"
    RENEWAL = "Renewal"
    VACANT = "Vacant"
    OWNER_OCCUPIED = "Owner Occupied"
    HOLIDAY_HOME = "Holiday Home"


class PropertySourceType(models.TextChoices):
    IN_APP = "IN_APP"
    WHATS_APP = "WHATS_APP"


class PropertyAgentType(models.TextChoices):
    """Text choices for property agents"""

    OPEN_TO_ALL = 0, _("Open to all agents")
    SELECTIVE_AGENTS = 1, _("Allow access to selective agents")
    EXCLUSIVE_AGENT = 2, _("Give access to an exclusive agent")


class PropertyMonitorSalesEvidence(models.TextChoices):
    """Text choices for property agents"""

    GIFT = 0, _("Gifts")
    OFF_PLAN_SALE = 1, _("Off-Plan Sales (Oqood Registrations)")
    TRANSFERRED_SALE = 2, _("Transferred Sales (Title Deed)")
    MORTGAGE = 3, _("Mortgage Registrations")


class UserRequestActions(models.TextChoices):
    """Text choices for request action"""

    PENDING = 0, _("Pending")
    DECLINED = 1, _("Declined")
    ACCEPTED = 2, _("Accepted")


class RequestType(models.TextChoices):
    """Text choices for request types"""

    # when investor add an agent
    AGENT_INVITE = 0, _("Agent Invite")
    # when agent sends a request to investor
    INVESTOR_REQUEST = 1, _("Investor Request")


class PropertyCategory(models.IntegerChoices):
    """
    Text choices for the category of property
    """

    RESIDENTIAL = 0, _("Residential")
    COMMERCIAL = 1, _("Commercial")


class PropertyBuildingType(models.IntegerChoices):
    """
    Text choices for the property building type
    """

    INDEPENDENT_HOUSE = 0, _("Independent House")
    BUSINESS_PARK = 1, _("Business Park")
    MALL = 2, _("Mall")
    STANDALONE_BUILDING = 3, _("Standalone Building")
    INDEPENDENT_SHOP = 4, _("Independent Shop")


class PreferredPaymentFrequency(models.IntegerChoices):
    """
    Integer choices for the preferred payment frequency
    """

    YEARLY = 0, _("Yearly")
    BI_YEARLY = 1, _("Bi-yearly")
    QUARTERLY = 2, _("Quarterly")
    MONTHLY = 3, _("Monthly")


class PropertyAttributesCategory(models.IntegerChoices):
    """
    Text choices for the category of property attributes
    """

    RESIDENTIAL = 0, _("Residential")
    COMMERCIAL = 1, _("Commercial")
    BOTH = 2, _("BOTH")


class FurnishedChoices(models.IntegerChoices):
    """
    Integer choices for furnished status
    """

    UNFURNISHED = 0, _("Unfurnished")
    FURNISHED = 1, _("Furnished")
    SEMI_FURNISHED = 2, _("Semi Furnished")


class PropertyExternalMediaType(models.TextChoices):
    """
    Text choices for external media
    """

    LINK = 0, _("Link")
    DOCUMENT = 1, _("Document")
    IMAGE = 2, _("Image")


class UserViewCTA(models.IntegerChoices):
    """Text choices for request action"""

    PENDING_REQUEST = 0, _("Pending Request")
    PROPERTY_AGENT = 1, _("Property Agent")
    REQUEST_ACCESS = 2, _("Request Access")
    CONTACT_AGENT = 3, _("Contact Agent")
    RECOMMEND_PROPERTY_TO_AGENT = 4, _("Recommend Property to Agent")


class OwnerInformationButtonText(models.IntegerChoices):
    """Text choices for owner information action button"""

    ADD_CO_OWNER = 0, _("Add/Edit Co-Owner")
    ADD_EDIT_CO_OWNER = 1, _("Add/Edit Co-Owner")
    ADD_OWNER_DETAILS = 2, _("Add Owner Details")


class PortfolioFilters(models.TextChoices):
    FOR_SALE_FILTER = "for_sale", _("Available for Sale")
    FOR_RENT_FILTER = "for_rent", _("Available for Rent")
    RESIDENTIAL_FILTER = "residential", _("Residential")
    COMMERCIAL_FILTER = "commercial", _("Commercial")


class ClaimPropertyChoices(models.IntegerChoices):
    """Text choices for owner information action button"""

    EXISTS_IN_OWN_PORTFOLIO = 0, _("Exists in Own Portfolio")
    CLAIM_PROPERTY = 1, _("Claim Property")
    REQUEST_ACCESS = 2, _("Request access to property")
    REQUEST_PENDING = 3, _("Request access pending")
    CONTACT_SUPPORT = 4, _("Contact Support")
    ADD_TO_PORTFOLIO = 5, _("Add to Portfolio")


class CoOwnerRequestType(models.TextChoices):
    """Text choices for request types"""

    # when investor add an agent
    CO_OWNER_INVITE = 0, _("Co-owner Invite")
    # when co-owner sends a request to investor
    REQUEST_TO_OWNER = 1, _("Request to Owner")


class PropertyLockType(models.TextChoices):
    """Text choices for property lock type"""

    SUBSCRIPTION_LOCK = 0, _("Subscription Lock")
    NOT_FOR_SALE_LOCK = 1, _("Not For Sale Lock")


class PropertyHierarchy(models.TextChoices):
    """Text choices for property hierarchy"""

    OWNER = "Owner", _("Owner")
    CO_OWNER = "Co-owner", _("Co-owner")
    AGENT = "Agent", _("Agent")
    COLLABORATOR = "Collaborator", _("Collaborator")
    PUBLIC = "Public", _("Public")


class PropertyMediaFeatures(models.IntegerChoices):
    """Text choices for property media features"""

    PROPERTY_UNIT_SECTION_MEDIA = 0, _("PROPERTY_UNIT_SECTION_MEDIA")
    EXTERNAL_MEDIA_IMAGE = 1, _("EXTERNAL_MEDIA_IMAGE")
    EXTERNAL_MEDIA_DOCUMENT = 2, _("EXTERNAL_MEDIA_DOCUMENT")
