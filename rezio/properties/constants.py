from collections import namedtuple

from rezio.properties.text_choices import (
    PropertyAvailabilityStatus,
    TenancyType,
    PropertyType,
)

occupancy_status_tenancy_mapping = {
    PropertyAvailabilityStatus.VACANT: TenancyType.VACANT,
    PropertyAvailabilityStatus.OWNER_OCCUPIED: TenancyType.OWNER_OCCUPIED,
    PropertyAvailabilityStatus.HOLIDAY_HOME: TenancyType.HOLIDAY_HOME,
}

MINIMUM_OWNERSHIP_PERCENTAGE = 0.01
EICAR_STRING = "X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*"

DEFAULT_PROPERTY_IMAGES = {
    PropertyType.APARTMENT: [
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/apartment-photos/default_apartment_1.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/apartment-photos/default_apartment_2.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/apartment-photos/default_apartment_3.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/apartment-photos/default_apartment_4.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/apartment-photos/default_apartment_5.png",
    ],
    PropertyType.VILLA: [
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/villa-photos/default_villa_1.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/villa-photos/default_villa_2.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/villa-photos/default_villa_3.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/villa-photos/default_villa_4.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/villa-photos/default_villa_5.png",
    ],
    PropertyType.TOWNHOUSE: [
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/townhouse-photos/default_townhouse_1.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/townhouse-photos/default_townhouse_2.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/townhouse-photos/default_townhouse_3.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/townhouse-photos/default_townhouse_4.png",
        "https://rezio-static-files.s3.me-central-1.amazonaws.com/default-propery-photos/townhouse-photos/default_townhouse_5.png",
    ],
}

COMMERCIAL_PROPERTY_TYPES = [
    PropertyType.OFFICE_SPACE,
    PropertyType.CO_WORKING,
    PropertyType.SHOP,
    PropertyType.SHOWROOM,
    PropertyType.GODOWN_WAREHOUSE,
    PropertyType.INDUSTRIAL_SHED,
    PropertyType.INDUSTRIAL_BUILDING,
    PropertyType.HOSPITAL_CLINIC,
    PropertyType.SCHOOL,
    PropertyType.RETAIL_SPACE,
    PropertyType.HOTEL,
    PropertyType.GUEST_HOUSE,
    PropertyType.SCO_PLOT,
    PropertyType.COMMERCIAL_PLOT,
    PropertyType.COMMERCIAL_LAND,
    PropertyType.FACTORY
]

property_editable_attributes = [
    "property_type",
    "total_area",
    "carpet_area",
    "balcony_area",
    "floor_number",
    "total_floors",
    "bedroom_data",
    "bathroom_data",
    "parking_data",
    "branded_building",
    "furnished",
    "premium_view",
    "water_storage_available",
    "security_available",
    "property_on_main_road",
    "corner_property",
    "ideal_for",
    "restroom_data",
    "lift_data",
    "parking_data",
    "occupancy_status",
    "tenancy_type",
    "start_date",
    "end_date",
    "annual_service_charge_data",
    "annual_rent_data",
    "security_deposit_data",
    "preferred_payment_frequency",
    "asking_price_data",
    "original_price_data",
    "valuation_data",
    "property_status",
    "handover_date",
    "payment_during_construction",
    "payment_plan_on_handover",
    "floor_plan",
    "electricity_water_number",
    "owner_details",
    "co_owner_details",
    "payment_plan_document",
    "rent_contract",
    "agent_details",
]


portfolio_fields_to_check = ["unit_number", "floor_number"]

PROPERTY_MONITOR_DETAILS_FIELD_MAPPING = {
    "property_type": "property_type",
    "total_area": "unit_bua_sqft",
    "carpet_area": "suite_area_sqft",
    "balcony_area": "balcony_size_sqft",
    "number_of_bedrooms": "no_beds",
    "floor_number": "floor",
    "parking_number": "parking",
    "number_of_study_rooms": "study",
    "number_of_maid_rooms": "maid",
    "number_of_bathrooms": "number_of_bathrooms",
}

REQUEST_BODY_DATA_MAPPING = {
    "number_of_bedrooms": "bedroom_details",
    "parking_number": "parking_details",
}

# for fetching transactions from property monitor
QUERY_PARAMS = namedtuple(
    "QueryParams",
    [
        "emirate",
        "master_development",
        "sub_loc_1",
        "sub_loc_2",
        "sub_loc_3",
        "sub_loc_4",
        "category",
        "property_types",
        "min_area",
        "max_area",
        "contract_type",
        "number_of_bedrooms",
        "min_price",
        "max_price",
        "page",
        "page_size",
        "start_date",
        "end_date",
    ],
)


# Constants for Indian states and union territories
# Total: 28 states + 8 union territories = 36 entries
# Source: Official list from Government of India
INDIAN_STATES = [
    # States (28)
    "Andhra Pradesh",
    "Arunachal Pradesh",
    "Assam",
    "Bihar",
    "Chhattisgarh",
    "Goa",
    "Gujarat",
    "Haryana",
    "Himachal Pradesh",
    "Jharkhand",
    "Karnataka",
    "Kerala",
    "Madhya Pradesh",
    "Maharashtra",
    "Manipur",
    "Meghalaya",
    "Mizoram",
    "Nagaland",
    "Odisha",
    "Punjab",
    "Rajasthan",
    "Sikkim",
    "Tamil Nadu",
    "Telangana",
    "Tripura",
    "Uttar Pradesh",
    "Uttarakhand",
    "West Bengal",
    # Union Territories (8)
    "Andaman and Nicobar Islands",
    "Chandigarh",
    "Dadra and Nagar Haveli and Daman and Diu",
    "Delhi",
    "Jammu and Kashmir",
    "Ladakh",
    "Lakshadweep",
    "Puducherry",
]

# Countries
INDIA = "India"

# Explore Visibility
EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES = [
    PropertyType.RESIDENTIAL_LAND,
    PropertyType.RESIDENTIAL_PLOT,
    PropertyType.COMMERCIAL_LAND,
    PropertyType.COMMERCIAL_PLOT,
]
EXPLORE_VISIBILITY_CONSTRAINT_LAND_ALLOWED_FIELDS = ["total_area"]

EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_TYPES = [
    PropertyType.HOTEL,
    PropertyType.GUEST_HOUSE,
]

EXPLORE_VISIBILITY_CONSTRAINT_COMMERCIAL_ALLOWED_FIELDS = [
    "total_area",
    "carpet_area",
    "floor_number",
]
