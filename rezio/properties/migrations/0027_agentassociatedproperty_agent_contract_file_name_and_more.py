# Generated by Django 4.1.7 on 2024-09-20 08:25

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0026_alter_propertycoowner_co_owner_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentassociatedproperty",
            name="agent_contract_file_name",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AddField(
            model_name="agentassociatedproperty",
            name="agent_contract_key",
            field=models.Char<PERSON>ield(blank=True, max_length=512, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="agentassociatedproperty",
            name="request_accepted",
            field=models.<PERSON><PERSON>anField(default=False),
        ),
        migrations.AddField(
            model_name="property",
            name="agent_type",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("0", "Open to all agents"),
                    ("1", "Allow access to selective agents"),
                    ("2", "Give access to an exclusive agent"),
                ],
                max_length=32,
                null=True,
            ),
        ),
    ]
