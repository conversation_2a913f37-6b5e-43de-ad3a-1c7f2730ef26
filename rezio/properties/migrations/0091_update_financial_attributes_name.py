# Generated by Django 4.1.7 on 2025-04-29 06:10

from django.db import migrations

from rezio.properties.text_choices import PropertyHierarchy


def update_property_financial_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions", attribute_name="annual_rent"
    ).update(attribute_name="expected_annual_rent")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions", attribute_name="security_deposit"
    ).update(attribute_name="expected_security_deposit")

    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    target_names = [
        PropertyHierarchy.AGENT,
        PropertyHierarchy.OWNER,
        PropertyHierarchy.CO_OWNER,
    ]

    # Add unit_number to editable_attributes required hierarchy levels
    hierarchy_levels = HierarchyLevel.objects.filter(name__in=target_names)
    for level in hierarchy_levels:
        if level.editable_attributes is None:
            level.editable_attributes = []

        if level.viewed_attributes is None:
            level.viewed_attributes = []

        if "expected_annual_rent" not in level.editable_attributes:
            level.editable_attributes.append("expected_annual_rent")

        if "expected_annual_rent" not in level.viewed_attributes:
            level.viewed_attributes.append("expected_annual_rent")

        if "expected_security_deposit" not in level.editable_attributes:
            level.editable_attributes.append("expected_security_deposit")

        if "expected_security_deposit" not in level.viewed_attributes:
            level.viewed_attributes.append("expected_security_deposit")

        level.save()

    hierarchy_level_public = HierarchyLevel.objects.filter(
        name=PropertyHierarchy.PUBLIC
    ).first()
    if hierarchy_level_public.viewed_attributes is None:
        hierarchy_level_public.viewed_attributes = []

    if "expected_annual_rent" not in hierarchy_level_public.viewed_attributes:
        hierarchy_level_public.viewed_attributes.append("expected_annual_rent")

    if "expected_security_deposit" not in hierarchy_level_public.viewed_attributes:
        hierarchy_level_public.viewed_attributes.append("expected_security_deposit")

    hierarchy_level_public.save()


def reverse_update_property_financial_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions", attribute_name="expected_annual_rent"
    ).update(attribute_name="annual_rent")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions", attribute_name="expected_security_deposit"
    ).update(attribute_name="security_deposit")

    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")
    target_names = [
        PropertyHierarchy.AGENT,
        PropertyHierarchy.OWNER,
        PropertyHierarchy.CO_OWNER,
    ]

    # Filter entries with the specified names
    hierarchy_levels = HierarchyLevel.objects.filter(name__in=target_names)

    for level in hierarchy_levels:
        if (
            level.editable_attributes
            and "expected_annual_rent" in level.editable_attributes
        ):
            level.editable_attributes.remove("expected_annual_rent")
        if (
            level.viewed_attributes
            and "expected_annual_rent" in level.viewed_attributes
        ):
            level.viewed_attributes.remove("expected_annual_rent")
        if (
            level.editable_attributes
            and "expected_security_deposit" in level.editable_attributes
        ):
            level.editable_attributes.remove("expected_security_deposit")
        if (
            level.viewed_attributes
            and "expected_security_deposit" in level.viewed_attributes
        ):
            level.viewed_attributes.remove("expected_security_deposit")

        level.save()

    hierarchy_level_public = HierarchyLevel.objects.filter(
        name=PropertyHierarchy.PUBLIC
    ).first()
    if hierarchy_level_public and hierarchy_level_public.viewed_attributes:
        if "expected_annual_rent" in hierarchy_level_public.viewed_attributes:
            hierarchy_level_public.viewed_attributes.remove("expected_annual_rent")
        if "expected_security_deposit" in hierarchy_level_public.viewed_attributes:
            hierarchy_level_public.viewed_attributes.remove("expected_security_deposit")

        hierarchy_level_public.save()


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0090_populate_indian_cities"),
    ]

    operations = [
        migrations.RunPython(
            update_property_financial_attributes,
            reverse_update_property_financial_attributes,
        ),
    ]
