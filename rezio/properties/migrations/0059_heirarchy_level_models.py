# Generated by Django 4.1.7 on 2025-01-13 05:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        (
            "properties",
            "0058_propertyexternalmedia_propertyexternalmediasection_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="HierarchyLevel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, unique=True)),
                ("level", models.PositiveIntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="PropertyAttributes",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("component_name", models.CharField(max_length=255)),
                ("attribute_name", models.Char<PERSON>ield(max_length=255)),
                ("is_public", models.BooleanField(default=True)),
                (
                    "subscription_level",
                    models.PositiveSmallIntegerField(
                        choices=[("0", "BASIC"), ("1", "PREMIUM")], default="1"
                    ),
                ),
                (
                    "hierarchy_level",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_attributes",
                        to="properties.hierarchylevel",
                    ),
                ),
            ],
        ),
    ]
