# Generated by Django 4.1.7 on 2025-06-02 13:40

import logging

from django.db import migrations, models

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def convert_furnished_boolean_to_integer(apps, schema_editor):
    """
    Convert furnished field from boolean to integer choices:
    - False (boolean) -> 0 (UNFURNISHED)
    - True (boolean) -> 1 (FURNISHED)
    """
    PropertyFeatures = apps.get_model("properties", "PropertyFeatures")
    UserLevelPropertyFeatures = apps.get_model(
        "properties", "UserLevelPropertyFeatures"
    )

    # FurnishedChoices values
    UNFURNISHED = 0
    FURNISHED = 1

    logger.info(
        "Starting conversion of furnished field from boolean to integer choices"
    )

    # Convert PropertyFeatures
    property_features_count = PropertyFeatures.objects.count()
    logger.info(f"Converting {property_features_count} PropertyFeatures records")

    property_features_updated = 0
    for feature in PropertyFeatures.objects.all():
        try:
            # Convert boolean to integer
            if feature.furnished is True:
                feature.furnished = FURNISHED
            elif feature.furnished is False:
                feature.furnished = UNFURNISHED
            else:
                # Handle None or other values
                feature.furnished = UNFURNISHED

            feature.save(update_fields=["furnished"])
            property_features_updated += 1

        except Exception as e:
            logger.error(f"Error updating PropertyFeatures ID {feature.id}: {str(e)}")
            continue

    # Convert UserLevelPropertyFeatures
    user_features_count = UserLevelPropertyFeatures.objects.count()
    logger.info(f"Converting {user_features_count} UserLevelPropertyFeatures records")

    user_features_updated = 0
    for feature in UserLevelPropertyFeatures.objects.all():
        try:
            # Convert boolean to integer
            if feature.furnished is True:
                feature.furnished = FURNISHED
            elif feature.furnished is False:
                feature.furnished = UNFURNISHED
            else:
                # Handle None or other values
                feature.furnished = UNFURNISHED

            feature.save(update_fields=["furnished"])
            user_features_updated += 1

        except Exception as e:
            logger.error(
                f"Error updating UserLevelPropertyFeatures ID {feature.id}: {str(e)}"
            )
            continue

    logger.info(
        f"Furnished field conversion completed. "
        f"PropertyFeatures updated: {property_features_updated}/{property_features_count}, "
        f"UserLevelPropertyFeatures updated: {user_features_updated}/{user_features_count}"
    )


def reverse_convert_furnished_integer_to_boolean(apps, schema_editor):
    """
    Reverse conversion from integer choices back to boolean:
    - 0 (UNFURNISHED) -> False
    - 1 (FURNISHED) -> True
    - 2 (SEMI_FURNISHED) -> True (closest boolean equivalent)
    """
    PropertyFeatures = apps.get_model("properties", "PropertyFeatures")
    UserLevelPropertyFeatures = apps.get_model(
        "properties", "UserLevelPropertyFeatures"
    )

    logger.info(
        "Starting reverse conversion of furnished field from integer to boolean"
    )

    # Convert PropertyFeatures back to boolean
    property_features_updated = 0
    for feature in PropertyFeatures.objects.all():
        try:
            # Convert integer to boolean
            if feature.furnished == 0:  # UNFURNISHED
                feature.furnished = False
            elif feature.furnished in [1, 2]:  # FURNISHED or SEMI_FURNISHED
                feature.furnished = True
            else:
                feature.furnished = False  # Default to False for any other value

            feature.save(update_fields=["furnished"])
            property_features_updated += 1

        except Exception as e:
            logger.error(
                f"Error reverse updating PropertyFeatures ID {feature.id}: {str(e)}"
            )
            continue

    # Convert UserLevelPropertyFeatures back to boolean
    user_features_updated = 0
    for feature in UserLevelPropertyFeatures.objects.all():
        try:
            # Convert integer to boolean
            if feature.furnished == 0:  # UNFURNISHED
                feature.furnished = False
            elif feature.furnished in [1, 2]:  # FURNISHED or SEMI_FURNISHED
                feature.furnished = True
            else:
                feature.furnished = False  # Default to False for any other value

            feature.save(update_fields=["furnished"])
            user_features_updated += 1

        except Exception as e:
            logger.error(
                f"Error reverse updating UserLevelPropertyFeatures ID {feature.id}: {str(e)}"
            )
            continue

    logger.info(
        f"Reverse furnished field conversion completed. "
        f"PropertyFeatures updated: {property_features_updated}, "
        f"UserLevelPropertyFeatures updated: {user_features_updated}"
    )


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0108_update_attribute_category_to_both"),
    ]

    operations = [
        # First, convert the data
        migrations.RunPython(
            convert_furnished_boolean_to_integer,
            reverse_convert_furnished_integer_to_boolean,
        ),
        migrations.AlterField(
            model_name="propertyfeatures",
            name="furnished",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Unfurnished"), (1, "Furnished"), (2, "Semi Furnished")],
                default=0,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="userlevelpropertyfeatures",
            name="furnished",
            field=models.IntegerField(
                blank=True,
                choices=[(0, "Unfurnished"), (1, "Furnished"), (2, "Semi Furnished")],
                default=0,
                null=True,
            ),
        ),
    ]
