# Generated by Django 4.1.7 on 2024-08-30 16:00
import django
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0007_remove_property_is_draft_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="property",
            name="dewa_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="property",
            name="owner_intent",
            field=models.CharField(
                choices=[
                    ("not for sale", "Not for Sale"),
                    ("open to bids", "Open To Bids"),
                    ("available for sale", "Available for Sale"),
                ],
                default="not for sale",
                max_length=32,
            ),
        ),
        migrations.CreateModel(
            name="PropertyFinancialDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "currency_code",
                    models.CharField(blank=True, max_length=32, null=True),
                ),
                (
                    "original_price",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "asking_price",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "property",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.property",
                    ),
                ),
            ],
        ),
    ]
