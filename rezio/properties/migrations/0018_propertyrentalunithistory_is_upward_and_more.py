# Generated by Django 4.1.7 on 2024-09-16 10:00

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0017_property_tenancy_end_date_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="is_upward",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="rent_increase_percentage",
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="is_upward",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="sale_price_increase_percentage",
            field=models.FloatField(blank=True, null=True),
        ),
    ]
