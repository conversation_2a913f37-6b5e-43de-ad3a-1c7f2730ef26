# Generated by Django 4.1.7 on 2025-01-XX XX:XX

import logging

from django.db import migrations

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def convert_to_sqft(value, unit):
    """
    Convert area value from a given unit to sqft
    Based on Flutter conversion logic
    """
    if value is None:
        return None

    # PropertyAreaUnit choices mapping
    unit_conversions = {
        "0": 1.0,  # SQFT - no conversion needed
        "1": 10.7639,  # SQM - 1 sqm = 10.7639 sqft
        "2": 9.0,  # SQYD - 1 sqyd = 9 sqft
        "3": 43560.0,  # ACRE - 1 acre = 43560 sqft
        "4": 107639.0,  # HECTARE - 1 hectare = 107639 sqft
        "5": 17452.0,  # BIGHA - Approx value (varies by region, North India average)
    }

    conversion_factor = unit_conversions.get(str(unit), 1.0)
    return value * conversion_factor


def get_unit_text(unit_value):
    """
    Get text representation of PropertyAreaUnit enum value
    """
    unit_mapping = {
        "0": "sqft",
        "1": "sqm",
        "2": "sqyd",
        "3": "acre",
        "4": "hectare",
        "5": "bigha",
    }
    return unit_mapping.get(str(unit_value), "sqft")


def convert_areas_to_sqft(apps, schema_editor):
    """
    Convert all area fields from their current unit to sqft
    and update user_unit_preference accordingly
    """
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")

    # Get all records that need conversion
    properties = UserLevelPropertyData.objects.all()

    total_count = properties.count()
    logger.info(
        f"Starting conversion of {total_count} UserLevelPropertyData records to sqft"
    )

    converted_count = 0
    skipped_count = 0
    error_count = 0

    # Process in batches for better performance
    batch_size = 1000

    for i in range(0, total_count, batch_size):
        batch = properties[i : i + batch_size]

        for property_data in batch:
            try:
                # Get the current unit type
                current_unit = property_data.property_unit_type

                # Skip if already in sqft
                if current_unit == "0":  # SQFT
                    skipped_count += 1
                    continue

                # Store original values for logging
                original_total_area = property_data.total_area
                original_carpet_area = property_data.carpet_area
                original_balcony_area = property_data.balcony_area
                original_unit_text = get_unit_text(current_unit)

                # Convert each area field if it has a value
                if property_data.total_area is not None:
                    converted_value = convert_to_sqft(
                        property_data.total_area, current_unit
                    )
                    if converted_value is not None:
                        property_data.total_area = round(
                            converted_value, 2
                        )  # Round to 2 decimal places

                if property_data.carpet_area is not None:
                    converted_value = convert_to_sqft(
                        property_data.carpet_area, current_unit
                    )
                    if converted_value is not None:
                        property_data.carpet_area = round(converted_value, 2)

                if property_data.balcony_area is not None:
                    converted_value = convert_to_sqft(
                        property_data.balcony_area, current_unit
                    )
                    if converted_value is not None:
                        property_data.balcony_area = round(converted_value, 2)

                # Update unit preference to sqft
                property_data.user_unit_preference = original_unit_text

                # Update property unit type to SQFT
                property_data.property_unit_type = "0"  # SQFT

                # Save the changes
                property_data.save(
                    update_fields=[
                        "total_area",
                        "carpet_area",
                        "balcony_area",
                        "user_unit_preference",
                        "property_unit_type",
                    ]
                )

                converted_count += 1

                # Log conversion details for debugging (only for first few records to avoid spam)
                if converted_count <= 10 or converted_count % 100 == 0:
                    logger.info(
                        f"Converted property ID {property_data.id}: "
                        f"Unit {original_unit_text} -> sqft, "
                        f"Total: {original_total_area} -> {property_data.total_area}, "
                        f"Carpet: {original_carpet_area} -> {property_data.carpet_area}, "
                        f"Balcony: {original_balcony_area} -> {property_data.balcony_area}"
                    )

            except Exception as e:
                error_count += 1
                logger.error(
                    f"Error converting property ID {property_data.id}: {str(e)}"
                )
                continue

        # Log progress every batch
        logger.info(
            f"Processed batch {i//batch_size + 1}/{(total_count + batch_size - 1)//batch_size}"
        )

    logger.info(
        f"Area conversion completed. "
        f"Total: {total_count}, Converted: {converted_count}, "
        f"Skipped (already sqft): {skipped_count}, Errors: {error_count}"
    )


def reverse_conversion(apps, schema_editor):
    """
    Reverse migration - this is complex because we lose the original unit information
    For safety, we'll just log a warning and not perform any changes
    """
    logger.warning(
        "Reverse migration for area conversion is not supported as original unit "
        "information is lost. Manual intervention required if rollback is needed."
    )


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0105_userlevelpropertydata_open_for_collaborations"),
    ]

    operations = [
        migrations.RunPython(convert_areas_to_sqft, reverse_conversion),
    ]
