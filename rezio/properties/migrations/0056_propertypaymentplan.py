# Generated by Django 4.1.7 on 2024-12-26 08:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("user", "0044_onboardingquestion_preference_category"),
        ("properties", "0055_deletedproperty"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyPaymentPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "media_file_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "media_file_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                ("media_file_size", models.BigIntegerField(blank=True, null=True)),
                ("order_no", models.IntegerField(null=True)),
                (
                    "media_file_content_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.property",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
