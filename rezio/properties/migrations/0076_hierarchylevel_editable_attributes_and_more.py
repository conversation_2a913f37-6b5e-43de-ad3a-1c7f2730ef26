# Generated by Django 5.1.6 on 2025-03-10 18:15

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0075_propertyfinancialdetails_original_price_data_source"),
        ("user", "0056_alter_agency_data_source"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="hierarchylevel",
            name="editable_attributes",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="hierarchylevel",
            name="viewed_attributes",
            field=models.J<PERSON>NField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="data_source",
            field=models.CharField(
                choices=[
                    ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                    ("DLD", "DLD"),
                    ("USER_ADDED", "USER_ADDED"),
                    ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                    ("REZIO_VERIFIED", "REZIO_VERIFIED"),
                ],
                default="PROPERTY_MONITOR",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="community",
            name="added_by",
            field=models.CharField(
                choices=[
                    ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                    ("DLD", "DLD"),
                    ("USER_ADDED", "USER_ADDED"),
                    ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                    ("REZIO_VERIFIED", "REZIO_VERIFIED"),
                ],
                default="PROPERTY_MONITOR",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="hierarchylevel",
            name="name",
            field=models.CharField(
                choices=[
                    ("Owner", "Owner"),
                    ("Co-owner", "Co-owner"),
                    ("Agent", "Agent"),
                    ("Collaborator", "Collaborator"),
                    ("Public", "Public"),
                ],
                max_length=255,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="propertycompletionstate",
            name="data_source",
            field=models.CharField(
                choices=[
                    ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                    ("DLD", "DLD"),
                    ("USER_ADDED", "USER_ADDED"),
                    ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                    ("REZIO_VERIFIED", "REZIO_VERIFIED"),
                ],
                default="PROPERTY_MONITOR",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="propertyfinancialdetails",
            name="original_price_data_source",
            field=models.CharField(
                choices=[
                    ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                    ("DLD", "DLD"),
                    ("USER_ADDED", "USER_ADDED"),
                    ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                    ("REZIO_VERIFIED", "REZIO_VERIFIED"),
                ],
                default="USER_ADDED",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="propertyfinancialdetails",
            name="valuation_data_source",
            field=models.CharField(
                choices=[
                    ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                    ("DLD", "DLD"),
                    ("USER_ADDED", "USER_ADDED"),
                    ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                    ("REZIO_VERIFIED", "REZIO_VERIFIED"),
                ],
                default="USER_ADDED",
                max_length=32,
            ),
        ),
        migrations.CreateModel(
            name="UserLevelPropertyData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "user_hierarchy",
                    models.CharField(
                        choices=[
                            ("Owner", "Owner"),
                            ("Co-owner", "Co-owner"),
                            ("Agent", "Agent"),
                            ("Collaborator", "Collaborator"),
                            ("Public", "Public"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "property_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Apartment", "Apartment"),
                            ("Villa", "Villa"),
                            ("Townhouse", "Townhouse"),
                            ("Office Space", "Office Space"),
                            ("Co-working", "Co-working"),
                            ("Shop", "Shop"),
                            ("Showroom", "Showroom"),
                            ("Godown/Warehouse", "Godown/Warehouse"),
                            ("Industrial Shed", "Industrial Shed"),
                            ("Industrial Building", "Industrial Building"),
                            ("Hospital/Clinic", "Hospital/Clinic"),
                        ],
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "floor_number",
                    models.CharField(blank=True, max_length=16, null=True),
                ),
                (
                    "property_unit_type",
                    models.CharField(
                        choices=[("0", "sqft"), ("1", "sqm")],
                        default="0",
                        max_length=32,
                    ),
                ),
                ("total_area", models.FloatField(blank=True, null=True)),
                ("carpet_area", models.FloatField(blank=True, null=True)),
                ("balcony_area", models.FloatField(blank=True, null=True)),
                ("number_of_bedrooms", models.IntegerField(blank=True, null=True)),
                (
                    "number_of_common_bathrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "number_of_attached_bathrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                ("number_of_powder_rooms", models.IntegerField(blank=True, null=True)),
                ("parking_available", models.BooleanField(default=False)),
                (
                    "number_of_covered_parking",
                    models.IntegerField(blank=True, null=True),
                ),
                ("number_of_open_parking", models.IntegerField(blank=True, null=True)),
                ("parking_number", models.TextField(blank=True, null=True)),
                (
                    "property_publish_status",
                    models.CharField(
                        choices=[("0", "Draft"), ("1", "Added to portfolio")],
                        default="0",
                        max_length=32,
                    ),
                ),
                ("dewa_id", models.CharField(blank=True, max_length=32, null=True)),
                (
                    "user_unit_preference",
                    models.CharField(default="sqft", max_length=8),
                ),
                (
                    "tenancy_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("New", "New"),
                            ("Renewal", "Renewal"),
                            ("Vacant", "Vacant"),
                            ("Owner Occupied", "Owner Occupied"),
                            ("Holiday Home", "Holiday Home"),
                        ],
                        max_length=25,
                        null=True,
                    ),
                ),
                ("tenancy_start_date", models.DateField(blank=True, null=True)),
                ("tenancy_end_date", models.DateField(blank=True, null=True)),
                (
                    "lead_owner_percentage",
                    models.DecimalField(decimal_places=2, default=100, max_digits=5),
                ),
                (
                    "agent_type",
                    models.CharField(
                        choices=[
                            ("0", "Open to all agents"),
                            ("1", "Allow access to selective agents"),
                            ("2", "Give access to an exclusive agent"),
                        ],
                        default="0",
                        max_length=32,
                    ),
                ),
                (
                    "number_of_master_bedrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "number_of_other_bedrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                ("number_of_maid_rooms", models.IntegerField(blank=True, null=True)),
                ("number_of_study_rooms", models.IntegerField(blank=True, null=True)),
                ("is_archived", models.BooleanField(default=False)),
                ("total_floors", models.IntegerField(blank=True, null=True)),
                (
                    "building_type",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (0, "Independent House"),
                            (1, "Business Park"),
                            (2, "Mall"),
                            (3, "Standalone Building"),
                            (4, "Independent Shop"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_user_level_data",
                        to="properties.property",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="UserLevelPropertyAvailabilityAndStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[("0", "Ready"), ("1", "Under Development/Off Plan")],
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "handover_date",
                    models.CharField(blank=True, max_length=32, null=True),
                ),
                (
                    "occupancy_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("0", "Rented"),
                            ("1", "Vacant"),
                            ("2", "Owner Occupied"),
                            ("3", "Holiday Home"),
                        ],
                        default="1",
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "rent_contract_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "rent_contract_file_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                (
                    "rent_contract_file_size",
                    models.BigIntegerField(blank=True, null=True),
                ),
                ("rent_available_start_date", models.DateField(blank=True, null=True)),
                ("enable_payment_plan", models.BooleanField(default=False)),
                (
                    "during_construction",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "on_handover",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("enable_post_handover", models.BooleanField(default=False)),
                (
                    "post_handover_time_frame",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "ownership_proof_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "ownership_proof_file_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                (
                    "ownership_proof_file_size",
                    models.BigIntegerField(blank=True, null=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
                (
                    "property_level_data",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_user_level_availability_and_status",
                        to="properties.userlevelpropertydata",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="UserLevelPropertyFeatures",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                ("branded_building", models.BooleanField(default=False)),
                ("furnished", models.BooleanField(default=False)),
                ("premium_view", models.BooleanField(default=False)),
                ("is_restroom_available", models.BooleanField(default=False)),
                ("no_of_shared_restrooms", models.IntegerField(blank=True, null=True)),
                ("no_of_private_restrooms", models.IntegerField(blank=True, null=True)),
                ("is_parking_available", models.BooleanField(default=False)),
                ("public_parking", models.BooleanField(default=False)),
                ("no_of_reserved_parking", models.IntegerField(blank=True, null=True)),
                ("reserved_parking_number", models.TextField(blank=True, null=True)),
                ("is_lift_available", models.BooleanField(default=False)),
                ("common_lift", models.BooleanField(default=False)),
                ("no_of_personal_lift", models.IntegerField(blank=True, null=True)),
                ("security_available", models.BooleanField(default=False)),
                ("water_storage_available", models.BooleanField(default=False)),
                ("property_on_main_road", models.BooleanField(default=False)),
                ("corner_property", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property_level_data",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_user_level_features",
                        to="properties.userlevelpropertydata",
                    ),
                ),
                ("tags", models.ManyToManyField(to="properties.propertytag")),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="UserLevelPropertyFinancialDetails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "property_currency_code",
                    models.CharField(
                        blank=True, default="USD", max_length=32, null=True
                    ),
                ),
                (
                    "original_price",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "asking_price",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "valuation",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "annual_rent",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "annual_service_charges",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "security_deposit",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                ("other_expenses", models.BigIntegerField(default=0)),
                (
                    "preferred_payment_frequency",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (0, "Yearly"),
                            (1, "Bi-yearly"),
                            (2, "Quarterly"),
                            (3, "Monthly"),
                        ],
                        null=True,
                    ),
                ),
                ("price_negotiable", models.BooleanField(default=False)),
                (
                    "expected_rent",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "expected_security_deposit",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property_level_data",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="property_user_level_financial_details",
                        to="properties.userlevelpropertydata",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
