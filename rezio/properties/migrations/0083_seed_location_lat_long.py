import googlemaps
from django.db import migrations, connection
import os

from rezio.rezio.settings import GOOGLE_MAPS_API_KEY

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
PARENT_DIR = os.path.dirname(BASE_DIR)
SQL_DIR = os.path.join(PARENT_DIR, 'sql')

COMMUNITY_PLSQL = os.path.join(SQL_DIR, 'get_properties_by_agent_and_community.sql')
GEOLOCATION_PLSQL = os.path.join(SQL_DIR, 'get_properties_by_agent_and_location.sql')

GMAPS = googlemaps.Client(key=GOOGLE_MAPS_API_KEY)

def read_sql_file(file_path):
    with open(file_path, 'r') as file:
        return file.read()

def get_coordinates(property_name):
    """Get coordinates using Google Geocoding API"""
    try:
        geocode_result = GMAPS.geocode(property_name)
        if geocode_result:
            location = geocode_result[0]['geometry']['location']
            return location['lat'], location['lng']
        return None, None
    except Exception as e:
        print(f"Geocoding error for {property_name}: {str(e)}")
        return None, None

def get_address(obj):
    """Generate an address string from a flat dictionary."""
    skip_loc = None
    for loc_no in range(5, 0, -1):
        if obj.get(f"sub_loc_{loc_no}"):
            skip_loc = f"sub_loc_{loc_no}"
            break
    
    address_parts = [
        *[
            obj.get(f"sub_loc_{loc_no}")
            for loc_no in range(5, 0, -1)
            if f"sub_loc_{loc_no}" != skip_loc 
            and obj.get(f"sub_loc_{loc_no}")
        ],
        obj.get("community_name") if skip_loc else None,
        obj.get("area_name"),
        obj.get("city_name"),
        obj.get("state_name"),
        obj.get("country_name")
    ]
    return ", ".join(filter(None, address_parts))

def update_properties(apps, schema_editor):
    """Batch update properties with coordinates"""
    cursor = None
    try:
        cursor = connection.cursor()
        cursor.execute("""
            SELECT
                p.id,
                c.sub_loc_1,
                c.sub_loc_2,
                c.sub_loc_3,
                c.sub_loc_4,
                c.sub_loc_5,
                c.name AS community_name,
                a.name AS area_name,
                city.name AS city_name,
                s.name AS state_name,
                country.name AS country_name,
                c.id AS community_id
            FROM properties_property p
            LEFT JOIN properties_community c ON p.community_id = c.id
            LEFT JOIN properties_area a ON p.area_id = a.id
            LEFT JOIN properties_city city ON a.city_id = city.id
            LEFT JOIN properties_state s ON a.state_id = s.id
            LEFT JOIN properties_country country ON a.country_id = country.id
            WHERE p.geom IS NULL
        """)
        rows = cursor.fetchall()

        for row in rows:
            obj = {
                "sub_loc_1": row[1],
                "sub_loc_2": row[2],
                "sub_loc_3": row[3],
                "sub_loc_4": row[4],
                "sub_loc_5": row[5],
                "community_name": row[6],
                "area_name": row[7],
                "city_name": row[8],
                "state_name": row[9],
                "country_name": row[10]
            }
            name = get_address(obj=obj)

            lat, lng = get_coordinates(name)
            print(f"LAT: {lat}, LNG: {lng}")

            property_id = row[0]
            if lat and lng:
                cursor.execute("""
                    UPDATE properties_property
                    SET geom = ST_SetSRID(ST_MakePoint(%s, %s), 4326)
                    WHERE id = %s
                """, (lng, lat, property_id))
                print(f"Updated {name} with {lat}, {lng}")

    except Exception as e:
        print(f"Database error: {str(e)}")
        raise

def reverse_update_properties(apps, schema_editor):
    """Reverse the update by clearing the geom column"""
    cursor = None
    try:
        cursor = connection.cursor()
        cursor.execute("""
            UPDATE properties_property
            SET geom = NULL
            WHERE geom IS NOT NULL
        """)
    except Exception as e:
        print(f"Database error during reversal: {str(e)}")
        raise

class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0082_unitsectionmedia_media_url_and_more'), 
    ]

    operations = [

        # 1: Install PostGIS for postgre
        migrations.RunSQL(
            "CREATE EXTENSION IF NOT EXISTS postgis;",
        ),
        
        # 2: Add PL/pgSQL COMMUNITY_PLSQL
    #     migrations.RunSQL(read_sql_file(COMMUNITY_PLSQL)),
    #
    #     # 3: Add PL/pgSQL GEOLOCATION_PLSQL
    #     migrations.RunSQL(read_sql_file(GEOLOCATION_PLSQL)),
    #
    #     # 4: Add the geom column
    #     migrations.RunSQL(
    #         "ALTER TABLE properties_property ADD COLUMN geom geometry(Point, 4326);",
    #         reverse_sql="ALTER TABLE properties_property DROP COLUMN geom;"
    #     ),
    #
    #     # 5: Populate geom with coordinates
    #     migrations.RunPython(update_properties, reverse_update_properties),
    ]