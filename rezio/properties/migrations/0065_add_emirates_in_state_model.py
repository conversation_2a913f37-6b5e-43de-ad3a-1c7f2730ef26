from django.db import migrations


def add_emirates(apps, schema_editor):
    # Fetch the UAE country ID
    Country = apps.get_model("properties", "Country")
    State = apps.get_model("properties", "State")

    uae, created = Country.objects.get_or_create(name="United Arab Emirates")

    # all emirates of UAE
    emirates = [
        "Abu Dhabi",
        "Dubai",
        "Sharjah",
        "Ajman",
        "<PERSON>m Al Quwain",
        "Fujairah",
        "Ras Al Khaimah",
    ]

    # Adding emirates to the State model under UAE country
    for emirate in emirates:
        # Get or create each emirate under the UAE country
        State.objects.get_or_create(
            country=uae, name=emirate, defaults={"is_active": True, "is_deleted": False}
        )


def reverse_func(apps, schema_editor):
    # This reverse function can delete the added states if needed
    Country = apps.get_model("properties", "Country")
    State = apps.get_model("properties", "State")

    uae = Country.objects.filter(name="UAE").first()
    if uae:
        State.objects.filter(country=uae).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0064_add_attributes_and_their_details"),
    ]

    operations = [
        migrations.RunPython(add_emirates, reverse_code=reverse_func),
    ]
