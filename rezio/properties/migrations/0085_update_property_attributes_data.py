# Generated by Django 4.1.7 on 2025-04-16 13:39

from django.db import migrations


def update_property_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(component_name="scrollable component").update(
        component_name="scrollable_component"
    )

    PropertyAttributes.objects.filter(
        component_name="property_specifications", attribute_name="bedroom_data"
    ).update(attribute_name="number_of_bedrooms")

    PropertyAttributes.objects.filter(
        component_name="property_specifications",
        attribute_name="residential_parking_data",
    ).update(attribute_name="parking_number")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions",
        attribute_name="anuual_rent_data",
    ).update(attribute_name="annual_rent")


def reverse_update_property_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(component_name="scrollable_component").update(
        component_name="scrollable component"
    )

    PropertyAttributes.objects.filter(
        component_name="property_specifications", attribute_name="number_of_bedrooms"
    ).update(attribute_name="bedroom_data")

    PropertyAttributes.objects.filter(
        component_name="property_specifications",
        attribute_name="parking_number",
    ).update(attribute_name="residential_parking_data")

    PropertyAttributes.objects.filter(
        component_name="lease_conditions",
        attribute_name="annual_rent",
    ).update(attribute_name="anuual_rent_data")


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0084_property_number_of_bathrooms_and_more"),
    ]

    operations = [
        migrations.RunPython(
            update_property_attributes, reverse_update_property_attributes
        ),
    ]
