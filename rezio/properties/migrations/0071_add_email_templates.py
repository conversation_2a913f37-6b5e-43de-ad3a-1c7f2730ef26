# Generated by Django 4.1.7 on 2025-02-06 13:05

from django.db import migrations


def add_email_templates(apps, schema_editor):
    EmailTemplate = apps.get_model("properties", "EmailTemplate")
    EmailTemplate.objects.create(
        name="claim_property",
        subject="Property Claim Request - {unit_number}, {building_street}",
        body=(
            "Hi Rezio Support,\n\n"
            "I am the rightful owner of the property {unit_number}, {building_street}, and I would like to claim it as part of my portfolio.\n"
            "Please find the attached documents to verify my ownership (e.g., [FOC/Deed of Ownership/Other Docs]).\n\n"
            "Best regards,\n{name}"
        ),
    )
    EmailTemplate.objects.create(
        name="add_property_to_portfolio",
        subject="Add {role_name} to property - {unit_number}, {building_street}",
        body=(
            "Hi Rezio Support,\n\n"
            "I am {name} an {role_name} of the property {unit_number}, {building_street}, and I would like to add it as part of my portfolio.\n\n"
            "Best regards,\n{name}"
        ),
    )


def remove_email_templates(apps, schema_editor):
    EmailTemplate = apps.get_model("properties", "EmailTemplate")
    EmailTemplate.objects.filter(
        subject__in=[
            "Property Claim Request - [Unit Number, Property Name]",
            "Add [agent/investor] to property - [Unit Number, Property Name]",
        ]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0070_emailtemplate"),
    ]

    operations = [
        migrations.RunPython(add_email_templates, remove_email_templates),
    ]
