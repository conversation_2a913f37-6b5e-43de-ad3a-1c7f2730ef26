from django.db import migrations, models


def hide_postal_codes_for_uae(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    try:
        uae = Country.objects.get(name="United Arab Emirates")
        postal_attributes = PropertyAttributes.objects.filter(
            attribute_name__in=["postal_code", "pin_code", "city"]
        )
        for attr in postal_attributes:
            attr.hidden_in_countries.add(uae)

    except Country.DoesNotExist:
        pass


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0092_populate_indian_cities_v2"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertyattributes",
            name="hidden_in_countries",
            field=models.ManyToManyField(
                blank=True,
                related_name="hidden_attributes",
                to="properties.country",
                help_text="Countries where this attribute should be hidden",
            ),
        ),
        migrations.RunPython(hide_postal_codes_for_uae),
    ]
