# Generated by Django 4.1.7 on 2025-03-20 06:43

from django.db import migrations, models

from rezio.properties.utils import get_s3_object


def update_media_urls(apps, schema_editor):
    """
    Update all existing UnitSectionMedia records with permanent CloudFront URLs
    """
    UnitSectionMedia = apps.get_model("properties", "UnitSectionMedia")

    # Batch process to avoid memory issues with large datasets
    batch_size = 1000
    total_updated = 0

    while True:
        media_batch = UnitSectionMedia.objects.filter(media_url__isnull=True)[
            :batch_size
        ]

        if not media_batch:
            break

        for media in media_batch:
            # Generate permanent URLs for media file
            if media.media_file:
                media.media_url = get_s3_object(media.media_file)

            # Generate permanent URLs for thumbnail
            if media.thumbnail_file:
                media.thumbnail_url = get_s3_object(media.thumbnail_file)

            media.save()
            total_updated += 1

        print(f"Updated {total_updated} media records with permanent URLs")


def reverse_media_urls(apps, schema_editor):
    """
    Reverse the migration by clearing the media_url and thumbnail_url fields
    """
    UnitSectionMedia = apps.get_model("properties", "UnitSectionMedia")
    UnitSectionMedia.objects.update(media_url=None, thumbnail_url=None)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0081_propertyverifieddatafields_field_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="unitsectionmedia",
            name="media_url",
            field=models.URLField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="unitsectionmedia",
            name="thumbnail_url",
            field=models.URLField(blank=True, null=True),
        ),
        migrations.RunPython(update_media_urls, reverse_code=reverse_media_urls),
    ]
