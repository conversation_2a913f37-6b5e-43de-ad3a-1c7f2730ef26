# Generated by Django 4.1.7 on 2025-05-26 08:10

from django.db import migrations, models


def set_open_for_collaboration(apps, schema_editor):
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")
    UserLevelPropertyData.objects.filter(source="WHATS_APP").update(
        open_for_collaboration=True
    )
    # IN_APP and others remain False (default)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0104_requirements_area_requirements_city_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userlevelpropertydata",
            name="open_for_collaboration",
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(
            set_open_for_collaboration, reverse_code=migrations.RunPython.noop
        ),
    ]
