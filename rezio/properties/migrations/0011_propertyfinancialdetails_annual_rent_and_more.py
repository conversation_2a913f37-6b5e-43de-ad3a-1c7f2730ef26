# Generated by Django 4.1.7 on 2024-09-10 17:01

import django.core.validators
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0010_property_user_unit_preference"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="annual_rent",
            field=models.BigIntegerField(
                blank=True,
                null=True,
                validators=[django.core.validators.MaxValueValidator(999999999999999)],
            ),
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="annual_service_charges",
            field=models.BigIntegerField(
                blank=True,
                null=True,
                validators=[django.core.validators.MaxValueValidator(999999999999999)],
            ),
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="security_deposit",
            field=models.BigIntegerField(
                blank=True,
                null=True,
                validators=[django.core.validators.MaxValueValidator(999999999999999)],
            ),
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="valuation",
            field=models.BigIntegerField(
                blank=True,
                null=True,
                validators=[django.core.validators.MaxValueValidator(999999999999999)],
            ),
        ),
        migrations.CreateModel(
            name="PropertySalesUnitHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("evidence_date", models.DateField(blank=True, null=True)),
                (
                    "evidence_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "total_sales_price",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                ("sales_price_sqft_unit", models.FloatField(blank=True, null=True)),
                ("sales_price_sqm_unit", models.FloatField(blank=True, null=True)),
                (
                    "sale_recurrence",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sales_history",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PropertyRentalUnitHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_date", models.DateField(blank=True, null=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                (
                    "evidence_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "total_rent",
                    models.BigIntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MaxValueValidator(999999999999999)
                        ],
                    ),
                ),
                ("rent_price_sqft_unit", models.FloatField(blank=True, null=True)),
                ("rent_price_sqm_unit", models.FloatField(blank=True, null=True)),
                (
                    "rent_recurrence",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="rental_history",
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.RenameField(
            model_name="propertyrentalunithistory",
            old_name="evidence_type",
            new_name="evidence",
        ),
        migrations.RenameField(
            model_name="propertysalesunithistory",
            old_name="evidence_type",
            new_name="evidence",
        ),
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="created_ts",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_updated_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertyrentalunithistory",
            name="updated_ts",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="created_ts",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_updated_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertysalesunithistory",
            name="updated_ts",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
