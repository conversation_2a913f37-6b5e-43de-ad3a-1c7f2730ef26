# Generated by Django 5.1.6 on 2025-04-18 06:47

import logging
from django.db import migrations
from rezio.properties.text_choices import PropertyHierarchy
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def add_new_attributes(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    # Get all hierarchy levels
    hierarchy_levels = HierarchyLevel.objects.all()

    # Add average_price_comparison to all hierarchy levels
    for level in hierarchy_levels:
        if level.viewed_attributes is None:
            level.viewed_attributes = []

        if "average_price_comparison" not in level.viewed_attributes:
            level.viewed_attributes.append("average_price_comparison")
            level.save()

    # Add tentative_commission_in_preferred_currency to Agent hierarchy
    agent_level = HierarchyLevel.objects.filter(name=PropertyHierarchy.AGENT).first()
    if agent_level:
        if agent_level.viewed_attributes is None:
            agent_level.viewed_attributes = []

        if (
            "tentative_commission_in_preferred_currency"
            not in agent_level.viewed_attributes
        ):
            agent_level.viewed_attributes.append(
                "tentative_commission_in_preferred_currency"
            )
        if (
            "tentative_commission_in_preferred_currency"
            not in agent_level.viewed_attributes
        ):
            agent_level.viewed_attributes.append(
                "tentative_commission_in_preferred_currency"
            )
        agent_level.save()


def remove_new_attributes(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    # Get all hierarchy levels
    hierarchy_levels = HierarchyLevel.objects.all()

    # Remove average_price_comparison from all hierarchy levels
    for level in hierarchy_levels:
        if (
            level.viewed_attributes
            and "average_price_comparison" in level.viewed_attributes
        ):
            level.viewed_attributes.remove("average_price_comparison")
            level.save()

    # Remove tentative_commission_in_preferred_currency from Agent hierarchy
    agent_level = HierarchyLevel.objects.filter(name=PropertyHierarchy.AGENT).first()
    if agent_level:
        if (
            agent_level.viewed_attributes
            and "tentative_commission_in_preferred_currency"
            in agent_level.viewed_attributes
        ):
            agent_level.viewed_attributes.remove(
                "tentative_commission_in_preferred_currency"
            )
        if (
            "tentative_commission_in_preferred_currency"
            not in agent_level.viewed_attributes
        ):
            agent_level.viewed_attributes.remove(
                "tentative_commission_in_preferred_currency"
            )
        agent_level.save()


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0085_update_property_attributes_data"),
    ]

    operations = [
        migrations.RunPython(add_new_attributes, remove_new_attributes),
    ]
