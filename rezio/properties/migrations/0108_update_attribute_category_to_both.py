# Generated by Django 4.1.7 on 2025-01-XX XX:XX

import logging
from django.db import migrations
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def update_attribute_category_to_both(apps, schema_editor):
    """
    Update attribute_category to BOTH for specific attribute names:
    - number_of_bedrooms
    - bathroom_data
    - parking_number
    """
    PropertyAttributes = apps.get_model('properties', 'PropertyAttributes')

    # Define the target attribute names
    target_attributes = [
        'number_of_bedrooms',
        'bathroom_data',
        'parking_number'
    ]

    # PropertyAttributesCategory values
    RESIDENTIAL_CATEGORY = 0
    COMMERCIAL_CATEGORY = 1
    BOTH_CATEGORY = 2

    logger.info(f"Starting update of attribute_category to BOTH for attributes: {target_attributes}")

    total_updated = 0
    total_skipped = 0
    error_count = 0

    for attribute_name in target_attributes:
        try:
            # Get all PropertyAttributes with this attribute_name
            attributes = PropertyAttributes.objects.filter(attribute_name=attribute_name)

            if attributes.exists():
                # Log current state before update
                logger.info(f"Found {attributes.count()} records for attribute_name='{attribute_name}'")

                # Track which records are being updated
                updated_records = []
                skipped_records = []

                for attr in attributes:
                    if attr.attribute_category != BOTH_CATEGORY:
                        # Store original value for logging
                        original_category = attr.attribute_category
                        original_category_name = {
                            RESIDENTIAL_CATEGORY: "RESIDENTIAL",
                            COMMERCIAL_CATEGORY: "COMMERCIAL",
                            BOTH_CATEGORY: "BOTH"
                        }.get(original_category, f"UNKNOWN({original_category})")

                        updated_records.append({
                            'id': attr.id,
                            'component_name': attr.component_name,
                            'original_category': original_category_name,
                            'hierarchy_level': str(attr.hierarchy_level)
                        })
                    else:
                        skipped_records.append(attr.id)

                # Perform bulk update for records that need updating
                if updated_records:
                    update_count = attributes.exclude(attribute_category=BOTH_CATEGORY).update(
                        attribute_category=BOTH_CATEGORY
                    )
                    total_updated += update_count

                    logger.info(
                        f"Updated {update_count} PropertyAttributes records with attribute_name='{attribute_name}' "
                        f"to attribute_category=BOTH"
                    )

                    # Log details of updated records
                    for record in updated_records:
                        logger.info(
                            f"Updated PropertyAttribute ID {record['id']}: "
                            f"component_name='{record['component_name']}', "
                            f"hierarchy_level='{record['hierarchy_level']}', "
                            f"attribute_category: {record['original_category']} -> BOTH"
                        )

                # Log skipped records
                if skipped_records:
                    total_skipped += len(skipped_records)
                    logger.info(
                        f"Skipped {len(skipped_records)} records for attribute_name='{attribute_name}' "
                        f"(already set to BOTH): IDs {skipped_records}"
                    )

            else:
                logger.warning(f"No PropertyAttributes found with attribute_name='{attribute_name}'")

        except Exception as e:
            error_count += 1
            logger.error(f"Error updating attribute_name='{attribute_name}': {str(e)}")
            continue

    logger.info(
        f"Migration completed. Total records updated: {total_updated}, "
        f"skipped: {total_skipped}, errors: {error_count}"
    )


def reverse_update_attribute_category(apps, schema_editor):
    """
    Reverse migration - this is complex because we don't know the original values
    For safety, we'll just log a warning and not perform any changes
    """
    logger.warning(
        "Reverse migration for attribute_category update is not supported as original "
        "category values are lost. Manual intervention required if rollback is needed."
    )
    logger.info(
        "To manually rollback, you would need to set attribute_category back to "
        "RESIDENTIAL (0) or COMMERCIAL (1) for the following attributes: "
        "number_of_bedrooms, bathroom_data, parking_number"
    )


class Migration(migrations.Migration):
    dependencies = [
        ('properties', '0099_convert_areas_to_sqft'),
    ]

    operations = [
        migrations.RunPython(update_attribute_category_to_both, reverse_update_attribute_category),
    ]
