# Generated by Django 4.1.7 on 2025-01-XX XX:XX

import logging

from django.db import migrations

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def update_attribute_category_to_both(apps, schema_editor):
    """
    Update attribute_category to BOTH for specific attribute names:
    - number_of_bedrooms
    - bathroom_data
    - parking_number
    """
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    # Define the target attribute names
    target_attributes = ["number_of_bedrooms", "bathroom_data", "parking_number"]

    attributes_to_update = PropertyAttributes.objects.filter(
        attribute_name__in=target_attributes
    ).update(attribute_category=2)


def reverse_update_attribute_category(apps, schema_editor):
    """
    Update attribute_category to RESIDENTIAL for specific attribute names:
    - number_of_bedrooms
    - bathroom_data
    - parking_number
    """
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    # Define the target attribute names
    target_attributes = ["number_of_bedrooms", "bathroom_data", "parking_number"]

    attributes_to_update = PropertyAttributes.objects.filter(
        attribute_name__in=target_attributes
    ).update(attribute_category=0)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0107_requirements_property_unit_type_and_more"),
    ]

    operations = [
        migrations.RunPython(
            update_attribute_category_to_both, reverse_update_attribute_category
        ),
    ]
