from django.db import migrations
from countryinfo import CountryInfo
import unicodedata
import re


def normalize_state_name(state_name):
    """
    Normalize state name by:
    1. Converting to lowercase
    2. Removing special characters
    3. Normalizing unicode characters
    """
    # Convert to lowercase
    state_name = state_name.lower()
    
    # Normalize unicode characters (e.g., ā -> a)
    state_name = unicodedata.normalize('NFKD', state_name)
    state_name = ''.join([c for c in state_name if not unicodedata.combining(c)])
    
    # Remove special characters and extra spaces
    state_name = re.sub(r'[^a-z0-9\s]', '', state_name)
    state_name = re.sub(r'\s+', ' ', state_name).strip()
    
    return state_name


def add_indian_states(apps, schema_editor):
    """
    Migration to add Indian states and union territories to the database.
    Uses countryinfo package to get the latest list of states.
    Handles special characters and updates existing states.
    """
    Country = apps.get_model('properties', 'Country')
    State = apps.get_model('properties', 'State')

    # Get or create India country
    india, created = Country.objects.get_or_create(
        name='India',
        defaults={
            'short_name': 'IN',
            'phone_code': '+91',
            'is_active': True,
            'is_deleted': False
        }
    )

    try:
        # Get states from countryinfo
        country_info = CountryInfo('India')
        indian_states = country_info.provinces()
    except Exception as e:
        # Fallback to hardcoded list if countryinfo fails
        indian_states = [
            # States (28)
            "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh",
            "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka",
            "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram",
            "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu",
            "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal",
            
            # Union Territories (8)
            "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu",
            "Delhi", "Jammu and Kashmir", "Ladakh", "Lakshadweep", "Puducherry"
        ]

    # Get all existing states for India
    existing_states = State.objects.filter(country=india)
    existing_state_map = {
        normalize_state_name(state.name): state 
        for state in existing_states
    }

    # Process each state
    states_to_create = []
    states_to_update = []

    for state_name in indian_states:
        normalized_name = normalize_state_name(state_name)
        
        if normalized_name in existing_state_map:
            # Update existing state
            existing_state = existing_state_map[normalized_name]
            if existing_state.name != state_name or not existing_state.is_active:
                existing_state.name = state_name
                existing_state.is_active = True
                existing_state.is_deleted = False
                states_to_update.append(existing_state)
        else:
            # Create new state
            states_to_create.append(
                State(
                    country=india,
                    name=state_name,
                    is_active=True,
                    is_deleted=False
                )
            )

    # Bulk create new states
    if states_to_create:
        State.objects.bulk_create(states_to_create, ignore_conflicts=True)
        print(f"Created {len(states_to_create)} new states")

    # Bulk update existing states
    if states_to_update:
        State.objects.bulk_update(
            states_to_update,
            fields=['name', 'is_active', 'is_deleted']
        )
        print(f"Updated {len(states_to_update)} existing states")


def reverse_func(apps, schema_editor):
    """
    Reverse migration to remove Indian states from the database.
    """
    Country = apps.get_model('properties', 'Country')
    State = apps.get_model('properties', 'State')

    # Get India country
    india = Country.objects.filter(name='India').first()
    if india:
        # Delete all states associated with India
        State.objects.filter(country=india).delete()


class Migration(migrations.Migration):
    dependencies = [
        ('properties', '0088_update_hierarchy_attribute_name'),
    ]

    operations = [
        migrations.RunPython(add_indian_states, reverse_func),
    ] 