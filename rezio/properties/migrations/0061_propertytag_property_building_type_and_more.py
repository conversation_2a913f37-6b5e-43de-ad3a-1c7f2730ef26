# Generated by Django 4.1.7 on 2025-01-22 03:57

from django.db import migrations, models


def add_default_property_tag(apps, schema_editor):
    property_tag = apps.get_model("properties", "PropertyTag")

    TAGS = ["Bank", "Service Center", "Show Room", "ATM", "Retail"]

    for tag in TAGS:
        property_tag.objects.get_or_create(name=tag)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0060_add_heirarchy_levels"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyTag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
            ],
        ),
        migrations.AddField(
            model_name="property",
            name="building_type",
            field=models.IntegerField(
                blank=True,
                choices=[
                    (0, "Independent House"),
                    (1, "Business Park"),
                    (2, "Mall"),
                    (3, "Standalone Building"),
                    (4, "Independent Shop"),
                ],
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="property",
            name="property_category",
            field=models.IntegerField(
                choices=[(0, "Residential"), (1, "Commercial")], default=0
            ),
        ),
        migrations.AddField(
            model_name="property",
            name="total_floors",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyavailabilityandstatus",
            name="rent_available_start_date",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_common_lift",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_corner_property",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_property_on_main_road",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_public_parking",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_security_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="is_water_storage_available",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="no_of_personal_lift",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="no_of_private_restrooms",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="no_of_reserved_parking",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="no_of_shared_restrooms",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="is_price_negotiable",
            field=models.BooleanField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="preferred_payment_frequency",
            field=models.IntegerField(
                blank=True,
                choices=[
                    (0, "Yearly"),
                    (1, "Bi-yearly"),
                    (2, "Quarterly"),
                    (3, "Monthly"),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="property",
            name="property_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Apartment", "Apartment"),
                    ("Villa", "Villa"),
                    ("Townhouse", "Townhouse"),
                    ("Office Space", "Office Space"),
                    ("Co-working", "Co-working"),
                    ("Shop", "Shop"),
                    ("Showroom", "Showroom"),
                    ("Godown/Warehouse", "Godown/Warehouse"),
                    ("Industrial Shed", "Industrial Shed"),
                    ("Industrial Building", "Industrial Building"),
                    ("Hospital/Clinic", "Hospital/Clinic"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="propertyfeatures",
            name="tags",
            field=models.ManyToManyField(to="properties.propertytag"),
        ),
        migrations.RunPython(add_default_property_tag),
    ]
