# Generated by Django 5.1.6 on 2025-03-17 14:53

from django.db import migrations


def update_attribute_names(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    # Update parking_data to residential_parking_data
    PropertyAttributes.objects.filter(
        component_name="property_specifications",
        attribute_category=0,  # for residential
        attribute_name="parking_data",
    ).update(attribute_name="residential_parking_data")

    # Update parking_data to commercial_parking_data
    PropertyAttributes.objects.filter(
        component_name="property_unit_features",
        attribute_category=1,  # for commercial
        attribute_name="parking_data",
    ).update(attribute_name="commercial_parking_data")

    # Remove _data suffix from financial attributes
    financial_attributes = [
        "annual_service_charge_data",
        "annual_rent_data",
        "security_deposit_data",
        "asking_price_data",
        "original_price_data",
        "valuation_data",
    ]

    for attr in financial_attributes:
        PropertyAttributes.objects.filter(attribute_name=attr).update(
            attribute_name=attr.replace("_data", "")
        )


def reverse_attribute_names(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    # Reverse parking data names
    PropertyAttributes.objects.filter(attribute_name="residential_parking_data").update(
        attribute_name="parking_data"
    )

    PropertyAttributes.objects.filter(attribute_name="commercial_parking_data").update(
        attribute_name="parking_data"
    )

    # Add back _data suffix to financial attributes
    financial_attributes = [
        "annual_service_charge",
        "annual_rent",
        "security_deposit",
        "asking_price",
        "original_price",
        "valuation",
    ]

    for attr in financial_attributes:
        PropertyAttributes.objects.filter(attribute_name=attr).update(
            attribute_name=f"{attr}_data"
        )


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0079_update_verify_blue_tick_data"),
    ]

    operations = [
        migrations.RunPython(update_attribute_names, reverse_attribute_names),
    ]
