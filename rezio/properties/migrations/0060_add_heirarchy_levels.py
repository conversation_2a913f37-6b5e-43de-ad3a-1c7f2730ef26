from django.db import migrations


def populate_hierarchy_levels(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")
    HierarchyLevel.objects.bulk_create(
        [
            HierarchyLevel(name="Owner", level=1),
            HierarchyLevel(name="Co-owner", level=1),
            HierarchyLevel(name="Agent", level=2),
            HierarchyLevel(name="Collaborator", level=3),
            HierarchyLevel(name="Public", level=4),
        ]
    )


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0059_heirarchy_level_models"),
    ]

    operations = [
        migrations.RunPython(populate_hierarchy_levels),
    ]
