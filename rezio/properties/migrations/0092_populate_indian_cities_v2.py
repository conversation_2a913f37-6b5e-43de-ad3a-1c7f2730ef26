from django.db import migrations
from indian_cities.dj_city import cities as INDIAN_CITIES_DATA

# Data structure of cities: ((state_name, ((city_name, city_name), ...)), ...)


def populate_cities_from_data(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    State = apps.get_model("properties", "State")
    City = apps.get_model("properties", "City")

    # Get or create India
    india, _ = Country.objects.get_or_create(
        name="India",
        defaults={"short_name": "IN", "is_active": True, "is_deleted": False},
    )

    # Process each state and its cities
    for state_name, cities in INDIAN_CITIES_DATA:
        try:
            state = State.objects.get(name=state_name, country=india)

            # Process each city
            for city_tuple in cities:
                city_name = city_tuple[0]  # Take first element since both are same

                # Create city if it doesn't exist
                if not City.objects.filter(
                    name=city_name, state=state, country=india
                ).exists():
                    City.objects.create(
                        name=city_name,
                        state=state,
                        country=india,
                        is_active=True,
                        is_deleted=False,
                    )
        except State.DoesNotExist:
            print(f"State not found: {state_name}")
            continue


def reverse_cities_population(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    City = apps.get_model("properties", "City")

    try:
        india = Country.objects.get(name="India")
        City.objects.filter(country=india).delete()
    except Country.DoesNotExist:
        pass


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0091_update_financial_attributes_name"),
    ]

    operations = [
        migrations.RunPython(populate_cities_from_data, reverse_cities_population),
    ]
