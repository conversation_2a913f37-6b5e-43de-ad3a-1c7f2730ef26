# Generated by Django 4.1.7 on 2024-09-17 21:15

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0021_agentprofile_commission_percentage'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('properties', '0022_propertyfinancialdetails_created_by_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AgentAssociatedProperty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_ts', models.DateTimeField(auto_now_add=True)),
                ('updated_ts', models.DateTimeField(auto_now=True)),
                ('is_associated', models.BooleanField(default=True)),
                ('agent_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.agentprofile')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(class)s_created_by', to=settings.AUTH_USER_MODEL)),
                ('property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='properties.property')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
