# Generated by Django 4.1.7 on 2025-04-04 09:41

from django.db import migrations, models


def populate_number_of_bathrooms(apps, schema_editor):
    Property = apps.get_model("properties", "Property")
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")

    # Update Property model
    for property_obj in Property.objects.all():
        common_bathrooms = property_obj.number_of_common_bathrooms or 0
        attached_bathrooms = property_obj.number_of_attached_bathrooms or 0
        powder_rooms = property_obj.number_of_powder_rooms or 0

        property_obj.number_of_bathrooms = (
            common_bathrooms + attached_bathrooms + powder_rooms
        )
        property_obj.save()

    # Update UserLevelPropertyData model since it's also storing bathroom data
    for user_level_property in UserLevelPropertyData.objects.all():
        common_bathrooms = user_level_property.number_of_common_bathrooms or 0
        attached_bathrooms = user_level_property.number_of_attached_bathrooms or 0
        powder_rooms = user_level_property.number_of_powder_rooms or 0

        user_level_property.number_of_bathrooms = (
            common_bathrooms + attached_bathrooms + powder_rooms
        )
        user_level_property.save()


def reverse_populate_number_of_bathrooms(apps, schema_editor):
    Property = apps.get_model("properties", "Property")
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")

    # Reset number_of_bathrooms to None for both models
    Property.objects.all().update(number_of_bathrooms=None)
    UserLevelPropertyData.objects.all().update(number_of_bathrooms=None)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0083_seed_location_lat_long"),
    ]

    operations = [
        migrations.AddField(
            model_name="property",
            name="number_of_bathrooms",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="userlevelpropertydata",
            name="number_of_bathrooms",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.RunPython(
            populate_number_of_bathrooms, reverse_populate_number_of_bathrooms
        ),
    ]
