# Generated by Django 4.1.7 on 2024-08-09 08:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("user", "0014_alter_agency_data_source"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Area",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=64)),
                ("is_active", models.BooleanField(default=False)),
                ("is_deleted", models.<PERSON>oleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="City",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=64)),
                ("is_active", models.<PERSON>olean<PERSON>ield(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Community",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("property_monitor_location_id", models.IntegerField()),
                ("name", models.CharField(max_length=128)),
                ("sub_loc_1", models.CharField(max_length=128)),
                ("sub_loc_2", models.CharField(max_length=128)),
                ("sub_loc_3", models.CharField(max_length=128)),
                ("sub_loc_4", models.CharField(max_length=128)),
                ("sub_loc_5", models.CharField(max_length=128)),
                ("is_active", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "added_by",
                    models.CharField(
                        choices=[
                            ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                            ("DLD", "DLD"),
                            ("USER_ADDED", "USER_ADDED"),
                            ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                        ],
                        default="PROPERTY_MONITOR",
                        max_length=32,
                    ),
                ),
                (
                    "area",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.area",
                    ),
                ),
                (
                    "city",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.city",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Country",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=64)),
                ("short_name", models.CharField(max_length=12)),
                ("phone_code", models.CharField(max_length=12)),
                ("is_active", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="State",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=64)),
                ("is_active", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.country",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Property",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "property_monitor_unit_id",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "property_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Apartment", "Apartment"),
                            ("Villa", "Villa"),
                            ("Townhouse", "Townhouse"),
                        ],
                        max_length=32,
                        null=True,
                    ),
                ),
                ("floor_number", models.IntegerField(blank=True, null=True)),
                ("postal_code", models.IntegerField(blank=True, null=True)),
                ("total_area", models.FloatField(blank=True, null=True)),
                ("carpet_area", models.FloatField(blank=True, null=True)),
                ("balcony_area", models.FloatField(blank=True, null=True)),
                ("number_of_bedrooms", models.IntegerField(blank=True, null=True)),
                (
                    "number_of_common_bathrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "number_of_attached_bathrooms",
                    models.IntegerField(blank=True, null=True),
                ),
                ("number_of_powder_rooms", models.IntegerField(blank=True, null=True)),
                ("parking_available", models.BooleanField(default=False)),
                (
                    "number_of_covered_parking",
                    models.IntegerField(blank=True, null=True),
                ),
                ("number_of_open_parking", models.IntegerField(blank=True, null=True)),
                ("parking_number", models.TextField(blank=True, null=True)),
                ("is_draft", models.BooleanField(default=True)),
                (
                    "area",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.area",
                    ),
                ),
                (
                    "city",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.city",
                    ),
                ),
                (
                    "community",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.community",
                    ),
                ),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.country",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="user.role"
                    ),
                ),
                (
                    "state",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.state",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="community",
            name="state",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="properties.state",
            ),
        ),
        migrations.AddField(
            model_name="city",
            name="country",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="properties.country"
            ),
        ),
        migrations.AddField(
            model_name="city",
            name="state",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="properties.state",
            ),
        ),
        migrations.AddField(
            model_name="area",
            name="city",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="properties.city",
            ),
        ),
        migrations.AddField(
            model_name="area",
            name="country",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to="properties.country"
            ),
        ),
        migrations.AddField(
            model_name="area",
            name="state",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="properties.state",
            ),
        ),
    ]
