# Generated by Django 4.1.7 on 2025-01-22 07:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0061_propertytag_property_building_type_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_common_lift",
            new_name="common_lift",
        ),
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_corner_property",
            new_name="corner_property",
        ),
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_property_on_main_road",
            new_name="property_on_main_road",
        ),
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_public_parking",
            new_name="public_parking",
        ),
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_security_available",
            new_name="security_available",
        ),
        migrations.RenameField(
            model_name="propertyfeatures",
            old_name="is_water_storage_available",
            new_name="water_storage_available",
        ),
        migrations.RemoveField(
            model_name="propertyfinancialdetails",
            name="is_price_negotiable",
        ),
        migrations.AddField(
            model_name="propertyfinancialdetails",
            name="price_negotiable",
            field=models.BooleanField(default=False),
        ),
    ]
