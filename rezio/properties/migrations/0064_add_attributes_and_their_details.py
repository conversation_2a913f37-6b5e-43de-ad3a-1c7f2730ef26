import math

from django.db import migrations
import pandas as pd


def populate_from_excel(apps, schema_editor):
    # Get the model classes
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    # Load the Excel sheet into a DataFrame
    file_path = "https://rezio-static-files.s3.me-central-1.amazonaws.com/Backend+_+Heirarchy+flow+attributes+.xlsx"
    df = pd.read_excel(file_path)

    # Create a mapping for hierarchy levels
    hierarchy_levels = {
        "Owner": HierarchyLevel.objects.get(name="Owner"),
        "Co-owner": HierarchyLevel.objects.get(name="Co-owner"),
        "Agent": HierarchyLevel.objects.get(name="Agent"),
        "Collaborator": HierarchyLevel.objects.get(name="Collaborator"),
        "Public": HierarchyLevel.objects.get(name="Public"),
    }

    # Subscription level mapping
    subscription_levels = {
        "basic": 1,  # Replace with actual value in your choices
        "premium": 2,  # Replace with actual value in your choices
    }

    attribute_category = {
        "Residential": 0,  # Replace with actual value in your choices
        "Commercial": 1,  # Replace with actual value in your choices
        "Both": 2,  # Replace with actual value in your choices
    }

    # Iterate over each row in the DataFrame
    attributes = []
    for _, row in df.iterrows():
        print(row)
        if math.isnan(row["is_public (T/F)"]):
            continue
        attributes.append(
            PropertyAttributes(
                component_name=row["Component"],  # Column name in Excel sheet
                attribute_name=row["BE Attribute"],
                is_public=row["is_public (T/F)"],
                hierarchy_level=hierarchy_levels[row["heirarchy_level"].capitalize()],
                subscription_level=subscription_levels[row["subscription_level"]],
                attribute_category=attribute_category[
                    row["Attribute Category (Residentail/Commercial)"]
                ],
            )
        )

    # Bulk create attributes in the database
    PropertyAttributes.objects.bulk_create(attributes)
    print(f"Successfully added {len(attributes)} attributes to the database.")


def migrate(apps, schema_editor):
    populate_from_excel(apps, schema_editor)


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0063_hierarchylevel_metadata_and_more"),
    ]

    operations = [
        migrations.RunPython(migrate),
    ]
