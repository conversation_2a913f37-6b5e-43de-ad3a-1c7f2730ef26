# Generated by Django 4.1.7 on 2025-04-18 08:12
import logging

from django.db import migrations

from rezio.properties.text_choices import PropertyHierarchy
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def add_unit_number_to_editable_attributes(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    target_names = [
        PropertyHierarchy.AGENT,
        PropertyHierarchy.OWNER,
        PropertyHierarchy.CO_OWNER,
    ]

    # Add unit_number to editable_attributes required hierarchy levels
    hierarchy_levels = HierarchyLevel.objects.filter(name__in=target_names)
    for level in hierarchy_levels:
        if level.editable_attributes is None:
            level.editable_attributes = []

        if "unit_number" not in level.editable_attributes:
            level.editable_attributes.append("unit_number")
            level.save()


def remove_unit_number_from_editable_attributes(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")
    target_names = [
        PropertyHierarchy.AGENT,
        PropertyHierarchy.OWNER,
        PropertyHierarchy.CO_OWNER,
    ]

    # Filter entries with the specified names
    hierarchy_levels = HierarchyLevel.objects.filter(name__in=target_names)

    for level in hierarchy_levels:
        if level.editable_attributes and "unit_number" in level.editable_attributes:
            level.editable_attributes.remove("unit_number")
            level.save()


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0086_add_new_hierarchy_attributes"),
    ]

    operations = [
        migrations.RunPython(
            add_unit_number_to_editable_attributes,
            remove_unit_number_from_editable_attributes,
        ),
    ]
