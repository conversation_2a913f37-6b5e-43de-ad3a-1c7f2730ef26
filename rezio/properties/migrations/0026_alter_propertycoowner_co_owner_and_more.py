# Generated by Django 4.1.7 on 2024-09-20 05:51

from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0021_agentprofile_commission_percentage"),
        ("properties", "0025_remove_unregisteredcoowner_property"),
    ]

    operations = [
        migrations.AlterField(
            model_name="propertycoowner",
            name="co_owner",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="user.investorprofile",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="unregisteredcoowner",
            name="email",
            field=models.EmailField(max_length=254),
        ),
        migrations.AlterField(
            model_name="unregisteredcoowner",
            name="phone_number",
            field=phonenumber_field.modelfields.PhoneNumberField(
                max_length=128, region=None
            ),
        ),
    ]
