# Generated by Django 4.1.7 on 2024-11-26 09:57

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0047_agentassociatedproperty_agent_action_status_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentassociatedproperty",
            name="request_agent_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("0", "Open to all agents"),
                    ("1", "Allow access to selective agents"),
                    ("2", "Give access to an exclusive agent"),
                ],
                max_length=32,
                null=True,
            ),
        ),
    ]
