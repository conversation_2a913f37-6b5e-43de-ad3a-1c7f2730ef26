import requests
from django.db import migrations

JSON_S3_URL = "https://rezio-static-files.s3.me-central-1.amazonaws.com/cities.json"  # Replace with your actual URL


def populate_cities_from_json(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    State = apps.get_model("properties", "State")
    City = apps.get_model("properties", "City")

    india, _ = Country.objects.get_or_create(
        name="India",
        defaults={"short_name": "IN", "is_active": True, "is_deleted": False},
    )

    response = requests.get(JSON_S3_URL)
    response.raise_for_status()
    cities_data = response.json()

    for city_data in cities_data:
        state_name = city_data["state"].strip()
        city_name = city_data["name"].strip()

        try:
            state = State.objects.get(name=state_name, country=india)
        except State.DoesNotExist:
            continue

        if not City.objects.filter(name=city_name, state=state, country=india).exists():
            City.objects.create(
                name=city_name,
                state=state,
                country=india,
                is_active=True,
                is_deleted=False,
            )


def reverse_indian_cities(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    City = apps.get_model("properties", "City")

    try:
        india = Country.objects.get(name="India")
        City.objects.filter(country=india).delete()
    except Country.DoesNotExist:
        pass


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0089_add_indian_states"),
    ]

    operations = [
        migrations.RunPython(populate_cities_from_json, reverse_indian_cities),
    ]
