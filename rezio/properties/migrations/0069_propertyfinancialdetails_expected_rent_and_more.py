# Generated by Django 4.1.7 on 2025-02-04 15:00

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0068_propertyfeatures_is_lift_available_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='propertyfinancialdetails',
            name='expected_rent',
            field=models.BigIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)]),
        ),
        migrations.AddField(
            model_name='propertyfinancialdetails',
            name='expected_security_deposit',
            field=models.BigIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)]),
        ),
    ]
