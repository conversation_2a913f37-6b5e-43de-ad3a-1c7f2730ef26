# Generated by Django 4.1.7 on 2025-04-24 07:28

from django.db import migrations


def update_property_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(
        component_name="scrollable_component", attribute_name="number_of_bathrooms"
    ).update(attribute_name="bathroom_data")


def reverse_update_property_attributes(apps, schema_editor):
    PropertyAttributes = apps.get_model("properties", "PropertyAttributes")

    PropertyAttributes.objects.filter(
        component_name="property_specifications", attribute_name="bathroom_data"
    ).update(attribute_name="number_of_bathrooms")


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0087_add_unit_number_in_hierarchy"),
    ]

    operations = [
        migrations.RunPython(
            update_property_attributes, reverse_update_property_attributes
        ),
    ]
