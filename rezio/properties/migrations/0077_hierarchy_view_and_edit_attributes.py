# Generated by Django 5.1.6 on 2025-03-04 04:05
import logging
from django.db import migrations
from rezio.properties.text_choices import PropertyHierarchy
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def update_hierarchy_fields(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    hierarchy_data = {
        PropertyHierarchy.OWNER: {
            "viewed_attributes": [
                "building_street",
                "unit_number",
                "state",
                "country",
                "city",
                "pin_code",
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "electricity_water_number",
                "owner_details",
                "co_owner_details",
                "agent_details",
                "address",
                "building_name",
                "country",
                "display_price_in_property_currency",
                "display_price_in_preferred_currency",
                "property_currency_code",
                "preferred_currency_code",
                "exchange_rate",
                "is_valuation",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "gross_yield",
                "net_yield",
                "rent_contract",
                "sales_history",
                "rental_history",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "default_image",
                "unit_images",
                "asking_price",
                "original_price",
                "valuation",
                "gains_data",
                "action_button",
                "owner_verified",
                "number_of_bedrooms",
                "number_of_bathrooms",
                "intent",
                "distressed_deal",
                "parking_number",
            ],
            "editable_attributes": [
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "electricity_water_number",
                "agent_details",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "asking_price",
                "original_price",
                "valuation",
                "enable_post_handover",
                "post_handover_time_frame",
                "on_handover",
                "parking_number",
                "number_of_bedrooms",
            ],
        },
        PropertyHierarchy.CO_OWNER: {
            "viewed_attributes": [
                "building_street",
                "unit_number",
                "state",
                "country",
                "city",
                "pin_code",
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "electricity_water_number",
                "owner_details",
                "co_owner_details",
                "agent_details",
                "address",
                "building_name",
                "country",
                "display_price_in_property_currency",
                "display_price_in_preferred_currency",
                "property_currency_code",
                "preferred_currency_code",
                "exchange_rate",
                "is_valuation",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "gross_yield",
                "net_yield",
                "rent_contract",
                "sales_history",
                "rental_history",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "default_image",
                "unit_images",
                "asking_price",
                "original_price",
                "valuation",
                "gains_data",
                "number_of_bedrooms",
                "number_of_bathrooms",
                "intent",
                "distressed_deal",
                "parking_number",
            ],
            "editable_attributes": [
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "electricity_water_number",
                "agent_details",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "asking_price",
                "original_price",
                "valuation",
                "enable_post_handover",
                "post_handover_time_frame",
                "on_handover",
                "parking_number",
                "number_of_bedrooms",
            ],
        },
        PropertyHierarchy.AGENT: {
            "viewed_attributes": [
                "building_street",
                "unit_number",
                "state",
                "country",
                "city",
                "pin_code",
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "owner_details",
                "co_owner_details",
                "agent_details",
                "address",
                "building_name",
                "country",
                "display_price_in_property_currency",
                "display_price_in_preferred_currency",
                "property_currency_code",
                "preferred_currency_code",
                "exchange_rate",
                "is_valuation",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "gross_yield",
                "net_yield",
                "rent_contract",
                "sales_history",
                "rental_history",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "default_image",
                "unit_images",
                "asking_price",
                "original_price",
                "valuation",
                "gains_data",
                "action_button",
                "owner_verified",
                "number_of_bedrooms",
                "number_of_bathrooms",
                "intent",
                "distressed_deal",
                "parking_number",
            ],
            "editable_attributes": [
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "floor_number",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "asking_price",
                "original_price",
                "valuation",
                "enable_post_handover",
                "post_handover_time_frame",
                "on_handover",
                "parking_number",
                "number_of_bedrooms",
            ],
        },
        PropertyHierarchy.PUBLIC: {
            "viewed_attributes": [
                "building_street",
                "state",
                "country",
                "city",
                "pin_code",
                "property_type",
                "building_type",
                "total_area",
                "carpet_area",
                "balcony_area",
                "bedroom_data",
                "bathroom_data",
                "residential_parking_data",
                "total_floors",
                "branded_building",
                "furnished",
                "premium_view",
                "water_storage_available",
                "commercial_parking_data",
                "security_available",
                "restroom_data",
                "lift_data",
                "parking_data",
                "property_on_main_road",
                "corner_property",
                "ideal_for",
                "floor_plan",
                "owner_details",
                "co_owner_details",
                "agent_details",
                "address",
                "building_name",
                "country",
                "display_price_in_property_currency",
                "display_price_in_preferred_currency",
                "property_currency_code",
                "preferred_currency_code",
                "exchange_rate",
                "is_valuation",
                "property_type",
                "property_status",
                "handover_date",
                "payment_plan_on_handover",
                "payment_during_construction",
                "payment_plan_document",
                "occupancy_status",
                "tenancy_type",
                "start_date",
                "end_date",
                "annual_service_charge",
                "annual_rent",
                "security_deposit",
                "gross_yield",
                "net_yield",
                "rent_contract",
                "sales_history",
                "rental_history",
                "preferred_payment_frequency",
                "available_from",
                "price_negotiable",
                "default_image",
                "unit_images",
                "asking_price",
                "original_price",
                "valuation",
                "gains_data",
                "number_of_bedrooms",
                "number_of_bathrooms",
                "intent",
                "distressed_deal",
                "parking_number",
            ],
            "editable_attributes": [],
        },
    }

    # Update each hierarchy level with its corresponding attributes
    for hierarchy_name, data in hierarchy_data.items():
        try:
            hierarchy = HierarchyLevel.objects.get(name=hierarchy_name)
            hierarchy.viewed_attributes = data["viewed_attributes"]
            hierarchy.editable_attributes = data["editable_attributes"]
            hierarchy.save()
        except HierarchyLevel.DoesNotExist:
            logger.error(f"Hierarchy level {hierarchy_name} not found")


def reverse_migration(apps, schema_editor):
    HierarchyLevel = apps.get_model("properties", "HierarchyLevel")

    # Reset the attributes to empty lists
    HierarchyLevel.objects.all().update(viewed_attributes=[], editable_attributes=[])


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0076_hierarchylevel_editable_attributes_and_more"),
    ]

    operations = [
        migrations.RunPython(update_hierarchy_fields, reverse_migration),
    ]
