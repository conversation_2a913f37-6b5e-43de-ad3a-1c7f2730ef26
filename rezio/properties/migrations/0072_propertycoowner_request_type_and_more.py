# Generated by Django 4.1.7 on 2025-02-06 15:29

from django.db import migrations, models
import rezio.properties.models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0071_add_email_templates"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertycoowner",
            name="request_type",
            field=models.CharField(
                choices=[("0", "Co-owner Invite"), ("1", "Request to Owner")],
                default="0",
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="propertycoowner",
            name="ownership_percentage",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                max_digits=5,
                null=True,
                validators=[rezio.properties.models.validate_ownership_percentage],
            ),
        ),
    ]
