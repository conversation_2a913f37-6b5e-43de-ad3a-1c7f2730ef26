# Generated by Django 4.1.7 on 2024-11-29 07:10

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0050_agentassociatedproperty_is_request_expired"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertycoowner",
            name="action_status",
            field=models.CharField(
                choices=[("0", "Pending"), ("1", "Declined"), ("2", "Accepted")],
                default="0",
                max_length=32,
            ),
        ),
        migrations.AddField(
            model_name="propertycoowner",
            name="is_associated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="propertycoowner",
            name="is_request_expired",
            field=models.BooleanField(default=False),
        ),
    ]
