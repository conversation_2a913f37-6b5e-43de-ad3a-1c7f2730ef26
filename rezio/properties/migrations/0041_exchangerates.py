# Generated by Django 4.1.7 on 2024-11-07 11:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0040_remove_propertyfinancialdetails_currency_code_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExchangeRates",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("base_currency", models.CharField(max_length=12)),
                ("target_currency", models.Char<PERSON>ield(max_length=12)),
                ("exchange_rate", models.FloatField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
