# Generated by Django 4.1.7 on 2025-05-13 13:07

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('user', '0059_agentprofile_trial_premium_card'),
        ('properties', '0095_userlevelpropertydata_meta_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Requirements',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_ts', models.DateTimeField(auto_now_add=True)),
                ('updated_ts', models.DateTimeField(auto_now=True)),
                ('property_type', models.CharField(blank=True, choices=[('Apartment', 'Apartment'), ('Villa', 'Villa'), ('Townhouse', 'Townhouse'), ('Office Space', 'Office Space'), ('Co-working', 'Co-working'), ('Shop', 'Shop'), ('Showroom', 'Showroom'), ('Godown/Warehouse', 'Godown/Warehouse'), ('Industrial Shed', 'Industrial Shed'), ('Industrial Building', 'Industrial Building'), ('Hospital/Clinic', 'Hospital/Clinic')], max_length=32, null=True)),
                ('property_category', models.IntegerField(choices=[(0, 'Residential'), (1, 'Commercial')], default=0)),
                ('building_type', models.IntegerField(blank=True, choices=[(0, 'Independent House'), (1, 'Business Park'), (2, 'Mall'), (3, 'Standalone Building'), (4, 'Independent Shop')], null=True)),
                ('floor_number', models.CharField(blank=True, max_length=16, null=True)),
                ('total_floors', models.IntegerField(blank=True, null=True)),
                ('total_area', models.FloatField(blank=True, null=True)),
                ('carpet_area', models.FloatField(blank=True, null=True)),
                ('number_of_bedrooms', models.IntegerField(blank=True, null=True)),
                ('number_of_bathrooms', models.IntegerField(blank=True, null=True)),
                ('property_currency_code', models.CharField(blank=True, default='USD', max_length=32, null=True)),
                ('annual_rent', models.BigIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(999999999999999)])),
                ('price_negotiable', models.BooleanField(default=False)),
                ('is_archived', models.BooleanField(default=False)),
                ('community', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='properties.community')),
                ('country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='properties.country')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by_role', to=settings.AUTH_USER_MODEL)),
                ('created_by_role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to='user.role')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by_role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by_role', to='user.role')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
