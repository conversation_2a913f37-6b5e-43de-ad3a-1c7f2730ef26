# Generated by Django 4.1.7 on 2024-08-23 08:04

from django.db import migrations, models
import django.db.models.deletion

from rezio.rezio import settings


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0005_alter_property_created_by_alter_property_updated_by"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyAvailabilityAndStatus",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        blank=True,
                        choices=[("0", "Ready"), ("1", "Under Development/Off Plan")],
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "handover_date",
                    models.CharField(blank=True, max_length=32, null=True),
                ),
                (
                    "occupancy_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("0", "Rented"),
                            ("1", "Vacant"),
                            ("2", "Owner Occupied"),
                            ("3", "Holiday Home"),
                        ],
                        max_length=32,
                        null=True,
                    ),
                ),
                (
                    "rent_contract_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                ("enable_payment_plan", models.BooleanField(default=False)),
                (
                    "during_construction",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                (
                    "on_handover",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=5, null=True
                    ),
                ),
                ("enable_post_handover", models.BooleanField(default=False)),
                (
                    "post_handover_time_frame",
                    models.IntegerField(blank=True, null=True),
                ),
                (
                    "ownership_proof_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "property",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.property",
                    ),
                ),
            ],
        ),
        migrations.RemoveField(
            model_name="paymentplan",
            name="property",
        ),
        migrations.DeleteModel(
            name="AvailabilityStatus",
        ),
        migrations.DeleteModel(
            name="PaymentPlan",
        ),
        migrations.CreateModel(
            name="PropertyCompletionState",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "data_source",
                    models.CharField(
                        choices=[
                            ("SYSTEM_DEFAULT", "SYSTEM_DEFAULT"),
                            ("DLD", "DLD"),
                            ("USER_ADDED", "USER_ADDED"),
                            ("PROPERTY_MONITOR", "PROPERTY_MONITOR"),
                        ],
                        default="PROPERTY_MONITOR",
                        max_length=32,
                    ),
                ),
                (
                    "state_completed",
                    models.CharField(
                        choices=[
                            ("location_details", "Location Details"),
                            (
                                "community_and_building_info",
                                "Community & Building Info",
                            ),
                            ("property_specifications", "Property Specifications"),
                            ("availability_and_status", "Availability & Status"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="%(class)s_created_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="completion_states",
                        to="properties.property",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
