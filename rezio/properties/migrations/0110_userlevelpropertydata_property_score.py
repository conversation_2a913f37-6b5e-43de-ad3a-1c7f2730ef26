# Generated by Django 4.1.7 on 2025-06-04 05:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0109_alter_propertyfeatures_furnished_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userlevelpropertydata",
            name="property_score",
            field=models.FloatField(
                blank=True,
                default=0.0,
                help_text="Calculated property score based on weighted attributes",
                null=True,
            ),
        ),
    ]
