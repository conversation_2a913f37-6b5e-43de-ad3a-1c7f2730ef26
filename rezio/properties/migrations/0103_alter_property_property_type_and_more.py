# Generated by Django 4.1.7 on 2025-05-25 13:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0102_calculate_unlock_properties'),
    ]

    operations = [
        migrations.AlterField(
            model_name='property',
            name='property_type',
            field=models.CharField(blank=True, choices=[('Apartment', 'Apartment'), ('Villa', 'Villa'), ('Townhouse', 'Townhouse'), ('Residential Plot', 'Residential Plot'), ('Residential Land', 'Residential Land'), ('Office Space', 'Office Space'), ('Co-working', 'Co-working'), ('Shop', 'Shop'), ('Showroom', 'Showroom'), ('Godown/Warehouse', 'Godown/Warehouse'), ('Industrial Shed', 'Industrial Shed'), ('Industrial Building', 'Industrial Building'), ('Hospital/Clinic', 'Hospital/Clinic'), ('School', 'School'), ('Retail Space', 'Retail Space'), ('Hotel', 'Hotel'), ('Guest House', 'Guest House'), ('S.C.O Plot', 'S.C.O Plot'), ('Commercial Plot', 'Commercial Plot'), ('Commercial Land', 'Commercial Land')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='property',
            name='property_unit_type',
            field=models.CharField(choices=[('0', 'sqft'), ('1', 'sqm'), ('2', 'sqyd'), ('3', 'acre'), ('4', 'hectare'), ('5', 'bigha')], default='0', max_length=32),
        ),
        migrations.AlterField(
            model_name='requirements',
            name='property_type',
            field=models.CharField(blank=True, choices=[('Apartment', 'Apartment'), ('Villa', 'Villa'), ('Townhouse', 'Townhouse'), ('Residential Plot', 'Residential Plot'), ('Residential Land', 'Residential Land'), ('Office Space', 'Office Space'), ('Co-working', 'Co-working'), ('Shop', 'Shop'), ('Showroom', 'Showroom'), ('Godown/Warehouse', 'Godown/Warehouse'), ('Industrial Shed', 'Industrial Shed'), ('Industrial Building', 'Industrial Building'), ('Hospital/Clinic', 'Hospital/Clinic'), ('School', 'School'), ('Retail Space', 'Retail Space'), ('Hotel', 'Hotel'), ('Guest House', 'Guest House'), ('S.C.O Plot', 'S.C.O Plot'), ('Commercial Plot', 'Commercial Plot'), ('Commercial Land', 'Commercial Land')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='userlevelpropertydata',
            name='property_type',
            field=models.CharField(blank=True, choices=[('Apartment', 'Apartment'), ('Villa', 'Villa'), ('Townhouse', 'Townhouse'), ('Residential Plot', 'Residential Plot'), ('Residential Land', 'Residential Land'), ('Office Space', 'Office Space'), ('Co-working', 'Co-working'), ('Shop', 'Shop'), ('Showroom', 'Showroom'), ('Godown/Warehouse', 'Godown/Warehouse'), ('Industrial Shed', 'Industrial Shed'), ('Industrial Building', 'Industrial Building'), ('Hospital/Clinic', 'Hospital/Clinic'), ('School', 'School'), ('Retail Space', 'Retail Space'), ('Hotel', 'Hotel'), ('Guest House', 'Guest House'), ('S.C.O Plot', 'S.C.O Plot'), ('Commercial Plot', 'Commercial Plot'), ('Commercial Land', 'Commercial Land')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='userlevelpropertydata',
            name='property_unit_type',
            field=models.CharField(choices=[('0', 'sqft'), ('1', 'sqm'), ('2', 'sqyd'), ('3', 'acre'), ('4', 'hectare'), ('5', 'bigha')], default='0', max_length=32),
        ),
    ]
