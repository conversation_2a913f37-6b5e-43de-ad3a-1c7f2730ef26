# Generated by Django 4.1.7 on 2024-08-14 13:32

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('properties', '0003_remove_property_property_monitor_unit_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PropertyVerifiedDataFields',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=32)),
                ('value', models.CharField(max_length=32)),
            ],
        ),
        migrations.AddField(
            model_name='property',
            name='property_unit_type',
            field=models.CharField(choices=[('0', 'sqft'), ('1', 'sqm')], default='0', max_length=32),
        ),
        migrations.AlterField(
            model_name='property',
            name='floor_number',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.CreateModel(
            name='PaymentPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('during_construction', models.IntegerField()),
                ('on_handover', models.IntegerField()),
                ('enable_post_handover', models.BooleanField(default=False)),
                ('post_handover_time_frame', models.IntegerField(blank=True, null=True)),
                ('property', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='properties.property')),
            ],
        ),
        migrations.CreateModel(
            name='AvailabilityStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('0', 'Ready'), ('1', 'Under Development/Off Plan')], max_length=32)),
                ('handover_date', models.CharField(blank=True, max_length=32, null=True)),
                ('occupancy_status', models.CharField(choices=[('0', 'Rented'), ('1', 'Vacant'), ('2', 'Owner Occupied'), ('3', 'Holiday Home')], max_length=32)),
                ('rent_contract_url', models.URLField(blank=True, null=True)),
                ('property', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='properties.property')),
            ],
        ),
    ]
