# Generated by Django 4.1.7 on 2025-06-04 08:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("properties", "0109_alter_propertyfeatures_furnished_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="property",
            name="property_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Apartment", "Apartment"),
                    ("Villa", "Villa"),
                    ("Townhouse", "Townhouse"),
                    ("Residential Plot", "Residential Plot"),
                    ("Residential Land", "Residential Land"),
                    ("Builder Floor", "Builder Floor"),
                    ("Office Space", "Office Space"),
                    ("Co-working", "Co-working"),
                    ("Shop", "Shop"),
                    ("Showroom", "Showroom"),
                    ("Godown/Warehouse", "Godown/Warehouse"),
                    ("Industrial Shed", "Industrial Shed"),
                    ("Industrial Building", "Industrial Building"),
                    ("Hospital/Clinic", "Hospital/Clinic"),
                    ("School", "School"),
                    ("Retail Space", "Retail Space"),
                    ("Hotel", "Hotel"),
                    ("Guest House", "Guest House"),
                    ("S.C.O Plot", "S.C.O Plot"),
                    ("S.C.O Building", "S.C.O Building"),
                    ("Commercial Plot", "Commercial Plot"),
                    ("Commercial Land", "Commercial Land"),
                    ("Factory", "Factory"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="requirements",
            name="property_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Apartment", "Apartment"),
                    ("Villa", "Villa"),
                    ("Townhouse", "Townhouse"),
                    ("Residential Plot", "Residential Plot"),
                    ("Residential Land", "Residential Land"),
                    ("Builder Floor", "Builder Floor"),
                    ("Office Space", "Office Space"),
                    ("Co-working", "Co-working"),
                    ("Shop", "Shop"),
                    ("Showroom", "Showroom"),
                    ("Godown/Warehouse", "Godown/Warehouse"),
                    ("Industrial Shed", "Industrial Shed"),
                    ("Industrial Building", "Industrial Building"),
                    ("Hospital/Clinic", "Hospital/Clinic"),
                    ("School", "School"),
                    ("Retail Space", "Retail Space"),
                    ("Hotel", "Hotel"),
                    ("Guest House", "Guest House"),
                    ("S.C.O Plot", "S.C.O Plot"),
                    ("S.C.O Building", "S.C.O Building"),
                    ("Commercial Plot", "Commercial Plot"),
                    ("Commercial Land", "Commercial Land"),
                    ("Factory", "Factory"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="userlevelpropertydata",
            name="property_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Apartment", "Apartment"),
                    ("Villa", "Villa"),
                    ("Townhouse", "Townhouse"),
                    ("Residential Plot", "Residential Plot"),
                    ("Residential Land", "Residential Land"),
                    ("Builder Floor", "Builder Floor"),
                    ("Office Space", "Office Space"),
                    ("Co-working", "Co-working"),
                    ("Shop", "Shop"),
                    ("Showroom", "Showroom"),
                    ("Godown/Warehouse", "Godown/Warehouse"),
                    ("Industrial Shed", "Industrial Shed"),
                    ("Industrial Building", "Industrial Building"),
                    ("Hospital/Clinic", "Hospital/Clinic"),
                    ("School", "School"),
                    ("Retail Space", "Retail Space"),
                    ("Hotel", "Hotel"),
                    ("Guest House", "Guest House"),
                    ("S.C.O Plot", "S.C.O Plot"),
                    ("S.C.O Building", "S.C.O Building"),
                    ("Commercial Plot", "Commercial Plot"),
                    ("Commercial Land", "Commercial Land"),
                    ("Factory", "Factory"),
                ],
                max_length=32,
                null=True,
            ),
        ),
    ]
