import logging
from django.db import migrations, transaction
from django.conf import settings

from rezio.user.constants import AGENT, INVESTOR
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def copy_property_data_to_user_level(apps, schema_editor):
    """
    Migration to copy property data to user level models for associated agents and verified owners.
    """
    # Get the historical models
    Role = apps.get_model("user", "Role")
    Property = apps.get_model("properties", "Property")
    AgentAssociatedProperty = apps.get_model("properties", "AgentAssociatedProperty")
    PropertyAvailabilityAndStatus = apps.get_model(
        "properties", "PropertyAvailabilityAndStatus"
    )
    PropertyFinancialDetails = apps.get_model("properties", "PropertyFinancialDetails")
    PropertyFeatures = apps.get_model("properties", "PropertyFeatures")
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")
    UserLevelPropertyAvailabilityAndStatus = apps.get_model(
        "properties", "UserLevelPropertyAvailabilityAndStatus"
    )
    UserLevelPropertyFinancialDetails = apps.get_model(
        "properties", "UserLevelPropertyFinancialDetails"
    )
    UserLevelPropertyFeatures = apps.get_model(
        "properties", "UserLevelPropertyFeatures"
    )

    agent_role = Role.objects.get(name=AGENT)
    investor_role = Role.objects.get(name=INVESTOR)

    def create_user_level_data(property_obj, user, role, user_hierarchy):
        """
        Create user level data for a specific user and property.
        """
        try:
            logger.info(
                f"Creating user level data for property {property_obj.id} and user {user.id} for role {role.name}"
            )
            # Create UserLevelPropertyData
            user_level_property = UserLevelPropertyData.objects.create(
                created_by=user,
                created_by_role=role,
                user_hierarchy=user_hierarchy,
                property=property_obj,
                property_type=property_obj.property_type,
                floor_number=property_obj.floor_number,
                property_unit_type=property_obj.property_unit_type,
                total_area=property_obj.total_area,
                carpet_area=property_obj.carpet_area,
                balcony_area=property_obj.balcony_area,
                number_of_bedrooms=property_obj.number_of_bedrooms,
                number_of_common_bathrooms=property_obj.number_of_common_bathrooms,
                number_of_attached_bathrooms=property_obj.number_of_attached_bathrooms,
                number_of_powder_rooms=property_obj.number_of_powder_rooms,
                parking_available=property_obj.parking_available,
                number_of_covered_parking=property_obj.number_of_covered_parking,
                number_of_open_parking=property_obj.number_of_open_parking,
                parking_number=property_obj.parking_number,
                property_publish_status=property_obj.property_publish_status,
                dewa_id=property_obj.dewa_id,
                user_unit_preference=property_obj.user_unit_preference,
                tenancy_type=property_obj.tenancy_type,
                tenancy_start_date=property_obj.tenancy_start_date,
                tenancy_end_date=property_obj.tenancy_end_date,
                lead_owner_percentage=property_obj.lead_owner_percentage,
                agent_type=property_obj.agent_type,
                number_of_master_bedrooms=property_obj.number_of_master_bedrooms,
                number_of_other_bedrooms=property_obj.number_of_other_bedrooms,
                number_of_maid_rooms=property_obj.number_of_maid_rooms,
                number_of_study_rooms=property_obj.number_of_study_rooms,
                is_archived=property_obj.is_archived,
                total_floors=property_obj.total_floors,
                building_type=property_obj.building_type,
            )

            logger.info(
                f"Created UserLevelPropertyData for property {property_obj.id} and user {user.id} for role {role.name}"
            )
            logger.info(
                f"Copying PropertyAvailabilityAndStatus data for property {property_obj.id} and user {user.id} for role {role.name}"
            )
            # Copy PropertyAvailabilityAndStatus data
            try:
                availability_status = PropertyAvailabilityAndStatus.objects.get(
                    property=property_obj
                )
                UserLevelPropertyAvailabilityAndStatus.objects.create(
                    property_level_data=user_level_property,
                    status=availability_status.status,
                    handover_date=availability_status.handover_date,
                    occupancy_status=availability_status.occupancy_status,
                    rent_contract_key=availability_status.rent_contract_key,
                    rent_contract_file_name=availability_status.rent_contract_file_name,
                    rent_contract_file_size=availability_status.rent_contract_file_size,
                    rent_available_start_date=availability_status.rent_available_start_date,
                    enable_payment_plan=availability_status.enable_payment_plan,
                    during_construction=availability_status.during_construction,
                    on_handover=availability_status.on_handover,
                    enable_post_handover=availability_status.enable_post_handover,
                    post_handover_time_frame=availability_status.post_handover_time_frame,
                    ownership_proof_key=availability_status.ownership_proof_key,
                    ownership_proof_file_name=availability_status.ownership_proof_file_name,
                    ownership_proof_file_size=availability_status.ownership_proof_file_size,
                )
            except PropertyAvailabilityAndStatus.DoesNotExist:
                logger.info(
                    f"No availability status found for property {property_obj.id}"
                )

            logger.info(
                f"Copying PropertyFinancialDetails data for property {property_obj.id} and user {user.id} for role {role.name}"
            )
            # Copy PropertyFinancialDetails data
            try:
                financial_details = PropertyFinancialDetails.objects.get(
                    property=property_obj
                )
                UserLevelPropertyFinancialDetails.objects.create(
                    property_level_data=user_level_property,
                    property_currency_code=financial_details.property_currency_code,
                    original_price=financial_details.original_price,
                    asking_price=financial_details.asking_price,
                    valuation=financial_details.valuation,
                    annual_rent=financial_details.annual_rent,
                    annual_service_charges=financial_details.annual_service_charges,
                    security_deposit=financial_details.security_deposit,
                    other_expenses=financial_details.other_expenses,
                    preferred_payment_frequency=financial_details.preferred_payment_frequency,
                    price_negotiable=financial_details.price_negotiable,
                    expected_rent=financial_details.expected_rent,
                    expected_security_deposit=financial_details.expected_security_deposit,
                )
            except PropertyFinancialDetails.DoesNotExist:
                logger.info(
                    f"No financial details found for property {property_obj.id}"
                )

            logger.info(
                f"Copying PropertyFeatures data for property {property_obj.id} and user {user.id} for role {role.name}"
            )
            # Copy PropertyFeatures data
            try:
                features = PropertyFeatures.objects.get(property=property_obj)
                user_level_features = UserLevelPropertyFeatures.objects.create(
                    property_level_data=user_level_property,
                    branded_building=features.branded_building,
                    furnished=features.furnished,
                    premium_view=features.premium_view,
                    is_restroom_available=features.is_restroom_available,
                    no_of_shared_restrooms=features.no_of_shared_restrooms,
                    no_of_private_restrooms=features.no_of_private_restrooms,
                    is_parking_available=features.is_parking_available,
                    public_parking=features.public_parking,
                    no_of_reserved_parking=features.no_of_reserved_parking,
                    reserved_parking_number=features.reserved_parking_number,
                    is_lift_available=features.is_lift_available,
                    common_lift=features.common_lift,
                    no_of_personal_lift=features.no_of_personal_lift,
                    security_available=features.security_available,
                    water_storage_available=features.water_storage_available,
                    property_on_main_road=features.property_on_main_road,
                    corner_property=features.corner_property,
                )
                # Copy tags
                user_level_features.tags.set(features.tags.all())
            except PropertyFeatures.DoesNotExist:
                logger.info(f"No features found for property {property_obj.id}")

            logger.info(
                f"Successfully created user level data for property {property_obj.id} and user {user.id}"
            )
            return True

        except Exception as e:
            logger.error(
                f"Error creating user level data for property {property_obj.id} and user {user.id}: {str(e)}"
            )
            return False

    # Process all properties
    properties = Property.objects.all()
    logger.info(f"Starting to process {properties.count()} properties")

    success_count = 0
    error_count = 0

    for property_obj in properties:
        logger.info(f"Copying data for property {property_obj.id}")
        try:
            with transaction.atomic():
                # Get associated agents
                agent_associations = AgentAssociatedProperty.objects.filter(
                    property=property_obj,
                    is_associated=True,
                )

                # Handle agents
                for agent_assoc in agent_associations:
                    if create_user_level_data(
                        property_obj=property_obj,
                        user=agent_assoc.agent_profile.user,
                        role=agent_role,
                        user_hierarchy="Agent",
                    ):
                        success_count += 1
                    else:
                        error_count += 1

                # Handle verified owner
                if property_obj.owner_verified and property_obj.owner:
                    if create_user_level_data(
                        property_obj=property_obj,
                        user=property_obj.owner.user,
                        role=investor_role,
                        user_hierarchy="Owner",
                    ):
                        success_count += 1
                    else:
                        error_count += 1

        except Exception as e:
            error_count += 1
            logger.error(f"Error processing property {property_obj.id}: {str(e)}")

    logger.info(
        f"Migration completed. Successful: {success_count}, Errors: {error_count}"
    )


def reverse_migration(apps, schema_editor):
    """
    Reverse the migration by deleting all user level property data
    """
    UserLevelPropertyData = apps.get_model("properties", "UserLevelPropertyData")
    UserLevelPropertyData.objects.all().delete()
    logger.info("Reversed migration - deleted all user level property data")


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0077_hierarchy_view_and_edit_attributes"),
    ]

    operations = [
        migrations.RunPython(copy_property_data_to_user_level, reverse_migration),
    ]
