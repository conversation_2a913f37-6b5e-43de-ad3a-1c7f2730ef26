# Generated by Django 4.1.7 on 2024-08-12 18:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0002_property_property_monitor_address_id"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="property",
            name="property_monitor_unit_id",
        ),
        migrations.AddField(
            model_name="property",
            name="building_number",
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.AddField(
            model_name="property",
            name="unit_number",
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name="community",
            name="property_monitor_location_id",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="community",
            name="sub_loc_1",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="community",
            name="sub_loc_2",
            field=models.Char<PERSON>ield(blank=True, max_length=128, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="community",
            name="sub_loc_3",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name="community",
            name="sub_loc_4",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AlterField(
            model_name="community",
            name="sub_loc_5",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
    ]
