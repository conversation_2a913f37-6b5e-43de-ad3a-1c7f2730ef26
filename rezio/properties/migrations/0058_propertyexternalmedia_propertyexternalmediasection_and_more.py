# Generated by Django 4.1.7 on 2025-01-10 12:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0046_alter_agentprofile_followers_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("properties", "0057_alter_property_tenancy_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="PropertyExternalMedia",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "media_type",
                    models.CharField(
                        choices=[("0", "Link"), ("1", "Document"), ("2", "Image")],
                        max_length=20,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.property",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PropertyExternalMediaSection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=100)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property_external_media",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.propertyexternalmedia",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PropertyExternalMediaLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                ("url", models.URLField()),
                (
                    "description",
                    models.TextField(blank=True, max_length=320, null=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property_external_media",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.propertyexternalmedia",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="PropertyExternalMediaDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_ts", models.DateTimeField(auto_now_add=True)),
                ("updated_ts", models.DateTimeField(auto_now=True)),
                (
                    "media_file_key",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                (
                    "media_file_name",
                    models.CharField(blank=True, max_length=128, null=True),
                ),
                ("media_file_size", models.BigIntegerField(blank=True, null=True)),
                ("order_no", models.IntegerField(null=True)),
                (
                    "media_file_content_type",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "created_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by",
                        to="user.role",
                    ),
                ),
                (
                    "property_external_media_section",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="properties.propertyexternalmediasection",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_updated_by",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by_role",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="%(class)s_created_by_role",
                        to="user.role",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
