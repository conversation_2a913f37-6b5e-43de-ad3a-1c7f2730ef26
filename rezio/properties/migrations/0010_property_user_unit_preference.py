# Generated by Django 4.1.7 on 2024-09-03 11:26
import django
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0009_propertycompletionstate_draft_data_json_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="property",
            name="user_unit_preference",
            field=models.CharField(default="sqft", max_length=8),
        ),
        migrations.RenameField(
            model_name="propertycompletionstate",
            old_name="state_completed",
            new_name="state",
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="property",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="property_monitor_verified_fields",
                to="properties.property",
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="propertyverifieddatafields",
            name="value",
            field=models.<PERSON><PERSON><PERSON><PERSON>(max_length=32, null=True),
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_created_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="created_ts",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="updated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="%(class)s_updated_by",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="updated_ts",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
