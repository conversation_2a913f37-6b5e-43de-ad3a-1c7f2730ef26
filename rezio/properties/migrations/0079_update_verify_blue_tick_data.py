# Generated by Django 4.1.7 on 2025-03-11 11:07
import datetime
import logging

from django.db import migrations

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)

FIELDS_TO_ADD = {
    "property_type": "property_type",
    "total_area": "unit_bua_sqft",
    "carpet_area": "suite_area_sqft",
    "balcony_area": "balcony_size_sqft",
    "number_of_bedrooms": "no_beds",
    "floor_number": "floor",
    "parking_number": "parking",
    "number_of_study_rooms": "study",
    "number_of_maid_rooms": "maid",
}


def get_unit_details(address_id):
    # Fetch unit details from Property Monitor API
    from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI

    property_monitor = PropertyMonitorAPI()
    unit_details = property_monitor.get_unit_details(address_id)

    unit_details["unit_bua_sqft"] = (
        unit_details.get("unit_bua_sqft")
        if unit_details.get("unit_bua_sqft") != 0.0
        else None
    )
    unit_details["suite_area_sqft"] = (
        unit_details.get("suite_area_sqft")
        if unit_details.get("suite_area_sqft") != 0.0
        else None
    )
    unit_details["floor"] = (
        unit_details.get("floor") if unit_details.get("floor") != "" else None
    )

    if (
        unit_details["balcony"] == "Y" and unit_details.get("balcony_size_sqft") == 0.0
    ) or unit_details.get("balcony_size_sqft") == 0.0:
        unit_details["balcony_size_sqft"] = None

    if unit_details["property_type"] not in ["Apartment", "Villa", "Townhouse"]:
        unit_details["property_type"] = "Apartment"

    if unit_details["no_beds"] == "s":
        unit_details["no_beds"] = 0
    elif unit_details["no_beds"] == "":
        unit_details["no_beds"] = None

    unit_details["maid"] = unit_details["maid"] if unit_details["maid"] != "" else None
    unit_details["study"] = (
        unit_details["study"] if unit_details["study"] != "" else None
    )
    logger.info(f"Property monitor data for {address_id}: {unit_details}")
    return unit_details


def update_rental_data(
    rental_data, property_obj, property_financials, PropertyVerifiedDataFields
):
    try:
        for each_rental_data in rental_data:
            current_date = datetime.date.today()
            start_date = datetime.datetime.strptime(
                each_rental_data["start_date"], "%Y-%m-%d"
            ).date()
            end_date = datetime.datetime.strptime(
                each_rental_data["end_date"], "%Y-%m-%d"
            ).date()
            if start_date < current_date < end_date:
                logger.info(
                    f"total_rent for {property_obj.id} from property monitor is: {each_rental_data['total_rent']} "
                )
                logger.info(
                    f"total_rent for {property_obj.id} from db is: {property_financials.annual_rent}"
                )
                if (
                    property_financials.annual_rent is not None
                    and each_rental_data.get("total_rent") is not None
                    and property_financials.annual_rent
                    == each_rental_data.get("total_rent")
                ):
                    PropertyVerifiedDataFields.objects.create(
                        property=property_obj,
                        field_name="annual_rent",
                        value=str(each_rental_data["total_rent"]),
                    )
    except Exception as error:
        message = "Unknown error occurred in update_rental_data"
        logger.error(f"Error in update_rental_data - {message} - {error}")


def update_sales_data(
    sales_data, property_obj, property_financials, PropertyVerifiedDataFields
):
    try:
        from rezio.properties.text_choices import PropertyMonitorSalesEvidence

        for each_sales_data in sales_data:
            if each_sales_data["evidence"] in [
                PropertyMonitorSalesEvidence.OFF_PLAN_SALE.label,
                PropertyMonitorSalesEvidence.TRANSFERRED_SALE.label,
            ]:
                original_price = each_sales_data["total_sales_price"]
                logger.info(
                    f"original_price for {property_obj.id} from property monitor is: {original_price} "
                )
                logger.info(
                    f"original_price for {property_obj.id} from db is: {property_financials.original_price}"
                )
                if (
                    original_price is not None
                    and property_financials.original_price is not None
                    and original_price == property_financials.original_price
                ):
                    PropertyVerifiedDataFields.objects.create(
                        property=property_obj,
                        field_name="original_price",
                        value=str(original_price),
                    )
    except Exception as error:
        message = "Unknown error occurred in update_sales_data"
        logger.error(f"Error in update_sales_data - {message} - {error}")


def update_valuation_data(
    property_obj, property_financials, PropertyVerifiedDataFields
):
    from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI

    last_sub_location = ""
    master_development = ""
    if property_obj.community:
        master_development = property_obj.community.name
        for loc_no in range(5, 0, -1):
            if getattr(property_obj.community, f"sub_loc_{loc_no}", None):
                last_sub_location = getattr(property_obj.community, f"sub_loc_{loc_no}")

    property_monitor = PropertyMonitorAPI()
    volume_trend_data = property_monitor.get_sale_price_volume_trend(
        master_development,
        last_sub_location,
        property_obj.property_type,
    )

    if volume_trend_data:
        for price_data in volume_trend_data[::-1]:
            avg_price_sqft = price_data.get("avg_price_sqft")
            if avg_price_sqft:
                valuation = round(property_obj.total_area * avg_price_sqft)
                if (
                    valuation is not None
                    and property_financials.valuation is not None
                    and valuation == property_financials.valuation
                ):
                    PropertyVerifiedDataFields.objects.create(
                        property=property_obj,
                        field_name="valuation",
                        value=str(valuation),
                    )
                break


def update_blue_tick_data(apps, schema_editor):
    Property = apps.get_model("properties", "Property")
    PropertyVerifiedDataFields = apps.get_model(
        "properties", "PropertyVerifiedDataFields"
    )
    PropertyFinancialDetails = apps.get_model("properties", "PropertyFinancialDetails")

    PropertyVerifiedDataFields.objects.all().delete()

    # Process property monitor properties
    properties = Property.objects.filter(property_monitor_address_id__isnull=False)

    logger.info(f"Starting to process {properties.count()} properties")

    success_count = 0
    error_count = 0

    for property_obj in properties:
        logger.info(f"Updating data for property {property_obj.id}")
        try:
            unit_details = get_unit_details(property_obj.property_monitor_address_id)
            for field in FIELDS_TO_ADD:
                unit_details_value = unit_details.get(FIELDS_TO_ADD.get(field))
                property_obj_value = getattr(property_obj, field)
                if (
                    unit_details_value is not None
                    and property_obj_value is not None
                    and unit_details_value == property_obj_value
                ):
                    PropertyVerifiedDataFields.objects.create(
                        property=property_obj,
                        field_name=field,
                        value=str(unit_details_value),
                    )

            property_financial_obj = PropertyFinancialDetails.objects.filter(
                property=property_obj
            ).first()

            if property_financial_obj:
                update_rental_data(
                    unit_details.get("rental_unit_history", []),
                    property_obj,
                    property_financial_obj,
                    PropertyVerifiedDataFields,
                )
                update_sales_data(
                    unit_details.get("sales_unit_history", []),
                    property_obj,
                    property_financial_obj,
                    PropertyVerifiedDataFields,
                )
                update_valuation_data(
                    property_obj, property_financial_obj, PropertyVerifiedDataFields
                )
            logger.info(f"Successfully updated all the data for {property_obj}")
            success_count += 1
        except Exception as e:
            error_count += 1
            logger.error(f"Error processing property {property_obj.id}: {str(e)}")

    logger.info(
        f"Migration completed. Successful: {success_count}, Errors: {error_count}"
    )


def reverse_blue_tick_data(apps, schema_editor):
    PropertyVerifiedDataFields = apps.get_model(
        "properties", "PropertyVerifiedDataFields"
    )
    PropertyVerifiedDataFields.objects.all().delete()
    logger.info(
        "Reversed blue tick data migration - deleted all PropertyVerifiedDataFields"
    )


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0078_copy_property_data_to_user_level"),
    ]

    operations = [
        migrations.RunPython(update_blue_tick_data, reverse_blue_tick_data),
    ]
