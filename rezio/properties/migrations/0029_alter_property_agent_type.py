# Generated by Django 4.1.7 on 2024-09-25 07:52

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        (
            "properties",
            "0028_propertyavailabilityandstatus_ownership_proof_file_name_and_more",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="property",
            name="agent_type",
            field=models.CharField(
                choices=[
                    ("0", "Open to all agents"),
                    ("1", "Allow access to selective agents"),
                    ("2", "Give access to an exclusive agent"),
                ],
                default="0",
                max_length=32,
            ),
        ),
    ]
