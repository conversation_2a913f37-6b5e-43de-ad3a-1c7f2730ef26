# Generated by Django 4.1.7 on 2025-03-18 13:55

from django.db import migrations, models


def update_existing_field_types(apps, schema_editor):
    property_verified_data_fields = apps.get_model(
        "properties", "PropertyVerifiedDataFields"
    )
    property_verified_data_fields.objects.filter(
        field_name__in=[
            "total_area",
            "carpet_area",
            "balcony_area",
            "original_price",
            "annual_rent",
        ]
    ).update(field_type="float")

    property_verified_data_fields.objects.filter(
        field_name__in=["property_type", "floor_number", "parking_number"]
    ).update(field_type="str")

    property_verified_data_fields.objects.filter(
        field_name__in=["number_of_bedrooms", "valuation"]
    ).update(field_type="int")


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0080_attribute_name_updates"),
    ]

    operations = [
        migrations.AddField(
            model_name="propertyverifieddatafields",
            name="field_type",
            field=models.CharField(default="str", max_length=10),
        ),
        migrations.RunPython(update_existing_field_types),
    ]
