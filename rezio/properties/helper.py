import io
import locale
import logging
import mimetypes
import re

from PIL import Image
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers

from rezio.properties.constants import PROPERTY_MONITOR_DETAILS_FIELD_MAPPING
from rezio.properties.models import (
    City,
    Country,
    State,
    PropertyVerifiedDataFields,
    UserLevelPropertyFinancialDetails,
    PropertyFinancialDetails,
)
from rezio.properties.serializers import UnitDetailSerializer
from rezio.properties.services.property_service import (
    get_unit_details,
    update_rental_history,
    update_sales_history,
)
from rezio.properties.utils import PropertyUtility
from rezio.rezio.constants import PRESIGNED_POST_STRUCTURES, KEY
from rezio.rezio.custom_error_codes import UNKNOWN_ERROR_IN_PM_DATA_PROCESS
from rezio.user.utils import get_or_create_db_object
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    <PERSON><PERSON><PERSON>_PAYLOAD,
    <PERSON><PERSON><PERSON>_ERROR,
    KEY_ERROR_MESSAGE,
    KEY_ERROR_CODE,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
)
from rezio.utils.decorators import log_input_output
from rezio.utils.validation_utils import validation_utility

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def is_valid_image_or_video(file, allowed_mime_types=settings.ALLOWED_MIME_TYPES):
    """
    Method to check if image or video is valid or not
    """

    file_size = file.size

    if file_size == 0:
        raise serializers.ValidationError("Blank file cannot be uploaded")

    # Get the MIME type of the file
    mime_type, _ = mimetypes.guess_type(file.name)

    if mime_type not in allowed_mime_types:
        raise serializers.ValidationError(f"File type '{mime_type}' is not supported")

    if mime_type.startswith("image"):
        # Define your size limit (in bytes) for images
        limit = settings.IMAGE_SIZE_LIMIT_IN_MB * 1024 * 1024

        if file_size > limit:
            raise serializers.ValidationError(f"File size should not exceed {limit} MB")

        try:
            with Image.open(file) as img:
                img.verify()
        except Exception as e:
            logger.error(f"Error occurred: {e}")
            raise serializers.ValidationError("The file is corrupted")

    elif mime_type.startswith("video"):
        limit = settings.VIDEO_SIZE_LIMIT_IN_MB * 1024 * 1024

        if file_size > limit:
            raise serializers.ValidationError(f"File size should not exceed {limit} MB")


def verify_bedroom_details(bedroom_details):
    """
    Method to verify the details of bedroom

    :param bedroom_details: Dictionary of different types of bedroom counts
    """
    number_of_bedrooms = (
        bedroom_details.get("number_of_bedrooms")
        if bedroom_details.get("number_of_bedrooms")
        else 0
    )
    number_of_master_bedrooms = (
        bedroom_details.get("number_of_master_bedrooms")
        if bedroom_details.get("number_of_master_bedrooms")
        else 0
    )
    number_of_other_bedrooms = (
        bedroom_details.get("number_of_other_bedrooms")
        if bedroom_details.get("number_of_other_bedrooms")
        else 0
    )
    number_of_maid_rooms = (
        bedroom_details.get("number_of_maid_rooms")
        if bedroom_details.get("number_of_maid_rooms")
        else 0
    )
    number_of_study_rooms = (
        bedroom_details.get("number_of_study_rooms")
        if bedroom_details.get("number_of_study_rooms")
        else 0
    )

    total_bedrooms = (
        number_of_master_bedrooms
        + number_of_other_bedrooms
        + number_of_maid_rooms
        + number_of_study_rooms
    )

    if (
        number_of_bedrooms > 0
        and number_of_master_bedrooms == 0
        and number_of_study_rooms == 0
        and number_of_maid_rooms == 0
        and number_of_other_bedrooms == 0
    ):
        pass

    elif (number_of_bedrooms > 0 and total_bedrooms != number_of_bedrooms) or (
        number_of_bedrooms == 0
        and (
            number_of_master_bedrooms != 0
            or number_of_study_rooms != 0
            or number_of_maid_rooms != 0
            or number_of_other_bedrooms != 0
        )
    ):
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: "The number of bedroom spaces entered doesn't match the "
                    "required number. Please make sure they are equal."
                },
            }
        )


def compress_image(image_path):
    """
    Method to compress the image

    :param image_path: Path of the image
    :return compressed_buffer: Compressed image
    """
    with Image.open(image_path) as img:
        max_size = (1200, 630)
        img.thumbnail(max_size, Image.LANCZOS)
        img = img.convert("RGB")
        compressed_buffer = io.BytesIO()
        img.save(
            compressed_buffer,
            format="JPEG",
            quality=settings.IMAGE_COMPRESSION_QUALITY,
            optimize=True,
        )
        compressed_buffer.seek(0)
        return compressed_buffer


def format_amount(amount, precision=2, currency_code="INR", omit_trailing_zeros=True):
    """
    Format an amount with appropriate scale suffix (Cr, L, K, B, M)

    Args:
        amount: The amount to format
        precision: Number of decimal places
        currency_code: Currency code ('INR' for Indian format, others for global)
        omit_trailing_zeros: Whether to remove trailing zeros

    Returns:
        str: Formatted amount string
    """
    # Define scales for Indian and global number systems
    amount_abs = abs(float(amount))

    indian_scales = [
        {"threshold": 1e7, "suffix": "Cr"},  # Crore
        {"threshold": 1e5, "suffix": "L"},  # Lakh
        {"threshold": 1e3, "suffix": "K"},  # Thousand
    ]

    global_scales = [
        {"threshold": 1e9, "suffix": "B"},  # Billion
        {"threshold": 1e6, "suffix": "M"},  # Million
        {"threshold": 1e3, "suffix": "K"},  # Thousand
    ]

    scales = indian_scales if currency_code == "INR" else global_scales

    # Format numbers smaller than the smallest scale
    if amount_abs < scales[-1]["threshold"]:
        sign = "-" if amount < 0 else ""
        return sign + _format_with_precision(amount_abs, precision, omit_trailing_zeros)

    # Loop through scales to determine the appropriate one
    for scale in scales:
        if amount_abs >= scale["threshold"]:
            scaled_amount = amount_abs / scale["threshold"]
            sign = "-" if amount < 0 else ""
            formatted_value = sign + _format_with_precision(
                scaled_amount, precision, omit_trailing_zeros
            )
            return formatted_value + scale["suffix"]

    # Default fallback (shouldn't be reached)
    sign = "-" if amount < 0 else ""
    return sign + _format_with_precision(amount_abs, precision, omit_trailing_zeros)


def _format_with_precision(value, precision, omit_trailing_zeros):
    """
    Format a number with the specified precision

    Args:
        value: The value to format
        precision: Number of decimal places
        omit_trailing_zeros: Whether to remove trailing zeros

    Returns:
        str: Formatted number string
    """
    # Set locale to ensure commas as thousand separators
    locale.setlocale(locale.LC_ALL, "")

    # Format with specified precision
    formatted_value = locale.format_string(f"%.{precision}f", value, grouping=True)

    # Remove trailing zeros if requested
    if omit_trailing_zeros:
        formatted_value = re.sub(r"\.?0*$", "", formatted_value)

    return formatted_value


class PropertyHelper:
    """
    Class for property helper methods
    """

    @staticmethod
    def add_country_state_details(validated_data, *args, **kwargs):
        """
        Method to add country and state details
        """
        logger.info(f"Validated data is: {validated_data}")
        country = validated_data.get("country")
        state = validated_data.get("state")
        country_short_name = validated_data.get("country_short_name")
        state_name = validated_data.get("state_name")

        try:
            country = Country.objects.get(name=country)
            logger.info(f"Country fetched {country}")
        except ObjectDoesNotExist:
            country = Country.objects.create(
                name=country, short_name=country_short_name
            )
            logger.info(f"Country added {country}")

        try:
            if state:
                state = State.objects.get(id=state, country=country)
            else:
                state = State.objects.filter(name=state_name, country=country)
                if not state.exists():
                    state = State.objects.create(name=state_name, country=country)
                    logger.info(f"New state added {state}")
                else:
                    state = state.first()

            logger.info(f"State fetched {state}")
        except ObjectDoesNotExist:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid state sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Incorrect state provided for country."
                    },
                }
            )

        return country, state

    @staticmethod
    def save_community_and_unit_data(
        validated_data, property_obj, user_level_property_obj, user, role_obj
    ):
        """
        Method to save community and unit data
        """
        try:
            unit_details = get_unit_details(validated_data)

            unit_detail_serializer = UnitDetailSerializer(data=unit_details)
            unit_detail_validated_data = validation_utility.validate_serializer(
                unit_detail_serializer
            )
            logger.info(f"Unit details validated data: {unit_detail_validated_data}")

            property_utility = PropertyUtility(
                property_obj=property_obj,
                user_level_property_obj=user_level_property_obj,
            )
            community, created = property_utility.get_or_create_community_obj(
                unit_detail_validated_data
            )
            property_obj.community = community
            property_obj.unit_number = unit_details.get("unit_number")
            property_obj.building_number = unit_details.get("building_number")
            property_obj.property_monitor_address_id = validated_data.get("address_id")
            user_level_property_obj.property_type = unit_detail_validated_data.get(
                "property_type"
            )
            user_level_property_obj.total_area = unit_detail_validated_data.get(
                "unit_bua_sqft"
            )
            user_level_property_obj.carpet_area = unit_detail_validated_data.get(
                "suite_area_sqft"
            )
            user_level_property_obj.balcony_area = unit_detail_validated_data.get(
                "balcony_size_sqft"
            )
            user_level_property_obj.number_of_bedrooms = unit_detail_validated_data.get(
                "no_beds"
            )

            if (
                unit_detail_validated_data.get("parking")
                and unit_detail_validated_data.get("parking") != ""
            ):
                user_level_property_obj.parking_number = unit_detail_validated_data.get(
                    "parking"
                )
                get_or_create_db_object(
                    PropertyVerifiedDataFields,
                    property=property_obj,
                    field_name="parking_number",
                    value=str(unit_detail_validated_data.get("parking")),
                    created_by=user,
                    created_by_role=role_obj,
                    field_type="str",
                )
                user_level_property_obj.parking_available = True

                number_of_parking = len(
                    unit_detail_validated_data.get("parking").split(",")
                )
                user_level_property_obj.number_of_covered_parking = number_of_parking

            user_level_property_obj.floor_number = unit_detail_validated_data.get(
                "floor"
            )
            user_level_property_obj.number_of_study_rooms = (
                unit_detail_validated_data.get("study")
            )

            user_level_property_obj.number_of_maid_rooms = (
                unit_detail_validated_data.get("maid")
            )

            for field in PROPERTY_MONITOR_DETAILS_FIELD_MAPPING:
                value = unit_detail_validated_data.get(
                    PROPERTY_MONITOR_DETAILS_FIELD_MAPPING[field]
                )

                if field in ["total_area", "balcony_area", "carpet_area"]:
                    field_type = "float"
                elif field in [
                    "number_of_bedrooms",
                    "number_of_maid_rooms",
                    "number_of_study_rooms",
                ]:
                    field_type = "int"
                else:
                    field_type = "str"

                if (value is not None) and (
                    field
                    not in [
                        "parking_number",
                        "number_of_bathrooms",
                    ]
                ):
                    get_or_create_db_object(
                        PropertyVerifiedDataFields,
                        property=property_obj,
                        field_name=field,
                        value=str(value),
                        created_by=user,
                        created_by_role=role_obj,
                        field_type=field_type,
                    )

            property_obj.updated_by = user
            property_obj.updated_by_role = role_obj

            property_obj.save()
            user_level_property_obj.save()

            (
                property_financials,
                created,
            ) = get_or_create_db_object(
                PropertyFinancialDetails,
                property=property_obj,
                created_by=user,
                created_by_role=role_obj,
            )
            (
                user_level_property_financial,
                user_level_property_financial_created,
            ) = get_or_create_db_object(
                UserLevelPropertyFinancialDetails,
                property_level_data=user_level_property_obj,
            )

            update_rental_history(
                unit_details.get("rental_unit_history", []),
                property_obj,
                user.id,
                role_obj,
                property_financials,
                created,
                user_level_property_financial,
            )
            update_sales_history(
                unit_details.get("sales_unit_history", []),
                property_obj,
                user.id,
                role_obj,
                property_financials,
                created,
                user_level_property_financial,
            )

            return property_obj.community, property_obj.unit_number

        except InvalidSerializerDataException:
            raise
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in save community and unit data - {message} - {error}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while saving community and unit details",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: UNKNOWN_ERROR_IN_PM_DATA_PROCESS.get(
                            "message"
                        ),
                        KEY_ERROR_CODE: UNKNOWN_ERROR_IN_PM_DATA_PROCESS.get("code"),
                    },
                }
            )

    @staticmethod
    def add_city_details(validated_data, country, state, *args, **kwargs):
        """
        Method to add city details
        """
        logger.info(f"Validated data is: {validated_data}")
        city_name = validated_data.get("city")

        city = City.objects.filter(name=city_name, country=country, state=state).first()
        logger.info(f"City fetched {city}")
        if not city:
            city = City.objects.create(name=city_name, state=state, country=country)
            logger.info(f"City added {city}")

        return city

    @staticmethod
    @log_input_output
    def generate_property_media_feature_key(
        property_media_feature, property_id, user_id, user_role, file_name
    ):
        """
        Method to generate property media feature key
        """
        logger.info(f"Generating key for {property_media_feature}")

        media_key = PRESIGNED_POST_STRUCTURES.get(property_media_feature.label, {}).get(
            KEY, ""
        )
        media_key = media_key.format(
            property_id=property_id,
            user_id=user_id,
            user_role=user_role,
            filename=file_name,
        )
        logger.info(f"Generated media key is: {media_key}")
        return media_key


property_helper = PropertyHelper()
