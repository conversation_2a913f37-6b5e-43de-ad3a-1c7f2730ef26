import datetime
import logging

import requests
from django.conf import settings
from django.db import transaction
from django.db.models import F

from rezio.properties.constants import property_editable_attributes
from rezio.properties.models import (
    Property,
    PropertyAvailabilityAndStatus,
    PropertyCompletionState,
    ExchangeRates,
    PropertyCoOwner,
    PropertyVerifiedDataFields,
    PropertyFinancialDetails,
    AgentAssociatedProperty,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyData,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyFeatures,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    Community,
)
from rezio.properties.text_choices import (
    OwnerIntentForProperty,
    OwnerInformationButtonText,
    ClaimPropertyChoices,
    UserRequestActions,
    PropertyPublishStatus,
    PropertyAreaUnit,
)
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    DD_MMM_YYYY,
    FLOOR_PLAN,
    KEY,
    PRESIGNED_POST_STRUCTURES,
    PROPERTY_OWNERSHIP_PROOF,
    PROPERTY_RENT_CONTRACT,
)
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import get_profile_object_by_role
from rezio.user.utils import get_or_create_db_object
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    KEY_MESSAGE,
)
from rezio.utils.custom_exceptions import ResourceNotFoundException
from rezio.utils.decorators import log_input_output, general_exception_handler

logger = logging.getLogger(DJANGO_LOGGER_NAME)


@log_input_output
def get_property_object(property_id):
    """
    Get property object

    Returns:
        Property: property object

    Raises:
        ResourceNotFoundException: if property does not exist
    """

    try:
        return Property.objects.get(id=property_id, is_archived=False)
    except Property.DoesNotExist:
        message = f"Property does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"Property object with ID {property_id} does not exist"
                },
            }
        )


@log_input_output
def get_property_availability_status_object(property_id):
    """
    Get property availability and status object

    Returns:
        PropertyAvailabilityAndStatus: property availability and status object

    Raises:
        ResourceNotFoundException: if property availability and status does not exist
    """

    try:
        return PropertyAvailabilityAndStatus.objects.get(property_id=property_id)
    except PropertyAvailabilityAndStatus.DoesNotExist:
        message = f"PropertyAvailabilityAndStatus does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


@log_input_output
def get_property_completion_state_object(property_id, state=None):
    """
    Get property completion state object

    Returns:
        PropertyCompletionState: property completion state object

    Raises:
        ResourceNotFoundException: if property completion state does not exist
    """

    try:
        # Create the base query
        query = PropertyCompletionState.objects.filter(property_id=property_id)

        # Apply state filter if provided
        if state:
            query = query.filter(state=state)

        # Fetch the latest record by updated_ts
        return query.latest("updated_ts")
    except PropertyCompletionState.DoesNotExist:
        message = f"PropertyCompletionState does not exist for given property_id {property_id}"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


@log_input_output
def get_s3_object(key):
    s3_client = S3Client()
    return s3_client.get_file(key)


# TODO: Need to discuss about this part
@log_input_output
def clean_availability_and_status_data(
    property_id, user_level_property_obj=None, user=None, role_obj=None
):
    availability_and_status = PropertyAvailabilityAndStatus.objects.filter(
        property_id=property_id
    )
    user_level_availability_and_status = (
        UserLevelPropertyAvailabilityAndStatus.objects.filter(
            proeprty=user_level_property_obj, created_by=user, created_by_role=role_obj
        )
    )
    if availability_and_status.exists():
        s3_client = S3Client()
        availability_and_status = availability_and_status.first()
        user_level_availability_and_status = user_level_availability_and_status.first()
        if availability_and_status.rent_contract_key:
            s3_client.delete_file(availability_and_status.rent_contract_key)
        if user_level_availability_and_status.rent_contract_key:
            s3_client.delete_file(user_level_availability_and_status.rent_contract_key)
        if availability_and_status.ownership_proof_key:
            s3_client.delete_file(availability_and_status.ownership_proof_key)
        if user_level_availability_and_status.rent_contract_key:
            s3_client.delete_file(
                user_level_availability_and_status.ownership_proof_key
            )
        availability_and_status.delete()
        user_level_availability_and_status.delete()
    return True


def get_primary_phone_code(primary_phone_number):
    return f"+{primary_phone_number.country_code}"


def get_primary_number(primary_phone_number):
    return str(primary_phone_number.national_number)


@log_input_output
def get_exchange_rates(base_currency: str, target_currency: str):
    today_date = datetime.date.today()
    exchange_rate = ExchangeRates.objects.filter(
        base_currency=base_currency,
        target_currency=target_currency,
        created_at=today_date,
    ).first()
    if exchange_rate:
        return exchange_rate.exchange_rate
    else:
        exchange_rate = ExchangeRates.objects.filter(
            base_currency=target_currency,
            target_currency=base_currency,
            created_at=today_date,
        ).first()
        if exchange_rate:
            return 1 / exchange_rate.exchange_rate

        else:
            try:
                url = (
                    f"{settings.EXCHANGE_RATE_API_URL}/{settings.EXCHANGE_RATE_API_KEY}/"
                    f"{settings.EXCHANGE_RATE_API_CONVERSION_ENDPOINT}/"
                    f"{base_currency}/{target_currency}"
                )
                logger.warning(f"Hitting exchange rate API - {url}")
                response = requests.get(url)
                if response.status_code == 200:
                    data = response.json()
                    exchange_rate = data.get("conversion_rate")
                    ExchangeRates.objects.create(
                        base_currency=base_currency,
                        target_currency=target_currency,
                        exchange_rate=exchange_rate,
                    )
                    return exchange_rate
                else:
                    exchange_rate = ExchangeRates.objects.filter(
                        base_currency=base_currency, target_currency=target_currency
                    ).last()
                    if exchange_rate:
                        return exchange_rate.exchange_rate
                    else:
                        exchange_rate = ExchangeRates.objects.filter(
                            base_currency=target_currency, target_currency=base_currency
                        ).last()
                        if exchange_rate:
                            return exchange_rate.exchange_rate
                logger.warning(f"Unable to fetch exchange rate - {response.json()}")
                return 0.0
            except Exception as error:
                logger.error(f"Unable to fetch exchange rate - {error}")
                return 0.0


@log_input_output
def build_price_details(
    property_currency_code: str,
    preferred_currency_code: str,
    asking_price: int,
    valuation: int,
):
    price_details_data = dict()
    price_details_data["property_currency_code"] = property_currency_code
    price_details_data["preferred_currency_code"] = preferred_currency_code
    price_details_data["property_currency_asking_price"] = asking_price
    price_details_data["property_currency_valuation"] = valuation
    preferred_currency_asking_price = None
    preferred_currency_valuation_price = None
    price_details_data["preferred_currency_valuation"] = None
    exchange_rate = get_exchange_rates(property_currency_code, preferred_currency_code)
    if exchange_rate:
        if asking_price:
            preferred_currency_asking_price = round(asking_price * exchange_rate, 3)
        else:
            preferred_currency_asking_price = None
        if valuation:
            preferred_currency_valuation_price = round(valuation * exchange_rate, 3)
        else:
            preferred_currency_valuation_price = None
        price_details_data["exchange_rate"] = round(1 / exchange_rate, 2)
    price_details_data["preferred_currency_asking_price"] = (
        preferred_currency_asking_price
    )
    price_details_data["preferred_currency_valuation"] = (
        preferred_currency_valuation_price
    )

    return price_details_data


@log_input_output
def build_property_address(property_object: Property):
    skip_loc = None
    if property_object.community:
        for loc_no in range(5, 0, -1):
            if getattr(property_object.community, f"sub_loc_{loc_no}", None):
                skip_loc = f"sub_loc_{loc_no}"
                break

    address_parts = [
        *[
            getattr(property_object.community, f"sub_loc_{loc_no}")
            for loc_no in range(5, 0, -1)
            if property_object.community
            and f"sub_loc_{loc_no}" != skip_loc
            and getattr(property_object.community, f"sub_loc_{loc_no}", None)
        ],
        (
            property_object.community.name
            if property_object.community and skip_loc
            else None
        ),
        property_object.area.name if property_object.area else None,
        property_object.city.name if property_object.city else None,
        property_object.state.name if property_object.state else None,
        property_object.country.name if property_object.country else None,
    ]

    return ", ".join(filter(None, address_parts))


@log_input_output
def build_property_address_explore(property_object: Property):
    skip_loc = None
    if property_object.community:
        for loc_no in range(5, 0, -1):
            if getattr(property_object.community, f"sub_loc_{loc_no}", None):
                skip_loc = f"sub_loc_{loc_no}"
                break

    address_parts = [
        *[
            getattr(property_object.community, f"sub_loc_{loc_no}")
            for loc_no in range(5, 0, -1)
            if property_object.community
            and f"sub_loc_{loc_no}" != skip_loc
            and getattr(property_object.community, f"sub_loc_{loc_no}", None)
        ],
        (property_object.community.name if property_object.community else None),
        property_object.area.name if property_object.area else None,
        property_object.city.name if property_object.city else None,
        property_object.state.name if property_object.state else None,
        property_object.country.name if property_object.country else None,
    ]

    return ", ".join(filter(None, address_parts))


@log_input_output
def build_serializer_representation(serializer_class, serializer_data: dict):
    non_display_fields = serializer_class.context.get("non_display_fields", [])
    for field in non_display_fields:
        serializer_data[field] = None
    return serializer_data


@log_input_output
def get_co_owner_list(property_object: Property, property_co_owners=None) -> list:
    if not property_co_owners:
        property_co_owners = PropertyCoOwner.objects.filter(
            property=property_object, is_associated=True
        )
    if not property_co_owners.exists():
        return []
    co_owner_list = []
    for each_co_owner in property_co_owners:
        if each_co_owner.co_owner:
            if each_co_owner.co_owner.profile_photo_key:
                profile_photo = get_s3_object(each_co_owner.co_owner.profile_photo_key)
            else:
                profile_photo = None

            # If the co-owner is a registered user
            co_owner_list.append(
                {
                    "user_id": each_co_owner.co_owner.id,
                    "name": each_co_owner.co_owner.name,
                    "ownership_percentage": each_co_owner.ownership_percentage,
                    "profile_photo": profile_photo,
                    "is_manually_added": False,
                    "phone_number": get_primary_number(
                        each_co_owner.co_owner.user.primary_phone_number
                    ),
                    "phone_code": get_primary_phone_code(
                        each_co_owner.co_owner.user.primary_phone_number
                    ),
                }
            )
        elif each_co_owner.unregistered_co_owner:  # If the co-owner is unregistered
            co_owner_list.append(
                {
                    "user_id": each_co_owner.unregistered_co_owner.id,
                    "name": each_co_owner.unregistered_co_owner.name,
                    "ownership_percentage": each_co_owner.ownership_percentage,
                    "profile_photo": None,
                    "is_manually_added": True,
                    "phone_number": get_primary_number(
                        each_co_owner.unregistered_co_owner.phone_number
                    ),
                    "phone_code": get_primary_phone_code(
                        each_co_owner.unregistered_co_owner.phone_number
                    ),
                }
            )
    return co_owner_list


@log_input_output
def get_property_building_name(property_object: Property):
    if property_object.community:
        for loc_no in range(5, 0, -1):
            last_sub_location = getattr(
                property_object.community, f"sub_loc_{loc_no}", None
            )
            if last_sub_location:
                return last_sub_location

        return property_object.community.name

    return None


@log_input_output
def build_return_values_for_attributes(
    value,
    attribute_name,
    viewable_attributes,
    editable_attributes,
    property_verified_fields_and_values,
    unit_field=None,
    property_object: Property = None,
    make_invisible=False,
    make_non_editable=False,
    display_by_default=False,
    keep_value=False,
    is_common_view=False,
    is_value_exists=True,
):
    verified_value = property_verified_fields_and_values.get(attribute_name, None)
    value = (
        value
        if (attribute_name in viewable_attributes and not make_invisible)
        or display_by_default
        or attribute_name in editable_attributes
        or keep_value
        else None
    )

    # is_visible = (
    #     attribute_name in viewable_attributes and not make_invisible
    # ) or display_by_default

    is_visible = (
        True
        if attribute_name in viewable_attributes
        and not make_invisible
        or display_by_default
        and not (is_common_view and not is_value_exists)
        else False
    )

    is_editable = (
        attribute_name in editable_attributes
        and not verified_value
        and not make_non_editable
    )

    attribute_data = {
        "value": value,
        "is_visible": is_visible,
        "is_editable": is_editable,
    }
    # todo move it to choices
    if unit_field and property_object:
        if property_object.user_unit_preference == "sqft":
            property_unit_type = 0
        elif property_object.user_unit_preference == "sqm":
            property_unit_type = 1
        elif property_object.user_unit_preference == "sqyd":
            property_unit_type = 2
        elif property_object.user_unit_preference == "acre":
            property_unit_type = 3
        elif property_object.user_unit_preference == "hectare":
            property_unit_type = 4
        elif property_object.user_unit_preference == "bigha":
            property_unit_type = 5
        else:
            property_unit_type = 0
        attribute_data.update(
            {
                "property_unit_type": int(property_unit_type),
            }
        )
    return attribute_data


@log_input_output
def get_manual_added_details(property_object: Property) -> list[str]:
    # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
    verified_fields = PropertyVerifiedDataFields.objects.filter(
        property=property_object
    )
    return [field.field_name for field in verified_fields]


@log_input_output
def build_return_values_for_scrollable_component_attributes(
    value,
    attribute_name,
    viewable_attributes,
    editable_attributes,
    property_verified_fields_and_values,
    unit_field=None,
    property_object: Property = None,
    make_non_editable=False,
    is_common_view=False,
):
    verified_value = property_verified_fields_and_values.get(attribute_name, None)
    value = value if attribute_name in viewable_attributes else None

    is_visible = (
        True
        if attribute_name in viewable_attributes
        and not (is_common_view and value is None)
        else False
    )

    is_editable = (
        True
        if attribute_name in editable_attributes
        and not verified_value
        and not make_non_editable
        else False
    )

    attribute_data = {
        "value": value,
        "is_visible": is_visible,
        "is_editable": is_editable,
        "manually_added": True,
    }
    logger.info(f"Manually added fields: {property_verified_fields_and_values}")
    logger.info(f"Attribute name: {attribute_name}")

    if verified_value:
        attribute_data.update({"manually_added": False})

    logger.info(f"manual_added: {attribute_data['manually_added']}")

    if unit_field and property_object:
        if property_object.user_unit_preference == "sqft":
            property_unit_type = 0
        elif property_object.user_unit_preference == "sqm":
            property_unit_type = 1
        elif property_object.user_unit_preference == "sqyd":
            property_unit_type = 2
        elif property_object.user_unit_preference == "acre":
            property_unit_type = 3
        elif property_object.user_unit_preference == "hectare":
            property_unit_type = 4
        elif property_object.user_unit_preference == "bigha":
            property_unit_type = 5
        else:
            property_unit_type = 0
        attribute_data.update({"property_unit_type": property_unit_type})
    return attribute_data


@log_input_output
def get_property_gross_yield(
    property_financial_details: PropertyFinancialDetails,
) -> float:
    gross_yield = None
    if (
        property_financial_details.annual_rent
        and property_financial_details.original_price
    ):
        gross_yield = (
            property_financial_details.annual_rent
            / property_financial_details.original_price
        ) * 100
        gross_yield = round(gross_yield, 2)

    return gross_yield


@log_input_output
def get_property_net_yield(
    property_financial_details: PropertyFinancialDetails,
) -> float:
    net_yield = None

    if property_financial_details.original_price:
        purchase_cost = (
            property_financial_details.original_price
            + property_financial_details.other_expenses
        )

        if not property_financial_details.annual_service_charges:
            annual_service_charges = 0
        else:
            annual_service_charges = property_financial_details.annual_service_charges

        if not property_financial_details.annual_rent:
            annual_rent = 0
        else:
            annual_rent = property_financial_details.annual_rent

        net_annual_income = annual_rent - annual_service_charges
        if net_annual_income:
            net_yield = (net_annual_income / purchase_cost) * 100
            if net_yield:
                net_yield = round(net_yield, 2)
            else:
                net_yield = 0

    return net_yield


@log_input_output
def get_property_gains_data(
    property_financial_details: UserLevelPropertyFinancialDetails,
):
    gain = None
    if (
        property_financial_details.property_level_data.property.owner_intent
        == OwnerIntentForProperty.NOT_FOR_SALE
    ):
        if (
            property_financial_details.valuation
            and property_financial_details.original_price
        ):
            gain = (
                property_financial_details.valuation
                - property_financial_details.original_price
            )
    else:
        if (
            property_financial_details.asking_price
            and property_financial_details.original_price
        ):
            gain = (
                property_financial_details.asking_price
                - property_financial_details.original_price
            )

    gain_percentage = None
    if gain:
        gain_percentage = round(
            (gain / property_financial_details.original_price) * 100, 2
        )
    return gain, gain_percentage


@log_input_output
def build_owner_information_action_button(
    viewer_role, owner_verified, co_owners=None, viewer_profile=None
):
    if viewer_role.name == INVESTOR:
        if co_owners and co_owners.exists():
            if co_owners.filter(co_owner=viewer_profile).exists():
                return None
            return OwnerInformationButtonText.ADD_EDIT_CO_OWNER.value
        else:
            return OwnerInformationButtonText.ADD_CO_OWNER.value
    elif viewer_role.name == AGENT and not owner_verified:
        return OwnerInformationButtonText.ADD_OWNER_DETAILS.value
    else:
        return None


@log_input_output
def build_return_values_portfolio_specification_attributes(
    property_object,
    value,
    attribute_name,
    manually_added_fields,
    portfolio_fields_to_check,
    unit_field=False,
    self_view=False,
    make_not_visible=False,
    is_common_view=False,
    sub_text="",
):
    attribute_data = {
        "value": (
            None
            if portfolio_fields_to_check
            and attribute_name in portfolio_fields_to_check
            and not self_view
            or make_not_visible
            else value
        ),
        "is_visible": (
            False
            if portfolio_fields_to_check
            and attribute_name in portfolio_fields_to_check
            and not self_view
            or make_not_visible
            or (is_common_view and value is None)
            else True
        ),
        "is_editable": False,
        "manually_added": True,
    }
    logger.info(f"Manually added fields: {manually_added_fields}")
    logger.info(f"Attribute name: {attribute_name}")
    if manually_added_fields and attribute_name in manually_added_fields:
        attribute_data.update({"manually_added": False})
    # if property_object.property_monitor_address_id:
    #     if manually_added_fields and (
    #             attribute_name in manually_added_fields
    #             or (
    #                     sub_text
    #                     and any(
    #                 sub_text in item
    #                 for item in manually_added_fields
    #                 if isinstance(item, str)
    #             )
    #             )
    #     ):
    #         attribute_data.update({"manually_added": True})
    # else:
    #     attribute_data.update({"manually_added": True})

    logger.info(f"manual_added: {attribute_data['manually_added']}")

    if unit_field and property_object:
        if property_object.user_unit_preference == "sqft":
            property_unit_type = 0
        elif property_object.user_unit_preference == "sqm":
            property_unit_type = 1
        elif property_object.user_unit_preference == "sqyd":
            property_unit_type = 2
        elif property_object.user_unit_preference == "acre":
            property_unit_type = 3
        elif property_object.user_unit_preference == "hectare":
            property_unit_type = 4
        elif property_object.user_unit_preference == "bigha":
            property_unit_type = 5
        else:
            property_unit_type = 0
        attribute_data.update(
            {
                "property_unit_type": int(property_unit_type),
            }
        )
    return attribute_data


@log_input_output
def build_return_values_portfolio_attributes(
    value,
    attribute_name,
    portfolio_fields_to_check,
    self_view=False,
    make_not_visible=False,
):
    attribute_data = {
        "value": (
            None
            if (
                portfolio_fields_to_check
                and attribute_name in portfolio_fields_to_check
                and not self_view
            )
            or make_not_visible
            else value
        ),
        "is_visible": (
            False
            if (
                portfolio_fields_to_check
                and attribute_name in portfolio_fields_to_check
                and not self_view
            )
            or make_not_visible
            else True
        ),
        "is_editable": False,
    }
    return attribute_data


@log_input_output
@general_exception_handler
def get_claim_property_choices(property_obj: Property, viewer_profile, viewer_role):
    """
    Determine the appropriate claim property choice based on user role and property status.

    Args:
        property_obj: Property object being viewed
        viewer_profile: Profile of the user viewing the property
        viewer_role: Role of the user viewing the property

    Returns:
        str: Appropriate ClaimPropertyChoices value based on conditions
    """
    # First check if property is owner verified

    if not property_obj.owner_verified:
        if viewer_role.name == AGENT:
            if (
                property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE.value
                or property_obj.is_archived
            ):
                return ClaimPropertyChoices.CONTACT_SUPPORT.value
            elif AgentAssociatedProperty.objects.filter(
                property=property_obj, agent_profile=viewer_profile, is_associated=True
            ):
                return ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO.value
            else:
                return ClaimPropertyChoices.ADD_TO_PORTFOLIO.value
        else:
            return ClaimPropertyChoices.CONTACT_SUPPORT.value

    # Handle AGENT role
    if viewer_role.name == AGENT:
        associated_property = AgentAssociatedProperty.objects.filter(
            property=property_obj, agent_profile=viewer_profile
        )

        # Check if property is already in agent's portfolio
        if associated_property.filter(is_associated=True).exists():
            return ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO.value

        # Check if there's a pending request
        if associated_property.filter(
            is_associated=False,
            is_request_expired=False,
            action_status=UserRequestActions.PENDING,
        ).exists():
            return ClaimPropertyChoices.REQUEST_PENDING.value

        if (
            property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE.value
            or property_obj.is_archived
        ):
            return ClaimPropertyChoices.CONTACT_SUPPORT.value

        # If neither, allow requesting access
        return ClaimPropertyChoices.REQUEST_ACCESS.value

    # Handle INVESTOR role
    if viewer_role.name == INVESTOR:
        # Check if investor is the owner
        if property_obj.owner == viewer_profile:
            return ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO.value

        # Check if investor is a co-owner
        co_owner_query = PropertyCoOwner.objects.filter(
            property=property_obj, co_owner=viewer_profile
        )

        if co_owner_query.filter(is_associated=True).exists():
            return ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO.value

        # Check if there's a pending request
        if co_owner_query.filter(
            is_associated=False,
            is_request_expired=False,
            action_status=UserRequestActions.PENDING,
        ).exists():
            return ClaimPropertyChoices.REQUEST_PENDING.value

        if (
            property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE.value
            or property_obj.is_archived
        ):
            return ClaimPropertyChoices.CONTACT_SUPPORT.value

        # If neither, allow requesting access
        return ClaimPropertyChoices.CLAIM_PROPERTY.value

    # Default fallback
    return ClaimPropertyChoices.CONTACT_SUPPORT.value


@log_input_output
def build_return_values_for_portfolio_attributes(
    value,
    attribute_name,
    non_display_fields,
    unit_field=None,
    property_object: Property = None,
    is_default_field=False,
    details_required=False,
    viewer_edit_access=False,
    make_not_visible=False,
):
    value = (
        value
        if (attribute_name not in non_display_fields or details_required)
        else None
    )
    is_visible = not (make_not_visible or attribute_name in non_display_fields)
    is_editable = (
        viewer_edit_access
        and attribute_name in property_editable_attributes
        and is_visible
        and not is_default_field
        and attribute_name not in non_display_fields
    )

    attribute_data = {
        "value": value,
        "is_visible": is_visible,
        "is_editable": is_editable,
    }
    if unit_field and property_object:
        if property_object.user_unit_preference == "sqft":
            property_unit_type = 0
        else:
            property_unit_type = 1
        attribute_data.update(
            {
                "property_unit_type": int(property_unit_type),
            }
        )
    return attribute_data


@log_input_output
def create_user_level_property_data(property_obj, user, role, user_hierarchy):
    """
    Create user level property data for a specific user and property.

    Args:
        property_obj: Property instance
        user: User instance
        role: Role instance
        user_hierarchy: String indicating user hierarchy ('Agent' or 'Owner')

    Returns:
        UserLevelPropertyData instance if successful, None if failed
    """
    try:
        with transaction.atomic():
            # Create UserLevelPropertyData
            property_data_of_creator = UserLevelPropertyData.objects.filter(
                created_by=property_obj.created_by,
                created_by_role=property_obj.created_by_role,
                property=property_obj,
            ).first()

            user_level_property, created = UserLevelPropertyData.objects.get_or_create(
                created_by=user,
                created_by_role=role,
                user_hierarchy=user_hierarchy,
                property=property_obj,
            )

            user_level_property.property_type = property_data_of_creator.property_type
            user_level_property.floor_number = property_data_of_creator.floor_number
            user_level_property.property_unit_type = (
                property_data_of_creator.property_unit_type
            )
            user_level_property.total_area = property_data_of_creator.total_area
            user_level_property.carpet_area = property_data_of_creator.carpet_area
            user_level_property.balcony_area = property_data_of_creator.balcony_area
            user_level_property.number_of_bedrooms = (
                property_data_of_creator.number_of_bedrooms
            )
            user_level_property.number_of_common_bathrooms = (
                property_data_of_creator.number_of_common_bathrooms
            )
            user_level_property.number_of_attached_bathrooms = (
                property_data_of_creator.number_of_attached_bathrooms
            )
            user_level_property.number_of_powder_rooms = (
                property_data_of_creator.number_of_powder_rooms
            )
            user_level_property.parking_available = (
                property_data_of_creator.parking_available
            )
            user_level_property.number_of_covered_parking = (
                property_data_of_creator.number_of_covered_parking
            )
            user_level_property.number_of_open_parking = (
                property_data_of_creator.number_of_open_parking
            )
            user_level_property.parking_number = property_data_of_creator.parking_number
            user_level_property.property_publish_status = (
                property_data_of_creator.property_publish_status
            )
            user_level_property.dewa_id = property_data_of_creator.dewa_id
            user_level_property.user_unit_preference = (
                property_data_of_creator.user_unit_preference
            )
            user_level_property.tenancy_type = property_data_of_creator.tenancy_type
            user_level_property.tenancy_start_date = (
                property_data_of_creator.tenancy_start_date
            )
            user_level_property.tenancy_end_date = (
                property_data_of_creator.tenancy_end_date
            )
            user_level_property.lead_owner_percentage = (
                property_data_of_creator.lead_owner_percentage
            )
            user_level_property.agent_type = property_data_of_creator.agent_type
            user_level_property.number_of_master_bedrooms = (
                property_data_of_creator.number_of_master_bedrooms
            )
            user_level_property.number_of_other_bedrooms = (
                property_data_of_creator.number_of_other_bedrooms
            )
            user_level_property.number_of_maid_rooms = (
                property_data_of_creator.number_of_maid_rooms
            )
            user_level_property.number_of_study_rooms = (
                property_data_of_creator.number_of_study_rooms
            )
            user_level_property.is_archived = property_data_of_creator.is_archived
            user_level_property.total_floors = property_data_of_creator.total_floors
            user_level_property.building_type = property_data_of_creator.building_type
            user_level_property.save()

            # Copy PropertyAvailabilityAndStatus data
            try:
                availability_status = (
                    property_data_of_creator.property_user_level_availability_and_status
                )
                (
                    user_level_availability_status,
                    created,
                ) = UserLevelPropertyAvailabilityAndStatus.objects.get_or_create(
                    property_level_data=user_level_property
                )
                user_level_availability_status.status = availability_status.status
                user_level_availability_status.handover_date = (
                    availability_status.handover_date
                )
                user_level_availability_status.occupancy_status = (
                    availability_status.occupancy_status
                )
                user_level_availability_status.rent_contract_key = (
                    availability_status.rent_contract_key
                )
                user_level_availability_status.rent_contract_file_name = (
                    availability_status.rent_contract_file_name
                )
                user_level_availability_status.rent_contract_file_size = (
                    availability_status.rent_contract_file_size
                )
                user_level_availability_status.rent_available_start_date = (
                    availability_status.rent_available_start_date
                )
                user_level_availability_status.enable_payment_plan = (
                    availability_status.enable_payment_plan
                )
                user_level_availability_status.during_construction = (
                    availability_status.during_construction
                )
                user_level_availability_status.on_handover = (
                    availability_status.on_handover
                )
                user_level_availability_status.enable_post_handover = (
                    availability_status.enable_post_handover
                )
                user_level_availability_status.post_handover_time_frame = (
                    availability_status.post_handover_time_frame
                )
                user_level_availability_status.ownership_proof_key = (
                    availability_status.ownership_proof_key
                )
                user_level_availability_status.ownership_proof_file_name = (
                    availability_status.ownership_proof_file_name
                )
                user_level_availability_status.ownership_proof_file_size = (
                    availability_status.ownership_proof_file_size
                )
                user_level_availability_status.save()

                if availability_status.rent_contract_key:
                    s3_client = S3Client()
                    old_key = availability_status.rent_contract_key
                    if old_key:
                        rent_contract_key = PRESIGNED_POST_STRUCTURES.get(
                            PROPERTY_RENT_CONTRACT, {}
                        ).get(KEY, "")
                        rent_contract_key = rent_contract_key.format(
                            property_id=property_obj.id,
                            filename=f"{user.pk}_{role.name}_{datetime.datetime.now().strftime(DD_MMM_YYYY)}_rent_contract",
                        )
                        s3_client.copy_file(old_key, rent_contract_key)

                        user_level_availability_status.rent_contract_key = (
                            rent_contract_key
                        )
                        user_level_availability_status.rent_contract_file_name = (
                            availability_status.rent_contract_file_name
                        )
                        user_level_availability_status.rent_contract_file_size = (
                            availability_status.rent_contract_file_size
                        )
                        user_level_availability_status.save()

                if availability_status.ownership_proof_key:
                    s3_client = S3Client()
                    old_key = availability_status.ownership_proof_key
                    if old_key:
                        ownership_proof_key = PRESIGNED_POST_STRUCTURES.get(
                            PROPERTY_OWNERSHIP_PROOF, {}
                        ).get(KEY, "")
                        ownership_proof_key = ownership_proof_key.format(
                            property_id=property_obj.id,
                            filename=f"{user.pk}_{role.name}_{datetime.datetime.now().strftime(DD_MMM_YYYY)}_ownership_proof",
                        )
                        s3_client.copy_file(old_key, ownership_proof_key)

                        user_level_availability_status.ownership_proof_key = (
                            ownership_proof_key
                        )
                        user_level_availability_status.ownership_proof_file_name = (
                            availability_status.ownership_proof_file_name
                        )
                        user_level_availability_status.ownership_proof_file_size = (
                            availability_status.ownership_proof_file_size
                        )
                        user_level_availability_status.save()

            except UserLevelPropertyAvailabilityAndStatus.DoesNotExist:
                logger.info(
                    f"No availability status found for property {property_obj.id}"
                )

            # Copy PropertyFinancialDetails data
            try:
                financial_details = (
                    property_data_of_creator.property_user_level_financial_details
                )
                (
                    user_level_financial_details,
                    created,
                ) = UserLevelPropertyFinancialDetails.objects.get_or_create(
                    property_level_data=user_level_property
                )
                user_level_financial_details.property_currency_code = (
                    financial_details.property_currency_code
                )
                user_level_financial_details.original_price = (
                    financial_details.original_price
                )
                user_level_financial_details.asking_price = (
                    financial_details.asking_price
                )
                user_level_financial_details.valuation = financial_details.valuation
                user_level_financial_details.annual_rent = financial_details.annual_rent
                user_level_financial_details.annual_service_charges = (
                    financial_details.annual_service_charges
                )
                user_level_financial_details.security_deposit = (
                    financial_details.security_deposit
                )
                user_level_financial_details.other_expenses = (
                    financial_details.other_expenses
                )
                user_level_financial_details.preferred_payment_frequency = (
                    financial_details.preferred_payment_frequency
                )
                user_level_financial_details.price_negotiable = (
                    financial_details.price_negotiable
                )
                user_level_financial_details.expected_rent = (
                    financial_details.expected_rent
                )
                user_level_financial_details.expected_security_deposit = (
                    financial_details.expected_security_deposit
                )
                user_level_financial_details.save()
            except UserLevelPropertyFinancialDetails.DoesNotExist:
                logger.info(
                    f"No financial details found for property {property_obj.id}"
                )

            # Copy PropertyFeatures data
            try:
                features = UserLevelPropertyFeatures.objects.get(
                    property_level_data=property_data_of_creator
                )
                (
                    user_level_features,
                    created,
                ) = UserLevelPropertyFeatures.objects.get_or_create(
                    property_level_data=user_level_property
                )
                user_level_features.branded_building = features.branded_building
                user_level_features.furnished = features.furnished
                user_level_features.premium_view = features.premium_view
                user_level_features.is_restroom_available = (
                    features.is_restroom_available
                )
                user_level_features.no_of_shared_restrooms = (
                    features.no_of_shared_restrooms
                )
                user_level_features.no_of_private_restrooms = (
                    features.no_of_private_restrooms
                )
                user_level_features.is_parking_available = features.is_parking_available
                user_level_features.public_parking = features.public_parking
                user_level_features.no_of_reserved_parking = (
                    features.no_of_reserved_parking
                )
                user_level_features.reserved_parking_number = (
                    features.reserved_parking_number
                )
                user_level_features.is_lift_available = features.is_lift_available
                user_level_features.common_lift = features.common_lift
                user_level_features.no_of_personal_lift = features.no_of_personal_lift
                user_level_features.security_available = features.security_available
                user_level_features.water_storage_available = (
                    features.water_storage_available
                )
                user_level_features.property_on_main_road = (
                    features.property_on_main_road
                )
                user_level_features.corner_property = features.corner_property
                user_level_features.save()
                # Copy tags
                user_level_features.tags.set(features.tags.all())
            except UserLevelPropertyFeatures.DoesNotExist:
                logger.info(f"No features found for property {property_obj.id}")

            if property_obj.created_by_role.name == AGENT:
                filters = {
                    "property": property_obj,
                    "created_by": property_obj.created_by,
                    "created_by_role": property_obj.created_by_role,
                }
            else:
                filters = {
                    "property": property_obj,
                    "created_by_role": property_obj.created_by_role,
                }
            # Copy floor plans
            try:
                floor_plans = PropertyFloorPlan.objects.filter(**filters)
                count = 1
                for floor_plan in floor_plans:
                    # Copy the media file from S3
                    s3_client = S3Client()
                    old_key = floor_plan.media_file_key
                    if old_key:
                        media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(
                            KEY, ""
                        )

                        media_key = media_key.format(
                            property_id=property_obj.id,
                            document_type="floor_plan_document",
                            user_id=user.id,
                            user_role=role.name,
                            filename=f"{datetime.datetime.now().strftime(DD_MMM_YYYY)}_floor_plan_{count}",
                        )
                        count += 1
                        s3_client.copy_file(old_key, media_key)

                    PropertyFloorPlan.objects.create(
                        property=property_obj,
                        created_by=user,
                        created_by_role=role,
                        media_file_key=media_key,
                        media_file_name=floor_plan.media_file_name,
                        media_file_size=floor_plan.media_file_size,
                        order_no=floor_plan.order_no,
                        media_file_content_type=floor_plan.media_file_content_type,
                    )
            except Exception as e:
                logger.info(
                    f"Error copying floor plans for property {property_obj.id}: {str(e)}"
                )

            # Copy payment plans
            try:
                payment_plans = PropertyPaymentPlan.objects.filter(**filters)
                count = 1
                for payment_plan in payment_plans:
                    # Copy the media file from S3
                    s3_client = S3Client()
                    old_key = payment_plan.media_file_key
                    if old_key:
                        media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(
                            KEY, ""
                        )

                        media_key = media_key.format(
                            property_id=property_obj.id,
                            document_type="payment_plan_document",
                            user_id=user.id,
                            user_role=role.name,
                            filename=f"{datetime.datetime.now().strftime(DD_MMM_YYYY)}_payment_plan_{count}",
                        )
                        count += 1
                        s3_client.copy_file(old_key, media_key)

                    PropertyPaymentPlan.objects.create(
                        property=property_obj,
                        created_by=user,
                        created_by_role=role,
                        media_file_key=media_key,
                        media_file_name=payment_plan.media_file_name,
                        media_file_size=payment_plan.media_file_size,
                        media_file_content_type=payment_plan.media_file_content_type,
                        order_no=payment_plan.order_no,
                    )
            except Exception as e:
                logger.info(
                    f"Error copying payment plans for property {property_obj.id}: {str(e)}"
                )

            return user_level_property

    except Exception as e:
        logger.error(
            f"Error creating user level data for property {property_obj.id} and user {user.id}: {str(e)}"
        )
        return None


def create_user_level_filter(property_obj, user, role_obj):
    if role_obj and role_obj.name == AGENT:
        filters = {
            "property": property_obj,
            "created_by": user,
            "created_by_role": role_obj,
        }
    else:
        filters = {
            "property": property_obj,
            "created_by_role": role_obj,
        }
    return filters


class PropertyUtility:
    def __init__(self, property_obj, user_level_property_obj):
        self.property_obj = property_obj
        self.user_level_property_obj = user_level_property_obj
        self.property_financial_model = PropertyFinancialDetails
        self.user_level_financial_model = UserLevelPropertyFinancialDetails
        self.property_completion_model = PropertyCompletionState
        self.community_model = Community
        self.property_app_name = "properties"

    @log_input_output
    def save_property_completion_state(
        self,
        data_source: str,
        state: str,
        user: object,
        role_obj: object,
        is_completed: bool,
        draft_data: object,
    ) -> None:
        completion_state, created = get_or_create_db_object(
            self.property_completion_model,
            property=self.property_obj,
            data_source=data_source,
            state=state,
        )
        if created:
            completion_state.created_by = user
            completion_state.created_by_role = role_obj
        else:
            completion_state.updated_by = user
            completion_state.updated_by_role = role_obj

        completion_state.is_completed = is_completed
        completion_state.draft_data_json = draft_data
        completion_state.save()

    def get_or_create_financial_obj(self):
        property_financials, created = get_or_create_db_object(
            self.property_financial_model, property=self.property_obj
        )
        return property_financials, created

    def get_or_create_user_level_financial_obj(self):
        user_level_financial, created = get_or_create_db_object(
            self.user_level_financial_model,
            property_level_data=self.user_level_property_obj,
        )
        return user_level_financial, created

    def get_or_create_community_obj(self, validated_data):
        location_id = validated_data.get("location_id")
        community, created = get_or_create_db_object(
            self.community_model, property_monitor_location_id=location_id
        )

        if created:
            community_name = ""
            if validated_data.get("master_development"):
                community_name = validated_data.get("master_development")
            else:
                community_name = validated_data.get("master_community")
            community.name = community_name
            community.sub_loc_1 = validated_data.get("sub_loc_1")
            community.sub_loc_2 = validated_data.get("sub_loc_2")
            community.sub_loc_3 = validated_data.get("sub_loc_3")
            community.sub_loc_4 = validated_data.get("sub_loc_4")
            community.save()
            logger.info(f"Community with {location_id} is created successfully")

        return community, created


@log_input_output
def user_has_dubai_property(user, user_role_object):
    """
    Check if a user has any properties in Dubai or his phone number is of dubai

    Args:
        user: The user to check
        user_role_object: The role of the user (e.g., INVESTOR, AGENT)

    Returns:
        bool: True if user has at least one property in Dubai, False otherwise
    """
    if user.primary_phone_number:
        country_code = user.primary_phone_number.country_code
        if country_code == "971" or country_code == 971:
            return True

    profile = get_profile_object_by_role(user, user_role_object)

    base_filters = {
        "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
        "state__name__iexact": "Dubai",  # Add filter for Dubai city
    }

    if user_role_object.name == INVESTOR:
        # Check properties where user is the owner
        properties_as_owner = Property.objects.filter(
            owner=profile, owner_verified=True, **base_filters
        )
        owner_properties = UserLevelPropertyData.objects.filter(
            property__in=properties_as_owner,
            created_by=profile.user,
            created_by_role=user_role_object,
        )

        # Check properties where user is a co-owner
        properties_with_co_owner = PropertyCoOwner.objects.filter(
            co_owner=profile,
            is_associated=True,
            property__is_archived=False,
            property__state__name__iexact="Dubai",  # Add filter for Dubai city
        ).values_list("property_id", flat=True)

        co_owned_properties = UserLevelPropertyData.objects.filter(
            property__id__in=properties_with_co_owner,
            created_by=F("property__owner__user"),
            created_by_role=user_role_object,
        )

        return owner_properties.exists() or co_owned_properties.exists()

    elif user_role_object.name == AGENT:
        # Check properties associated with agent
        agent_associated_properties = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            agent_profile=profile,
            is_request_expired=False,
            property__state__name__iexact="Dubai",  # Add filter for Dubai city
        ).values_list("property_id", flat=True)

        properties = Property.objects.filter(
            id__in=agent_associated_properties, **base_filters
        ).exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)

        user_level_properties = UserLevelPropertyData.objects.filter(
            property__in=properties,
            created_by=profile.user,
            created_by_role=user_role_object,
        )

        return user_level_properties.exists()

    return False


@log_input_output
def user_has_india_property(user, user_role_object):
    """
    Check if a user has any properties in India or his phone number is of India

    Args:
        user: The user to check
        user_role_object: The role of the user (e.g., INVESTOR, AGENT)

    Returns:
        bool: True if user has at least one property in Dubai, False otherwise
    """
    if user.primary_phone_number:
        country_code = user.primary_phone_number.country_code
        if country_code in [
            "91",
            91,
            "+91",
            "IN",
            "India",
            "INDIA",
            "india",
            "in",
            "ind",
        ]:
            return True

    profile = get_profile_object_by_role(user, user_role_object)

    base_filters = {
        "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
        "country__name__iexact": "India",  # Add filter for India city
    }

    if user_role_object.name == INVESTOR:
        # Check properties where user is the owner
        properties_as_owner = Property.objects.filter(
            owner=profile, owner_verified=True, **base_filters
        )
        owner_properties = UserLevelPropertyData.objects.filter(
            property__in=properties_as_owner,
            created_by=profile.user,
            created_by_role=user_role_object,
        )

        # Check properties where user is a co-owner
        properties_with_co_owner = PropertyCoOwner.objects.filter(
            co_owner=profile,
            is_associated=True,
            property__is_archived=False,
            property__state__name__iexact="Dubai",  # Add filter for Dubai city
        ).values_list("property_id", flat=True)

        co_owned_properties = UserLevelPropertyData.objects.filter(
            property__id__in=properties_with_co_owner,
            created_by=F("property__owner__user"),
            created_by_role=user_role_object,
        )

        return owner_properties.exists() or co_owned_properties.exists()

    elif user_role_object.name == AGENT:
        # Check properties associated with agent
        agent_associated_properties = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            agent_profile=profile,
            is_request_expired=False,
            property__state__name__iexact="Dubai",  # Add filter for Dubai city
        ).values_list("property_id", flat=True)

        properties = Property.objects.filter(
            id__in=agent_associated_properties, **base_filters
        ).exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)

        user_level_properties = UserLevelPropertyData.objects.filter(
            property__in=properties,
            created_by=profile.user,
            created_by_role=user_role_object,
        )

        return user_level_properties.exists()

    return False


def get_property_trends_filters(
    property_obj, viewer_level_property, start_date="", end_date=""
):
    total_area = (
        round(viewer_level_property.total_area)
        if viewer_level_property.total_area
        else None
    )
    twenty_percent = round(total_area * 0.20) if total_area else 0
    category = (
        "rent"
        if property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
        else "sale"
    )
    number_of_bedrooms = property_obj.number_of_bedrooms
    if not number_of_bedrooms is None:
        if number_of_bedrooms == 0:
            number_of_bedrooms = "s"
        else:
            number_of_bedrooms = str(number_of_bedrooms)
    else:
        number_of_bedrooms = ""
    filters = {
        "property_type": property_obj.property_type,
        "number_of_bedrooms": number_of_bedrooms,
        "min_area": str(round(total_area - twenty_percent) if total_area else None),
        "max_area": str(round(total_area + twenty_percent) if total_area else None),
        "start_date": str(start_date),
        "end_date": str(end_date),
        "category": category,
    }
    if property_obj.community.property_monitor_location_id:
        filters.update(
            {
                "master_development": property_obj.community.name,
                "sub_loc_1": property_obj.community.sub_loc_1,
                "sub_loc_2": property_obj.community.sub_loc_2,
                "sub_loc_3": property_obj.community.sub_loc_3,
                "sub_loc_4": property_obj.community.sub_loc_4,
                "sub_loc_5": property_obj.community.sub_loc_5,
                "building": get_property_building_name(property_obj),
                "community_string": build_property_address(property_obj),
            }
        )
    else:
        filters.update(
            {
                "master_development": "",
                "sub_loc_1": "",
                "sub_loc_2": "",
                "sub_loc_3": "",
                "sub_loc_4": "",
                "sub_loc_5": "",
                "building": "",
                "community_string": "",
            }
        )
    return filters
