import logging

from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import BasePermission
from rest_framework.request import Request

from rezio.properties.models import Property, AgentAssociatedProperty, PropertyCoOwner
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.permissions import <PERSON><PERSON><PERSON>enticatedA<PERSON>, IsAuthenticatedInvestor
from rezio.user.utils import (
    get_investor_profile_object,
    get_agent_profile_object,
    get_agent_role_object,
    get_role_object,
)
from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    DJANGO_LOGGER_NAME,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    ResourceNotFoundException,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class IsPropertyOwner(BasePermission):
    message = "Given property does not exists"

    def has_permission(self, request, view):
        """
        Check if the requesting user is the owner of the property

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            investor_profile = get_investor_profile_object(request.user)
            if request.method == "GET":
                if request.query_params.get("property_id", None):
                    property_id = request.query_params.get("property_id")
                else:
                    property_id = view.kwargs["property_id"]
            elif view.kwargs.get("pk", None):
                property_id = view.kwargs["pk"]
            elif view.kwargs.get("property_id", None):
                property_id = view.kwargs["property_id"]
            else:
                property_id = request.data.get("property_id")
            Property.objects.get(
                id=property_id, owner=investor_profile, owner_verified=True
            )
            return True
        except Property.DoesNotExist:
            logger.error("Property does not exist")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Property object with ID {property_id}"
                        f" does not exist for {request.user} with"
                        f" Investor role"
                    },
                }
            )
        except (AttributeError, KeyError) as error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: " IsPropertyOwner Permission class error",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class IsPropertyAssociateAgent(BasePermission):
    message = "Given property does not exists"

    def has_permission(self, request, view):
        """
        Check if the requesting user is an associated agent of the property

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            agent_profile = get_agent_profile_object(request.user)
            if request.method == "GET":
                if request.query_params.get("property_id", None):
                    property_id = request.query_params.get("property_id")
                else:
                    property_id = view.kwargs["property_id"]
            elif view.kwargs.get("pk", None):
                property_id = view.kwargs["pk"]
            elif view.kwargs.get("property_id", None):
                property_id = view.kwargs["property_id"]
            else:
                property_id = request.data.get("property_id")
            AgentAssociatedProperty.objects.get(
                property_id=property_id, agent_profile=agent_profile, is_associated=True
            )
            return True
        except AgentAssociatedProperty.DoesNotExist:
            logger.error("Property does not exist")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Property object with ID {property_id}"
                        f" does not exist for {request.user} with"
                        f" Agent role"
                    },
                }
            )
        except (AttributeError, KeyError) as error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: " IsPropertyAssociateAgent Permission class error",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class IsAgentCreatedProperty(BasePermission):
    def has_permission(self, request, view):
        """
        If given property is created by agent

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            role = get_agent_role_object()
            if request.method == "GET":
                if request.query_params.get("property_id", None):
                    property_id = request.query_params.get("property_id")
                else:
                    property_id = view.kwargs["property_id"]
            elif view.kwargs.get("pk", None):
                property_id = view.kwargs["pk"]
            elif view.kwargs.get("property_id", None):
                property_id = view.kwargs["property_id"]
            else:
                property_id = request.data.get("property_id")
            Property.objects.get(
                id=property_id, created_by=request.user, created_by_role=role
            )
            return True
        except Property.DoesNotExist:
            logger.error("Property does not exist")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Property object with ID {property_id}"
                        f" does not exist for {request.user} with"
                        f" Agent role"
                    },
                }
            )
        except (AttributeError, KeyError) as error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: " IsAgentCreatedProperty Permission class error",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class IsPropertyOwnerOrCoOwner(BasePermission):
    def has_permission(self, request, view):
        """
        Check if the requesting user is the owner or co-owner of the property

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            investor_profile = get_investor_profile_object(request.user)

            if request.method == "GET":
                if request.query_params.get("property_id", None):
                    property_id = request.query_params.get("property_id")
                else:
                    property_id = view.kwargs["property_id"]
            elif view.kwargs.get("pk", None):
                property_id = view.kwargs["pk"]
            elif view.kwargs.get("property_id", None):
                property_id = view.kwargs["property_id"]
            else:
                property_id = request.data.get("property_id")

            is_owner = Property.objects.filter(
                id=property_id, owner=investor_profile, owner_verified=True
            ).exists()

            is_co_owner = PropertyCoOwner.objects.filter(
                co_owner=investor_profile,
                property_id=property_id,
                is_associated=True,
            ).exists()
            if is_owner or is_co_owner:
                return True
            else:
                logger.error("Property does not exist")
                raise ResourceNotFoundException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: f"Property object with ID {property_id}"
                            f" does not exist for {request.user} with"
                            f" Owner or Co-owner"
                        },
                    }
                )
        except (AttributeError, KeyError) as error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: " IsPropertyOwnerOrCoOwner Permission class error",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


def build_permission_classes(request: Request):
    """
    Method to build permission class for agent or investor who has created the property
    """
    # Define your default permissions
    role_name = request.query_params.get("user_role", None)
    role = get_role_object(role_name)
    if role.name == AGENT:
        permission_classes = [IsPropertyAssociateAgent]
    elif role.name == INVESTOR:
        permission_classes = [IsPropertyOwnerOrCoOwner]
    else:
        raise PermissionDenied(detail="Role not found")
    # Return the appropriate permission classes
    return [permission() for permission in permission_classes]


def build_property_creation_permission_classes(request: Request, http_methods):
    """
    Method to build permission class for property creation
    """
    role_name = request.query_params.get("user_role", None)
    role = get_role_object(role_name)
    logger.info(role)
    if role.name == AGENT:
        permission_classes = [IsAuthenticatedAgent]
        if http_methods in ["destroy", "update"]:
            permission_classes.append(IsPropertyAssociateAgent)
    elif role.name == INVESTOR:
        permission_classes = [IsAuthenticatedInvestor]
        if http_methods in ["destroy", "update"]:
            permission_classes.append(IsPropertyOwner)
    else:
        raise PermissionDenied(detail="Role not found")
    return [permission() for permission in permission_classes]
