from django.contrib.postgres.aggregates.general import ArrayAgg
from django.db.models import Value, CharField, F, Prefetch
from django.db.models.expressions import Subquery, OuterRef
from django.db.models.query_utils import Q
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from rezio.properties.models import (
    Property,
    PropertyRentalUnitHistory,
    PropertySalesUnitHistory,
    PropertyCoOwner,
    AgentAssociatedProperty,
    PropertyVerifiedDataFields, UserLevelPropertyData,
)
from rezio.properties.serializers.property_hierarchy_serializer import (
    PortfolioSerializer,
)
from rezio.properties.serializers.web_serializer.property_details_serializer import (
    WebPropertyDetailSerializer,
    WebPropertyFinancialSerializer,
)
from rezio.properties.text_choices import (
    PropertyPublishStatus,
    OwnerIntentForProperty,
    PortfolioFilters,
    UserRequestActions,
    PropertyCategory,
)
from rezio.rezio.constants import (
    PropertyDetailsThrottle,
    PropertyFinancialsThrottle,
    PropertyBasicDetailsThrottle,
    UserPortfolioThrottle,
)
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import get_profile_object_by_role
from rezio.user.text_choices import AgentSubscriptionPlanChoices
from rezio.user.utils import get_role_object, get_user_object, get_agent_profile_object, get_investor_profile_object
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    ResourceNotFoundException,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.hierarchy import Hierarchy
from rezio.utils.paginators import StandardResultsSetPagination


class WebPropertyDetailsView(APIView):
    """
    View to get property details in web
    """

    throttle_classes = [PropertyDetailsThrottle]

    @general_exception_handler
    def get(self, request, property_id):
        # property_obj = get_property_object(property_id)

        property_obj = (
            Property.objects.filter(id=property_id, is_archived=False)
            .annotate(
                preferred_currency=Value(None, output_field=CharField()),
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                property_asking_price=F("propertyfinancialdetails__asking_price"),
                property_valuation_price=F("propertyfinancialdetails__valuation"),
                valuation_data_source=F(
                    "propertyfinancialdetails__valuation_data_source"
                ),
            )
            .first()
        )

        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property does not exists",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property does not exists"},
                }
            )

        if (
            not property_obj.property_publish_status
            == PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Only property added to portfolio can be viewed here"
                    },
                }
            )

        if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property is not for sale"},
                }
            )

        serializer = WebPropertyDetailSerializer(property_obj)
        data = serializer.data

        price_details_data = dict()
        price_details_data["property_currency_code"] = (
            property_obj.property_currency_code
        )
        price_details_data["preferred_currency_code"] = None
        price_details_data["property_currency_asking_price"] = (
            property_obj.property_asking_price
        )
        price_details_data["property_currency_valuation"] = (
            property_obj.property_valuation_price
        )

        data["price_details"] = price_details_data
        data["is_owner"] = None
        data["can_archive"] = None
        data["is_co_owner"] = None
        data["is_added_by_investor"] = None

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )


class WebPropertyFinancialsView(APIView):
    """
    View to get property financials in web
    """

    throttle_classes = [PropertyFinancialsThrottle]

    @general_exception_handler
    def get(self, request, property_id):
        # property_obj = get_property_object(property_id)

        property_obj = (
            (
                Property.objects.filter(id=property_id, is_archived=False).annotate(
                    preferred_currency=Value(None, output_field=CharField()),
                    property_currency_code=F(
                        "propertyfinancialdetails__property_currency_code"
                    ),
                )
            )
            .prefetch_related(
                Prefetch(
                    "rental_history",
                    queryset=PropertyRentalUnitHistory.objects.order_by("-start_date"),
                ),
                Prefetch(
                    "sales_history",
                    queryset=PropertySalesUnitHistory.objects.order_by(
                        "-evidence_date"
                    ),
                ),
            )
            .first()
        )

        if (
            not property_obj.property_publish_status
            == PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Only property added to portfolio can be viewed here"
                    },
                }
            )
        if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property is not for sale"},
                }
            )

        serializer = WebPropertyFinancialSerializer(property_obj)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property financials fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class WebPropertyDetailsViewSet(APIView):
    """
    This API will return the associated attributes of a Property
    """

    throttle_classes = [PropertyDetailsThrottle]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        self_view = False
        if viewed_user_role_object.name == INVESTOR:
            investor_profile = get_investor_profile_object(viewed_user)
            if not investor_profile.profile_public_view:
                raise ResourceNotFoundException({
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    }
                })
        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )

        hierarchy = Hierarchy(
            None,
            None,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
            is_common_view=True,
        )
        property_details = hierarchy.build_property_details_response()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class WebPropertyFinancialDetailsViewSet(APIView):
    """
    This API will return the associated financial attributes of a Property
    """

    throttle_classes = [PropertyFinancialsThrottle]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        # property_obj = get_property_object(property_id)
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        if viewed_user_role_object.name == INVESTOR:
            investor_profile = get_investor_profile_object(viewed_user)
            if not investor_profile.profile_public_view:
                raise ResourceNotFoundException({
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    }
                })
        self_view = False
        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )

        hierarchy = Hierarchy(
            None,
            None,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
            is_common_view=True,
        )
        property_details = hierarchy.build_property_financial_details_response()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class WebPropertyBasicDetailsViewSet(APIView):
    """
    This API will return the associated basic detail attributes of a Property
    """

    throttle_classes = [PropertyBasicDetailsThrottle]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        # property_obj = get_property_object(property_id)
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        if viewed_user_role_object.name == INVESTOR:
            investor_profile = get_investor_profile_object(viewed_user)
            if not investor_profile.profile_public_view:
                raise ResourceNotFoundException({
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    }
                })
        self_view = False
        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )

        hierarchy = Hierarchy(
            None,
            None,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
            is_common_view=True,
        )
        property_details = hierarchy.build_basic_details_response()

        if viewed_user_role_object.name == INVESTOR:
            property_details["profile_card"] = {
                "component_is_editable": False,
                "component_is_visible": False,
                "profile_card_data": {
                    "value": None,
                    "is_editable": False,
                    "is_visible": False,
                },
            }

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class WebPropertyPortfolioViewSet(APIView):
    """
    This API will return the user portfolio of properties
    """

    throttle_classes = [UserPortfolioThrottle]

    def get(self, request, viewed_user_id, viewed_user_role_name):
        filter_list = request.query_params.get("filters", None)
        search_query = request.query_params.get("search", "").strip()
        filters = None
        if filter_list:
            filter_list = [s.strip() for s in filter_list.split(",")]

            valid_filters = set(PortfolioFilters.values)
            filters = [val for val in filter_list if val in valid_filters]

        is_basic_subscription = False
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        agent_profile = None
        viewer_role = None
        self_view = False

        viewed_profile = get_profile_object_by_role(
            viewed_user, viewed_user_role_object
        )

        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }

        if viewed_user_role_object.name == INVESTOR:
            if not viewed_profile.profile_public_view:
                raise ResourceNotFoundException(
                    {
                        KEY_MESSAGE: "Invalid request received",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Data not found"},
                    }
                )
            properties_as_owner = Property.objects.filter(
                owner=viewed_profile, owner_verified=True, **base_filters
            )
            owner_properties = UserLevelPropertyData.objects.filter(
                property__in=properties_as_owner,
                created_by=viewed_profile.user,
                created_by_role=viewed_user_role_object,
            )

            properties_with_co_owner = PropertyCoOwner.objects.filter(
                co_owner=viewed_profile, is_associated=True, property__is_archived=False
            ).values_list("property_id", flat=True)

            co_owned_properties = UserLevelPropertyData.objects.filter(
                property__id__in=properties_with_co_owner,
                created_by=F("property__owner__user"),
                created_by_role=viewed_user_role_object,
            )
            # if not self_view:
            #     properties_with_cow_owner_queryset = (
            #         properties_with_cow_owner_queryset.exclude(
            #             owner_intent=OwnerIntentForProperty.NOT_FOR_SALE
            #         )
            #     )

            user_level_properties = owner_properties | co_owned_properties

        elif viewed_user_role_object.name == AGENT:
            agent_profile = get_agent_profile_object(viewed_user)
            agent_associated_properties = AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=viewed_profile,
                is_request_expired=False,
            ).values_list("property_id", flat=True)

            properties = Property.objects.filter(
                id__in=agent_associated_properties, **base_filters
            ).exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            user_level_properties = UserLevelPropertyData.objects.filter(
                property__in=properties,
                created_by=viewed_profile.user,
                created_by_role=viewed_user_role_object,
            )

        else:
            user_level_properties = UserLevelPropertyData.objects.none()

        sales_property_count = user_level_properties.filter(
            property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_SALE
        ).count()
        rental_property_count = user_level_properties.filter(
            property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_RENT
        ).count()
        commercial_property_count = user_level_properties.filter(
            property__property_category=PropertyCategory.COMMERCIAL
        ).count()
        residential_property_count = user_level_properties.filter(
            property__property_category=PropertyCategory.RESIDENTIAL
        ).count()

        if user_level_properties:
            user_level_properties = user_level_properties.annotate(
                manually_added_fields=Subquery(
                    PropertyVerifiedDataFields.objects.filter(
                        property=OuterRef("property")
                    )
                    .order_by()
                    .values("property")
                    .annotate(field_names=ArrayAgg("field_name"))
                    .values("field_names")[:1]
                ),
            )

            filter_conditions = Q()

            if filters:
                category_filters = list()
                intent_filters = list()
                if "residential" in filters:
                    category_filters.append(PropertyCategory.RESIDENTIAL.value)
                if "commercial" in filters:
                    category_filters.append(PropertyCategory.COMMERCIAL.value)
                if "for_sale" in filters:
                    intent_filters.append(
                        OwnerIntentForProperty.AVAILABLE_FOR_SALE.value
                    )
                if "for_rent" in filters:
                    intent_filters.append(
                        OwnerIntentForProperty.AVAILABLE_FOR_RENT.value
                    )

                if category_filters:
                    filter_conditions &= Q(
                        property__property_category__in=category_filters
                    )

                if intent_filters:
                    filter_conditions &= Q(property__owner_intent__in=intent_filters)
            if filter_conditions:
                user_level_properties = user_level_properties.filter(filter_conditions)

            if search_query:
                search_filters = Q()
                search_filters |= Q(property__country__name__icontains=search_query)
                search_filters |= Q(property__state__name__icontains=search_query)
                search_filters |= Q(property__community__name__icontains=search_query)
                search_filters |= Q(property__community__sub_loc_1__icontains=search_query)
                search_filters |= Q(property__community__sub_loc_2__icontains=search_query)
                search_filters |= Q(property__community__sub_loc_3__icontains=search_query)
                search_filters |= Q(property__community__sub_loc_4__icontains=search_query)
                search_filters |= Q(property__property_type__icontains=search_query)
                # Special handling for studio apartments
                if search_query.lower().strip() in 'studio':
                    search_filters |= (
                            Q(property__property_type__icontains='apartment') &
                            Q(property__number_of_bedrooms=0)
                    )

                if self_view:
                    search_filters |= Q(property__unit_number__icontains=search_query)

                user_level_properties = user_level_properties.filter(search_filters)

            if viewed_user_role_object.name == AGENT:

                is_basic_subscription = (
                        viewed_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC
                )

                if is_basic_subscription:
                    unlocked_ids = agent_profile.unlocked_properties.values_list(
                        "id", flat=True
                    )
                    if not self_view:
                        user_level_properties = user_level_properties.filter(
                            property_id__in=unlocked_ids
                        )

        user_level_properties = user_level_properties.order_by("-property__created_ts")

        context = {
            "self_view": self_view,
            "viewer_role": viewer_role,
            "viewed_role": viewed_user_role_object,
            "viewed_profile": viewed_profile,
            "is_common_view": True,
            "viewed_user": viewed_user,
            "is_basic_subscription": is_basic_subscription,
            "agent_profile": agent_profile,
        }
        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(user_level_properties, request)
        serializer = PortfolioSerializer(paginated_queryset, many=True, context=context)

        response = paginator.get_paginated_response(serializer.data)
        response.data.get("data").update(
            {
                "sales_property_count": sales_property_count,
                "rental_property_count": rental_property_count,
                "commercial_property_count": commercial_property_count,
                "residential_property_count": residential_property_count,
            }
        )

        return response
