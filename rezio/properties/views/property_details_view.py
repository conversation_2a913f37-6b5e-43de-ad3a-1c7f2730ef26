import copy
import logging
import mimetypes
import traceback
from datetime import datetime
from decimal import Decimal
from urllib.parse import urlencode

from django.conf import settings
from django.contrib.postgres.aggregates.general import ArrayAgg
from django.db import transaction
from django.db.models import (
    Sum,
    Value,
    Count,
    Case,
    When,
    F,
    DecimalField,
    Subquery,
    OuterRef,
    Q,
    CharField,
    Prefetch,
)
from django.db.models.functions import Coalesce, Cast, Round
from django.http.response import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.utils.safestring import mark_safe
from rest_framework import status, generics
from rest_framework.response import Response
from rest_framework.views import APIView

from rezio.properties.helper import compress_image, format_amount
from rezio.properties.models import (
    PropertyFinancialDetails,
    UnitSectionMedia,
    PropertyUnitSections,
    PropertyCoOwner,
    UnregisteredCoOwner,
    Property,
    AgentAssociatedProperty,
    UnregisteredOwner,
    PropertyRentalUnitHistory,
    PropertySalesUnitHistory,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    PropertyExternalMedia,
    PropertyExternalMediaSection,
    PropertyExternalMediaDocument,
    PropertyVerifiedDataFields,
    UserLevelPropertyData,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyFeatures,
    PropertyAttributes,
)
from rezio.properties.permissions import (
    IsPropertyOwner,
    IsPropertyAssociateAgent,
    IsPropertyOwnerOrCoOwner,
)
from rezio.properties.permissions import build_permission_classes
from rezio.properties.serializers import (
    SavePropertyAgentSerializer,
    UploadFloorPlanSerializer,
    UploadPaymentPlanSerializer,
    PropertyExternalMediaImageSerializer,
    CommercialPropertyFeaturesSerializer,
)
from rezio.properties.serializers.property_details_serializer import (
    PropertyDetailSerializer,
    PropertyFinancialDetailsSerializer,
    PropertySectionImagesSerializer,
    PropertyUnitSectionImageUploadSerializer,
    PropertySectionSerializer,
    PropertyGetFinancialDetailsSerializer,
    PropertyDewaIDSerializer,
    EditIncomeFromPropertySerializer,
    PropertyFeaturesSerializer,
    CoOwnerSerializer,
    ManualAddedCoOwnerSerializer,
    OwnerIntentSerializer,
    PropertyCostDetailsSerializer,
    AddOwnerSerializer,
    ManualAddedOwnerSerializer,
    AIPropertyDetailSerializer,
    LeaseConditionsSerializer,
    BasicPropertyDetailSerializer,
    PresignedURLRequestSerializer,
    PropertyPaymentPlanSerializer,
    PropertyFloorPlanSerializer,
)
from rezio.properties.serializers.property_hierarchy_serializer import (
    PortfolioSerializer,
)
from rezio.properties.services.property_service import (
    get_property_unit_section_object,
    delete_unit_section_media,
    delete_unit_section_media_from_media_ids,
    edit_section_medias,
    fetch_role_obj_and_name,
    validate_serializer,
    map_media_order,
    delete_media_from_media_ids,
)
from rezio.properties.text_choices import (
    PropertyPublishStatus,
    PropertyAvailabilityStatus,
    OwnerIntentForProperty,
    UserRequestActions,
    RequestType,
    PropertyExternalMediaType,
    PropertyCategory,
    PortfolioFilters,
    PropertyAreaUnit,
    PropertyAttributesCategory,
    PropertyType,
)
from rezio.properties.utils import (
    get_property_object,
    get_s3_object,
    get_primary_phone_code,
    get_primary_number,
    get_exchange_rates,
    build_price_details,
    create_user_level_filter,
    build_property_address,
)
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    PRESIGNED_POST_STRUCTURES,
    PROPERTY_UNIT_SECTION_MEDIA,
    DD_MMM_YYYY,
    KEY,
    FLOOR_PLAN,
    EXTERNAL_MEDIA,
)
from rezio.user.authentication import (
    JWTAuthentication,
    JWTTokenAuthentication,
    FirebaseAuthentication,
)
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import (
    get_profile_object_by_role,
    raise_invalid_data_exception,
)
from rezio.user.models import (
    InvestorProfile,
    AgentProfile,
    UserPropertySharingControlAttributes,
)
from rezio.user.permissions import IsAuthenticatedInvestor, IsAuthenticatedAgent
from rezio.user.serializers import (
    PropertyAssociatedAgentsSerializer,
    ViewAgentProfileSerializer,
)
from rezio.user.text_choices import AgentSubscriptionPlanChoices
from rezio.user.utils import (
    get_agent_role_object,
    get_investor_role_object,
    get_role_object,
    get_list_of_object_with_filters,
    create_db_object,
    get_db_object,
    delete_db_object,
    get_user_object,
    get_or_create_db_object,
    get_agent_profile_object,
    get_investor_profile_object,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
    ResourceNotFoundException,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.hierarchy import Hierarchy
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import SectionChoices

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PropertyDetailsView(APIView):
    """
    This API will be called when user add his/her property in portfolio & then from profile he wants to view that property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id):
        try:
            # property_obj = get_property_object(property_id)
            viewer_role = request.query_params.get("user_role")
            role_obj = get_role_object(viewer_role)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            property_obj = Property.objects.filter(id=property_id, is_archived=False)
            if not property_obj:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Property not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                    }
                )
            # associated_co_owner = None
            # if viewer_role == INVESTOR:
            #     associated_co_owner = PropertyCoOwner.objects.filter(
            #         Q(is_associated=True) |
            #         (Q(is_request_expired=False) & Q(action_status=UserRequestActions.PENDING)),
            #         co_owner=viewer_profile, property_id=property_id
            #     ).exists()
            #
            # if ((viewer_role == AGENT and property_obj.first().owner_intent == OwnerIntentForProperty.NOT_FOR_SALE)
            #         or (viewer_role == INVESTOR and property_obj.first().owner_verified and not (
            #                 property_obj.first().owner == viewer_profile or associated_co_owner))):
            #     raise InvalidSerializerDataException(
            #         {
            #             KEY_MESSAGE: "Property not found",
            #             KEY_PAYLOAD: {},
            #             KEY_ERROR: {
            #                 KEY_ERROR_MESSAGE: "Property not found"
            #             }
            #         }
            #     )
            property_obj = (
                property_obj.annotate(
                    preferred_currency=Value(
                        viewer_profile.preferred_currency_code, output_field=CharField()
                    ),
                    property_currency_code=F(
                        "propertyfinancialdetails__property_currency_code"
                    ),
                    property_asking_price=F("propertyfinancialdetails__asking_price"),
                    property_valuation_price=F("propertyfinancialdetails__valuation"),
                    valuation_data_source=F(
                        "propertyfinancialdetails__valuation_data_source"
                    ),
                )
            ).first()

            if (
                not property_obj.property_publish_status
                == PropertyPublishStatus.ADDED_TO_PORTFOLIO
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Only property added to portfolio can be viewed here"
                        },
                    }
                )

            serializer = PropertyDetailSerializer(
                property_obj,
                context={"viewer": request.user, "viewer_role": viewer_role},
            )
            data = serializer.data

            property_currency_code = property_obj.property_currency_code
            preferred_currency_code = viewer_profile.preferred_currency_code

            price_details_data = build_price_details(
                property_currency_code,
                preferred_currency_code,
                property_obj.property_asking_price,
                property_obj.property_valuation_price,
            )

            data["price_details"] = price_details_data

            if viewer_role == INVESTOR:
                data["is_owner"] = (
                    True
                    if property_obj.owner
                    and property_obj.owner.user == request.user
                    and property_obj.owner_verified
                    else False
                )
                data["can_archive"] = data["is_owner"]
                data["is_co_owner"] = PropertyCoOwner.objects.filter(
                    property=property_obj, co_owner__user=request.user
                ).exists()
            else:
                data["can_archive"] = (
                    True
                    if property_obj.created_by_role.name == AGENT
                    and property_obj.created_by == request.user
                    else False
                )
                data["is_owner"] = False
                data["is_co_owner"] = False

            data["is_editable"] = (
                True if data["is_owner"] and property_obj.owner_verified else False
            )
            data["is_added_by_investor"] = (
                True if property_obj.created_by_role.name == INVESTOR else False
            )

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Details fetched successfully",
                    KEY_PAYLOAD: data,
                    KEY_ERROR: {},
                },
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in GET PropertyDetailView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class SavePrice(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request, property_id):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            property_obj = get_property_object(property_id)
            user_level_filters = create_user_level_filter(
                property_obj, request.user, role_obj
            )
            user_level_property_obj = UserLevelPropertyData.objects.filter(
                **user_level_filters
            ).first()

            # Get the financial details for the property if they already exist
            financial_details = PropertyFinancialDetails.objects.filter(
                property=property_obj
            ).first()
            user_level_property_financial_details = (
                UserLevelPropertyFinancialDetails.objects.filter(
                    property_level_data=user_level_property_obj
                ).first()
            )

            data = {
                "property_level_data": user_level_property_obj.id,
                "property_currency_code": request.data.get("property_currency_code"),
                **{
                    key: value
                    for key, value in request.data.items()
                    if key in ("asking_price", "original_price")
                },
            }

            if user_level_property_financial_details:
                data["updated_by"] = request.user.id
                # If financial details exist, update them using the serializer
                serializer = PropertyFinancialDetailsSerializer(
                    user_level_property_financial_details, data=data
                )
            else:
                data["created_by"] = request.user.id
                # If no financial details exist, create a new record
                serializer = PropertyFinancialDetailsSerializer(data=data)

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            financial_details_obj = serializer.save()
            if user_level_property_financial_details:
                financial_details_obj.updated_by = request.user
                financial_details_obj.updated_by_role = role_obj
            else:
                financial_details_obj.created_by = request.user
                financial_details_obj.created_by_role = role_obj

            financial_details_obj.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"Price saved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in SavePrice - {message} - {error}")
            return InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetPropertyFinancials(APIView):
    """
    This API will be called when user add his/her property in portfolio & then from profile he wants to view that property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id):
        try:
            viewer_role = request.query_params.get("user_role", "Investor")
            role_obj = get_role_object(viewer_role)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            property_obj = Property.objects.filter(id=property_id, is_archived=False)

            if not property_obj:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Property not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                    }
                )

            property_obj = (
                (
                    property_obj.annotate(
                        preferred_currency=Value(
                            viewer_profile.preferred_currency_code,
                            output_field=CharField(),
                        ),
                        property_currency_code=F(
                            "propertyfinancialdetails__property_currency_code"
                        ),
                    )
                )
                .prefetch_related(
                    Prefetch(
                        "rental_history",
                        queryset=PropertyRentalUnitHistory.objects.order_by(
                            "-start_date"
                        ),
                    ),
                    Prefetch(
                        "sales_history",
                        queryset=PropertySalesUnitHistory.objects.order_by(
                            "-evidence_date"
                        ),
                    ),
                )
                .first()
            )
            if (
                not property_obj.property_publish_status
                == PropertyPublishStatus.ADDED_TO_PORTFOLIO
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Only property added to portfolio can be viewed here"
                        },
                    }
                )
            property_currency_code = property_obj.property_currency_code
            preferred_currency_code = viewer_profile.preferred_currency_code
            exchange_rate = get_exchange_rates(
                property_currency_code, preferred_currency_code
            )
            serializer = PropertyGetFinancialDetailsSerializer(
                property_obj,
                context={
                    "exchange_rate": exchange_rate,
                    "preferred_currency_code": preferred_currency_code,
                },
            )
            data = serializer.data

            if viewer_role == INVESTOR:
                data["is_owner"] = (
                    True
                    if property_obj.owner and property_obj.owner.user == request.user
                    else False
                )
            else:
                data["is_owner"] = False

            data["is_editable"] = data["is_owner"]

            # todo handle this when we start getting sales & rental data for different countires
            data["transactions_currency_code"] = property_currency_code

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Details fetched successfully",
                    KEY_PAYLOAD: data,
                    KEY_ERROR: {},
                },
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in GET PropertyDetailView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class SectionTypeListAPIView(APIView):
    """
    API is created to send list of section choices available while user wants to add unit images
    """

    # todo: remove this API after product release, this API will no longer be used as section type are user defined

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request):
        try:
            choices = [choice[1] for choice in SectionChoices.choices]
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Section type list fetched successfully",
                    KEY_PAYLOAD: {"section_types": choices},
                    KEY_ERROR: {},
                },
            )

        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in GET SectionTypeListAPIView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyMediaUploadAPIView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_obj = get_property_object(property_id)

        serializer = PropertyUnitSectionImageUploadSerializer(data=request.data)
        validated_data = validate_serializer(serializer)

        logger.info(f"PropertyMediaUploadAPIView serializer data {serializer.data}")
        if request.data.get("deleted_media"):
            # this block will delete all media connected to that section & add new media
            delete_unit_section_media_from_media_ids(
                request.data["deleted_media"], request.user, role_obj
            )

        if request.data.get("section_id") and not request.data["section_id"] in [
            "0",
            "null",
        ]:
            logger.info("modifying existing section images if section is there")
            property_unit_section = edit_section_medias(
                request.data, serializer, request.user
            )

        else:
            property_unit_section = create_db_object(
                PropertyUnitSections,
                property=property_obj,
                section_type=validated_data.get("section"),
                created_by=request.user,
                attached_balcony=validated_data.get("attached_balcony"),
                created_by_role=role_obj,
            )

            logger.info(
                f"New Data added in Property unit section {property_unit_section}"
            )

        media_files_list = (
            validated_data.get("media_files")
            if validated_data.get("media_files")
            else []
        )

        for media_file in media_files_list:
            media_id = media_file.get("media_id")
            media_type = media_file.get("media_type")
            thumbnail_key = media_file.get("thumbnail_file_key")
            media_order = media_file.get("media_order")
            media_file_key = media_file.get("media_file_key")

            # Generate permanent URLs
            media_url = get_s3_object(media_file_key)
            thumbnail_url = None
            if thumbnail_key:
                thumbnail_url = get_s3_object(thumbnail_key)

            if media_id:
                media_obj = get_db_object(
                    app_name="properties",
                    model_name=UnitSectionMedia.__name__,
                    single_field_value_dict={
                        "id": media_id,
                    },
                )
                media_obj.order_no = media_order
                media_obj.updated_by = request.user
                media_obj.updated_by_role = role_obj
                media_obj.save()
                continue

            create_db_object(
                UnitSectionMedia,
                section=property_unit_section,
                media_type=media_type,
                media_file=media_file_key,
                order_no=media_order,
                created_by=request.user,
                created_by_role=role_obj,
                thumbnail_file=thumbnail_key,
                media_url=media_url,
                thumbnail_url=thumbnail_url,
            )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Unit section images saved successfully",
                KEY_PAYLOAD: {"property_section_id": property_unit_section.id},
                KEY_ERROR: {},
            },
        )

    def delete(self, request, property_id):
        try:
            section_id = request.data.get("section_id")

            # section_cache_key = f"property_media_data_{property_id}_{section_id}"
            # cache.delete(section_cache_key)
            # logger.info(f" Delete PropertyMediaUploadAPIView deleting cache info for {section_cache_key}")

            # all_cache_key = f"property_media_data_{property_id}_all"
            # cache.delete(all_cache_key)
            # logger.info(f" Delete PropertyMediaUploadAPIView deleting cache info for {all_cache_key}")

            if not section_id:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent section id",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid data sent section id"},
                    }
                )

            property_obj = get_property_object(property_id)

            with transaction.atomic():
                section = get_property_unit_section_object(
                    property=property_obj, section_id=section_id
                )

                deleted_media = delete_unit_section_media(section)

                section.delete()

                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Property unit section and related media deleted successfully",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {},
                    },
                )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in delete ection media"
            traceback.print_exc()
            logger.error(f"Error in Delete section media - {message} - {error}")
            return InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyMediaRetrieveAPIView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)

            section_id = request.query_params.get("section_id")
            # this user_role is the role of user whose profile is being viewed by other user
            user_role = request.query_params.get("user_role")
            user_role_object = get_role_object(user_role)

            # Fetch all sections for the property, and filter by section_id if it's provided
            filters = create_user_level_filter(
                property_obj, request.user, user_role_object
            )
            if section_id and int(section_id) != 0:
                # cache_key = f"property_media_data_{property_id}_{section_id}"
                # logger.info(f"cache_key PropertyMediaRetrieveAPIView for section {cache_key} ")
                sections = PropertyUnitSections.objects.filter(
                    id=section_id, **filters
                ).order_by("id")
            else:
                # cache_key = f"property_media_data_{property_id}_all"
                # logger.info(f"cache_key PropertyMediaRetrieveAPIView for all {cache_key} ")
                sections = PropertyUnitSections.objects.filter(**filters).order_by("id")

            # Serialize the section data including media
            serializer = PropertySectionSerializer(sections, many=True)

            data = {"property_media_data": serializer.data, "property_id": property_id}

            # Cache the response with a timeout
            # cache.set(cache_key, data, timeout=settings.CACHE_TIMEOUT)

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property media details fetched successfully",
                    KEY_PAYLOAD: data,
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            traceback.print_exc()
            logger.error(f"Error in PropertyMediaRetrieveAPIView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class UpdateDewaID(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request, property_id):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            property_obj = get_property_object(property_id)

            if "dewa_id" not in request.data:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Error in dewa Id"},
                    }
                )
            # Serialize the data and validate
            serializer = PropertyDewaIDSerializer(
                property_obj, data=request.data, partial=True
            )

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            property_obj = serializer.save()
            property_obj.updated_by = request.user
            property_obj.updated_by_role = role_obj
            property_obj.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property dewa id details added successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred"
            traceback.print_exc()
            logger.error(f"Error in PropertyMediaRetrieveAPIView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class EditIncomeFromPropertyView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request, property_id):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            property_obj = get_property_object(property_id)

            if user_role == AGENT:
                filters = {
                    "property": property_obj,
                    "created_by": request.user,
                    "created_by_role": role_obj,
                }
            else:
                filters = {
                    "property": property_obj,
                    "created_by_role": role_obj,
                }

            user_level_property_obj = UserLevelPropertyData.objects.filter(
                **filters
            ).first()
            (
                property_financials,
                created,
            ) = UserLevelPropertyFinancialDetails.objects.get_or_create(
                property_level_data=user_level_property_obj,
            )
            serializer = EditIncomeFromPropertySerializer(
                data=request.data,
                context={
                    "user_level_property_obj": user_level_property_obj,
                    "property_financials": property_financials,
                },
            )

            if serializer.is_valid():
                # Use serializer to update the property instance
                property_obj = serializer.update_property(
                    user_level_property_obj,
                    serializer.validated_data,
                    request.user,
                    role_obj,
                )
                user_level_property_obj.updated_by = request.user
                user_level_property_obj.updated_by_role = role_obj
                user_level_property_obj.save()
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Property Income details added successfully",
                        KEY_PAYLOAD: {"property_id": property_id},
                        KEY_ERROR: {},
                    },
                )
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in EditIncomeFromPropertyView"
            traceback.print_exc()
            logger.error(f"Error in EditIncomeFromPropertyView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class EditPropertyFeaturesView(APIView):
    """
    API view to edit property features
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        property_obj = get_property_object(property_id)
        _, role_obj = fetch_role_obj_and_name(request)

        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
        )

        (
            user_level_property_feature,
            user_level_property_feature_created,
        ) = get_or_create_db_object(
            UserLevelPropertyFeatures, property_level_data=user_level_property_obj
        )

        logger.info(
            f"Property feature exists {user_level_property_feature}, {user_level_property_feature_created}"
        )

        if property_obj.property_category == PropertyCategory.RESIDENTIAL:
            serializer = PropertyFeaturesSerializer(
                user_level_property_feature,
                data=request.data,
                context={
                    "user": request.user,
                    "created": user_level_property_feature_created,
                    "role_obj": role_obj,
                },
            )
        else:
            serializer = CommercialPropertyFeaturesSerializer(
                user_level_property_feature,
                data=request.data,
                context={
                    "user": request.user,
                    "created": user_level_property_feature_created,
                    "role_obj": role_obj,
                },
            )

        validate_serializer(serializer)

        serializer.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property feature details added successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class PropertyAgentsViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwnerOrCoOwner]

    def post(self, request, property_id):
        """
        Add agents to property
        """
        try:
            property_obj = get_property_object(property_id)
            if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Cannot add agents to property which is not for sale"
                        },
                    }
                )
            data = copy.copy(request.data)
            data["property_id"] = property_id
            serializer = SavePropertyAgentSerializer(
                data=data, context={"user": request.user}
            )
            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property agents added successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyAgentsViewSet"
            traceback.print_exc()
            logger.error(f"Error in PropertyAgentsViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyCoOwnerView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwner]

    def post(self, request, property_id):
        """
        Add a new co-owner which is already registered as an investor in app
        """
        try:
            property_obj = get_property_object(property_id)

            input_data = request.data
            input_data["property"] = property_obj.id
            input_data["created_by"] = request.user.id
            serializer = CoOwnerSerializer(data=input_data)

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property co-owner details added successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyCoOwnerView"
            traceback.print_exc()
            logger.error(f"Error in PropertyCoOwnerView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def put(self, request, property_id):
        """
        Update the ownership percentage for an existing co-owner
        """
        try:
            property_obj = get_property_object(property_id)

            # Find the co-owner instance to update
            co_owner_instance = PropertyCoOwner.objects.filter(
                property=property_id,
                co_owner=request.data.get("co_owner"),
            ).first()

            if not co_owner_instance:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Co-owner not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Co-owner details not found"},
                    }
                )

            input_data = request.data
            input_data["property"] = property_obj.id
            input_data["updated_by"] = request.user.id
            serializer = CoOwnerSerializer(co_owner_instance, data=input_data)

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property co-owner details added successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in update for PropertyCoOwnerView"
            traceback.print_exc()
            logger.error(
                f"Error in update for  PropertyCoOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyManuallyAddedCoOwnerView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwner]

    def post(self, request, property_id):
        """
        Add a new co-owner which is already registered as a investor in app
        """
        try:
            property_obj = get_property_object(property_id)

            input_data = request.data
            input_data["property"] = property_obj.id
            input_data["created_by"] = request.user.id

            serializer = ManualAddedCoOwnerSerializer(data=input_data)

            if not serializer.is_valid():
                errors = serializer.errors
                unregistered_co_owner_errors = {}

                # Move errors for phone_number, name, and email under "unregistered_co_owner"
                for field in ["phone_number", "name", "email"]:
                    if (
                        "unregistered_co_owner" in errors.keys()
                        and field in errors["unregistered_co_owner"]
                    ):
                        unregistered_co_owner_errors[field] = errors[
                            "unregistered_co_owner"
                        ][field]

                if unregistered_co_owner_errors:
                    errors = unregistered_co_owner_errors

                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: errors,
                    }
                )

            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property co-owner details added successfully",
                    KEY_PAYLOAD: {"unregistered_co_owner": serializer.data["id"]},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyManuallyAddedCoOwnerView"
            traceback.print_exc()
            logger.error(
                f"Error in PropertyManuallyAddedCoOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def put(self, request, property_id):
        """
        Update the ownership percentage for an existing co-owner
        """
        try:
            property_obj = get_property_object(property_id)

            if "unregistered_co_owner_id" not in request.data:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "unregistered_co_owner not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Unregistered Co-owner id details not found"
                        },
                    }
                )

            # Find the co-owner instance to update
            co_owner_instance = PropertyCoOwner.objects.filter(
                property=property_id,
                unregistered_co_owner=request.data.get("unregistered_co_owner_id"),
            ).first()

            if not co_owner_instance:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Co-owner not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Co-owner details not found"},
                    }
                )

            input_data = request.data
            input_data["property"] = property_obj.id
            input_data["updated_by"] = request.user.id
            serializer = ManualAddedCoOwnerSerializer(
                co_owner_instance, data=input_data, partial=True
            )

            if not serializer.is_valid():
                errors = serializer.errors
                unregistered_co_owner_errors = {}

                # Move errors for phone_number, name, and email under "unregistered_co_owner"
                for field in ["phone_number", "name", "email"]:
                    if (
                        "unregistered_co_owner" in errors.keys()
                        and field in errors["unregistered_co_owner"]
                    ):
                        unregistered_co_owner_errors[field] = errors[
                            "unregistered_co_owner"
                        ][field]

                if unregistered_co_owner_errors:
                    errors = unregistered_co_owner_errors

                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: errors,
                    }
                )
            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Unregistered co-owner details added successfully",
                    KEY_PAYLOAD: {
                        "unregistered_co_owner": request.data.get(
                            "unregistered_co_owner_id"
                        )
                    },
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = (
                "Unknown error occurred in update for PropertyManuallyAddedCoOwnerView"
            )
            traceback.print_exc()
            logger.error(
                f"Error in update for  PropertyManuallyAddedCoOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetPropertyCoOwnersView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    """
    Get list of coowners linked with the property (Registered & unregistered)
    """

    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)

            # Fetch all co-owners for the property
            property_co_owners = PropertyCoOwner.objects.filter(property=property_obj)

            co_owner_list = []
            for each_co_owner in property_co_owners:
                if each_co_owner.co_owner:
                    if each_co_owner.co_owner.profile_photo_key:
                        profile_photo = get_s3_object(
                            each_co_owner.co_owner.profile_photo_key
                        )
                    else:
                        profile_photo = None

                    # If the co-owner is a registered user
                    co_owner_list.append(
                        {
                            "user_id": each_co_owner.co_owner.id,
                            "name": each_co_owner.co_owner.name,
                            "email": each_co_owner.co_owner.user.email,
                            "primary_phone_number": get_primary_number(
                                each_co_owner.co_owner.user.primary_phone_number
                            ),
                            "primary_phone_code": get_primary_phone_code(
                                each_co_owner.co_owner.user.primary_phone_number
                            ),
                            "ownership_percentage": each_co_owner.ownership_percentage,
                            "profile_photo": profile_photo,
                            "is_manually_added": False,
                            "is_associated": each_co_owner.is_associated,
                            "action_status": each_co_owner.action_status,
                        }
                    )
                elif (
                    each_co_owner.unregistered_co_owner
                ):  # If the co-owner is unregistered
                    co_owner_list.append(
                        {
                            "user_id": each_co_owner.unregistered_co_owner.id,
                            "name": each_co_owner.unregistered_co_owner.name,
                            "email": each_co_owner.unregistered_co_owner.email,
                            "primary_phone_number": get_primary_number(
                                each_co_owner.unregistered_co_owner.phone_number
                            ),
                            "primary_phone_code": get_primary_phone_code(
                                each_co_owner.unregistered_co_owner.phone_number
                            ),
                            "ownership_percentage": each_co_owner.ownership_percentage,
                            "profile_photo": None,
                            "is_manually_added": True,
                            "is_associated": each_co_owner.is_associated,
                            "action_status": each_co_owner.action_status,
                        }
                    )

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property co-owner details fetched successfully",
                    KEY_PAYLOAD: {"co_owner_list": co_owner_list},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in fetching details of coonwers"
            traceback.print_exc()
            logger.error(
                f"Error in getting details of coowners in  GetPropertyCoOwnersView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class DeleteCoOwnerView(APIView):
    """
    API View to delete a co-owner (registered or unregistered) from the property.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwner]

    def delete(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)

            user_id = request.data.get("user_id")
            is_manually_added = request.data.get("is_manually_added")

            # Validate that user_id and is_manually_added are provided
            if user_id is None or is_manually_added is None:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "User_id or is_manually_added key missing"
                        },
                    }
                )

            if is_manually_added:
                # Case: unregistered_co_owner
                unregistered_co_owner = get_object_or_404(
                    UnregisteredCoOwner, id=user_id
                )

                co_ownership_records = PropertyCoOwner.objects.filter(
                    property=property_obj, unregistered_co_owner=unregistered_co_owner
                ).first()

                ownership_percentage_to_add_back = (
                    co_ownership_records.ownership_percentage
                )

                # Delete the co-owner record(s)
                co_ownership_records.delete()

                # Then delete the UnregisteredCoOwner record
                unregistered_co_owner.delete()

            else:
                # Case: co_owner (registered co-owner)
                co_owner = get_object_or_404(InvestorProfile, id=user_id)

                co_ownership_records = PropertyCoOwner.objects.filter(
                    property=property_obj, co_owner=co_owner
                ).first()

                ownership_percentage_to_add_back = (
                    co_ownership_records.ownership_percentage
                )

                # Delete the co-owner record(s)
                co_ownership_records.delete()

            property_obj.lead_owner_percentage += ownership_percentage_to_add_back

            # Ensure the lead owner's percentage does not exceed 100%
            if property_obj.lead_owner_percentage > 100:
                property_obj.lead_owner_percentage = 100

            # Save the updated property record
            property_obj.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"Property co-owner data deleted successfully for {property_id}",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in fetching details of coonwers"
            traceback.print_exc()
            logger.error(
                f"Error in getting details of coowners in  DeleteCoOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyAnalyticsViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    def get(self, request):
        """
        Get analytics for all properties of an investor
        """
        try:
            preferred_currency_code = (
                request.user.investorprofile.preferred_currency_code
            )
            investor_profile = get_investor_profile_object(request.user)
            # all_properties = Property.objects.filter(
            #     Q(owner=investor_profile)
            #     | Q(
            #         id__in=PropertyCoOwner.objects.filter(
            #             co_owner=investor_profile, is_associated=True
            #         ).values("property_id")
            #     ),
            #     property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            #     owner_verified=True,
            #     is_archived=False,
            # )
            properties_as_owner = Property.objects.filter(
                owner=investor_profile,
                owner_verified=True,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            owner_properties = UserLevelPropertyData.objects.filter(
                property__in=properties_as_owner,
                created_by=investor_profile.user,
                created_by_role=get_investor_role_object(),
            )
            properties_with_co_owner = PropertyCoOwner.objects.filter(
                co_owner=investor_profile,
                is_associated=True,
                property__is_archived=False,
            ).values_list("property_id", flat=True)
            co_owned_properties = UserLevelPropertyData.objects.filter(
                property__id__in=properties_with_co_owner,
                created_by=F("property__owner__user"),
                created_by_role=get_investor_role_object(),
            )
            user_level_properties = owner_properties | co_owned_properties
            # all_properties = all_properties.exclude(is_archived=True)
            # user_level_properties = UserLevelPropertyData.objects.filter(
            #     property__in=all_properties, created_by_role=get_investor_role_object()
            # ).select_related("property_user_level_financial_details")
            user_level_properties = user_level_properties.annotate(
                ownership_percentage=Coalesce(
                    Case(
                        # If the user is the lead owner, use lead_owner_percentage
                        When(
                            property__owner__user=request.user,
                            then=Cast(
                                F("property__lead_owner_percentage"),
                                output_field=DecimalField(
                                    max_digits=5, decimal_places=2
                                ),
                            )
                            / 100,
                        ),
                        # If the user is not the lead owner, fetch ownership_percentage from PropertyCoOwner
                        default=Subquery(
                            PropertyCoOwner.objects.filter(
                                property=OuterRef("property"),
                                co_owner__user=request.user,
                            ).values("ownership_percentage")[:1]
                        )
                        / 100,
                        output_field=DecimalField(max_digits=5, decimal_places=2),
                    ),
                    Value(Decimal("0.00")),
                    output_field=DecimalField(max_digits=5, decimal_places=2),
                )
            )
            # Step 1: Get unique property currencies
            property_currencies = user_level_properties.values_list(
                "property_user_level_financial_details__property_currency_code",
                flat=True,
            ).distinct()

            # Step 2: Fetch exchange rates for all the unique property currencies
            exchange_rate_cache = {
                currency: get_exchange_rates(currency, preferred_currency_code)
                for currency in property_currencies
                if currency != preferred_currency_code
            }
            exchange_rate_cases = [
                When(
                    property_user_level_financial_details__property_currency_code=currency,
                    then=Value(rate),
                )
                for currency, rate in exchange_rate_cache.items()
            ]

            properties = user_level_properties.annotate(
                original_price=Round(
                    Coalesce(
                        Cast(
                            F("property_user_level_financial_details__original_price"),
                            output_field=DecimalField(max_digits=20, decimal_places=4),
                        ),
                        Value(Decimal("0.00")),
                    ),
                    3,
                ),
                asking_price=Round(
                    Coalesce(
                        Cast(
                            Case(
                                When(
                                    Q(
                                        property__owner_intent__in=[
                                            "not for sale",
                                            "available for rent",
                                        ]
                                    ),
                                    then=F(
                                        "property_user_level_financial_details__valuation"
                                    ),
                                ),
                                default=F(
                                    "property_user_level_financial_details__asking_price"
                                ),
                            ),
                            output_field=DecimalField(max_digits=20, decimal_places=4),
                        ),
                        Value(Decimal("0.00")),
                    ),
                    3,
                ),
                annual_rent=Round(
                    Case(
                        When(
                            property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED,
                            then=(
                                F("property_user_level_financial_details__annual_rent")
                            ),
                        ),
                        default=Value(Decimal("0.00")),
                        output_field=DecimalField(max_digits=20, decimal_places=4),
                    ),
                    3,
                ),
                annual_service_charges=Round(
                    Coalesce(
                        Cast(
                            F(
                                "property_user_level_financial_details__annual_service_charges"
                            ),
                            output_field=DecimalField(max_digits=20, decimal_places=4),
                        ),
                        Value(Decimal("0.00")),
                    ),
                    2,
                ),
                exchange_rate=Case(
                    *exchange_rate_cases,
                    default=Value(1),
                    output_field=DecimalField(max_digits=12, decimal_places=6),
                ),
                # Converted fields (price * exchange_rate)
                converted_original_price=Round(
                    Cast(
                        F("original_price") * F("exchange_rate"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                converted_asking_price=Round(
                    Cast(
                        F("asking_price") * F("exchange_rate"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                converted_annual_rent=Round(
                    Cast(
                        F("annual_rent") * F("exchange_rate"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                converted_annual_service_charges=Round(
                    Cast(
                        F("annual_service_charges") * F("exchange_rate"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                # Calculated fields (converted_price * ownership_percentage)
                calculated_original_price=Round(
                    Cast(
                        F("converted_original_price") * F("ownership_percentage"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                calculated_asking_price=Round(
                    Cast(
                        F("converted_asking_price") * F("ownership_percentage"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                individual_gain=Round(
                    Cast(
                        (F("converted_asking_price") - F("converted_original_price"))
                        * F("ownership_percentage"),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                annual_rent_rented_property=Round(
                    Case(
                        When(
                            property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED,
                            then=(
                                F("converted_annual_rent") * F("ownership_percentage")
                            ),
                        ),
                        default=Value(Decimal("0.00")),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                annual_service_charge_rented_property=Round(
                    Case(
                        When(
                            property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED,
                            then=(
                                F("converted_annual_service_charges")
                                * F("ownership_percentage")
                            ),
                        ),
                        default=Value(Decimal("0.00")),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                rented_property_original_price=Round(
                    Case(
                        When(
                            property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED,
                            then=(F("calculated_original_price")),
                        ),
                        default=Value(Decimal("0.00")),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
                annual_net_income=Round(
                    Cast(
                        (
                            F("annual_rent_rented_property")
                            - F("annual_service_charge_rented_property")
                        ),
                        output_field=DecimalField(max_digits=25, decimal_places=4),
                    ),
                    3,
                ),
            ).select_related(
                "property_user_level_financial_details",
                "property_user_level_availability_and_status",
            )

            raw_properties_summary = properties.aggregate(
                gain=Round(Coalesce(Sum("individual_gain"), Value(Decimal("0.00"))), 2),
                total_annual_net_income=Round(
                    Coalesce(Sum("annual_net_income"), Value(Decimal("0.00"))), 3
                ),
                property_valuation=Round(
                    Coalesce(Sum("calculated_asking_price"), Value(Decimal("0.00"))), 3
                ),
                total_annual_rent_rented_property=Round(
                    Coalesce(
                        Sum("annual_rent_rented_property"), Value(Decimal("0.00"))
                    ),
                    3,
                ),
                total_original_price_rented_property=Round(
                    Coalesce(
                        Sum("rented_property_original_price"), Value(Decimal("0.00"))
                    ),
                    3,
                ),
                calculated_original_price_sum=Round(
                    Coalesce(Sum("calculated_original_price"), Value(Decimal("0.00"))),
                    3,
                ),
                number_of_properties=Count("id"),
                rented_property_count=Count(
                    "id",
                    filter=Q(
                        property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED
                    ),
                ),
                vacant_property_count=Count(
                    "id",
                    filter=Q(
                        property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.VACANT
                    ),
                ),
                occupied_property_count=Count(
                    "id",
                    filter=Q(
                        property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.OWNER_OCCUPIED
                    ),
                ),
                holiday_home_property_count=Count(
                    "id",
                    filter=Q(
                        property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.HOLIDAY_HOME
                    ),
                ),
            )

            # Performing the final calculations based on the aggregated data
            calculated_original_price_sum = raw_properties_summary[
                "calculated_original_price_sum"
            ]
            calculated_asking_price_sum = raw_properties_summary[
                "property_valuation"
            ] = round(raw_properties_summary["property_valuation"], 3)
            total_annual_rent_rented_property = raw_properties_summary[
                "total_annual_rent_rented_property"
            ]
            total_original_price_rented_property = raw_properties_summary[
                "total_original_price_rented_property"
            ]
            total_gains = raw_properties_summary["gain"] = round(
                raw_properties_summary["gain"], 3
            )
            total_annual_net_income = raw_properties_summary[
                "total_annual_net_income"
            ] = round(raw_properties_summary["total_annual_net_income"], 3)

            if calculated_original_price_sum > 0:
                gains_percentage = round(
                    (total_gains / calculated_original_price_sum) * 100, 2
                )
                is_gain_upward = (
                    calculated_asking_price_sum > calculated_original_price_sum
                )
                net_yield = round(
                    (total_annual_net_income / calculated_original_price_sum) * 100, 2
                )
            else:
                gains_percentage = Decimal("0.00")
                net_yield = Decimal("0.00")
                is_gain_upward = None

            if total_original_price_rented_property > 0:
                gross_yield = round(
                    (
                        total_annual_rent_rented_property
                        / total_original_price_rented_property
                    )
                    * 100,
                    2,
                )
            else:
                gross_yield = Decimal("0.00")

            # Add the calculated fields back to the summary
            properties_summary = {
                **raw_properties_summary,
                "gain_percentage": gains_percentage,
                "net_yield": net_yield,
                "gross_yield": gross_yield,
                "is_gain_upward": is_gain_upward,
                "preferred_currency_code": preferred_currency_code,
            }

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property analytics fetched successfully",
                    KEY_PAYLOAD: properties_summary,
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyAnalyticsViewSet"
            traceback.print_exc()
            logger.error(f"Error in PropertyAnalyticsViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyIntentViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        """
        Update property intent
        """
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            serializer = OwnerIntentSerializer(
                data=request.data,
                context={
                    "property_id": property_id,
                    "user": request.user,
                    "user_role": user_role,
                    "role_obj": role_obj,
                },
            )

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property owner intent details saved successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyIntentViewSet"
            traceback.print_exc()
            logger.error(f"Error in PropertyIntentViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyAssociatedAgentsViewSet(APIView):
    """
    An API to get agent list associated to property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwnerOrCoOwner]

    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)

            current_associated_agents = AgentAssociatedProperty.objects.filter(
                (
                    Q(action_status=UserRequestActions.PENDING)
                    & Q(is_associated=False)
                    & Q(request_type=RequestType.AGENT_INVITE)
                )
                | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True))
                | (
                    Q(action_status=UserRequestActions.ACCEPTED)
                    & Q(request_type=RequestType.INVESTOR_REQUEST)
                ),
                property=property_obj,
                is_request_expired=False,
            ).values_list("agent_profile", flat=True)
            currently_associated_agent_list = AgentProfile.objects.filter(
                id__in=current_associated_agents
            )

            agent_list_serializer = PropertyAssociatedAgentsSerializer(
                currently_associated_agent_list,
                many=True,
                context={"property_id": property_id},
            )

            data = dict()
            data["agents"] = agent_list_serializer.data
            data["agent_type"] = (
                int(property_obj.agent_type) if data["agents"] else None
            )

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Details fetched successfully",
                    KEY_PAYLOAD: data,
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"GetAgentsListView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class PropertyCostView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)

            financial_details = PropertyFinancialDetails.objects.get(
                property__id=property_id
            )

            serializer = PropertyCostDetailsSerializer(financial_details)

            response_data = serializer.data

            response_data["total_area"] = property_obj.total_area
            response_data["is_owner"] = (
                True
                if property_obj.owner and property_obj.owner.user.id == request.user.id
                else False
            )
            response_data["area_unit"] = "sqft"

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Cost Details fetched successfully",
                    KEY_PAYLOAD: response_data,
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in PropertyCostView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class PropertyOwnerView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyAssociateAgent]

    def post(self, request, property_id):
        """
        Add owner which is already registered as an investor in app
        """
        try:
            property_obj = get_property_object(property_id)
            role_obj = get_agent_role_object()
            serializer = AddOwnerSerializer(
                property_obj,
                data=request.data,
                context={"user": request.user, "role_obj": role_obj},
            )

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property owner details added successfully",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyOwnerView"
            traceback.print_exc()
            logger.error(f"Error in PropertyOwnerView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyManuallyAddedOwnerView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyAssociateAgent]

    def post(self, request, property_id):
        """
        Add a new owner which is already registered as a investor in app
        """
        try:
            input_data = request.data
            input_data["property_id"] = property_id

            serializer = ManualAddedOwnerSerializer(
                data=input_data, context={"user": request.user}
            )

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            serializer.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property owner details added successfully",
                    KEY_PAYLOAD: {"unregistered_owner": serializer.data["id"]},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in PropertyManuallyAddedOwnerView"
            traceback.print_exc()
            logger.error(
                f"Error in PropertyManuallyAddedOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def put(self, request, property_id):
        """
        Update the owner details for an existing owner
        """
        try:
            property_obj = get_property_object(property_id)
            unregistered_owner = UnregisteredOwner.objects.filter(
                id=request.data.get("id")
            )
            if not property_obj.unregistered_owner or not unregistered_owner.exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Owner not found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Owner details not found"},
                    }
                )

            input_data = request.data
            input_data["property_id"] = property_id
            serializer = ManualAddedOwnerSerializer(
                unregistered_owner.first(), data=input_data, partial=True
            )
            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property Unregistered owner details added successfully",
                    KEY_PAYLOAD: {"unregistered_owner": serializer.data["id"]},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = (
                "Unknown error occurred in update for PropertyManuallyAddedOwnerView"
            )
            traceback.print_exc()
            logger.error(
                f"Error in update for  PropertyManuallyAddedOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetPropertyOwnerView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyAssociateAgent]
    """
    Get owner details linked with the property (Registered or unregistered)
    """

    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)
            return_object = {}
            if property_obj.owner:
                owner = property_obj.owner
                return_object = {
                    "id": owner.id,
                    "user_id": owner.user_id,
                    "name": owner.name,
                    "email": owner.email,
                    "primary_phone_number": get_primary_number(
                        owner.user.primary_phone_number
                    ),
                    "primary_phone_code": get_primary_phone_code(
                        owner.user.primary_phone_number
                    ),
                    "profile_photo": (
                        get_s3_object(owner.profile_photo_key)
                        if owner.profile_photo_key
                        else None
                    ),
                    "is_manually_added": False,
                }
            elif property_obj.unregistered_owner:
                unregistered_owner = property_obj.unregistered_owner
                return_object = {
                    "id": unregistered_owner.id,
                    "user_id": None,
                    "name": unregistered_owner.name,
                    "email": unregistered_owner.email,
                    "primary_phone_number": get_primary_number(
                        unregistered_owner.phone_number
                    ),
                    "primary_phone_code": get_primary_phone_code(
                        unregistered_owner.phone_number
                    ),
                    "profile_photo": None,
                    "is_manually_added": True,
                }

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property owner details fetched successfully",
                    KEY_PAYLOAD: {"owner_data": return_object},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in fetching details of onwer"
            traceback.print_exc()
            logger.error(
                f"Error in getting details of owner in  GetPropertyOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class DeleteOwnerView(APIView):
    """
    API View to delete a owner (registered or unregistered) from the property.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyAssociateAgent]

    def delete(self, request, property_id):
        try:
            property_obj = get_property_object(property_id)
            if property_obj.created_by_role.name == INVESTOR:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Cannot remove the owner"},
                    }
                )
            agent_role = get_agent_role_object()
            owner_id = request.data.get("owner_id")
            is_manually_added = request.data.get("is_manually_added")

            # Validate that user_id and is_manually_added are provided
            if owner_id is None or is_manually_added is None:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "owner or is_manually_added key missing"
                        },
                    }
                )

            if (is_manually_added and not property_obj.unregistered_owner) or (
                not is_manually_added and not property_obj.owner
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Owner details not found"},
                    }
                )

            if is_manually_added:
                # Case: unregistered_co_owner
                property_obj.unregistered_owner = None
                get_object_or_404(UnregisteredOwner, id=owner_id).delete()

            else:
                # Case: co_owner (registered co-owner)
                owner = get_object_or_404(InvestorProfile, id=owner_id)
                property_obj.owner = None

            # Save the updated property record
            property_obj.updated_by = request.user
            property_obj.updated_by_role = agent_role
            property_obj.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"Property owner data deleted successfully for {property_id}",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred in fetching details of coonwers"
            traceback.print_exc()
            logger.error(
                f"Error in getting details of coowners in  DeleteCoOwnerView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class UploadFloorPlanView(APIView):
    """
    View to add/remove floor plan
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        property_obj = get_property_object(property_id)
        serializer = UploadFloorPlanSerializer(data=request.data)
        validated_data = validate_serializer(serializer)

        document_media_file = validated_data.get("document_media_file")
        image_media_files = validated_data.get("image_media_files")
        image_media_order = validated_data.get("image_media_order")
        is_deleted_document_media = validated_data.get("is_deleted_document_media")
        deleted_image_media = validated_data.get("deleted_image_media")

        s3_client = S3Client()
        user_role, role_obj = fetch_role_obj_and_name(request)

        # To delete uploaded floor plan image media files
        if role_obj.name == AGENT:
            filters = {
                "property": property_obj,
                "created_by": request.user,
                "created_by_role": role_obj,
            }
        else:
            filters = {"property": property_obj, "created_by_role": role_obj}
        if deleted_image_media:
            delete_media_from_media_ids(
                deleted_image_media,
                "properties",
                PropertyFloorPlan.__name__,
                filters,
            )

        # Check if the floor plan is document media file
        if document_media_file:
            uploaded_floor_plan = get_list_of_object_with_filters(
                "properties",
                PropertyFloorPlan.__name__,
                filters,
                None,
            )

            # If floor plan document media file is already there then raise an error
            if uploaded_floor_plan:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Floor plan document already exist"
                        },
                    }
                )

            mime_type, _ = mimetypes.guess_type(document_media_file.name)
            media_file_name = document_media_file.name
            media_file_size = document_media_file.size

            media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(KEY, "")

            media_key = media_key.format(
                property_id=property_id,
                document_type="floor_plan_document",
                user_id=request.user.pk,
                user_role=user_role,
                filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_floor_plan",
            )

            s3_client.upload_file(document_media_file, media_key)

            create_db_object(
                PropertyFloorPlan,
                property=property_obj,
                media_file_key=media_key,
                media_file_name=media_file_name,
                media_file_size=media_file_size,
                media_file_content_type=mime_type,
                created_by=request.user,
                created_by_role=role_obj,
            )

        # To delete uploaded floor plan document media file
        elif is_deleted_document_media:
            uploaded_floor_plan = PropertyFloorPlan.objects.filter(
                property_id=property_id, **filters
            ).first()
            file_key = uploaded_floor_plan.media_file_key
            if not file_key:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Floor plan document does not exist"
                        },
                    }
                )

            s3_client.delete_file(file_key)
            uploaded_floor_plan.delete()

        # To upload images or update order of uploaded images
        if image_media_files or image_media_order:
            uploaded_floor_plan = get_list_of_object_with_filters(
                "properties",
                PropertyFloorPlan.__name__,
                filters,
                {"media_file_content_type": settings.ALLOWED_DOCUMENT_MIME_TYPES},
            )

            # If floor plan document media file is already there then raise an error
            if uploaded_floor_plan:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Floor plan document already exist"
                        },
                    }
                )

            order_no_list, existing_media_new_sequence = map_media_order(
                image_media_order,
                "properties",
                PropertyFloorPlan.__name__,
                filters,
            )

            if len(order_no_list) != len(image_media_files):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Order no does not match with image media files",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Order no does not match with image media files"
                        },
                    }
                )
            order_index = 0

            for media_file in image_media_files:
                mime_type, _ = mimetypes.guess_type(media_file.name)
                media_file_name = media_file.name
                media_file_size = media_file.size

                media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(KEY, "")

                media_key = media_key.format(
                    property_id=property_id,
                    document_type="floor_plan_image",
                    user_id=request.user.pk,
                    user_role=user_role,
                    filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_floor_plan_{order_no_list[order_index]}",
                )

                s3_client.upload_file(media_file, media_key)

                create_db_object(
                    PropertyFloorPlan,
                    property=property_obj,
                    media_file_key=media_key,
                    media_file_name=media_file_name,
                    media_file_size=media_file_size,
                    media_file_content_type=mime_type,
                    order_no=order_no_list[order_index],
                    created_by=request.user,
                    created_by_role=role_obj,
                )
                order_index += 1
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property floor plan updated successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class UploadPaymentPlanView(APIView):
    """
    View to add/remove payment plan
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        property_obj = get_property_object(property_id)
        serializer = UploadPaymentPlanSerializer(data=request.data)
        validated_data = validate_serializer(serializer)

        document_media_file = validated_data.get("document_media_file")
        image_media_files = validated_data.get("image_media_files")
        image_media_order = validated_data.get("image_media_order")
        is_deleted_document_media = validated_data.get("is_deleted_document_media")
        deleted_image_media = validated_data.get("deleted_image_media")

        s3_client = S3Client()
        user_role, role_obj = fetch_role_obj_and_name(request)

        # To delete uploaded floor plan image media files
        if role_obj.name == AGENT:
            filters = {
                "property": property_obj,
                "created_by": request.user,
                "created_by_role": role_obj,
            }
        else:
            filters = {"property": property_obj, "created_by_role": role_obj}
        if deleted_image_media:
            delete_media_from_media_ids(
                deleted_image_media,
                "properties",
                PropertyPaymentPlan.__name__,
                filters,
            )

        # Check if the floor plan is document media file
        if document_media_file:
            uploaded_payment_plan = get_list_of_object_with_filters(
                "properties",
                PropertyPaymentPlan.__name__,
                filters,
                None,
            )

            # If floor plan document media file is already there then raise an error
            if uploaded_payment_plan:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Payment plan document already exist"
                        },
                    }
                )

            mime_type, _ = mimetypes.guess_type(document_media_file.name)
            media_file_name = document_media_file.name
            media_file_size = document_media_file.size

            media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(KEY, "")

            media_key = media_key.format(
                property_id=property_id,
                document_type="payment_plan_document",
                user_id=request.user.pk,
                user_role=user_role,
                filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_payment_plan",
            )

            s3_client.upload_file(document_media_file, media_key)

            create_db_object(
                PropertyPaymentPlan,
                property=property_obj,
                media_file_key=media_key,
                media_file_name=media_file_name,
                media_file_size=media_file_size,
                media_file_content_type=mime_type,
                created_by=request.user,
                created_by_role=role_obj,
            )

        # To delete uploaded floor plan document media file
        elif is_deleted_document_media:
            uploaded_payment_plan = PropertyPaymentPlan.objects.filter(
                property_id=property_id, **filters
            ).first()
            file_key = uploaded_payment_plan.media_file_key
            if not file_key:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Payment plan document does not exist"
                        },
                    }
                )

            s3_client.delete_file(file_key)
            uploaded_payment_plan.delete()

        # To upload images or update order of uploaded images
        if image_media_files or image_media_order:
            uploaded_payment_plan = get_list_of_object_with_filters(
                "properties",
                PropertyPaymentPlan.__name__,
                filters,
                {"media_file_content_type": settings.ALLOWED_DOCUMENT_MIME_TYPES},
            )

            # If floor plan document media file is already there then raise an error
            if uploaded_payment_plan:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Payment plan document already exist"
                        },
                    }
                )

            order_no_list, existing_media_new_sequence = map_media_order(
                image_media_order,
                "properties",
                PropertyPaymentPlan.__name__,
                filters,
            )

            if len(order_no_list) != len(image_media_files):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Order no does not match with image media files",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Order no does not match with image media files"
                        },
                    }
                )
            order_index = 0

            for media_file in image_media_files:
                mime_type, _ = mimetypes.guess_type(media_file.name)
                media_file_name = media_file.name
                media_file_size = media_file.size

                media_key = PRESIGNED_POST_STRUCTURES.get(FLOOR_PLAN, {}).get(KEY, "")

                media_key = media_key.format(
                    property_id=property_id,
                    document_type="payment_plan_image",
                    user_id=request.user.pk,
                    user_role=user_role,
                    filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_payment_plan_{order_no_list[order_index]}",
                )

                s3_client.upload_file(media_file, media_key)

                create_db_object(
                    PropertyPaymentPlan,
                    property=property_obj,
                    media_file_key=media_key,
                    media_file_name=media_file_name,
                    media_file_size=media_file_size,
                    media_file_content_type=mime_type,
                    order_no=order_no_list[order_index],
                    created_by=request.user,
                    created_by_role=role_obj,
                )

                order_index += 1

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property payment plan updated successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class AIPropertyDetailsView(APIView):
    """
    API view for get property details API for AI
    """

    authentication_classes = [JWTTokenAuthentication]

    @general_exception_handler
    def get(self, request, property_id):
        property_obj = Property.objects.filter(id=property_id, is_archived=False)
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )
        property_obj = (
            property_obj.annotate(
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                property_asking_price=F("propertyfinancialdetails__asking_price"),
                property_valuation_price=F("propertyfinancialdetails__valuation"),
                valuation_data_source=F(
                    "propertyfinancialdetails__valuation_data_source"
                ),
            ).prefetch_related(
                Prefetch(
                    "rental_history",
                    queryset=PropertyRentalUnitHistory.objects.order_by("-start_date"),
                ),
                Prefetch(
                    "sales_history",
                    queryset=PropertySalesUnitHistory.objects.order_by(
                        "-evidence_date"
                    ),
                ),
            )
        ).first()

        if (
            not property_obj.property_publish_status
            == PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Only property added to portfolio can be viewed here"
                    },
                }
            )

        serializer = AIPropertyDetailSerializer(property_obj)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully for AI",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class PropertyExternalMediaImage(APIView):
    """
    API view for property external media image
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        property_obj = get_property_object(property_id)

        serializer = PropertyExternalMediaImageSerializer(data=request.data)
        validated_data = validate_serializer(serializer)

        section_id = validated_data.get("section_id")
        title = validated_data.get("title")
        image_media_files = validated_data.get("image_media_files")
        image_media_order = validated_data.get("image_media_order")
        deleted_image_media = validated_data.get("deleted_image_media")
        user_role, role_obj = fetch_role_obj_and_name(request)

        s3_client = S3Client()

        if section_id:
            # If section id is present then getting the id
            media_section_object = get_db_object(
                "properties",
                PropertyExternalMediaSection.__name__,
                {"id": section_id},
                None,
                f"External media section id {section_id} for {property_obj} not found",
            )

            # If deleted media is present then deleting them from S3 and DB
            if deleted_image_media:
                delete_media_from_media_ids(
                    deleted_image_media,
                    "properties",
                    PropertyExternalMediaDocument.__name__,
                    {"property_external_media_section": media_section_object.id},
                )
        else:
            # Create external media object
            media_object = create_db_object(
                PropertyExternalMedia,
                property=property_obj,
                media_type=PropertyExternalMediaType.IMAGE,
                created_by=request.user,
                created_by_role=role_obj,
            )

            # Crete section object
            media_section_object = create_db_object(
                PropertyExternalMediaSection,
                property_external_media=media_object,
                title=title,
                created_by=request.user,
                created_by_role=role_obj,
            )

        if image_media_files or image_media_order:
            order_no_list, existing_media_new_sequence = map_media_order(
                image_media_order,
                "properties",
                PropertyExternalMediaDocument.__name__,
                {"property_external_media_section": media_section_object},
                None,
                20,
            )

            if len(order_no_list) != len(image_media_files):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Order no does not match with image media files",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Order no does not match with image media files"
                        },
                    }
                )
            order_index = 0

            for media_file in image_media_files:
                mime_type, _ = mimetypes.guess_type(media_file.name)
                media_file_name = media_file.name
                media_file_size = media_file.size

                media_key = PRESIGNED_POST_STRUCTURES.get(EXTERNAL_MEDIA, {}).get(
                    KEY, ""
                )

                media_key = media_key.format(
                    property_id=property_id,
                    section_id=media_section_object.id,
                    document_type="image",
                    filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_external_media_image_{order_no_list[order_index]}",
                )

                s3_client.upload_file(media_file, media_key)

                create_db_object(
                    PropertyExternalMediaDocument,
                    property_external_media_section=media_section_object,
                    media_file_key=media_key,
                    media_file_name=media_file_name,
                    media_file_size=media_file_size,
                    media_file_content_type=mime_type,
                    order_no=order_no_list[order_index],
                    created_by=request.user,
                    created_by_role=role_obj,
                )

                order_index += 1

        if section_id:
            # Get remaining documents after deletion
            remaining_section_media = get_list_of_object_with_filters(
                "properties",
                PropertyExternalMediaDocument.__name__,
                {"property_external_media_section": media_section_object.id},
                None,
            )

            # If no remaining document after deletion
            if not remaining_section_media:
                # Remove section media record
                media_section_object.delete()

                # Remove external media record
                delete_db_object(
                    "properties",
                    PropertyExternalMedia.__name__,
                    {"id": media_section_object.property_external_media.id},
                    None,
                    f"External media for {property_obj} not found",
                )
            else:
                media_section_object.title = title
                media_section_object.updated_by = request.user
                media_section_object.updated_by_role = role_obj
                media_section_object.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "External media image is updated successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class PropertyDetailsViewSet(APIView):
    """
    This API will return the associated attributes of a Property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        # property_obj = get_property_object(property_id)
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)
        self_view = False
        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )
        hierarchy = Hierarchy(
            request.user,
            viewer_user_role_object,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
        )
        property_details = hierarchy.build_property_details_response()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class PropertyBasicDetailsViewSet(APIView):
    """
    This API will return the basic details of Property like address, specifications etc
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)
        self_view = False

        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

            # block to check if property is locked then return error
            if viewed_user_role_object.name == AGENT:
                viewed_profile = get_profile_object_by_role(
                    viewed_user, viewed_user_role_object
                )
                is_basic_subscription = (
                    viewed_profile.subscription_status
                    == AgentSubscriptionPlanChoices.BASIC
                )

                agent_profile = get_agent_profile_object(viewed_user)

                if (
                    is_basic_subscription
                    and not property_obj in agent_profile.unlocked_properties.all()
                ):
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Access Denied",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {KEY_ERROR_MESSAGE: "This property is locked"},
                        }
                    )

        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )

        hierarchy = Hierarchy(
            request.user,
            viewer_user_role_object,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
        )
        property_details = hierarchy.build_basic_details_response()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class PropertyFinancialDetailsViewSet(APIView):
    """
    This API will return the financial details of Property like asking price, valuation etc
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)
        self_view = False
        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        exclude_filter = {}
        if viewed_user_role_name == AGENT:
            exclude_filter = {"owner_intent": OwnerIntentForProperty.NOT_FOR_SALE}
        property_obj = (
            Property.objects.filter(id=property_id, **base_filters)
            .exclude(**exclude_filter)
            .first()
        )
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (
            not self_view
            and property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
        ):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )

        hierarchy = Hierarchy(
            request.user,
            viewer_user_role_object,
            property_obj,
            self_view,
            viewed_user,
            viewed_user_role_object,
        )
        property_details = hierarchy.build_property_financial_details_response()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Details fetched successfully",
                KEY_PAYLOAD: property_details,
                KEY_ERROR: {},
            },
        )


class PropertyPortfolioViewSet(APIView):
    """
    This API will return the financial details of Property like asking price, valuation etc
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, viewed_user_id, viewed_user_role_name):
        filter_list = request.query_params.get("filters", None)
        search_query = request.query_params.get("search", "").strip()
        agent_profile = None
        filters = None
        if filter_list:
            filter_list = [s.strip() for s in filter_list.split(",")]

            valid_filters = set(PortfolioFilters.values)
            filters = [val for val in filter_list if val in valid_filters]

        is_basic_subscription = False
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)

        self_view = False

        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

        if (
            not self_view
            and viewed_user_role_object.name == INVESTOR
            and viewer_user_role_object.name == INVESTOR
        ):
            raise_invalid_data_exception(
                "An investor cannot view other investors profile"
            )

        viewer_profile = get_profile_object_by_role(
            request.user, viewer_user_role_object
        )
        viewed_profile = get_profile_object_by_role(
            viewed_user, viewed_user_role_object
        )

        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }

        if viewed_user_role_object.name == INVESTOR:
            properties_as_owner = Property.objects.filter(
                owner=viewed_profile, owner_verified=True, **base_filters
            )
            owner_properties = UserLevelPropertyData.objects.filter(
                property__in=properties_as_owner,
                created_by=viewed_profile.user,
                created_by_role=viewed_user_role_object,
            )

            properties_with_co_owner = PropertyCoOwner.objects.filter(
                co_owner=viewed_profile, is_associated=True, property__is_archived=False
            ).values_list("property_id", flat=True)

            co_owned_properties = UserLevelPropertyData.objects.filter(
                property__id__in=properties_with_co_owner,
                created_by=F("property__owner__user"),
                created_by_role=viewed_user_role_object,
            )

            user_level_properties = owner_properties | co_owned_properties

        elif viewed_user_role_object.name == AGENT:
            agent_profile = get_agent_profile_object(viewed_user)
            agent_associated_properties = AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=viewed_profile,
                is_request_expired=False,
            ).values_list("property_id", flat=True)

            properties = Property.objects.filter(
                id__in=agent_associated_properties, **base_filters
            ).exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            user_level_properties = UserLevelPropertyData.objects.filter(
                property__in=properties,
                created_by=viewed_profile.user,
                created_by_role=viewed_user_role_object,
            )

        else:
            user_level_properties = UserLevelPropertyData.objects.none()

        sales_property_count = user_level_properties.filter(
            property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_SALE
        ).count()
        rental_property_count = user_level_properties.filter(
            property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_RENT
        ).count()
        commercial_property_count = user_level_properties.filter(
            property__property_category=PropertyCategory.COMMERCIAL
        ).count()
        residential_property_count = user_level_properties.filter(
            property__property_category=PropertyCategory.RESIDENTIAL
        ).count()

        if user_level_properties:
            user_level_properties = user_level_properties.annotate(
                preferred_currency_code=Value(
                    viewer_profile.preferred_currency_code, output_field=CharField()
                ),
                manually_added_fields=Subquery(
                    PropertyVerifiedDataFields.objects.filter(
                        property=OuterRef("property")
                    )
                    .order_by()
                    .values("property")
                    .annotate(field_names=ArrayAgg("field_name"))
                    .values("field_names")[:1]
                ),
            )

            filter_conditions = Q()

            if filters:
                category_filters = list()
                intent_filters = list()
                if "residential" in filters:
                    category_filters.append(PropertyCategory.RESIDENTIAL.value)
                if "commercial" in filters:
                    category_filters.append(PropertyCategory.COMMERCIAL.value)
                if "for_sale" in filters:
                    intent_filters.append(
                        OwnerIntentForProperty.AVAILABLE_FOR_SALE.value
                    )
                if "for_rent" in filters:
                    intent_filters.append(
                        OwnerIntentForProperty.AVAILABLE_FOR_RENT.value
                    )

                if category_filters:
                    filter_conditions &= Q(
                        property__property_category__in=category_filters
                    )

                if intent_filters:
                    filter_conditions &= Q(property__owner_intent__in=intent_filters)
            if filter_conditions:
                user_level_properties = user_level_properties.filter(filter_conditions)

            if search_query:
                search_filters = Q()
                search_filters |= Q(property__country__name__icontains=search_query)
                search_filters |= Q(property__state__name__icontains=search_query)
                search_filters |= Q(property__community__name__icontains=search_query)
                search_filters |= Q(
                    property__community__sub_loc_1__icontains=search_query
                )
                search_filters |= Q(
                    property__community__sub_loc_2__icontains=search_query
                )
                search_filters |= Q(
                    property__community__sub_loc_3__icontains=search_query
                )
                search_filters |= Q(
                    property__community__sub_loc_4__icontains=search_query
                )
                search_filters |= Q(property__property_type__icontains=search_query)
                # Special handling for studio apartments
                if search_query.lower().strip() in "studio":
                    search_filters |= Q(
                        property__property_type__icontains="apartment"
                    ) & Q(property__number_of_bedrooms=0)

                if self_view:
                    search_filters |= Q(property__unit_number__icontains=search_query)

                user_level_properties = user_level_properties.filter(search_filters)

        if viewed_user_role_object.name == AGENT:
            is_basic_subscription = (
                viewed_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC
            )

            if is_basic_subscription:
                unlocked_ids = agent_profile.unlocked_properties.values_list(
                    "id", flat=True
                )
                if not self_view:
                    user_level_properties = user_level_properties.filter(
                        property_id__in=unlocked_ids
                    )

        user_level_properties = user_level_properties.order_by("-property__created_ts")
        sorted_properties = sorted(
            properties,
            key=lambda prop: sorting_serializer.get_sorting_key(prop)
        )

        context = {
            "self_view": self_view,
            "viewer_role": viewer_role,
            "viewed_role": viewed_user_role_object,
            "viewed_profile": viewed_profile,
            "viewed_user": viewed_user,
            "is_basic_subscription": is_basic_subscription,
            "agent_profile": agent_profile,
            "viewer_profile": viewer_profile,
        }
        logger.info(f"Context of PropertyPortfolioViewSet {context}")
        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(user_level_properties, request)
        serializer = PortfolioSerializer(paginated_queryset, many=True, context=context)

        response = paginator.get_paginated_response(serializer.data)
        response.data.get("data").update(
            {
                "sales_property_count": sales_property_count,
                "rental_property_count": rental_property_count,
                "commercial_property_count": commercial_property_count,
                "residential_property_count": residential_property_count,
            }
        )

        return response


class LeaseConditionsViewSet(generics.UpdateAPIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = LeaseConditionsSerializer

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def get_queryset(self, request):
        property_id = self.kwargs.get("property_id")
        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }
        property_obj = Property.objects.filter(pk=property_id, **base_filters).first()
        user_level_property_obj = UserLevelPropertyData.objects.filter(
            property=property_obj,
            created_by=request.user,
            created_by_role__name=request.query_params.get("user_role"),
        ).first()
        financial_details = UserLevelPropertyFinancialDetails.objects.filter(
            property_level_data=user_level_property_obj
        ).first()
        return financial_details

    @general_exception_handler
    def update(self, request, *args, **kwargs):
        queryset = self.get_queryset(request)
        role_obj = get_role_object(request.query_params.get("user_role"))
        serializer = self.get_serializer(
            queryset,
            data=request.data,
            context={"user": request.user, "role_obj": role_obj},
        )
        validate_serializer(serializer)
        serializer.save()
        return Response(
            {
                KEY_MESSAGE: "Lease conditions updated successfully.",
                KEY_PAYLOAD: {"property_id": self.kwargs.get("property_id")},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class PropertyOptionsAPIView(APIView):
    """
    API to share all available property options
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)
        viewer = request.user
        self_view = False

        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

        edit_rights = True if self_view else False
        agent_associated = True if self_view else False
        owner_visibility = False

        try:
            property_obj = (
                Property.all_objects.filter(id=property_id)
                .prefetch_related("completion_states")
                .first()
            )
        except Property.DoesNotExist:
            message = f"Property does not exist"
            logger.error(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Property object with ID {property_id} does not exist"
                    },
                }
            )
        co_owner = False
        if viewed_user_role_name == INVESTOR:
            co_owner = PropertyCoOwner.objects.filter(
                property_id=property_id,
                is_associated=True,
                co_owner=viewed_user.investorprofile,
            ).exists()
        if (property_obj.created_by == request.user or co_owner) and self_view:
            owner_viewing_property = True
        else:
            owner_viewing_property = False

        if viewer_role == AGENT:
            if not AgentAssociatedProperty.objects.filter(
                property=property_obj,
                agent_profile__user=viewer,
                is_associated=True,
                action_status=UserRequestActions.ACCEPTED,
                is_request_expired=False,
            ).exists():
                edit_rights = False
                agent_associated = False

        if viewer_role == INVESTOR:
            agent_associated = False
            if (property_obj.owner and property_obj.owner.user == viewer) or co_owner:
                if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
                    owner_visibility = False
                else:
                    owner_visibility = True if self_view else False
            else:
                edit_rights = False
        if viewed_user_role_name == AGENT:
            filters = {
                "property": property_obj,
                "created_by": viewed_user,
                "created_by_role": viewed_user_role_object,
            }
        else:
            filters = {
                "property": property_obj,
                "created_by_role": viewed_user_role_object,
            }
        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=filters,
        )
        logger.info(f"User level property obj: {user_level_property_obj}")
        property_details = BasicPropertyDetailSerializer(
            user_level_property_obj,
            context={
                "viewed_user": viewed_user,
                "viewed_user_role_obj": viewed_user_role_object,
            },
        ).data

        if not edit_rights:
            property_details["unit_number"] = None

        owner_expectation_details = {
            "details_visible": False,
            "asking_price": None,
            "expected_rent": None,
            "expected_security_deposit": None,
            "preferred_payment_frequency": None,
            "rent_available_start_date": None,
        }

        if (
            property_obj.owner_verified
            and property_obj.owner is not None
            and viewer_role == AGENT
            and edit_rights
        ):
            if property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE:
                owner_expectation_details[
                    "asking_price"
                ] = property_obj.propertyfinancialdetails.asking_price
            elif property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT:
                owner_expectation_details[
                    "expected_rent"
                ] = property_obj.propertyfinancialdetails.expected_rent
                owner_expectation_details[
                    "expected_security_deposit"
                ] = property_obj.propertyfinancialdetails.expected_security_deposit
                owner_expectation_details[
                    "preferred_payment_frequency"
                ] = property_obj.propertyfinancialdetails.preferred_payment_frequency
                owner_expectation_details[
                    "rent_available_start_date"
                ] = property_obj.propertyavailabilityandstatus.rent_available_start_date
            owner_expectation_details["details_visible"] = True

        options_data = {
            "verify_property_with_owner": {
                "value": None,
                "is_visible": False,
                "is_editable": False,
                "feature_available": False,
            },
            "property_intent": {
                "value": property_obj.owner_intent,
                "is_visible": True,
                "is_editable": edit_rights,
                "feature_available": True,
            },
            "edit_property_features": {
                "value": None,
                "is_visible": edit_rights,
                "is_editable": edit_rights,
                "feature_available": True,
            },
            "add_edit_unit_images": {
                "value": None,
                "is_visible": edit_rights,
                "is_editable": edit_rights,
                "feature_available": True,
            },
            "add_edit_agents": {
                "value": None,
                "is_visible": owner_visibility,
                "is_editable": owner_visibility,
                "feature_available": True,
            },
            "copy_public_link": {
                "value": None,
                "is_visible": False,
                "is_editable": False,
                "feature_available": False,
            },
            "share_property_details": {
                "value": None,
                "is_visible": False,
                "is_editable": False,
                "feature_available": False,
            },
            "view_property_qr": {
                "value": None,
                "is_visible": True,
                "is_editable": True,
                "feature_available": True,
            },
            "archive_property": {
                "value": None,
                "is_visible": owner_viewing_property,
                "is_editable": owner_viewing_property,
                "feature_available": True,
            },
            "move_to_draft": {
                "value": None,
                "is_visible": False,
                "is_editable": False,
                "feature_available": False,
            },
            "remove_self_from_property": {
                "value": None,
                "is_visible": (
                    agent_associated if viewer != property_obj.created_by else False
                ),
                "is_editable": (
                    agent_associated if viewer != property_obj.created_by else False
                ),
                "feature_available": True,
            },
        }

        user_share_control = UserPropertySharingControlAttributes.share_control_exists(
            user=viewed_user,
            role=viewed_user_role_object,
            property_category=property_obj.property_category,
        )

        return Response(
            {
                KEY_MESSAGE: "Property options are fetched successfully.",
                KEY_PAYLOAD: {
                    "property_details": property_details,
                    "privacy_sharing_control": {
                        "value": None,
                        "is_visible": (
                            True
                            if self_view
                            and user_share_control
                            and property_obj.owner_intent
                            != OwnerIntentForProperty.NOT_FOR_SALE
                            else False
                        ),
                        "is_editable": False,
                        "feature_available": True,
                    },
                    "options_data": options_data,
                    "owner_expectation_details": owner_expectation_details,
                },
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class PropertyMediaPresignedURLView(APIView):
    """
    API view to generate presigned URLs for property media files
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_obj = get_property_object(property_id)

        serializer = PresignedURLRequestSerializer(data=request.data)
        validated_data = validate_serializer(serializer)
        logger.info(f"PropertyMediaPresignedURLView serializer data: {validated_data}")

        files = validated_data.get("media_files")

        # Get S3 client
        s3_client = S3Client()

        # Generate presigned URLs for each file
        presigned_meida_data = []
        presigned_thumbnail_data = []
        for file in files:
            # Generate unique file name
            mime_type = file.get("media_mime_type")
            media_type = mime_type.split("/")[0]
            file_name = file.get("media_file_name")

            logger.info(f"The media type of {file_name} is: {media_type}")

            unit_section_media_key = PRESIGNED_POST_STRUCTURES.get(
                PROPERTY_UNIT_SECTION_MEDIA, {}
            ).get(KEY, "")

            unit_section_media_key = unit_section_media_key.format(
                property_id=property_obj.id,
                user_id=request.user.id,
                user_role=user_role,
                filename=f"{datetime.now()}_{media_type}",
            )
            logger.info(f"Media file key is: {unit_section_media_key}")

            presigned_url = s3_client.generate_presigned_url(
                unit_section_media_key, content_type=mime_type
            )
            presigned_meida_data.append(
                {
                    "file_name": file_name,
                    "presigned_url": presigned_url,
                    "media_key": unit_section_media_key,
                    "media_type": media_type,
                }
            )

            if media_type == "video":
                thumbnail_key = f"{unit_section_media_key}_thumbnail"
                thumbnail_file = file.get("thumbnail_file_name")
                presigned_url = s3_client.generate_presigned_url(
                    thumbnail_key, content_type=mime_type
                )
                presigned_thumbnail_data.append(
                    {
                        "file_name": thumbnail_file,
                        "presigned_url": presigned_url,
                        "media_key": thumbnail_key,
                        "media_type": media_type,
                    }
                )

        return Response(
            {
                KEY_MESSAGE: "Presigned URLs generated successfully",
                KEY_PAYLOAD: {
                    "media_data": presigned_meida_data,
                    "thumbnail_data": presigned_thumbnail_data,
                },
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class LinkPreview(APIView):
    def get(self, request, property_id, viewed_user_id, viewed_user_role):
        # Fetch property details from DB
        property_obj = get_property_object(property_id)
        role_obj = get_role_object(viewed_user_role)

        # Creating redirect url
        query_params = request.GET
        redirect_url = query_params.get("redirect_url", "")
        extra_params = {
            k: v for k, v in query_params.items() if k not in ["redirect_url"]
        }
        if extra_params:
            redirect_url += "&" + urlencode(extra_params)
        logger.info(f"Redirected url is: {redirect_url}")

        viewed_user = get_user_object(viewed_user_id)
        viewed_user_role_object = get_role_object(viewed_user_role)

        user_level_filters = create_user_level_filter(
            property_obj, viewed_user_id, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj} with user id {viewed_user_id} and "
            f"role {viewed_user_role}",
        )

        serializer = BasicPropertyDetailSerializer(
            user_level_property_obj,
            context={
                "viewed_user": viewed_user,
                "viewed_user_role_obj": viewed_user_role_object,
            },
        ).data

        address = build_property_address(property_obj)
        total_area = user_level_property_obj.total_area
        number_of_bedrooms = user_level_property_obj.number_of_bedrooms

        if serializer.get("unit_images"):
            if serializer.get("unit_images").get("media_file"):
                unit_image = serializer.get("unit_images").get("media_file")
            else:
                unit_image = serializer.get("unit_images").get("thumbnail_file")
        else:
            unit_image = property_obj.default_image

        currency_code = serializer.get("currency_code")
        property_unit_type = serializer.get("property_unit_type")
        number_of_bathrooms = user_level_property_obj.get_total_bathroom_count()
        asking_price = serializer.get("display_price_in_property_currency")
        address = serializer.get("address")
        building_name = serializer.get("building_name")
        if property_obj.property_category == PropertyCategory.COMMERCIAL:
            number_of_bedrooms = None
            number_of_bathrooms = None

        is_studio = False
        if (
            property_obj.property_type == PropertyType.APARTMENT
            and number_of_bedrooms == 0
        ):
            is_studio = True

        if asking_price:
            asking_price = format_amount(
                amount=asking_price,
                currency_code=currency_code,
            )

        is_rent = False
        if property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT:
            is_rent = True

        # Define metadata
        context = {
            "title": building_name,
            "address": address,
            "total_area": total_area,
            "number_of_bedrooms": number_of_bedrooms,
            "currency_code": currency_code,
            "property_unit_type": PropertyAreaUnit(property_unit_type).label,
            "asking_price": asking_price,
            "image_url": unit_image,
            "number_of_bathrooms": number_of_bathrooms,
            "redirect_url": mark_safe(redirect_url),
            "project_env_url": settings.PROJECT_ENV_URL,
            "is_studio": is_studio,
            "is_rent": is_rent,
        }

        return render(request, "share_property_details.html", context)


class OwnerUnitImagesView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyAssociateAgent]

    @general_exception_handler
    def get(self, request, property_id):
        # Get all sections for the property
        property_obj = get_property_object(property_id)
        sections = PropertyUnitSections.objects.filter(
            property_id=property_id,
            created_by=property_obj.owner.user,
            created_by_role=get_investor_role_object(),
        )

        # Prepare data for serialization
        property_section_data = {
            "section_types": sections.values_list("section_type", flat=True).distinct(),
            "sections": sections,
        }

        # Serialize the data
        serializer = PropertySectionImagesSerializer(property_section_data)

        return Response(
            {
                KEY_MESSAGE: "Owner unit images fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class CompressedImageView(APIView):
    """
    Django view to dynamically compress an image and return it as a response.
    """

    def get(self, request):
        image_url = request.query_params.get("image_url")
        s3_client = S3Client()
        image_buffer = s3_client.fetch_image_from_s3(image_url)
        compressed_image = compress_image(image_buffer)

        # Return image as an HTTP response
        return HttpResponse(compressed_image.getvalue(), content_type="image/jpeg")


class AgentPortfolioAnalyticsViewSet(APIView):
    """
    API view to get portfolio analytics summary for an agent.
    Provides metrics like total sales value, rental value, commissions, and property counts.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @general_exception_handler
    def get(self, request):
        """
        Get portfolio analytics summary for an agent.

        Returns:
            Response containing:
            - Sales summary (total value, commission, property counts by status)
            - Rental summary (total value, commission, property counts by status)
            - Overall totals and preferred currency
        """
        # Get agent profile and preferences
        agent_profile = get_agent_profile_object(request.user)
        preferred_currency_code = agent_profile.preferred_currency_code
        commission_percentage = agent_profile.commission_percentage

        # Get all non-archived properties associated with this agent
        associated_properties = Property.objects.filter(
            agentassociatedproperty__agent_profile=agent_profile,
            agentassociatedproperty__is_associated=True,
            agentassociatedproperty__action_status=UserRequestActions.ACCEPTED,
            property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            is_archived=False,
        )

        # Get property data created by this agent
        user_level_properties = UserLevelPropertyData.objects.filter(
            property_id__in=associated_properties,
            created_by=request.user,
            created_by_role=get_agent_role_object(),
        ).select_related("property_user_level_financial_details")

        # Get unique property currencies and their exchange rates
        property_currencies = user_level_properties.values_list(
            "property_user_level_financial_details__property_currency_code",
            flat=True,
        ).distinct()

        # Build cache of exchange rates for currency conversion
        exchange_rate_cache = {
            currency: get_exchange_rates(currency, preferred_currency_code)
            for currency in property_currencies
            if currency != preferred_currency_code
        }
        exchange_rate_cases = [
            When(
                property_user_level_financial_details__property_currency_code=currency,
                then=Value(rate),
            )
            for currency, rate in exchange_rate_cache.items()
        ]

        # Split properties by intent (sale vs rental)
        sales_properties = user_level_properties.filter(
            property__owner_intent="available for sale"
        )
        rental_properties = user_level_properties.filter(
            property__owner_intent="available for rent"
        )

        # Calculate sales metrics with currency conversion
        sales_properties_data = sales_properties.annotate(
            asking_price=Round(
                Coalesce(
                    Cast(
                        F("property_user_level_financial_details__asking_price"),
                        output_field=DecimalField(max_digits=20, decimal_places=4),
                    ),
                    Value(Decimal("0.00")),
                ),
                3,
            ),
            exchange_rate=Case(
                *exchange_rate_cases,
                default=Value(1),
                output_field=DecimalField(max_digits=12, decimal_places=6),
            ),
            converted_asking_price=Round(
                Cast(
                    F("asking_price") * F("exchange_rate"),
                    output_field=DecimalField(max_digits=25, decimal_places=4),
                ),
                3,
            ),
        ).aggregate(
            total_sales_value=Coalesce(
                Sum("converted_asking_price"), Value(Decimal("0.00"))
            ),
            vacant_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.VACANT
                ),
            ),
            owner_occupied_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.OWNER_OCCUPIED
                ),
            ),
            holiday_home_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.HOLIDAY_HOME
                ),
            ),
            rented_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED
                ),
            ),
        )

        # Calculate rental metrics with currency conversion
        rental_properties_data = rental_properties.annotate(
            expected_rent=Round(
                Coalesce(
                    Cast(
                        F("property_user_level_financial_details__expected_rent"),
                        output_field=DecimalField(max_digits=20, decimal_places=4),
                    ),
                    Value(Decimal("0.00")),
                ),
                3,
            ),
            exchange_rate=Case(
                *exchange_rate_cases,
                default=Value(1),
                output_field=DecimalField(max_digits=12, decimal_places=6),
            ),
            converted_expected_rent=Round(
                Cast(
                    F("expected_rent") * F("exchange_rate"),
                    output_field=DecimalField(max_digits=25, decimal_places=4),
                ),
                3,
            ),
        ).aggregate(
            total_rent_value=Coalesce(
                Sum("converted_expected_rent"), Value(Decimal("0.00"))
            ),
            vacant_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.VACANT
                ),
            ),
            owner_occupied_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.OWNER_OCCUPIED
                ),
            ),
            holiday_home_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.HOLIDAY_HOME
                ),
            ),
            rented_count=Count(
                "id",
                filter=Q(
                    property_user_level_availability_and_status__occupancy_status=PropertyAvailabilityStatus.RENTED
                ),
            ),
        )

        # Calculate commission amounts and totals
        total_sales_tentative_commission = round(
            sales_properties_data["total_sales_value"] * commission_percentage / 100, 3
        )
        total_rental_tentative_commission = round(
            rental_properties_data["total_rent_value"]
            * settings.AGENT_COMMISSION_PERCENTAGE_FOR_EXPECTED_RENT
            / 100,
            3,
        )
        sales_property_count = sales_properties.count()
        rental_property_count = rental_properties.count()

        total_properties_count = sales_property_count + rental_property_count
        trial_premium_card = (
            agent_profile.trial_premium_card
            and agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC
        )

        basic_plan_user_property_adding_limit = (
            settings.BASIC_PLAN_PROPERTIES_COUNT - total_properties_count) if agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC else 0

        # Build final response payload
        portfolio_summary = {
            "sales_summary": {
                "total_sales_value": sales_properties_data["total_sales_value"],
                "total_sales_commission": total_sales_tentative_commission,
                "total_sales_properties": sales_property_count,
                "property_status": {
                    "vacant": sales_properties_data["vacant_count"],
                    "owner_occupied": sales_properties_data["owner_occupied_count"],
                    "holiday_home": sales_properties_data["holiday_home_count"],
                    "rented": sales_properties_data["rented_count"],
                },
            },
            "rental_summary": {
                "total_rental_value": rental_properties_data["total_rent_value"],
                "total_rental_commission": total_rental_tentative_commission,
                "total_rental_properties": rental_property_count,
                "property_status": {
                    "vacant": rental_properties_data["vacant_count"],
                    "owner_occupied": rental_properties_data["owner_occupied_count"],
                    "holiday_home": rental_properties_data["holiday_home_count"],
                    "rented": rental_properties_data["rented_count"],
                },
            },
            "currency_code": preferred_currency_code,
            "total_tentative_commission": round(
                total_sales_tentative_commission + total_rental_tentative_commission, 3
            ),
            "total_properties": total_properties_count,
            "trial_premium_card": trial_premium_card,
            "premium_card_visibility_after_property": settings.PREMIUM_CARD_VISIBILITY_AFTER_PROPERTY,
            "basic_plan_user_property_adding_limit": basic_plan_user_property_adding_limit,
            "basic_plan_properties_count": settings.BASIC_PLAN_PROPERTIES_COUNT,
        }

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent portfolio analytics fetched successfully",
                KEY_PAYLOAD: portfolio_summary,
                KEY_ERROR: {},
            },
        )


class PropertyShareOptionsAPIView(APIView):
    """
    API to share property options
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)

        property_obj = get_property_object(property_id=property_id)

        if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Not allowed to share property",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {"Not allowed to share property"},
                }
            )

        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)
        self_view = False
        user_share_control = UserPropertySharingControlAttributes.share_control_exists(
            user=viewed_user,
            role=viewed_user_role_object,
            property_category=property_obj.property_category,
        )

        logger.info(f"Share control status: {user_share_control}")

        if (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        ):
            self_view = True

        user_level_filter = create_user_level_filter(
            property_obj=property_obj,
            user=viewed_user,
            role_obj=viewed_user_role_object,
        )
        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filter,
        )
        logger.info(f"User level property obj: {user_level_property_obj}")
        property_details = BasicPropertyDetailSerializer(
            user_level_property_obj,
            context={
                "viewed_user": viewed_user,
                "viewed_user_role_obj": viewed_user_role_object,
            },
        ).data

        payment_plan_document = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyPaymentPlan.__name__,
            single_field_value_dict=user_level_filter,
        ).order_by("order_no")

        payment_plan_document = PropertyPaymentPlanSerializer(
            payment_plan_document, many=True
        ).data

        floor_plan_document = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyFloorPlan.__name__,
            single_field_value_dict=user_level_filter,
        ).order_by("order_no")

        floor_plan_document = PropertyFloorPlanSerializer(
            floor_plan_document, many=True
        ).data

        unit_sections = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyUnitSections.__name__,
            single_field_value_dict=user_level_filter,
        ).order_by("id")
        unit_sections = PropertySectionSerializer(unit_sections, many=True).data

        agent_details = None
        if viewed_user_role_name == AGENT:
            agent_profile = get_list_of_object_with_filters(
                app_name="user",
                model_name=AgentProfile.__name__,
                single_field_value_dict={"user": viewed_user},
            )
            if not agent_profile.exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "User profile not found.",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "The profile you are looking for does not exist on Rezio anymore."
                        },
                    }
                )
            agent_profile = agent_profile.first()
            agent_details = ViewAgentProfileSerializer(
                agent_profile,
                context={
                    "user": viewed_user,
                    "role": viewed_user_role_name,
                    "request": request,
                    "viewing_role": AGENT,
                },
            ).data

        options_data = {
            "privacy_sharing_control": {
                "value": None,
                "is_visible": True if self_view else False,
                "is_editable": False,
                "feature_available": True,
                "initial": True if self_view and not user_share_control else False,
            },
            "share_property_link": {
                "value": None,
                "is_visible": True,
                "is_editable": False,
                "feature_available": True,
            },
            "share_property_social_media": {
                "value": None,
                "is_visible": True,
                "is_editable": False,
                "feature_available": True,
            },
            "unit_sections": {
                "value": unit_sections,
                "is_visible": True,
                "is_editable": False,
                "feature_available": True,
            },
            "payment_plan": {
                "value": payment_plan_document,
                "is_visible": True,
                "is_editable": False,
                "feature_available": True,
            },
            "floor_plan": {
                "value": floor_plan_document,
                "is_visible": True,
                "is_editable": False,
                "feature_available": True,
            },
        }

        return Response(
            {
                KEY_MESSAGE: "Property options are fetched successfully.",
                KEY_PAYLOAD: {
                    "property_details": property_details,
                    "options_data": options_data,
                    "agent_details": agent_details,
                },
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class PropertyAttributesViewSet(APIView):
    """
    This API will return the list of all property attributes and their visibility status
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def get(self, request, property_id, viewed_user_id, viewed_user_role_name):
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewed_user = get_user_object(viewed_user_id)
        viewer_role = request.query_params.get("user_role")
        viewer_user_role_object = get_role_object(viewer_role)

        # Check if it's self view
        self_view = (
            viewed_user == request.user
            and viewed_user_role_object == viewer_user_role_object
        )

        if not self_view:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Not allowed to view property attributes",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Not allowed to view property attributes"
                    },
                }
            )

        # Get property object and category
        property_obj = Property.objects.filter(
            id=property_id,
            property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
        ).first()

        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        # Get private fields from UserPropertySharingControlAttributes
        private_fields = []
        share_control = UserPropertySharingControlAttributes.objects.filter(
            created_by=viewed_user,
            created_by_role=viewed_user_role_object,
            property_category=property_obj.property_category,
        ).first()

        if share_control:
            private_fields = share_control.private_fields or []
        else:
            private_fields = ["valuation"]

        # Get property attributes based on category
        property_attributes = (
            PropertyAttributes.objects.filter(
                Q(attribute_category=property_obj.property_category)
                | Q(attribute_category=PropertyAttributesCategory.BOTH),
                is_public=True,
            )
            .exclude(
                component_name__in=[
                    "location_details",
                    "address_details",
                    "price_details",
                    "scrollable_component",
                    "location_details_upper_component",
                    "unit_images_component",
                ]
            )
            .values("component_name", "attribute_name")
        )

        # Group attributes by component
        grouped_attributes = {}
        for attr in property_attributes:
            is_editable = True
            component = attr["component_name"]
            if component not in grouped_attributes:
                grouped_attributes[component] = []

            #  attributes that are not editable
            if component in ["lease_conditions", "gains_component",] and attr[
                "attribute_name"
            ] in [
                "expected_annual_rent",
                "expected_security_deposit",
                "preferred_payment_frequency",
                "available_from",
                "asking_price",
            ]:
                is_editable = False
                logger.info(
                    "This attribute is not editable component and name is: %s", attr
                )

            # Check if attribute is private based on privacy settings
            is_private = (
                attr["attribute_name"] in private_fields if private_fields else False
            )

            attribute_name = attr["attribute_name"]
            icon_url = f"{settings.PROPERTY_SHARING_ICONS_URL}/{component}/{attribute_name}.svg"
            grouped_attributes[component].append(
                {
                    "name": attr["attribute_name"],
                    "icon_url": icon_url,
                    "is_private": is_private,
                    "is_editable": is_editable,
                }
            )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property attributes fetched successfully",
                KEY_PAYLOAD: {
                    "attributes_list": grouped_attributes,
                    "property_category": property_obj.property_category,
                    "non_editable_attributes_message": "This will always be shared",
                },
                KEY_ERROR: {},
            },
        )
