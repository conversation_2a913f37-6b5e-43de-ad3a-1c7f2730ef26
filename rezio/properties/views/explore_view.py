from rest_framework import generics, status, filters
from rest_framework.response import Response
from rezio.properties.models import UserLevelPropertyData, PropertySourceType
from rezio.properties.serializers.property_hierarchy_serializer import (
    EnhancedWhatsAppPropertiesListSerializer,
)
from rezio.user.serializers import AgentProfileSerializer

from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import (
    DjangoFilterBackend,
    FilterSet,
    NumberFilter,
    Char<PERSON>ilter,
    ChoiceFilter,
)
from django.db.models import Q
from django.db import connection

from rezio.properties.text_choices import PropertyCategory, OwnerIntentForProperty
from rezio.properties.utils import build_property_address
from rezio.user.authentication import JWTAuthentication
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
import json


class PropertyFilter(FilterSet):
    # Location filters
    country = CharFilter(field_name="property__country__name", lookup_expr="iexact")
    city = CharFilter(field_name="property__city__name", lookup_expr="iexact")
    area = CharFilter(field_name="property__area__name", lookup_expr="iexact")
    community = CharFilter(field_name="property__community__name", lookup_expr="iexact")

    # Price range filters
    min_price = NumberFilter(
        field_name="property_user_level_financial_details__asking_price",
        lookup_expr="gte",
    )
    max_price = NumberFilter(
        field_name="property_user_level_financial_details__asking_price",
        lookup_expr="lte",
    )

    # Bedroom/Bathroom filters
    bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="exact")
    min_bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="gte")
    max_bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="lte")

    bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="exact")
    min_bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="gte")
    max_bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="lte")

    # Property type filter
    property_type = CharFilter(method="filter_property_type")

    # Additional filters for features supporting multiple selections
    furnished = CharFilter(method="filter_furnished")
    branded_building = CharFilter(method="filter_branded_building")

    # Total area filters
    min_area = NumberFilter(method="filter_min_area")
    max_area = NumberFilter(method="filter_max_area")

    # Address filter - searches across multiple address components
    address = CharFilter(method="filter_address")

    # Property category filter (Residential/Commercial)
    property_category = ChoiceFilter(
        field_name="property__property_category", choices=PropertyCategory.choices
    )

    # New filters for sale/rent properties
    for_sale = CharFilter(method="filter_for_sale")
    for_rent = CharFilter(method="filter_for_rent")

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "country",
            "city",
            "area",
            "community",
            "property_type",
            "min_price",
            "max_price",
            "bedrooms",
            "min_bedrooms",
            "max_bedrooms",
            "bathrooms",
            "min_bathrooms",
            "max_bathrooms",
            "furnished",
            "branded_building",
            "min_area",
            "max_area",
            "address",
            "property_category",
            "for_sale",
            "for_rent",
        ]

    def filter_furnished(self, queryset, name, value):
        """
        Filter properties by furnished status
        Supports comma-separated values for multiple selection
        """
        if not value:
            return queryset

        # Handle comma-separated values (e.g. "true,false")
        values = [v.strip().lower() for v in value.split(",")]

        # If both true and false are selected, don't filter
        if any(v in ["true", "yes", "1"] for v in values) and any(
            v in ["false", "no", "0"] for v in values
        ):
            return queryset

        # Apply filter for single selection
        if any(v in ["true", "yes", "1"] for v in values):
            return queryset.filter(property_user_level_features__furnished=True)
        elif any(v in ["false", "no", "0"] for v in values):
            return queryset.filter(property_user_level_features__furnished=False)

        return queryset

    def filter_branded_building(self, queryset, name, value):
        """
        Filter properties by branded building status
        Supports comma-separated values for multiple selection
        """
        if not value:
            return queryset

        # Handle comma-separated values (e.g. "true,false")
        values = [v.strip().lower() for v in value.split(",")]

        # If both true and false are selected, don't filter
        if any(v in ["true", "yes", "1"] for v in values) and any(
            v in ["false", "no", "0"] for v in values
        ):
            return queryset

        # Apply filter for single selection
        if any(v in ["true", "yes", "1"] for v in values):
            return queryset.filter(property_user_level_features__branded_building=True)
        elif any(v in ["false", "no", "0"] for v in values):
            return queryset.filter(property_user_level_features__branded_building=False)

        return queryset

    def filter_property_type(self, queryset, name, value):
        # Check if it's a comma-separated list
        if "," in value:
            property_types = [pt.strip() for pt in value.split(",")]
            return queryset.filter(property_type__in=property_types)
        # Handle single value case
        return queryset.filter(property_type__iexact=value)

    def filter_min_area(self, queryset, name, value):
        # Default unit is sqft
        # unit = self.request.query_params.get("area_unit", "sqft").lower()
        #
        # # Convert sqm to sqft if needed
        # if unit == "sqm":
        #     # 1 sqm = 10.764 sqft
        #     value = value * 10.764

        return queryset.filter(total_area__gte=value)

    def filter_max_area(self, queryset, name, value):
        # Default unit is sqft
        # unit = self.request.query_params.get("area_unit", "sqft").lower()
        #
        # # Convert sqm to sqft if needed
        # if unit == "sqm":
        #     # 1 sqm = 10.764 sqft
        #     value = value * 10.764

        return queryset.filter(total_area__lte=value)

    def filter_address(self, queryset, name, value):
        """
        Filter properties by address components
        Searches across unit number, building number, street, area, city, etc.
        """
        # First split by comma to get each location phrase
        location_phrases = [phrase.strip() for phrase in value.split(",")]
        combined_query = Q()

        # Process each location phrase separately
        for phrase in location_phrases:
            # Split the phrase into individual terms
            search_terms = phrase.split()
            phrase_query = Q()

            # Build a query that searches each term across all address components
            for term in search_terms:
                term_query = (
                    Q(property__unit_number__icontains=term)
                    | Q(property__building_number__icontains=term)
                    | Q(property__area__name__icontains=term)
                    | Q(property__city__name__icontains=term)
                    | Q(property__state__name__icontains=term)
                    | Q(property__country__name__icontains=term)
                    | Q(property__community__name__icontains=term)
                    | Q(property__postal_code__icontains=term)
                    | Q(property__property_category__icontains=term)
                )
                phrase_query &= term_query

            # Add this phrase's query to the combined query with OR logic
            combined_query |= phrase_query

        return queryset.filter(combined_query)

    def filter_for_sale(self, queryset, name, value):
        """Filter properties that are available for sale"""
        if value.lower() in ["true", "yes", "1", True]:
            return queryset.filter(
                property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_SALE
            )
        return queryset

    def filter_for_rent(self, queryset, name, value):
        """Filter properties that are available for rent"""
        if value.lower() in ["true", "yes", "1", True]:
            return queryset.filter(
                property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_RENT
            )
        return queryset


class ExploreViewSet(generics.ListAPIView):
    """
    API view to list properties that were sourced from WhatsApp

    Supports filtering by:
    - Location (country, city, area, community)
    - Property type
    - Price range (min_price, max_price)
    - Number of bedrooms/bathrooms
    - Features (furnished, etc.)
    """

    serializer_class = EnhancedWhatsAppPropertiesListSerializer
    authentication_classes = [JWTAuthentication]
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = PropertyFilter
    search_fields = [
        "property__unit_number",
        "property__building_number",
        "property__area__name",
        "property__city__name",
        "property__country__name",
        "property__state__name",
        "property__community__name",
        "property__postal_code",
        "property__property_type",
        "property__property_category",
        "property__owner_intent",
    ]

    ordering_fields = [
        "created_ts",
        "property_user_level_financial_details__asking_price",
        "number_of_bedrooms",
    ]
    ordering = ["-created_ts"]

    @general_exception_handler
    def get_queryset(self):
        return (
            UserLevelPropertyData.objects.filter(
                open_for_collaboration=True, property__is_archived=False
            )
            .select_related(
                "property",
                "property__country",
                "property__state",
                "property__city",
                "property__area",
                "property__community",
                "property_user_level_financial_details",
                "created_by",
                "created_by__agentprofile",
            )
            .prefetch_related(
                "property_user_level_features",
                "property_user_level_availability_and_status",
            )
            .order_by("-created_ts")
        )

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(paginated_queryset, many=True)

        response = paginator.get_paginated_response(serializer.data)

        return response


class PropertyAddressSearchView(generics.ListAPIView):
    """
    API view to get property addresses for search suggestions

    This endpoint returns addresses of properties from WhatsApp source
    for use in autocomplete/location search functionality.
    """

    authentication_classes = [JWTAuthentication]

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        search_query = request.query_params.get("search", "")
        search_query = request.query_params.get("search", "")

        # Create a raw SQL query to get unique addresses efficiently
        with connection.cursor() as cursor:
            params = []
            sql = """
                WITH address_data AS (
                    SELECT DISTINCT ON (address) 
                        p.id,
                        COALESCE(
                            NULLIF(
                                TRIM(
                                    CONCAT_WS(', ', 
                                        -- Include community sub-locations and other address components
                                        -- Only include non-null and non-empty values
                                        NULLIF(TRIM(c.sub_loc_1), ''),
                                        NULLIF(TRIM(c.sub_loc_2), ''),
                                        NULLIF(TRIM(c.sub_loc_3), ''),
                                        NULLIF(TRIM(c.sub_loc_4), ''),
                                        NULLIF(TRIM(c.sub_loc_5), ''),
                                        NULLIF(TRIM(c.name), ''),
                                        NULLIF(TRIM(a.name), ''),
                                        NULLIF(TRIM(ci.name), ''),
                                        NULLIF(TRIM(s.name), '')
                                    )
                                ), ''
                            ), NULL
                        ) as address,
                        CASE WHEN co.name IS NOT NULL THEN co.name END as country_name
                    FROM 
                        properties_userlevelpropertydata ulpd
                    JOIN 
                        properties_property p ON ulpd.property_id = p.id
                    LEFT JOIN 
                        properties_community c ON p.community_id = c.id
                    LEFT JOIN 
                        properties_area a ON p.area_id = a.id
                    LEFT JOIN 
                        properties_city ci ON p.city_id = ci.id
                    LEFT JOIN 
                        properties_state s ON p.state_id = s.id
                    LEFT JOIN 
                        properties_country co ON p.country_id = co.id
                    WHERE 
                        ulpd.source = 'WHATS_APP' AND p.is_archived = FALSE
                """

            # Add search filter if provided
            if search_query:
                sql += """
                    AND (
                        LOWER(co.name) LIKE %s OR
                        LOWER(s.name) LIKE %s OR
                        LOWER(ci.name) LIKE %s OR
                        LOWER(a.name) LIKE %s OR
                        LOWER(c.name) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_1, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_2, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_3, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_4, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_5, '')) LIKE %s OR
                        LOWER(COALESCE(p.unit_number, '')) LIKE %s OR
                        LOWER(COALESCE(p.building_number, '')) LIKE %s OR
                        -- Cast postal_code to text before applying LOWER
                        LOWER(COALESCE(p.postal_code::text, '')) LIKE %s
                    )
                """
                search_param = f"%{search_query.lower()}%"
                params.extend([search_param] * 13)

            sql += """
                )
                SELECT * FROM address_data WHERE address IS NOT NULL
                ORDER BY address
            """

            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(results, request)

        # Format the response
        addresses = []
        for result in paginated_queryset:
            if result["address"]:
                addresses.append(
                    {"address": result["address"], "country": result["country_name"]}
                )

        return paginator.get_paginated_response(addresses)
