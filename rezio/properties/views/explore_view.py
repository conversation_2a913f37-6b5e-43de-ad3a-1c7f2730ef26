import logging

from django.db import connection
from django.db.models import Q
from django_filters.rest_framework import (
    DjangoFilterBackend,
    FilterSet,
    NumberFilter,
    Char<PERSON>ilter,
    ChoiceFilter,
)
from rest_framework import generics, filters

from rezio.properties.models import UserLevelPropertyData
from rezio.properties.serializers.property_hierarchy_serializer import (
    EnhancedWhatsAppPropertiesListSerializer,
)
from rezio.properties.text_choices import (
    PropertyCategory,
    OwnerIntentForProperty,
    FurnishedChoices,
)
from rezio.properties.text_choices import PropertyCategory, OwnerIntentForProperty
from rezio.properties.text_choices import (
    UserRequestActions,
)
from rezio.user.authentication import JWTAuthentication
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class EnhancedSearchFilter(filters.SearchFilter):
    """
    Enhanced SearchFilter that extends search_fields capabilities to include:
    - Agent Name (from AgentAssociatedProperty and meta.contact)
    - Agent Number (from AgentAssociatedProperty and meta.contact)
    - Address (from meta.contact)
    - Notes (from meta.notes)
    """

    def filter_queryset(self, request, queryset, view):
        # Get the search query
        search_query = self.get_search_terms(request)
        if not search_query:
            return queryset

        # Join search terms back to a single string
        search_value = " ".join(search_query)
        logger.info(f"Search value in filter_queryset is: {search_value}")

        # Apply standard search filter first
        standard_results = super().filter_queryset(request, queryset, view)

        if not search_value:
            return standard_results

        # Get original queryset for additional searches
        original_queryset = view.get_queryset()
        logger.info(f"Original queryset in filter_queryset is: {original_queryset}")

        # Collect all matching IDs
        all_ids = set(standard_results.values_list("id", flat=True))
        logger.info(f"All filtered IDs in filter_queryset are: {all_ids}")

        # 1. Notes search from meta.notes (simple field search)
        try:
            notes_results = original_queryset.filter(
                meta__notes__icontains=search_value
            )
            notes_results_ids = notes_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Notes results in filter_queryset are: {notes_results_ids}"
            )
            all_ids.update(notes_results_ids)
        except Exception as e:
            logger.error(f"Error in Notes search: {e}")
            pass

        # 2. Agent Name search from AgentAssociatedProperty
        try:
            agent_name_results = (
                original_queryset.filter(
                    property__agentassociatedproperty__is_associated=True,
                    property__agentassociatedproperty__action_status=UserRequestActions.ACCEPTED,
                )
                .filter(
                    Q(
                        property__agentassociatedproperty__agent_profile__name__icontains=search_value
                    )
                    | Q(
                        property__agentassociatedproperty__agent_profile__user__first_name__icontains=search_value
                    )
                    | Q(
                        property__agentassociatedproperty__agent_profile__user__last_name__icontains=search_value
                    )
                )
                .distinct()
            )
            agent_name_results_ids = agent_name_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Agent Name results in filter_queryset are: {agent_name_results_ids}"
            )
            all_ids.update(agent_name_results_ids)
        except Exception as e:
            logger.error(f"Error in Agent Name search: {e}")
            pass

        # 3. Agent Phone search from AgentAssociatedProperty
        try:
            agent_phone_results = (
                original_queryset.filter(
                    property__agentassociatedproperty__is_associated=True,
                    property__agentassociatedproperty__action_status=UserRequestActions.ACCEPTED,
                )
                .filter(
                    Q(
                        property__agentassociatedproperty__agent_profile__user__primary_phone_number__icontains=search_value
                    )
                )
                .distinct()
            )
            agent_phone_results_ids = agent_phone_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Agent Phone results in filter_queryset are: {agent_phone_results_ids}"
            )
            all_ids.update(agent_phone_results_ids)
        except Exception as e:
            logger.error(f"Error in Agent Phone search: {e}")
            pass

        # 4. JSON field searches for meta.contact (using raw SQL)
        try:
            # Agent name in meta.contact
            name_results = original_queryset.filter(meta__contact__isnull=False).extra(
                where=[
                    "EXISTS (SELECT 1 FROM jsonb_array_elements(meta->'contact') AS contact WHERE LOWER(contact->>'name') LIKE LOWER(%s))"
                ],
                params=[f"%{search_value}%"],
            )
            name_results_ids = name_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Agent Name in meta.contact results in filter_queryset are: {name_results_ids}"
            )
            all_ids.update(name_results_ids)
        except Exception as e:
            logger.error(f"Error in Agent Name in meta.contact search: {e}")
            pass

        try:
            # Agent phone in meta.contact
            phone_results = original_queryset.filter(meta__contact__isnull=False).extra(
                where=[
                    "EXISTS (SELECT 1 FROM jsonb_array_elements(meta->'contact') AS contact WHERE contact->>'phone' LIKE %s)"
                ],
                params=[f"%{search_value}%"],
            )
            phone_results_ids = phone_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Agent Phone in meta.contact results in filter_queryset are: {phone_results_ids}"
            )
            all_ids.update(phone_results_ids)
        except Exception as e:
            logger.error(f"Error in Agent Phone in meta.contact search: {e}")
            pass

        try:
            # Address in meta.contact
            address_results = original_queryset.filter(
                meta__contact__isnull=False
            ).extra(
                where=[
                    "EXISTS (SELECT 1 FROM jsonb_array_elements(meta->'contact') AS contact WHERE LOWER(contact->>'address') LIKE LOWER(%s))"
                ],
                params=[f"%{search_value}%"],
            )
            address_results_ids = address_results.values_list("id", flat=True)
            logger.info(
                f"ID's from Address in meta.contact results in filter_queryset are: {address_results_ids}"
            )
            all_ids.update(address_results_ids)
        except Exception as e:
            logger.error(f"Error in Address in meta.contact search: {e}")
            pass

        # Return filtered queryset with all matching IDs
        if all_ids:
            return original_queryset.filter(id__in=all_ids)
        else:
            return original_queryset.none()


class PropertyFilter(FilterSet):
    # Location filters
    country = CharFilter(field_name="property__country__name", lookup_expr="iexact")
    city = CharFilter(field_name="property__city__name", lookup_expr="iexact")
    area = CharFilter(field_name="property__area__name", lookup_expr="iexact")
    community = CharFilter(field_name="property__community__name", lookup_expr="iexact")

    # Price range filters
    min_price = NumberFilter(
        field_name="property_user_level_financial_details__asking_price",
        lookup_expr="gte",
    )
    max_price = NumberFilter(
        field_name="property_user_level_financial_details__asking_price",
        lookup_expr="lte",
    )

    # Bedroom/Bathroom filters
    bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="exact")
    min_bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="gte")
    max_bedrooms = NumberFilter(field_name="number_of_bedrooms", lookup_expr="lte")

    bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="exact")
    min_bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="gte")
    max_bathrooms = NumberFilter(field_name="number_of_bathrooms", lookup_expr="lte")

    # Property type filter
    property_type = CharFilter(method="filter_property_type")

    # Additional filters for features supporting multiple selections
    furnished = CharFilter(method="filter_furnished")
    branded_building = CharFilter(method="filter_branded_building")

    # Total area filters
    min_area = NumberFilter(method="filter_min_area")
    max_area = NumberFilter(method="filter_max_area")

    # Address filter - searches across multiple address components
    address = CharFilter(method="filter_address")

    # Property category filter (Residential/Commercial)
    property_category = ChoiceFilter(
        field_name="property__property_category", choices=PropertyCategory.choices
    )

    # New filters for sale/rent properties
    for_sale = CharFilter(method="filter_for_sale")
    for_rent = CharFilter(method="filter_for_rent")

    class Meta:
        model = UserLevelPropertyData
        fields = [
            "country",
            "city",
            "area",
            "community",
            "property_type",
            "min_price",
            "max_price",
            "bedrooms",
            "min_bedrooms",
            "max_bedrooms",
            "bathrooms",
            "min_bathrooms",
            "max_bathrooms",
            "furnished",
            "branded_building",
            "min_area",
            "max_area",
            "address",
            "property_category",
            "for_sale",
            "for_rent",
        ]

    def filter_furnished(self, queryset, name, value):
        """
        Filter properties by furnished status
        Supports comma-separated values for multiple selection
        Values: 0=Unfurnished, 1=Furnished, 2=Semi Furnished
        """
        if not value:
            return queryset

        # Handle comma-separated values (e.g. "0,1,2" or "unfurnished,furnished")
        values = [v.strip().lower() for v in value.split(",")]

        # Convert string values to integer choices
        furnished_choices = []

        for v in values:
            if v in ["0", "unfurnished"]:
                furnished_choices.append(FurnishedChoices.UNFURNISHED)
            elif v in ["1", "furnished"]:
                furnished_choices.append(FurnishedChoices.FURNISHED)
            elif v in ["2", "semi_furnished", "semi furnished"]:
                furnished_choices.append(FurnishedChoices.SEMI_FURNISHED)

        # Remove duplicates
        furnished_choices = list(set(furnished_choices))

        # If no valid choices found or all 3 filters selected, return original queryset
        if not furnished_choices or len(furnished_choices) == 3:
            return queryset

        # Apply filter for selected choices
        return queryset.filter(
            property_user_level_features__furnished__in=furnished_choices
        )

    def filter_branded_building(self, queryset, name, value):
        """
        Filter properties by branded building status
        Supports comma-separated values for multiple selection
        """
        if not value:
            return queryset

        # Handle comma-separated values (e.g. "true,false")
        values = [v.strip().lower() for v in value.split(",")]

        # If both true and false are selected, don't filter
        if any(v in ["true", "yes", "1"] for v in values) and any(
            v in ["false", "no", "0"] for v in values
        ):
            return queryset

        # Apply filter for single selection
        if any(v in ["true", "yes", "1"] for v in values):
            return queryset.filter(property_user_level_features__branded_building=True)
        elif any(v in ["false", "no", "0"] for v in values):
            return queryset.filter(property_user_level_features__branded_building=False)

        return queryset

    def filter_property_type(self, queryset, name, value):
        # Check if it's a comma-separated list
        if "," in value:
            property_types = [pt.strip() for pt in value.split(",")]
            return queryset.filter(property_type__in=property_types)
        # Handle single value case
        return queryset.filter(property_type__iexact=value)

    def filter_min_area(self, queryset, name, value):
        # Default unit is sqft
        # unit = self.request.query_params.get("area_unit", "sqft").lower()
        #
        # # Convert sqm to sqft if needed
        # if unit == "sqm":
        #     # 1 sqm = 10.764 sqft
        #     value = value * 10.764

        return queryset.filter(total_area__gte=value)

    def filter_max_area(self, queryset, name, value):
        # Default unit is sqft
        # unit = self.request.query_params.get("area_unit", "sqft").lower()
        #
        # # Convert sqm to sqft if needed
        # if unit == "sqm":
        #     # 1 sqm = 10.764 sqft
        #     value = value * 10.764

        return queryset.filter(total_area__lte=value)

    def filter_address(self, queryset, name, value):
        """
        Filter properties by address components
        Searches across unit number, building number, street, area, city, etc.
        """
        # First split by comma to get each location phrase
        location_phrases = [phrase.strip() for phrase in value.split(",")]
        combined_query = Q()

        # Process each location phrase separately
        for phrase in location_phrases:
            # Split the phrase into individual terms
            search_terms = phrase.split()
            phrase_query = Q()

            # Build a query that searches each term across all address components
            for term in search_terms:
                term_query = (
                    Q(property__unit_number__icontains=term)
                    | Q(property__building_number__icontains=term)
                    | Q(property__area__name__icontains=term)
                    | Q(property__city__name__icontains=term)
                    | Q(property__state__name__icontains=term)
                    | Q(property__country__name__icontains=term)
                    | Q(property__community__name__icontains=term)
                    | Q(property__postal_code__icontains=term)
                    | Q(property__property_category__icontains=term)
                )
                phrase_query &= term_query

            # Add this phrase's query to the combined query with OR logic
            combined_query |= phrase_query

        return queryset.filter(combined_query)

    def filter_for_sale(self, queryset, name, value):
        """Filter properties that are available for sale"""
        if value.lower() in ["true", "yes", "1", True]:
            return queryset.filter(
                property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_SALE
            )
        return queryset

    def filter_for_rent(self, queryset, name, value):
        """Filter properties that are available for rent"""
        if value.lower() in ["true", "yes", "1", True]:
            return queryset.filter(
                property__owner_intent=OwnerIntentForProperty.AVAILABLE_FOR_RENT
            )
        return queryset


class ExploreViewSet(generics.ListAPIView):
    """
    API view to list properties that were sourced from WhatsApp

    Supports filtering by:
    - Location (country, city, area, community)
    - Property type
    - Price range (min_price, max_price)
    - Number of bedrooms/bathrooms
    - Features (furnished, etc.)
    """

    serializer_class = EnhancedWhatsAppPropertiesListSerializer
    authentication_classes = [JWTAuthentication]
    pagination_class = StandardResultsSetPagination
    filter_backends = [
        DjangoFilterBackend,
        EnhancedSearchFilter,
        filters.OrderingFilter,
    ]
    filterset_class = PropertyFilter
    search_fields = [
        "property__unit_number",
        "property__building_number",
        "property__area__name",
        "property__city__name",
        "property__country__name",
        "property__state__name",
        "property__community__name",
        "property__postal_code",
        "property__property_type",
        "property__property_category",
        "property__owner_intent",
    ]

    ordering_fields = [
        "created_ts",
        "property_user_level_financial_details__asking_price",
        "number_of_bedrooms",
    ]
    ordering = ["-created_ts"]

    @general_exception_handler
    def get_queryset(self):
        return (
            UserLevelPropertyData.objects.filter(
                open_for_collaboration=True, property__is_archived=False
            )
            .select_related(
                "property",
                "property__country",
                "property__state",
                "property__city",
                "property__area",
                "property__community",
                "property_user_level_financial_details",
                "created_by",
                "created_by__agentprofile",
            )
            .prefetch_related(
                "property_user_level_features",
                "property_user_level_availability_and_status",
                # Add prefetch for AgentAssociatedProperty to optimize agent searches
                "property__agentassociatedproperty_set",
                "property__agentassociatedproperty_set__agent_profile",
                "property__agentassociatedproperty_set__agent_profile__user",
            )
            .order_by("-created_ts")
        )

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(paginated_queryset, many=True)

        response = paginator.get_paginated_response(serializer.data)

        return response


class PropertyAddressSearchView(generics.ListAPIView):
    """
    API view to get property addresses for search suggestions

    This endpoint returns addresses of properties from WhatsApp source
    for use in autocomplete/location search functionality.
    """

    authentication_classes = [JWTAuthentication]

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        search_query = request.query_params.get("search", "")
        search_query = request.query_params.get("search", "")

        # Create a raw SQL query to get unique addresses efficiently
        with connection.cursor() as cursor:
            params = []
            sql = """
                WITH address_data AS (
                    SELECT DISTINCT ON (address)
                        p.id,
                        COALESCE(
                            NULLIF(
                                TRIM(
                                    CONCAT_WS(', ',
                                        -- Include community sub-locations and other address components
                                        -- Only include non-null and non-empty values
                                        NULLIF(TRIM(c.sub_loc_1), ''),
                                        NULLIF(TRIM(c.sub_loc_2), ''),
                                        NULLIF(TRIM(c.sub_loc_3), ''),
                                        NULLIF(TRIM(c.sub_loc_4), ''),
                                        NULLIF(TRIM(c.sub_loc_5), ''),
                                        NULLIF(TRIM(c.name), ''),
                                        NULLIF(TRIM(a.name), ''),
                                        NULLIF(TRIM(ci.name), ''),
                                        NULLIF(TRIM(s.name), '')
                                    )
                                ), ''
                            ), NULL
                        ) as address,
                        CASE WHEN co.name IS NOT NULL THEN co.name END as country_name
                    FROM
                        properties_userlevelpropertydata ulpd
                    JOIN
                        properties_property p ON ulpd.property_id = p.id
                    LEFT JOIN
                        properties_community c ON p.community_id = c.id
                    LEFT JOIN
                        properties_area a ON p.area_id = a.id
                    LEFT JOIN
                        properties_city ci ON p.city_id = ci.id
                    LEFT JOIN
                        properties_state s ON p.state_id = s.id
                    LEFT JOIN
                        properties_country co ON p.country_id = co.id
                    WHERE
                        ulpd.source = 'WHATS_APP' AND p.is_archived = FALSE
                """

            # Add search filter if provided
            if search_query:
                sql += """
                    AND (
                        LOWER(co.name) LIKE %s OR
                        LOWER(s.name) LIKE %s OR
                        LOWER(ci.name) LIKE %s OR
                        LOWER(a.name) LIKE %s OR
                        LOWER(c.name) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_1, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_2, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_3, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_4, '')) LIKE %s OR
                        LOWER(COALESCE(c.sub_loc_5, '')) LIKE %s OR
                        LOWER(COALESCE(p.unit_number, '')) LIKE %s OR
                        LOWER(COALESCE(p.building_number, '')) LIKE %s OR
                        -- Cast postal_code to text before applying LOWER
                        LOWER(COALESCE(p.postal_code::text, '')) LIKE %s
                    )
                """
                search_param = f"%{search_query.lower()}%"
                params.extend([search_param] * 13)

            sql += """
                )
                SELECT * FROM address_data WHERE address IS NOT NULL
                ORDER BY address
            """

            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(results, request)

        # Format the response
        addresses = []
        for result in paginated_queryset:
            if result["address"]:
                addresses.append(
                    {"address": result["address"], "country": result["country_name"]}
                )

        return paginator.get_paginated_response(addresses)
