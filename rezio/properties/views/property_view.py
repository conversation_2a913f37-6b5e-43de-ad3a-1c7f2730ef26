import datetime as dt
import logging
import traceback
from datetime import datetime

from countryinfo import CountryInfo
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Value, CharField, Subquery, OuterRef
from rest_framework import status, generics
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet

from rezio.properties.constants import (
    DEFAULT_PROPERTY_IMAGES,
    INDIA,
    INDIAN_STATES,
    PROPERTY_MONITOR_DETAILS_FIELD_MAPPING,
    QUERY_PARAMS,
)
from rezio.properties.helper import verify_bedroom_details, property_helper
from rezio.properties.models import (
    Country,
    State,
    Property,
    Community,
    PropertyAvailabilityAndStatus,
    PropertyCompletionState,
    PropertyVerifiedDataFields,
    PropertyFinancialDetails,
    AgentAssociatedProperty,
    DeletedProperty,
    EmailTemplate,
    PropertyCoOwner,
    UserLevelPropertyData,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyAvailabilityAndStatus,
    PropertySalesUnitHistory,
    PropertyRentalUnitHistory,
    City,
)
from rezio.properties.permissions import (
    IsPropertyOwner,
    IsPropertyAssociateAgent,
    build_property_creation_permission_classes,
)
from rezio.properties.permissions import build_permission_classes
from rezio.properties.serializers import (
    PropertyIDSerializer,
    CombinedPropertyMonitorSerializer,
    ManualLocationDetails,
    PropertyMonitorLocationSerializer,
    PropertyMonitorUnitSerializer,
    PropertyMonitorResponseSerializer,
    UserAddedResponseSerializer,
    UnitDetailSerializer,
    PropertyCompletionStateSerializer,
    PropertyAvailabilitySerializer,
    PropertyFileUploadSerializer,
    ChoiceFieldSerializer,
    PropertyFileDeleteSerializer,
    PropertySpecificationSerializer,
    PropertyLocationDetailsSerializer,
    DraftCombinedPropertyMonitorSerializer,
    DraftManualLocationDetails,
    DraftPropertyAvailabilitySerializer,
    ManualPropertySpecificationSerializer,
    DraftPropertySpecificationSerializer,
    DraftManualPropertySpecificationSerializer,
    ManualCommercialPropertySpecificationSerializer,
    StateSerializer,
    ManualAddLocationDetails,
    PMAddLocationDetails,
    ClaimPropertyBasicDetailSerializer,
    UserLevelPropertyAvailabilitySerializer,
    UpdateUnitNumberSerializerV1,
)
from rezio.properties.serializers.property_hierarchy_serializer import (
    PortfolioSerializer,
)
from rezio.properties.serializers.property_serializer import (
    PaginatedResponseSerializer,
    PropertySpecificationsSerializer,
    PropertySaveUnitDetailsV1Serializer,
    ManualUpdateLocationDetailsV1,
    ManualAddLocationDetailsV1,
    PMUpdateLocationDetailsV1,
    PMAddLocationDetailsV1,
    PropertySpecificationSerializerV1,
    ManualResidentialPropertySpecificationSerializerV1,
    ManualCommercialPropertySpecificationSerializerV1,
    EditPropertySpecificationSerializerV1,
    EditManualCommercialPropertySpecificationSerializerV1,
    CountrySerializer,
    CitySerializer,
)
from rezio.properties.services.property_service import (
    validate_serializer,
    get_unit_details,
    save_community_data,
    update_rental_history,
    update_sales_history,
    fetch_role_obj_and_name,
    save_property_completion_state,
    delete_property,
    recalculate_unlocked_properties,
)
from rezio.properties.services.similar_transactions_notify_service import (
    SimilarTransactionNotifier,
)
from rezio.properties.text_choices import (
    PropertyAvailabilityStatus,
    CoOwnerRequestType,
    ClaimPropertyChoices,
    PropertyHierarchy,
)
from rezio.properties.text_choices import (
    PropertyCompletionStateChoices,
    PropertyPublishStatus,
    PropertyAgentType,
    OwnerIntentForProperty,
    PropertyType,
    UserRequestActions,
    RequestType,
    PropertyCategory,
)
from rezio.properties.utils import (
    create_user_level_property_data,
    get_property_object,
    get_property_availability_status_object,
    get_property_completion_state_object,
    clean_availability_and_status_data,
    get_claim_property_choices,
    create_user_level_filter,
    PropertyUtility,
)
from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    PRESIGNED_POST_STRUCTURES,
    PROPERTY_RENT_CONTRACT,
    KEY,
    DD_MMM_YYYY,
    PROPERTY_OWNERSHIP_PROOF,
)
from rezio.user.authentication import JWTAuthentication, FirebaseAuthentication
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.helper import (
    raise_invalid_data_exception,
    build_profile_permission_classes,
    get_profile_object_by_role,
)
from rezio.user.permissions import IsAuthenticatedInvestor, IsAuthenticatedAgent
from rezio.user.utils import (
    get_role_object,
    get_investor_profile_object,
    get_agent_profile_object,
    get_agent_role_object,
    get_investor_role_object,
    get_user_object,
    get_db_object,
    get_or_create_db_object,
    create_db_object,
    get_list_of_object_with_filters,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    ResourceNotFoundException,
    InternalServerException,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import DataSource
from rezio.utils.validation_utils import validation_utility

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PropertyViewSet(ModelViewSet):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = PropertyIDSerializer
    http_method_names = ["get", "post", "delete", "put"]

    def get_permissions(self):
        permission_classes = []
        role_name = self.request.query_params.get("user_role", None)
        role = get_role_object(role_name)
        if role.name == AGENT:
            permission_classes = [IsAuthenticatedAgent]
            if self.action in ["destroy", "update"]:
                permission_classes.append(IsPropertyAssociateAgent)
        elif role.name == INVESTOR:
            permission_classes = [IsAuthenticatedInvestor]
            if self.action in ["destroy", "update"]:
                permission_classes.append(IsPropertyOwner)
        else:
            raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get_property_object_or_404(self, pk):
        # queryset = self.get_queryset()
        # try:
        request_obj = get_db_object(
            app_name="properties",
            model_name=Property.__name__,
            single_field_value_dict={"pk": pk},
            not_found_text=f"Property object with ID {pk} does not exist",
        )
        return request_obj

    def get_user_level_property_object_or_404(
        self, property_obj, created_by, created_by_role
    ):
        request_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict={
                "property": property_obj,
                "created_by": created_by,
                "created_by_role": created_by_role,
            },
            not_found_text=f"UserLevelProperty object {property_obj} does not exist",
        )
        return request_obj

    @staticmethod
    def add_country_state_details(validated_data, *args, **kwargs):
        """
        Method to add country, state and community details
        """
        logger.info(validated_data)
        country = validated_data["country"]
        state = validated_data["state"]
        country_short_name = validated_data["country_short_name"]

        try:
            country = Country.objects.get(name=country)
            logger.info(f"Country fetched {country}")
        except ObjectDoesNotExist:
            country = Country.objects.create(
                name=country, short_name=country_short_name
            )
            logger.info(f"Country added {country}")

        try:
            state = State.objects.get(id=state, country=country)
            logger.info(f"state fetched {state}")
        except ObjectDoesNotExist:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid state sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Incorrect state provided for country."
                    },
                }
            )

        return country, state

    @staticmethod
    def save_community_and_unit_data(
        request, property_obj, user_level_property_obj, role_obj
    ):
        try:
            # user_role, role_obj = fetch_role_obj_and_name(request)

            is_draft = request.data.get("is_draft", None)
            if is_draft is None:
                request.data["is_draft"] = False
                is_draft = False
            if is_draft:
                serializer = DraftCombinedPropertyMonitorSerializer(data=request.data)
            else:
                serializer = CombinedPropertyMonitorSerializer(data=request.data)

            validated_data = validate_serializer(serializer)
            logger.info(
                f"Validated data for save_community_and_unit : {validated_data}"
            )

            if validated_data.get("is_draft"):
                (
                    PropertyCompletionState.objects.filter(
                        property=property_obj
                    ).exclude(state=PropertyCompletionStateChoices.LOCATION_DETAILS)
                ).delete()

                # clearing property data
                property_obj.clear_fields(["state"])

                message = "Community and unit info saved to draft"
            else:
                unit_details = get_unit_details(validated_data)

                unit_detail_serializer = UnitDetailSerializer(data=unit_details)
                unit_detail_validated_data = validate_serializer(unit_detail_serializer)

                community = save_community_data(
                    unit_detail_validated_data, property_obj
                )

                property_obj.community = community
                property_obj.unit_number = validated_data.get("unit_number")
                property_obj.building_number = validated_data.get("building_number")
                property_obj.property_monitor_address_id = validated_data.get(
                    "address_id"
                )

                property_obj.property_type = unit_detail_validated_data.get(
                    "property_type"
                )
                user_level_property_obj.property_type = unit_detail_validated_data.get(
                    "property_type"
                )

                property_obj.total_area = unit_detail_validated_data.get(
                    "unit_bua_sqft"
                )
                user_level_property_obj.total_area = unit_detail_validated_data.get(
                    "unit_bua_sqft"
                )

                property_obj.carpet_area = unit_detail_validated_data.get(
                    "suite_area_sqft"
                )
                user_level_property_obj.carpet_area = unit_detail_validated_data.get(
                    "suite_area_sqft"
                )

                property_obj.balcony_area = unit_detail_validated_data.get(
                    "balcony_size_sqft"
                )
                user_level_property_obj.balcony_area = unit_detail_validated_data.get(
                    "balcony_size_sqft"
                )

                property_obj.number_of_bedrooms = unit_detail_validated_data.get(
                    "no_beds"
                )
                user_level_property_obj.number_of_bedrooms = (
                    unit_detail_validated_data.get("no_beds")
                )

                if (
                    unit_detail_validated_data.get("parking")
                    and unit_detail_validated_data.get("parking") != ""
                ):
                    property_obj.parking_number = unit_detail_validated_data.get(
                        "parking"
                    )
                    user_level_property_obj.parking_number = (
                        unit_detail_validated_data.get("parking")
                    )
                    get_or_create_db_object(
                        PropertyVerifiedDataFields,
                        property=property_obj,
                        field_name="parking_number",
                        value=str(unit_detail_validated_data.get("parking")),
                        created_by=request.user,
                        created_by_role=role_obj,
                        field_type="str",
                    )

                    property_obj.parking_available = True
                    user_level_property_obj.parking_available = True

                    number_of_parking = len(
                        unit_detail_validated_data.get("parking").split(",")
                    )
                    property_obj.number_of_covered_parking = number_of_parking
                    user_level_property_obj.number_of_covered_parking = (
                        number_of_parking
                    )

                property_obj.floor_number = unit_detail_validated_data.get("floor")
                user_level_property_obj.floor_number = unit_detail_validated_data.get(
                    "floor"
                )

                property_obj.number_of_study_rooms = unit_detail_validated_data.get(
                    "study"
                )
                user_level_property_obj.number_of_study_rooms = (
                    unit_detail_validated_data.get("study")
                )

                property_obj.number_of_maid_rooms = unit_detail_validated_data.get(
                    "maid"
                )
                user_level_property_obj.number_of_maid_rooms = (
                    unit_detail_validated_data.get("maid")
                )

                for field in PROPERTY_MONITOR_DETAILS_FIELD_MAPPING:
                    value = unit_detail_validated_data.get(
                        PROPERTY_MONITOR_DETAILS_FIELD_MAPPING[field]
                    )

                    if field in ["total_area", "balcony_area", "carpet_area"]:
                        field_type = "float"
                    elif field in [
                        "number_of_bedrooms",
                        "number_of_maid_rooms",
                        "number_of_study_rooms",
                    ]:
                        field_type = "int"
                    else:
                        field_type = "str"

                    if (value is not None) and (
                        field
                        not in [
                            "parking_number",
                            "number_of_bathrooms",
                        ]
                    ):
                        get_or_create_db_object(
                            PropertyVerifiedDataFields,
                            property=property_obj,
                            field_name=field,
                            value=str(value),
                            created_by=request.user,
                            created_by_role=role_obj,
                            field_type=field_type,
                        )

                property_obj.updated_by = request.user
                property_obj.updated_by_role = role_obj

                property_obj.save()
                user_level_property_obj.updated_by = request.user
                user_level_property_obj.updated_by_role = role_obj
                user_level_property_obj.save()

                (
                    property_financials,
                    created,
                ) = get_or_create_db_object(
                    PropertyFinancialDetails,
                    property=property_obj,
                    created_by=request.user,
                    created_by_role=role_obj,
                )
                (
                    user_level_property_financial,
                    user_level_property_financial_created,
                ) = get_or_create_db_object(
                    UserLevelPropertyFinancialDetails,
                    property_level_data=user_level_property_obj,
                    created_by=request.user,
                    created_by_role=role_obj,
                )

                update_rental_history(
                    unit_details.get("rental_unit_history", []),
                    property_obj,
                    request.user.id,
                    role_obj,
                    property_financials,
                    created,
                    user_level_property_financial,
                )
                update_sales_history(
                    unit_details.get("sales_unit_history", []),
                    property_obj,
                    request.user.id,
                    role_obj,
                    property_financials,
                    created,
                    user_level_property_financial,
                )
                message = "Community and unit info saved successfully"

            logger.info(message)

            return property_obj.community, property_obj.unit_number

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in CommunityBuildingViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    @general_exception_handler
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)

        serializer = PMAddLocationDetails(data=request.data)

        validated_data = validate_serializer(serializer)
        logger.info(f"Validated data for create PropertyViewSet : {validated_data}")
        created_by = request.user

        address_id = request.data.get("address_id")
        property_instance = Property.objects.filter(
            property_monitor_address_id=address_id
        ).first()
        continue_claim_flow = False
        if property_instance:
            continue_claim_flow = True
            if (
                property_instance.property_publish_status
                == PropertyPublishStatus.DRAFT.value
            ):
                property_instance.delete()
                continue_claim_flow = False
        if continue_claim_flow:
            logger.info(f"Property already exists {property_instance}")

            property_id = PropertyIDSerializer(property_instance)

            viewer_profile = get_profile_object_by_role(request.user, role_obj)

            claim_property_choices = get_claim_property_choices(
                property_instance, viewer_profile, role_obj
            )

            if claim_property_choices == ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO:
                user_level_filter = create_user_level_filter(
                    property_instance, request.user, role_obj
                )
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    **user_level_filter
                ).first()
            else:
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    property=property_instance,
                    created_by=property_instance.created_by,
                    created_by_role=property_instance.created_by_role,
                ).first()

            property_info = ClaimPropertyBasicDetailSerializer(
                creator_level_property_data
            ).data
            property_specifications = PropertySpecificationsSerializer(
                creator_level_property_data
            ).data

            logger.info(
                f"For property monitor address id {address_id}, "
                f"property specifications {property_specifications} and property info {property_info}"
            )

            email_template = None
            if (
                claim_property_choices == ClaimPropertyChoices.CLAIM_PROPERTY
                and user_role == INVESTOR
            ):
                template = EmailTemplate.objects.filter(name="claim_property").first()
                if template:
                    email_subject = template.subject.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }
            elif claim_property_choices == ClaimPropertyChoices.CONTACT_SUPPORT:
                template = EmailTemplate.objects.filter(
                    name="add_property_to_portfolio"
                ).first()
                if template:
                    email_subject = template.subject.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }
            response = {
                "claim_property_choices": claim_property_choices,
                "property_info": property_info,
                "property_specifications": property_specifications,
                "email_template": email_template,
                "duplicate_found": True,
                "created_by": property_instance.created_by.id,
                "created_by_role": property_instance.created_by_role.name,
            }
            response.update(property_id.data)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property already exists",
                    KEY_PAYLOAD: response,
                    KEY_ERROR: {},
                },
            )

        logger.info(
            f"Property with property monitor address id {address_id} does not exist"
        )
        country, state = self.add_country_state_details(validated_data)

        property_category = validated_data.get("property_category")

        property_obj = create_db_object(
            Property,
            country=country,
            state=state,
            property_category=property_category,
            created_by_role=role_obj,
            created_by=created_by,
        )

        user_level_property_obj = create_db_object(
            UserLevelPropertyData,
            property=property_obj,
            created_by=created_by,
            created_by_role=role_obj,
        )

        if user_role == INVESTOR:
            investor_profile = get_investor_profile_object(request.user)
            property_obj.owner = investor_profile
            user_level_property_obj.user_hierarchy = PropertyHierarchy.OWNER
            property_obj.save()
            user_level_property_obj.save()
        elif user_role == AGENT:
            property_obj.owner_verified = False
            property_obj.agent_type = PropertyAgentType.SELECTIVE_AGENTS
            user_level_property_obj.user_hierarchy = PropertyHierarchy.AGENT
            property_obj.save()
            agent_profile = get_agent_profile_object(request.user)
            create_db_object(
                AgentAssociatedProperty,
                property=property_obj,
                agent_profile=agent_profile,
                is_associated=True,
                created_by_role=role_obj,
                created_by=created_by,
                action_status=UserRequestActions.ACCEPTED,
            )
            user_level_property_obj.save()

        property_financials, created = get_or_create_db_object(
            PropertyFinancialDetails, property=property_obj
        )
        user_level_property_financial, created = get_or_create_db_object(
            UserLevelPropertyFinancialDetails,
            property_level_data=user_level_property_obj,
            created_by=request.user,
            created_by_role=role_obj,
        )

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            property_financials.property_currency_code = currency_code
            user_level_property_financial.property_currency_code = currency_code
            if created:
                property_financials.created_by = created_by
                property_financials.created_by_role = role_obj
            else:
                property_financials.updated_by = request.user
                property_financials.updated_by_role = role_obj
                user_level_property_financial.updated_by = request.user
                user_level_property_financial.updated_by_role = role_obj
            property_financials.save()
            user_level_property_financial.save()

        is_completed = True
        draft_data = None
        if serializer.data.get("is_draft"):
            is_completed = False
            # draft_data = json.dumps(serializer.data)
            serializer.data.pop("is_draft")
            draft_data = serializer.data

        community, unit = self.save_community_and_unit_data(
            request,
            property_obj=property_obj,
            user_level_property_obj=user_level_property_obj,
            role_obj=role_obj,
        )
        logger.info(f"Community & building info {community} and unit {unit}")

        # Update PropertyCompletionState for property location
        save_property_completion_state(
            property_obj,
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            is_completed,
            draft_data,
        )

        # Update PropertyCompletionState for property specifications
        save_property_completion_state(
            property_obj,
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            request.user,
            role_obj,
            False,
            None,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: Location Details & Property "
            "Specifications from Property Monitor"
        )

        property_serializer = PropertyIDSerializer(property_obj)

        response = {
            "claim_property_choices": None,
            "property_info": None,
            "property_specifications": None,
            "email_template": None,
            "duplicate_found": False,
            "created_by": property_obj.created_by.id,
            "created_by_role": property_obj.created_by_role.name,
        }
        response.update(property_serializer.data)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property created successfully",
                KEY_PAYLOAD: response,
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    def destroy(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )
        property_obj.delete()
        user_level_property_obj.delete()
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property discarded successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    def retrieve_property_specifications(self, request, *args, **kwargs):
        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))

        property_completion_state = get_property_completion_state_object(
            self.kwargs.get("pk"),
            state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
        )
        logger.info(f"Property Completion status {property_completion_state}")

        property_category = property_obj.property_category

        if property_category == PropertyCategory.RESIDENTIAL:
            serializer = PropertySpecificationSerializer(property_obj)
        else:
            serializer = ManualCommercialPropertySpecificationSerializer(property_obj)

        data = serializer.data

        data["data_source"] = property_completion_state.data_source

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property specifications fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def update(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        if request.data.get("is_draft") is None:
            request.data["is_draft"] = False

        serializer = PMAddLocationDetails(data=request.data)

        validated_data = validate_serializer(serializer)
        logger.info(f"Validated data for update PropertyViewSet : {validated_data}")
        address_id = request.data.get("address_id")

        property_instance = (
            Property.objects.filter(property_monitor_address_id=address_id)
            .exclude(id=self.kwargs.get("pk"))
            .first()
        )
        continue_claim_flow = False
        if property_instance:
            continue_claim_flow = True
            if (
                property_instance.property_publish_status
                == PropertyPublishStatus.DRAFT.value
            ):
                property_instance.delete()
                continue_claim_flow = False
        if continue_claim_flow:
            logger.info(f"Property already exists {property_instance}")
            property_id = PropertyIDSerializer(property_instance)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            claim_property_choices = get_claim_property_choices(
                property_instance, viewer_profile, role_obj
            )
            if claim_property_choices == ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO:
                user_level_filter = create_user_level_filter(
                    property_instance, request.user, role_obj
                )
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    **user_level_filter
                ).first()
            else:
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    property=property_instance,
                    created_by=property_instance.created_by,
                    created_by_role=property_instance.created_by_role,
                ).first()

            property_info = ClaimPropertyBasicDetailSerializer(
                creator_level_property_data
            ).data
            property_specifications = PropertySpecificationsSerializer(
                creator_level_property_data
            ).data

            logger.info(
                f"For property monitor address id {address_id}, "
                f"property specifications {property_specifications} and property info {property_info}"
            )

            email_template = None
            if (
                claim_property_choices == ClaimPropertyChoices.CLAIM_PROPERTY
                and user_role == INVESTOR
            ):
                template = EmailTemplate.objects.filter(name="claim_property").first()
                if template:
                    email_subject = template.subject.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }
            elif claim_property_choices == ClaimPropertyChoices.CONTACT_SUPPORT:
                template = EmailTemplate.objects.filter(
                    name="add_property_to_portfolio"
                ).first()
                if template:
                    email_subject = template.subject.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }
            response = {
                "claim_property_choices": claim_property_choices,
                "property_info": property_info,
                "property_specifications": property_specifications,
                "email_template": email_template,
                "duplicate_found": True,
                "created_by": property_instance.created_by.id,
                "created_by_role": property_instance.created_by_role.name,
            }
            response.update(property_id.data)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property already exists",
                    KEY_PAYLOAD: response,
                    KEY_ERROR: {},
                },
            )

        logger.info(
            f"Property with property monitor address id {address_id} does not exist"
        )
        country, state = self.add_country_state_details(validated_data)

        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )

        if property_obj.country != country or property_obj.state != state:
            # cleaning availability and status data if location is updated
            clean_availability_and_status_data(
                self.kwargs.get("pk"), user_level_property_obj, request.user, role_obj
            )

            # remove completion states
            PropertyCompletionState.objects.filter(property=property_obj).delete()

            # clearing property data
            property_obj.clear_fields()
            user_level_property_obj.clear_fields()

        (
            property_financials,
            financial_created,
        ) = get_or_create_db_object(PropertyFinancialDetails, property=property_obj)

        user_level_property_financial, created = get_or_create_db_object(
            UserLevelPropertyFinancialDetails,
            property_level_data=user_level_property_obj,
            created_by=request.user,
            created_by_role=role_obj,
        )

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()

        if currency_data:
            currency_code = currency_data[0]
            property_financials.property_currency_code = currency_code
            user_level_property_financial.property_currency_code = currency_code
            if financial_created:
                property_financials.created_by = request.user
                property_financials.created_by_role = role_obj
            else:
                property_financials.updated_by = request.user
                property_financials.updated_by_role = role_obj
                user_level_property_financial.updated_by = request.user
                user_level_property_financial.updated_by_role = role_obj
            property_financials.save()
            user_level_property_financial.save()

        property_obj.country = country
        property_obj.state = state
        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj
        user_level_property_obj.updated_by = request.user
        user_level_property_obj.updated_by_role = role_obj
        property_obj.save()
        user_level_property_obj.save()

        is_completed = True
        draft_data = None
        if serializer.data.get("is_draft"):
            is_completed = False
            draft_data = serializer.data

        community, unit = self.save_community_and_unit_data(
            request,
            property_obj=property_obj,
            user_level_property_obj=user_level_property_obj,
            role_obj=role_obj,
        )

        logger.info(f"Community & building info {community} and unit {unit}")

        # Update PropertyCompletionState for property location
        save_property_completion_state(
            property_obj,
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            is_completed,
            draft_data,
        )

        # Update PropertyCompletionState for property specifications
        save_property_completion_state(
            property_obj,
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            request.user,
            role_obj,
            False,
            None,
        )

        property_serializer = PropertyIDSerializer(property_obj)
        response = {
            "claim_property_choices": None,
            "property_info": None,
            "property_specifications": None,
            "email_template": None,
            "duplicate_found": False,
            "created_by": property_obj.created_by.id,
            "created_by_role": property_obj.created_by_role.name,
        }
        response.update(property_serializer.data)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property updated successfully",
                KEY_PAYLOAD: response,
                KEY_ERROR: {},
            },
        )


class CommunityBuildingViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    # def get_permissions(self):
    #     # Define your default permissions
    #     return build_permission_classes(self.request)

    def split_data(self, data):
        for item in data["data"]:
            name_parts = item["name"].split(",", 1)

            if len(name_parts) == 2:
                item["building"] = name_parts[0].strip()
                item["community_string"] = name_parts[1].strip()
            elif len(name_parts) == 1:
                item["building"] = name_parts[0].strip()
                item["community_string"] = ""
            else:
                item["building"] = ""
                item["community_string"] = ""

        return data

    def get(self, request):
        try:
            keyword = request.query_params.get("keyword", None)
            state = request.query_params.get("state", None)
            if not keyword or not state:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Property details not found"},
                    }
                )
            keyword = f'"{keyword.upper()}"'

            property_monitor = PropertyMonitorAPI()
            location_data = property_monitor.get_location(state, keyword)
            location_data.pop("status")
            location_data = self.split_data(location_data)
            serializer = PropertyMonitorLocationSerializer(location_data)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Locations fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in CommunityBuildingViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class UnitNoSearchViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    # def get_permissions(self):
    #     # Define your default permissions
    #     return build_permission_classes(self.request)

    def generate_location_address(self, data):
        for item in data["data"]:
            location_name = f"""{f"{item['sub_loc_4']}, " if item['sub_loc_4'] != '' else ''}{f"{item['sub_loc_3']}, " if item['sub_loc_3'] != '' else ''}{f"{item['sub_loc_2']}, " if item['sub_loc_2'] != '' else ''}{f"{item['sub_loc_1']}, " if item['sub_loc_1'] != '' else ''}{item['master_community'] if item['master_community'] != '' else ''}"""

            item["location_name"] = location_name

        return data

    def get(self, request):
        try:
            unit_no = request.query_params.get("unit_no", None)
            page = request.query_params.get("page", None)
            state = request.query_params.get("state", None)
            location_id = request.query_params.get("location_id", None)
            if not unit_no or not page or not state or not location_id:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Property details not found"},
                    }
                )

            # adding for wildcard search
            unit_no = f"~{unit_no}~"
            property_monitor = PropertyMonitorAPI()
            unit_data = property_monitor.get_units_for_location(
                location_id, unit_no, page
            )
            unit_data.pop("status")
            self.generate_location_address(unit_data)
            serializer = PropertyMonitorUnitSerializer(unit_data)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Units fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in UnitNoSearchViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class SaveCommunityUnitViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            is_draft = request.data.get("is_draft", None)
            if is_draft is None:
                request.data["is_draft"] = False
                is_draft = False
            if is_draft:
                serializer = DraftCombinedPropertyMonitorSerializer(data=request.data)
            else:
                serializer = CombinedPropertyMonitorSerializer(data=request.data)

            validated_data = validate_serializer(serializer)

            property_obj = get_property_object(request.data.get("property_id"))

            is_completed = True

            if (
                Property.objects.filter(
                    property_monitor_address_id=validated_data.get("address_id")
                ).exclude(id=property_obj.id)
            ).exists():
                raise_invalid_data_exception(
                    "This property already exists in your portfolio or "
                    "in the portfolio of a co-owner."
                )

            property_financials = PropertyFinancialDetails.objects.filter(
                property=property_obj
            ).first()

            if validated_data.get("is_draft"):
                # cleaning availability and status data if location is updated
                clean_availability_and_status_data(
                    self.kwargs.get("pk"), request.user, role_obj
                )

                (
                    PropertyCompletionState.objects.filter(
                        property=property_obj
                    ).exclude(state=PropertyCompletionStateChoices.LOCATION_DETAILS)
                ).delete()

                # clearing property data
                property_obj.clear_fields(["state"])

                message = "Community and unit info saved to draft"
            else:
                unit_details = get_unit_details(validated_data)

                unit_detail_serializer = UnitDetailSerializer(data=unit_details)
                unit_detail_validated_data = validate_serializer(unit_detail_serializer)

                community = save_community_data(
                    unit_detail_validated_data, property_obj
                )

                if (
                    property_obj.community != community
                    or property_obj.property_monitor_address_id
                    != validated_data.get("address_id")
                ):
                    # cleaning availability and status data if location is updated
                    clean_availability_and_status_data(self.kwargs.get("pk"))

                    (
                        PropertyCompletionState.objects.filter(
                            property=property_obj
                        ).exclude(state=PropertyCompletionStateChoices.LOCATION_DETAILS)
                    ).delete()
                    if property_financials and property_financials.valuation:
                        property_financials.valuation = None
                        property_financials.valuation_data_source = (
                            DataSource.USER_ADDED
                        )
                        property_financials.save()

                    # clearing property data
                    property_obj.clear_fields(["state"])

                property_obj.community = community
                property_obj.unit_number = validated_data.get("unit_number")
                property_obj.building_number = validated_data.get("building_number")
                property_obj.property_monitor_address_id = validated_data.get(
                    "address_id"
                )
                property_obj.property_type = unit_detail_validated_data.get(
                    "property_type"
                )
                property_obj.total_area = unit_detail_validated_data.get(
                    "unit_bua_sqft"
                )
                property_obj.carpet_area = unit_detail_validated_data.get(
                    "suite_area_sqft"
                )
                property_obj.balcony_area = unit_detail_validated_data.get(
                    "balcony_size_sqft"
                )
                property_obj.number_of_bedrooms = unit_detail_validated_data.get(
                    "no_beds"
                )
                if (
                    unit_detail_validated_data.get("parking")
                    and unit_detail_validated_data.get("parking") != ""
                ):
                    property_obj.parking_number = unit_detail_validated_data.get(
                        "parking"
                    )
                    property_obj.parking_available = True
                    number_of_parking = len(
                        unit_detail_validated_data.get("parking").split(",")
                    )
                    property_obj.number_of_covered_parking = number_of_parking
                property_obj.floor_number = unit_detail_validated_data.get("floor")
                property_obj.number_of_study_rooms = unit_detail_validated_data.get(
                    "study"
                )
                property_obj.number_of_maid_rooms = unit_detail_validated_data.get(
                    "maid"
                )
                property_obj.updated_by = request.user
                property_obj.updated_by_role = role_obj
                property_obj.save()
                (
                    property_financials,
                    created,
                ) = PropertyFinancialDetails.objects.get_or_create(
                    property=property_obj
                )
                update_rental_history(
                    unit_details.get("rental_unit_history", []),
                    property_obj,
                    request.user.id,
                    role_obj,
                    property_financials,
                    created,
                )
                update_sales_history(
                    unit_details.get("sales_unit_history", []),
                    property_obj,
                    request.user.id,
                    role_obj,
                    property_financials,
                    created,
                )
                message = "Community and unit info saved successfully"

            # Update PropertyCompletionState for community and building info
            draft_data = None
            if validated_data.pop("is_draft", None):
                draft_data = validated_data
                is_completed = False
            save_property_completion_state(
                property_obj,
                DataSource.PROPERTY_MONITOR,
                PropertyCompletionStateChoices.COMMUNITY_AND_BUILDING_INFO,
                request.user,
                role_obj,
                is_completed,
                draft_data,
            )

            # Update PropertyCompletionState for property specifications
            save_property_completion_state(
                property_obj,
                DataSource.PROPERTY_MONITOR,
                PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
                request.user,
                role_obj,
                False,
                None,
            )

            logger.info("Data added in PropertyCompletionState")
            property_serializer = PropertyIDSerializer(property_obj)

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: property_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in CommunityBuildingViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class ManualSaveCommunityUnitViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            is_draft = request.data.get("is_draft", None)
            if is_draft is None:
                request.data["is_draft"] = False
                is_draft = False
            if is_draft:
                serializer = DraftManualLocationDetails(data=request.data)
            else:
                serializer = ManualLocationDetails(data=request.data)

            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            is_completed = True
            property_obj = get_property_object(request.data.get("property_id"))
            validated_data = serializer.data
            property_financials = PropertyFinancialDetails.objects.filter(
                property=property_obj
            ).first()

            if validated_data.get("is_draft"):
                # cleaning availability and status data if location is updated
                clean_availability_and_status_data(self.kwargs.get("pk"))

                (
                    PropertyCompletionState.objects.filter(
                        property=property_obj
                    ).exclude(state=PropertyCompletionStateChoices.LOCATION_DETAILS)
                ).delete()

                # clearing property data
                property_obj.clear_fields(["state"])

                message = "Community and unit info saved to draft"
            else:
                community, created = Community.objects.get_or_create(
                    name=validated_data.get("community"), added_by=DataSource.USER_ADDED
                )

                if (
                    property_obj.community != community
                    or property_obj.property_monitor_address_id
                    or property_obj.unit_number != validated_data.get("unit_number")
                ):
                    # cleaning availability and status data if location is updated
                    clean_availability_and_status_data(request.data.get("property_id"))
                    if property_financials and property_financials.valuation:
                        property_financials.valuation = None
                        property_financials.valuation_data_source = (
                            DataSource.USER_ADDED
                        )
                        property_financials.save()

                    (
                        PropertyCompletionState.objects.filter(
                            property=property_obj
                        ).exclude(state=PropertyCompletionStateChoices.LOCATION_DETAILS)
                    ).delete()

                    property_obj.clear_fields(exclude=["id", "country", "state"])

                property_obj.community = community
                property_obj.unit_number = validated_data.get("unit_number")
                property_obj.property_type = validated_data.get("property_type")
                property_obj.property_type = validated_data.get("property_type")
                property_obj.floor_number = validated_data.get("floor_number")
                property_obj.postal_code = validated_data.get("postal_code")
                property_obj.updated_by = request.user
                property_obj.updated_by_role = role_obj
                property_obj.save()

                if (
                    property_financials
                    and property_financials.valuation
                    and property_financials.valuation_data_source
                    == DataSource.PROPERTY_MONITOR
                ):
                    property_financials.valuation = None
                    property_financials.valuation_data_source = DataSource.USER_ADDED
                    property_financials.save()

                message = "Community and unit info saved successfully"

            # Update PropertyCompletionState for community and building info
            draft_data = None
            if validated_data.pop("is_draft", None):
                draft_data = validated_data
                is_completed = False
            save_property_completion_state(
                property_obj,
                DataSource.USER_ADDED,
                PropertyCompletionStateChoices.COMMUNITY_AND_BUILDING_INFO,
                request.user,
                role_obj,
                is_completed,
                draft_data,
            )

            property_serializer = PropertyIDSerializer(property_obj)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: property_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"Error in ManualCommunityBuildingViewSet - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetSavedCommunityAndBuildingInfo(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def get(self, request, property_id):
        try:
            property_obj = get_property_object(property_id=property_id)
            property_completion_state = get_property_completion_state_object(
                property_id,
                state=PropertyCompletionStateChoices.COMMUNITY_AND_BUILDING_INFO,
            )
            logger.info(f"Property Completion status {property_completion_state}")
            if not property_completion_state:
                return Response(
                    {"detail": "PropertyCompletionState not found."},
                    status=status.HTTP_404_NOT_FOUND,
                )

            if property_completion_state.data_source == DataSource.PROPERTY_MONITOR:
                serializer = PropertyMonitorResponseSerializer(property_obj)
            elif property_completion_state.data_source == DataSource.USER_ADDED:
                serializer = UserAddedResponseSerializer(property_obj)
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data source found",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: f"Invalid data source found {property_completion_state.data_source}"
                        },
                    }
                )

            data = serializer.data
            data["data_source"] = property_completion_state.data_source
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Community & building info data fetched successfully",
                    KEY_PAYLOAD: data,
                    KEY_ERROR: {},
                },
            )
        except Property.DoesNotExist:
            message = f"Property object with ID {property_id} does not exist"
            logger.error(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"Error in GetSavedCommunityAndBuildingInfo - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class AvailabilityAndStatusViewSet(APIView):
    """
    API view for property availability and status
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request):
        user_role, role_obj = fetch_role_obj_and_name(request)
        is_draft = request.data.get("is_draft")
        if is_draft is None:
            request.data["is_draft"] = False
            is_draft = False
        if is_draft:
            serializer = DraftPropertyAvailabilitySerializer(
                data=request.data, context={"user_role": user_role}
            )
        else:
            serializer = PropertyAvailabilitySerializer(
                data=request.data, context={"user_role": user_role}
            )

        validated_data = validate_serializer(serializer)
        logger.info(
            f"Validated data for AvailabilityAndStatusViewSet : {validated_data}"
        )
        property_id = validated_data.get("property_id")
        property_status = validated_data.get("status")
        handover_date = validated_data.get("handover_date")
        enable_payment_plan = validated_data.get("enable_payment_plan")
        during_construction = validated_data.get("during_construction")
        on_handover = validated_data.get("on_handover")
        enable_post_handover = validated_data.get("enable_post_handover")
        post_handover_time_frame = validated_data.get("post_handover_time_frame")
        property_intent = validated_data.get("property_intent")
        asking_price = validated_data.get("asking_price")
        valuation = validated_data.get("valuation")
        expected_rent = validated_data.get("expected_rent")
        expected_security_deposit = validated_data.get("expected_security_deposit")
        preferred_payment_frequency = validated_data.get("preferred_payment_frequency")
        rent_available_start_date = validated_data.get("rent_available_start_date")
        price_negotiable = validated_data.get("price_negotiable")
        edit_availability_status_data = validated_data.get(
            "edit_availability_status_data"
        )

        is_completed = True
        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )
        (
            property_availability_status,
            availability_created,
        ) = get_or_create_db_object(
            PropertyAvailabilityAndStatus, property=property_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
        )
        logger.info(f"UserLevelProperty is {user_level_property_obj}")

        (
            user_level_property_availability_status,
            user_level_property_created,
        ) = get_or_create_db_object(
            UserLevelPropertyAvailabilityAndStatus,
            property_level_data=user_level_property_obj,
        )

        if validated_data.get("is_draft"):
            message = "Availability data saved to draft"
        else:
            property_availability_status.status = property_status
            property_availability_status.handover_date = handover_date
            property_availability_status.enable_payment_plan = enable_payment_plan
            property_availability_status.during_construction = during_construction
            property_availability_status.on_handover = on_handover
            property_availability_status.enable_post_handover = enable_post_handover
            property_availability_status.post_handover_time_frame = (
                post_handover_time_frame
            )
            user_level_property_availability_status.status = property_status
            user_level_property_availability_status.handover_date = handover_date
            user_level_property_availability_status.enable_payment_plan = (
                enable_payment_plan
            )
            user_level_property_availability_status.during_construction = (
                during_construction
            )
            user_level_property_availability_status.on_handover = on_handover
            user_level_property_availability_status.enable_post_handover = (
                enable_post_handover
            )
            user_level_property_availability_status.post_handover_time_frame = (
                post_handover_time_frame
            )

            if not edit_availability_status_data:
                (
                    property_financials,
                    financial_created,
                ) = PropertyFinancialDetails.objects.get_or_create(
                    property=property_obj
                )
                (
                    user_level_property_financials,
                    user_level_property_financial_created,
                ) = get_or_create_db_object(
                    UserLevelPropertyFinancialDetails,
                    property_level_data=user_level_property_obj,
                )
                property_verified_fields = get_list_of_object_with_filters(
                    app_name="properties",
                    model_name=PropertyVerifiedDataFields.__name__,
                    single_field_value_dict={"property": property_obj},
                ).values_list("field_name", flat=True)

                if (
                    property_financials.valuation
                    and "valuation" in property_verified_fields
                ):
                    logger.info(
                        f"Valuation {property_financials.valuation} is fetch from property monitor for property "
                        f"{property_obj.id}"
                    )
                    pass
                else:
                    property_financials.valuation = valuation
                    user_level_property_financials.valuation = valuation

                if user_role == AGENT:
                    property_obj.owner_intent = property_intent
                    property_obj.updated_by = request.user
                    property_obj.updated_by_role = role_obj
                    property_obj.save()
                    user_level_property_obj.save()
                    property_financials.asking_price = asking_price
                    user_level_property_financials.asking_price = asking_price
                    property_financials.expected_rent = expected_rent
                    user_level_property_financials.expected_rent = expected_rent
                    property_financials.expected_security_deposit = (
                        expected_security_deposit
                    )
                    user_level_property_financials.expected_security_deposit = (
                        expected_security_deposit
                    )
                    property_financials.preferred_payment_frequency = (
                        preferred_payment_frequency
                    )
                    user_level_property_financials.preferred_payment_frequency = (
                        preferred_payment_frequency
                    )
                    property_financials.price_negotiable = price_negotiable
                    user_level_property_financials.price_negotiable = price_negotiable
                    property_availability_status.rent_available_start_date = (
                        rent_available_start_date
                    )
                    user_level_property_availability_status.rent_available_start_date = rent_available_start_date

                if financial_created:
                    property_financials.created_by = request.user
                    property_financials.created_by_role = role_obj
                else:
                    property_financials.updated_by = request.user
                    user_level_property_financials.updated_by = request.user
                    property_financials.updated_by_role = role_obj
                    user_level_property_financials.updated_by_role = role_obj
                property_financials.save()
                user_level_property_financials.save()

            if availability_created:
                property_availability_status.occupancy_status = (
                    PropertyAvailabilityStatus.VACANT
                )
                user_level_property_availability_status.occupancy_status = (
                    PropertyAvailabilityStatus.VACANT
                )
                property_availability_status.created_by = request.user
                property_availability_status.created_by_role = role_obj
            else:
                property_availability_status.updated_by = request.user
                user_level_property_availability_status.updated_by = request.user
                property_availability_status.updated_by_role = role_obj
                user_level_property_availability_status.updated_by_role = role_obj
            property_availability_status.save()
            user_level_property_availability_status.save()
            message = "Availability data saved successfully"

        # Update PropertyCompletionState for community and building info
        draft_data = None
        if is_draft:
            draft_data = serializer.data
            is_completed = False
        save_property_completion_state(
            property_obj,
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.AVAILABILITY_AND_STATUS,
            request.user,
            role_obj,
            is_completed,
            draft_data,
        )

        property_serializer = PropertyIDSerializer(property_obj)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: message,
                KEY_PAYLOAD: property_serializer.data,
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    def get(self, request):
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_id = request.query_params.get("property_id", None)
        property_obj = get_property_object(property_id)

        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"{property_obj} does not exist at user level",
        )

        user_level_property_availability_and_status = get_list_of_object_with_filters(
            "properties",
            UserLevelPropertyAvailabilityAndStatus.__name__,
            {"property_level_data": user_level_property_obj},
            None,
        ).first()

        user_level_property_financial_details = get_list_of_object_with_filters(
            "properties",
            UserLevelPropertyFinancialDetails.__name__,
            {"property_level_data": user_level_property_obj},
            None,
        ).first()

        property_verified_fields = get_list_of_object_with_filters(
            app_name="properties",
            model_name=PropertyVerifiedDataFields.__name__,
            single_field_value_dict={"property": property_obj},
        ).values_list("field_name", flat=True)

        serializer = UserLevelPropertyAvailabilitySerializer(
            user_level_property_availability_and_status
        )

        data = serializer.data
        if user_role == AGENT:
            data["property_intent"] = property_obj.owner_intent
            data["asking_price"] = user_level_property_financial_details.asking_price

        data["valuation"] = user_level_property_financial_details.valuation
        data["valuation_data_source"] = DataSource.USER_ADDED
        if "valuation" in property_verified_fields:
            data["valuation_data_source"] = DataSource.PROPERTY_MONITOR
        data["property_currency_code"] = (
            user_level_property_financial_details.property_currency_code
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Availability data fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )


class PropertyDocumentsViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            serializer = PropertyFileUploadSerializer(data=request.data)
            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            property_obj = get_property_object(
                serializer.validated_data.get("property_id")
            )
            s3_client = S3Client()
            # * Upload prepared contract PDF to S3
            if serializer.validated_data.get("is_rent_contract"):
                rent_contract_key = PRESIGNED_POST_STRUCTURES.get(
                    PROPERTY_RENT_CONTRACT, {}
                ).get(KEY, "")
                rent_contract_key = rent_contract_key.format(
                    property_id=property_obj.id,
                    filename=f"{request.user.pk}_{user_role}_{datetime.now().strftime(DD_MMM_YYYY)}_rent_contract",
                )
                file = serializer.validated_data.get("file")

                s3_client.upload_file(file, rent_contract_key)

                (
                    availability_and_status,
                    created,
                ) = PropertyAvailabilityAndStatus.objects.get_or_create(
                    property=property_obj
                )
                availability_and_status.rent_contract_key = rent_contract_key
                availability_and_status.rent_contract_file_name = file.name
                availability_and_status.rent_contract_file_size = file.size
                if created:
                    availability_and_status.created_by = request.user
                    availability_and_status.created_by_role = role_obj
                else:
                    availability_and_status.updated_by = request.user
                    availability_and_status.updated_by_role = role_obj
                availability_and_status.save()
                message = "Rent contract uploaded successfully"
            else:
                ownership_proof_key = PRESIGNED_POST_STRUCTURES.get(
                    PROPERTY_OWNERSHIP_PROOF, {}
                ).get(KEY, "")
                ownership_proof_key = ownership_proof_key.format(
                    property_id=property_obj.id,
                    filename=f"{request.user.pk}_{user_role}_{datetime.now().strftime(DD_MMM_YYYY)}_ownership_proof",
                )
                file = serializer.validated_data.get("file")

                s3_client.upload_file(file, ownership_proof_key)

                (
                    availability_and_status,
                    created,
                ) = PropertyAvailabilityAndStatus.objects.get_or_create(
                    property=property_obj
                )
                availability_and_status.ownership_proof_key = ownership_proof_key
                availability_and_status.ownership_proof_file_name = file.name
                availability_and_status.ownership_proof_file_size = file.size
                if created:
                    availability_and_status.created_by = request.user
                    availability_and_status.created_by_role = role_obj
                else:
                    availability_and_status.updated_by = request.user
                    availability_and_status.updated_by_role = role_obj
                availability_and_status.save()
                message = "Ownership proof uploaded successfully"
            return Response(
                status=status.HTTP_200_OK,
                data={KEY_MESSAGE: message, KEY_PAYLOAD: {}, KEY_ERROR: {}},
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in CommunityBuildingViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def delete(self, request):
        try:
            serializer = PropertyFileDeleteSerializer(data=request.data)
            if not serializer.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            s3_client = S3Client()
            # * Upload prepared contract PDF to S3
            if serializer.validated_data.get("is_rent_contract"):
                availability_and_status = get_property_availability_status_object(
                    serializer.validated_data.get("property_id")
                )
                if availability_and_status.rent_contract_key:
                    s3_client.delete_file(availability_and_status.rent_contract_key)
                    availability_and_status.rent_contract_key = None
                    availability_and_status.rent_contract_file_name = None
                    availability_and_status.rent_contract_file_size = None
                    availability_and_status.save()
                    message = "Rent contract deleted successfully"
                else:
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "Rent contract document does not exists"
                            },
                        }
                    )
            else:
                availability_and_status = get_property_availability_status_object(
                    serializer.validated_data.get("property_id")
                )
                if availability_and_status.ownership_proof_key:
                    s3_client.delete_file(availability_and_status.ownership_proof_key)
                    availability_and_status.ownership_proof_key = None
                    availability_and_status.ownership_proof_file_name = None
                    availability_and_status.ownership_proof_file_size = None
                    availability_and_status.save()
                    message = "Ownership proof deleted successfully"
                else:
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "Ownership proof document does not exists"
                            },
                        }
                    )
            return Response(
                status=status.HTTP_200_OK,
                data={KEY_MESSAGE: message, KEY_PAYLOAD: {}, KEY_ERROR: {}},
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in PropertyDocumentsViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class OccupancyStatusViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(
        self,
        request,
    ):
        try:
            # Serialize the choices
            serializer = ChoiceFieldSerializer(
                [
                    {"value": value, "display_name": display}
                    for value, display in PropertyAvailabilityStatus.choices
                ],
                many=True,
            )

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Occupancy status fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in OccupancyStatusViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetSavedLocationDetails(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def get(self, request, property_id):
        property_obj = get_property_object(property_id=property_id)

        property_completion_state = get_property_completion_state_object(
            property_id, state=PropertyCompletionStateChoices.LOCATION_DETAILS
        )
        logger.info(f"Property Completion status {property_completion_state}")
        if not property_completion_state:
            return Response(
                {"detail": "PropertyCompletionState not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

        if property_completion_state.data_source == DataSource.PROPERTY_MONITOR:
            serializer = PropertyMonitorResponseSerializer(property_obj)
        elif property_completion_state.data_source == DataSource.USER_ADDED:
            serializer = serializer = PropertyLocationDetailsSerializer(property_obj)
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data source found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Invalid data source found {property_completion_state.data_source}"
                    },
                }
            )

        data = serializer.data
        data["data_source"] = property_completion_state.data_source

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Location details fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )


class AddToPortfolioViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def post(self, request, property_id):
        try:
            user_role, role_obj = fetch_role_obj_and_name(request)
            property_completion_state = get_property_completion_state_object(
                property_id
            )
            if (
                property_completion_state.state
                != PropertyCompletionStateChoices.AVAILABILITY_AND_STATUS
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Cannot add to portfolio due to incomplete property details"
                        },
                    }
                )
            property_obj = get_property_object(property_id)
            if not property_obj.property_publish_status == PropertyPublishStatus.DRAFT:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Only property draft can be added to portfolio"
                        },
                    }
                )
            property_obj.property_publish_status = (
                PropertyPublishStatus.ADDED_TO_PORTFOLIO
            )
            property_obj.updated_by = request.user
            property_obj.updated_by_role = role_obj
            property_obj.save()

            if user_role == AGENT:
                agent_profile = get_agent_profile_object(request.user)
                recalculate_unlocked_properties(agent_profile)

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property has been added to your portfolio",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in AddToPortfolioViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class PropertyCompletionStateViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    def get(self, request, property_id):
        try:
            property_completion_state = get_property_completion_state_object(
                property_id
            )
            if (
                property_completion_state.property.property_publish_status
                != PropertyPublishStatus.DRAFT
            ):
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "State can be fetched for draft properties only"
                        },
                    }
                )
            serializer = PropertyCompletionStateSerializer(property_completion_state)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property completion state fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"Error in PropertyCompletionStateViewSet - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class ManualSavePropertySpecifications(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)
        is_draft = request.data.get("is_draft", None)

        if is_draft is None:
            request.data["is_draft"] = False
            is_draft = False

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj}",
        )

        bathroom_details = request.data.get("bathroom_details", {})
        parking_details = request.data.get("parking_details", {})
        bedroom_details = request.data.get("bedroom_details", {})
        property_category = property_obj.property_category

        if is_draft:
            serializer = DraftManualPropertySpecificationSerializer(
                property_obj, data=request.data
            )
        else:
            if property_category == PropertyCategory.RESIDENTIAL:
                serializer = ManualPropertySpecificationSerializer(
                    property_obj,
                    data=request.data,
                    context={"user_level_property_obj": user_level_property_obj},
                )
            else:
                serializer = ManualCommercialPropertySpecificationSerializer(
                    property_obj,
                    data=request.data,
                    context={"user_level_property_obj": user_level_property_obj},
                )

        validated_data = validate_serializer(serializer)

        if is_draft:
            # TODO: Need to update the exclude part after all the changes - remove COMMUNITY_AND_BUILDING_INFO
            # as the state is removed
            (
                PropertyCompletionState.objects.filter(property=property_obj).exclude(
                    state__in=[
                        PropertyCompletionStateChoices.LOCATION_DETAILS,
                        PropertyCompletionStateChoices.COMMUNITY_AND_BUILDING_INFO,
                    ]
                )
            ).delete()

            validated_data.pop("is_draft")
            validated_data["parking_details"] = parking_details
            validated_data["bathroom_details"] = bathroom_details
            validated_data["address"] = serializer.get_address(property_obj)
            validated_data["manual_added_details"] = (
                serializer.get_manual_added_details(property_obj)
            )
            validated_data["bedroom_details"] = bedroom_details

            # Update PropertyCompletionState
            save_property_completion_state(
                property_obj,
                DataSource.USER_ADDED,
                PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
                request.user,
                role_obj,
                False,
                validated_data,
            )
            message = "Property specifications are saved to draft"
            logger.info(message)

        else:
            if property_obj.property_category == PropertyCategory.RESIDENTIAL:
                if request.data.get("parking_available") and not parking_details:
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "Parking details are missing"
                            },
                        }
                    )

                if bedroom_details:
                    verify_bedroom_details(bedroom_details)

                # Update bathroom details (use parsed dictionary)
                property_obj.number_of_common_bathrooms = bathroom_details.get(
                    "common", property_obj.number_of_common_bathrooms
                )
                user_level_property_obj.number_of_common_bathrooms = (
                    bathroom_details.get(
                        "common", user_level_property_obj.number_of_common_bathrooms
                    )
                )

                property_obj.number_of_attached_bathrooms = bathroom_details.get(
                    "attached", property_obj.number_of_attached_bathrooms
                )
                user_level_property_obj.number_of_attached_bathrooms = (
                    bathroom_details.get(
                        "attached", user_level_property_obj.number_of_attached_bathrooms
                    )
                )

                property_obj.number_of_powder_rooms = bathroom_details.get(
                    "powder_room", property_obj.number_of_powder_rooms
                )
                user_level_property_obj.number_of_powder_rooms = bathroom_details.get(
                    "powder_room", user_level_property_obj.number_of_powder_rooms
                )

                # Update parking details (use parsed dictionary)
                property_obj.number_of_covered_parking = parking_details.get(
                    "covered", property_obj.number_of_covered_parking
                )
                user_level_property_obj.number_of_covered_parking = parking_details.get(
                    "covered", user_level_property_obj.number_of_covered_parking
                )

                property_obj.number_of_open_parking = parking_details.get(
                    "open", property_obj.number_of_open_parking
                )
                user_level_property_obj.number_of_open_parking = parking_details.get(
                    "open", user_level_property_obj.number_of_open_parking
                )

                property_obj.parking_number = (
                    ",".join(
                        parking_details.get(
                            "parking_number", property_obj.get_parking_number()
                        )
                    )
                    if parking_details.get("parking_number", None)
                    else None
                )
                user_level_property_obj.parking_number = (
                    ",".join(
                        parking_details.get(
                            "parking_number",
                            user_level_property_obj.get_parking_number(),
                        )
                    )
                    if parking_details.get("parking_number", None)
                    else None
                )

                # Update bedroom details (use parsed dictionary)
                property_obj.number_of_bedrooms = bedroom_details.get(
                    "number_of_bedrooms", property_obj.number_of_bedrooms
                )
                user_level_property_obj.number_of_bedrooms = bedroom_details.get(
                    "number_of_bedrooms", user_level_property_obj.number_of_bedrooms
                )

                property_obj.number_of_master_bedrooms = bedroom_details.get(
                    "number_of_master_bedrooms", property_obj.number_of_master_bedrooms
                )
                user_level_property_obj.number_of_master_bedrooms = bedroom_details.get(
                    "number_of_master_bedrooms",
                    user_level_property_obj.number_of_master_bedrooms,
                )

                property_obj.number_of_other_bedrooms = bedroom_details.get(
                    "number_of_other_bedrooms", property_obj.number_of_other_bedrooms
                )
                user_level_property_obj.number_of_other_bedrooms = bedroom_details.get(
                    "number_of_other_bedrooms",
                    user_level_property_obj.number_of_other_bedrooms,
                )

                property_obj.number_of_maid_rooms = bedroom_details.get(
                    "number_of_maid_rooms", property_obj.number_of_maid_rooms
                )
                user_level_property_obj.number_of_maid_rooms = bedroom_details.get(
                    "number_of_maid_rooms", user_level_property_obj.number_of_maid_rooms
                )

                property_obj.number_of_study_rooms = bedroom_details.get(
                    "number_of_study_rooms", property_obj.number_of_study_rooms
                )
                user_level_property_obj.number_of_study_rooms = bedroom_details.get(
                    "number_of_study_rooms",
                    user_level_property_obj.number_of_study_rooms,
                )

            updated_property, changes = serializer.save()

            property_obj.updated_by = request.user
            property_obj.updated_by_role = role_obj

            image_index = property_id % 5
            if property_obj.property_type == PropertyType.APARTMENT:
                default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                    image_index
                ]
                # property_obj.default_image = default_image
            elif property_obj.property_type == PropertyType.VILLA:
                default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.VILLA)[
                    image_index
                ]
                # property_obj.default_image = default_image
            elif property_obj.property_type == PropertyType.TOWNHOUSE:
                default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.TOWNHOUSE)[
                    image_index
                ]
                # property_obj.default_image = default_image
            # TODO: Need to add other images of new commercial property type
            else:
                default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                    image_index
                ]

            property_obj.default_image = default_image
            # user_level_property_obj.default_image = default_image
            property_obj.save()
            user_level_property_obj.save()
            message = "Property specifications are added successfully"

            logger.info(
                f"Property {updated_property.id} updated with changes: {changes}"
            )

            # Update PropertyCompletionState
            save_property_completion_state(
                property_obj,
                DataSource.USER_ADDED,
                PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
                request.user,
                role_obj,
                True,
                None,
            )
            logger.info("Data saved in PropertyCompletionState")

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )


class GetSavedPropertySpecifications(APIView):
    """
    API view to get saved property specifications
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def get(self, request, property_id):
        property_obj = get_property_object(property_id=property_id)

        property_completion_state = get_property_completion_state_object(
            property_id, state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS
        )
        logger.info(f"Property Completion status {property_completion_state}")

        property_category = property_obj.property_category

        if property_category == PropertyCategory.RESIDENTIAL:
            serializer = PropertySpecificationSerializer(property_obj)
        else:
            serializer = ManualCommercialPropertySpecificationSerializer(property_obj)

        data = serializer.data

        if property_category == PropertyCategory.RESIDENTIAL:
            data["data_source"] = property_completion_state.data_source

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property Specification info retrieved successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )


class SavePropertySpecifications(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)
        is_draft = request.data.get("is_draft", None)

        if is_draft is None:
            request.data["is_draft"] = False
            is_draft = False

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj} with user id {request.user} and "
            f"role {user_role}",
        )

        bathroom_details = request.data.get("bathroom_details", {})
        parking_details = request.data.get("parking_details", {})
        bedroom_details = request.data.get("bedroom_details", {})

        if is_draft:
            serializer = DraftPropertySpecificationSerializer(
                property_obj, data=request.data
            )
        else:
            serializer = PropertySpecificationSerializer(
                property_obj,
                data=request.data,
                context={
                    "user_level_property_obj": user_level_property_obj,
                    "request_obj": request,
                },
            )

        if serializer.is_valid():
            if is_draft:
                (
                    PropertyCompletionState.objects.filter(
                        property=property_obj
                    ).exclude(
                        state__in=[
                            PropertyCompletionStateChoices.LOCATION_DETAILS,
                            PropertyCompletionStateChoices.COMMUNITY_AND_BUILDING_INFO,
                        ]
                    )
                ).delete()

                serializer.validated_data.pop("is_draft")
                serializer.validated_data["parking_details"] = parking_details
                serializer.validated_data["bathroom_details"] = bathroom_details
                serializer.validated_data["bedroom_details"] = bedroom_details
                serializer.validated_data["address"] = serializer.get_address(
                    property_obj
                )
                serializer.validated_data["manual_added_details"] = (
                    serializer.get_manual_added_details(property_obj)
                )

                # Update PropertyCompletionState
                save_property_completion_state(
                    property_obj,
                    DataSource.PROPERTY_MONITOR,
                    PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
                    request.user,
                    role_obj,
                    False,
                    serializer.validated_data,
                )

                logger.info("Data saved in PropertyCompletionState")

                message = "Property specifications saved to draft"

            else:
                if request.data.get("parking_available") and not parking_details:
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "Parking details are missing"
                            },
                        }
                    )

                if bedroom_details:
                    verify_bedroom_details(bedroom_details)

                updated_property, changes = serializer.save()

                # Update bathroom details(use parsed dictionary)
                property_obj.number_of_common_bathrooms = bathroom_details.get(
                    "common", property_obj.number_of_common_bathrooms
                )
                user_level_property_obj.number_of_common_bathrooms = (
                    bathroom_details.get(
                        "common", user_level_property_obj.number_of_common_bathrooms
                    )
                )

                property_obj.number_of_attached_bathrooms = bathroom_details.get(
                    "attached", property_obj.number_of_attached_bathrooms
                )
                user_level_property_obj.number_of_attached_bathrooms = (
                    bathroom_details.get(
                        "attached",
                        user_level_property_obj.number_of_attached_bathrooms,
                    )
                )

                property_obj.number_of_powder_rooms = bathroom_details.get(
                    "powder_room", property_obj.number_of_powder_rooms
                )
                user_level_property_obj.number_of_powder_rooms = bathroom_details.get(
                    "powder_room",
                    user_level_property_obj.number_of_powder_rooms,
                )

                # Update parking details (use parsed dictionary)
                property_obj.number_of_covered_parking = parking_details.get(
                    "covered", property_obj.number_of_covered_parking
                )
                user_level_property_obj.number_of_covered_parking = parking_details.get(
                    "covered", user_level_property_obj.number_of_covered_parking
                )

                property_obj.number_of_open_parking = parking_details.get(
                    "open", property_obj.number_of_open_parking
                )
                user_level_property_obj.number_of_open_parking = parking_details.get(
                    "open", user_level_property_obj.number_of_open_parking
                )

                property_obj.parking_number = (
                    ",".join(
                        parking_details.get(
                            "parking_number", property_obj.get_parking_number()
                        )
                    )
                    if parking_details.get("parking_number", None)
                    else None
                )
                user_level_property_obj.parking_number = (
                    ",".join(
                        parking_details.get(
                            "parking_number",
                            user_level_property_obj.get_parking_number(),
                        )
                    )
                    if parking_details.get("parking_number", None)
                    else None
                )

                # Update bedroom details (use parsed dictionary)
                property_obj.number_of_bedrooms = bedroom_details.get(
                    "number_of_bedrooms", property_obj.number_of_bedrooms
                )
                user_level_property_obj.number_of_bedrooms = bedroom_details.get(
                    "number_of_bedrooms", user_level_property_obj.number_of_bedrooms
                )

                property_obj.number_of_master_bedrooms = bedroom_details.get(
                    "number_of_master_bedrooms",
                    property_obj.number_of_master_bedrooms,
                )
                user_level_property_obj.number_of_master_bedrooms = bedroom_details.get(
                    "number_of_master_bedrooms",
                    user_level_property_obj.number_of_master_bedrooms,
                )

                property_obj.number_of_other_bedrooms = bedroom_details.get(
                    "number_of_other_bedrooms",
                    property_obj.number_of_other_bedrooms,
                )
                user_level_property_obj.number_of_other_bedrooms = bedroom_details.get(
                    "number_of_other_bedrooms",
                    user_level_property_obj.number_of_other_bedrooms,
                )

                property_obj.number_of_maid_rooms = bedroom_details.get(
                    "number_of_maid_rooms", property_obj.number_of_maid_rooms
                )
                user_level_property_obj.number_of_maid_rooms = bedroom_details.get(
                    "number_of_maid_rooms",
                    user_level_property_obj.number_of_maid_rooms,
                )

                property_obj.number_of_study_rooms = bedroom_details.get(
                    "number_of_study_rooms", property_obj.number_of_study_rooms
                )
                user_level_property_obj.number_of_study_rooms = bedroom_details.get(
                    "number_of_study_rooms",
                    user_level_property_obj.number_of_study_rooms,
                )

                property_obj.updated_by = request.user
                property_obj.updated_by_role = role_obj
                user_level_property_obj.updated_by = request.user
                user_level_property_obj.updated_by_role = role_obj

                image_index = property_id % 5
                if property_obj.property_type == PropertyType.APARTMENT:
                    default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                        image_index
                    ]
                    property_obj.default_image = default_image
                    # user_level_proeprty_obj.default_image = default_image

                elif property_obj.property_type == PropertyType.VILLA:
                    default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.VILLA)[
                        image_index
                    ]
                    property_obj.default_image = default_image
                    # user_level_proeprty_obj.default_image = default_image
                elif property_obj.property_type == PropertyType.TOWNHOUSE:
                    default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.TOWNHOUSE)[
                        image_index
                    ]
                    property_obj.default_image = default_image
                    # user_level_proeprty_obj.default_image = default_image

                property_obj.save()
                user_level_property_obj.save()
                logger.info(
                    f"Property {updated_property.id} updated with changes: {changes}"
                )

                # Update PropertyCompletionState
                save_property_completion_state(
                    property_obj,
                    DataSource.PROPERTY_MONITOR,
                    PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
                    request.user,
                    role_obj,
                    True,
                    None,
                )
                last_sub_location = ""
                master_development = ""
                if property_obj.community:
                    master_development = property_obj.community.name
                    for loc_no in range(5, 0, -1):
                        if getattr(property_obj.community, f"sub_loc_{loc_no}", None):
                            last_sub_location = getattr(
                                property_obj.community, f"sub_loc_{loc_no}"
                            )

                property_monitor = PropertyMonitorAPI()
                volume_trend_data = property_monitor.get_sale_price_volume_trend(
                    master_development,
                    last_sub_location,
                    user_level_property_obj.property_type,
                )

                if volume_trend_data:
                    for price_data in volume_trend_data[::-1]:
                        avg_price_sqft = price_data.get("avg_price_sqft")
                        if avg_price_sqft:
                            valuation = round(
                                user_level_property_obj.total_area * avg_price_sqft
                            )
                            (
                                property_financials,
                                created,
                            ) = PropertyFinancialDetails.objects.get_or_create(
                                property=property_obj
                            )
                            (
                                user_level_property_financials,
                                created,
                            ) = UserLevelPropertyFinancialDetails.objects.get_or_create(
                                property_level_data=user_level_property_obj,
                            )
                            property_financials.valuation = valuation
                            user_level_property_financials.valuation = valuation
                            get_or_create_db_object(
                                PropertyVerifiedDataFields,
                                property=property_obj,
                                field_name="valuation",
                                value=str(valuation),
                                created_by=request.user,
                                created_by_role=role_obj,
                                field_type="int",
                            )

                            property_financials.valuation_data_source = (
                                DataSource.PROPERTY_MONITOR
                            )

                            property_financials.updated_by = request.user
                            user_level_property_financials.updated_by = request.user
                            property_financials.updated_by_role = role_obj
                            user_level_property_financials.updated_by_role = role_obj
                            property_financials.save()
                            user_level_property_financials.save()
                            break

                logger.info("Data saved in PropertyCompletionState")
                message = "Property specifications are added successfully"

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {"property_id": property_obj.id},
                    KEY_ERROR: {},
                },
            )

        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )


class PropertyArchiveViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_permission_classes(self.request)

    @general_exception_handler
    def post(self, request, property_id):
        """
        archive, un-archive a property
        """
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_obj = Property.objects.filter(id=property_id).first()
        if not property_obj:
            raise_invalid_data_exception(f"Property {property_id} does not exists")
        archive = request.data.get("archive")
        if property_obj.is_archived and archive:
            raise_invalid_data_exception("Property is already archived")
        elif not property_obj.is_archived and not archive:
            raise_invalid_data_exception("Property is already un-archived")

        if user_role == AGENT:
            if property_obj.owner_verified:
                raise_invalid_data_exception("Cannot archive this property")
            if not (
                property_obj.created_by == request.user
                and property_obj.created_by_role == role_obj
            ):
                raise_invalid_data_exception("Cannot archive this property")

        property_obj.is_archived = archive
        if user_role == INVESTOR:
            property_obj.owner_intent = OwnerIntentForProperty.NOT_FOR_SALE
        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj
        property_obj.save()

        if archive:
            status_message = "archived"
        else:
            status_message = "un-archived"

        if user_role == AGENT:
            agent_profile = get_agent_profile_object(request.user)
            recalculate_unlocked_properties(agent_profile)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Property {status_message} successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class ListArchivedPropertyViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    @general_exception_handler
    def get(self, request, viewed_user_id, viewed_user_role_name):
        """
        get list of archived properties
        """
        user_role, role_obj = fetch_role_obj_and_name(request)
        viewed_user_role_object = get_role_object(viewed_user_role_name)
        viewer_role = request.query_params.get("user_role")
        viewed_user = get_user_object(viewed_user_id)
        viewer_profile = get_profile_object_by_role(request.user, role_obj)
        viewed_profile = get_profile_object_by_role(
            viewed_user, viewed_user_role_object
        )
        archived_properties = Property.objects.filter(
            created_by=request.user, created_by_role=role_obj, is_archived=True
        )
        user_level_properties = UserLevelPropertyData.objects.filter(
            property__in=archived_properties,
            created_by=request.user,
            created_by_role=role_obj,
        )
        archived_properties = user_level_properties.annotate(
            preferred_currency_code=Value(
                viewer_profile.preferred_currency_code, output_field=CharField()
            ),
            manually_added_fields=Subquery(
                PropertyVerifiedDataFields.objects.filter(property=OuterRef("property"))
                .order_by()
                .values("property")
                .annotate(field_names=ArrayAgg("field_name"))
                .values("field_names")[:1]
            ),
        ).order_by("-property__created_ts")

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(archived_properties, request)
        serializer = PortfolioSerializer(
            paginated_queryset,
            many=True,
            context={
                "self_user": True,
                "viewer_role": viewer_role,
                "viewed_role": viewed_user_role_object,
                "viewed_profile": viewed_profile,
                "viewed_user": viewed_user,
            },
        )
        return paginator.get_paginated_response(serializer.data)


class AgentPropertyRequestViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @general_exception_handler
    def post(self, request, property_id):
        """
        agent requesting access to property
        """
        agent_profile = request.user.agentprofile
        property_obj = get_property_object(property_id)
        if (
            property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
            or property_obj.agent_type
            not in [PropertyAgentType.OPEN_TO_ALL, PropertyAgentType.SELECTIVE_AGENTS]
            or not property_obj.owner_verified
        ):
            raise_invalid_data_exception("Cannot request access to this property")
        if AgentAssociatedProperty.objects.filter(
            agent_profile=agent_profile,
            property=property_obj,
            is_associated=True,
            action_status=UserRequestActions.ACCEPTED,
            is_request_expired=False,
        ).exists():
            raise_invalid_data_exception("Agent is already associated to the Property")
        if AgentAssociatedProperty.objects.filter(
            agent_profile=agent_profile,
            property=property_obj,
            request_accepted=False,
            action_status=UserRequestActions.PENDING,
            is_associated=False,
            is_request_expired=False,
        ).exists():
            raise_invalid_data_exception("The request is already sent")

        agent_role = get_agent_role_object()
        AgentAssociatedProperty.objects.create(
            agent_profile=agent_profile,
            property=property_obj,
            created_by=request.user,
            created_by_role=agent_role,
            request_agent_type=property_obj.agent_type,
            request_type=RequestType.INVESTOR_REQUEST,
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Requested access successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class DeletePropertyView(APIView):
    """
    API view to handle the deletion of an archived property.
    Only the property owner (Investor) or agent who added the property can delete.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    # permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """
        Dynamically assign permission classes based on the user's role.
        """
        return build_permission_classes(self.request)

    @general_exception_handler
    def delete(self, request, property_id):
        """
        Handle the deletion of a property if conditions are met.

        :param request: DRF request object
        :param property_id: ID of the property to be deleted
        :return: Response with success or error message
        """
        # Step 1: Validate the presence of property_id
        if not property_id:
            logger.error("Property ID not provided in the request.")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property ID is required.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Property ID was not provided in the request."
                    },
                }
            )

        logger.info(
            f"Attempting to delete Property ID: {property_id} by User ID: {request.user.id}"
        )

        # Step 2: Fetch the property using the ActivePropertyManager (excludes deleted properties)
        try:
            property_instance = Property.objects.get(id=property_id)
            logger.debug(f"Property found: {property_instance}")
        except Property.DoesNotExist:
            # Check if the property exists in all_objects (includes deleted properties)
            property_instance = Property.all_objects.filter(id=property_id).first()
            if (
                not property_instance
                or DeletedProperty.objects.filter(property=property_instance).exists()
            ):
                logger.error(
                    f"Property ID {property_id} does not exist or has been deleted."
                )
                raise ResourceNotFoundException(
                    {
                        KEY_MESSAGE: "Property not found.",
                        KEY_PAYLOAD: {},
                        KEY_ERROR_MESSAGE: "The property you are trying to delete does not exist.",
                    }
                )
            logger.debug(f"Property found in all_objects: {property_instance}")

        # Step 3: Check if the property is archived
        if not property_instance.is_archived:
            logger.warning(
                f"Property ID {property_id} is not archived. Deletion denied."
            )
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid operation.",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Only archived properties can be deleted."
                    },
                }
            )

        # Step 4: Check if the property has already been deleted
        if DeletedProperty.objects.filter(property=property_instance).exists():
            logger.warning(f"Property ID {property_id} has already been deleted.")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid operation.",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Property has already been deleted."
                    },
                }
            )

        # Step 5: Determine the user's role
        user_role, role_obj = fetch_role_obj_and_name(request)

        if not user_role or not role_obj:
            logger.error("User role not provided or invalid.")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "User role is required.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "User role was not provided or is invalid."
                    },
                }
            )

        # Step 6: Verify permissions based on role
        if user_role == INVESTOR:
            # Check if the user is the owner and creator of the property
            try:
                investor_profile = get_investor_profile_object(request.user)
                # Verify ownership
                if (
                    property_instance.owner != investor_profile
                    or not property_instance.owner_verified
                ):
                    logger.warning(
                        f"User {request.user.id} is not the verified owner of Property ID {property_id}."
                    )
                    raise PermissionDenied(
                        detail="You do not have permission to delete this property."
                    )
                # Verify creation
                if property_instance.created_by != request.user:
                    logger.warning(
                        f"User {request.user.id} is not the creator of Property ID {property_id}."
                    )
                    raise PermissionDenied(
                        detail="You do not have permission to delete this property."
                    )
            except Exception as e:
                logger.error(f"Error verifying property owner: {e}")
                raise PermissionDenied(
                    detail="You do not have permission to delete this property."
                )

        elif user_role == AGENT:
            # Check if the user is the creator agent of the property
            try:
                agent_profile = get_agent_profile_object(request.user)
                agent_role = get_agent_role_object()
                if (
                    property_instance.created_by != request.user
                    or property_instance.created_by_role != agent_role
                ):
                    logger.warning(
                        f"User {request.user.id} is not the creator agent of Property ID {property_id}."
                    )
                    raise PermissionDenied(
                        detail="You do not have permission to delete this property."
                    )
            except Exception as e:
                logger.error(f"Error verifying agent creator: {e}")
                raise PermissionDenied(
                    detail="You do not have permission to delete this property."
                )
        else:
            logger.error(f"Invalid user role: {user_role}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid user role.",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "The provided user role is invalid."
                    },
                }
            )

        logger.info(
            f"User {request.user.id} authorized to delete Property ID {property_id}."
        )

        # Step 7: Proceed with deletion via service within a transaction
        try:
            with transaction.atomic():
                result = delete_property(property_id, request.user)
                logger.info(f"Deletion result for Property ID {property_id}: {result}")
        except Exception as e:
            logger.error(
                f"Error during deletion of Property ID {property_id}: {e}",
                exc_info=True,
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: "Deletion failed.",
                    KEY_PAYLOAD: {"property_id": property_id},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "An error occurred while deleting the property. Please try again later."
                    },
                }
            )

        # Step 8: Return success response
        return Response(
            {
                KEY_MESSAGE: result.get("message", "Property deleted successfully."),
                KEY_PAYLOAD: {"property_id": result.get("property_id", property_id)},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class CountryListView(generics.ListAPIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = CountrySerializer

    def get_queryset(self):
        return Country.objects.filter(
            name__in=["United Arab Emirates", "India"], is_deleted=False
        ).order_by("id")

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        """
        Override list method to return countries
        """

        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(
            {
                KEY_MESSAGE: "Country list fetched successfully.",
                KEY_PAYLOAD: {"country_list": serializer.data},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class StateListView(generics.ListAPIView):
    """
    API View to list states based on country ID.
    Special handling for Indian states using pycountry with fallback to hardcoded list.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = StateSerializer

    def get_queryset(self):
        """
        Get queryset of states based on country ID.
        Special handling for Indian states using pycountry with fallback to hardcoded list.
        """
        country_id = self.kwargs.get("country_id")
        if not country_id:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid country ID",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "The provided country ID is invalid."
                    },
                }
            )
        cache_key = f"states_country_{country_id}"

        # Try to get from cache first
        cached_states = cache.get(cache_key)
        if cached_states is not None:
            logger.info(f"Retrieved states from cache for country_id: {country_id}")
            return cached_states

        try:
            country = Country.objects.get(id=country_id)
            logger.info(f"Fetching states for country: {country.name}")

            states = State.objects.filter(
                country_id=country_id, is_active=True, is_deleted=False
            ).order_by("name")

            # Cache the results for 1 hour
            cache.set(cache_key, states, timeout=3600)
            return states

        except Country.DoesNotExist:
            logger.warning(f"Country with ID {country_id} not found")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Country not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Country with ID {country_id} does not exist"
                    },
                }
            )
        except Exception as e:
            logger.error(
                f"Error fetching states for country {country_id}: {str(e)}",
                exc_info=True,
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: "Error fetching states",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "An error occurred while fetching the states"
                    },
                }
            )

    def _get_indian_states(self, country_id):
        """
        Helper method to get Indian states using countryinfo with fallback to hardcoded list.
        """
        try:
            # Try to get states from countryinfo first
            country_info = CountryInfo("India")
            indian_states = country_info.provinces()
            logger.info("Successfully retrieved Indian states from countryinfo")
        except Exception as e:
            logger.warning(
                f"Could not get states from countryinfo: {str(e)}. Using fallback list."
            )
            indian_states = INDIAN_STATES

        # Get or create states in database using bulk_create for better performance
        states_to_create = []
        existing_states = State.objects.filter(
            country_id=country_id, name__in=indian_states
        ).values_list("name", flat=True)

        # Filter out states that already exist
        new_states = [state for state in indian_states if state not in existing_states]

        # Create new states in bulk
        if new_states:
            states_to_create = [
                State(
                    country_id=country_id,
                    name=state_name,
                    is_active=True,
                    is_deleted=False,
                )
                for state_name in new_states
            ]
            State.objects.bulk_create(states_to_create)
            logger.info(f"Created {len(states_to_create)} new states")

        # Get all states (both existing and newly created)
        states = State.objects.filter(country_id=country_id, name__in=indian_states)

        # Reactivate any inactive states
        inactive_states = states.filter(is_active=False)
        if inactive_states.exists():
            inactive_states.update(is_active=True, is_deleted=False)
            logger.info(f"Reactivated {inactive_states.count()} states")

        return states

    def list(self, request, *args, **kwargs):
        """
        Override list method to return states based on country ID with proper error handling.
        """
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            return Response(
                {
                    KEY_MESSAGE: "State list fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
                status=status.HTTP_200_OK,
            )

        except ResourceNotFoundException as e:
            raise e
        except Exception as e:
            logger.error(f"Error in list method: {str(e)}", exc_info=True)
            raise InternalServerException(
                {
                    KEY_MESSAGE: "Fetching failed",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "An error occurred while fetching the states. Please try again later."
                    },
                }
            )


class CityListView(generics.ListAPIView):
    """
    API View to list cities based on country and state.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = CitySerializer

    def get_queryset(self):
        """
        Get queryset of cities based on country and state.
        """
        country_id = self.request.query_params.get("country_id")
        state_id = self.request.query_params.get("state_id")

        if not country_id or not state_id:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Both country_id and state_id are required"
                    },
                }
            )

        # cache_key = f"cities_country_{country_id}_state_{state_id}"
        #
        # # Try to get from cache first
        # cached_cities = cache.get(cache_key)
        # if cached_cities is not None:
        #     logger.info(
        #         f"Retrieved cities from cache for country_id: {country_id}, state_id: {state_id}"
        #     )
        #     return cached_cities

        try:
            # Get country and state objects
            country = Country.objects.get(id=country_id)
            state = State.objects.get(id=state_id, country=country)

            # Get cities
            cities = City.objects.filter(
                country=country, state=state, is_active=True, is_deleted=False
            ).order_by("name")

            # Cache the results for 1 hour
            # cache.set(cache_key, cities, timeout=3600)

            return cities

        except Country.DoesNotExist:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid country",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Country not found"},
                }
            )
        except State.DoesNotExist:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid state",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "State not found for the given country"
                    },
                }
            )

    @general_exception_handler
    def list(self, request, *args, **kwargs):
        """
        Override list method to return cities with proper response format
        """
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        return Response(
            {
                KEY_MESSAGE: "City list fetched successfully",
                KEY_PAYLOAD: {"city_list": serializer.data},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class ManualSavePropertyViewSet(ModelViewSet):
    """
    Class to add property when user clicks on adding new property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    serializer_class = PropertyIDSerializer
    http_method_names = ["get", "post", "delete", "put"]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []
        try:
            # Access the request data (for POST, PUT, PATCH, etc.)
            # if self.request.method in ['GET']:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            logger.info(role)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
                if self.action in ["destroy", "update"]:
                    permission_classes.append(IsPropertyAssociateAgent)
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
                if self.action in ["destroy", "update"]:
                    permission_classes.append(IsPropertyOwner)
            else:
                raise PermissionDenied(detail="Role not found")
            # Return the appropriate permission classes
            return [permission() for permission in permission_classes]

        except Exception as e:
            logger.error(e)
            raise e

    def get_queryset(self):
        return Property.objects.all()

    def get_property_object_or_404(self, pk):
        # queryset = self.get_queryset()
        # try:
        #     request_obj = queryset.get(pk=pk)
        # except queryset.model.DoesNotExist:
        #     message = f"Property object with ID {pk} does not exist"
        #     logger.error(message)
        #     raise ResourceNotFoundException(
        #         {
        #             KEY_MESSAGE: "Invalid data sent",
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: {
        #                 KEY_ERROR_MESSAGE: f"Property object with ID {pk} does not exist"
        #             },
        #         }
        #     )
        request_obj = get_db_object(
            app_name="properties",
            model_name=Property.__name__,
            single_field_value_dict={"pk": pk},
            not_found_text=f"Property object with ID {pk} does not exist",
        )
        return request_obj

    def get_user_level_property_object_or_404(
        self, property_obj, created_by, created_by_role
    ):
        # queryset = self.get_queryset()
        request_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict={
                "property": property_obj,
                "created_by": created_by,
                "created_by_role": created_by_role,
            },
            not_found_text=f"UserLevelProperty object {property_obj} does not exist",
        )

        return request_obj

    @staticmethod
    def add_country_state_and_community_details(validated_data, *args, **kwargs):
        """
        Method to add country, state and community details
        """
        logger.info(validated_data)
        country = validated_data["country"]
        state = validated_data["state"]
        country_short_name = validated_data["country_short_name"]
        community = validated_data["community"]

        try:
            country = Country.objects.get(name=country)
            logger.info(f"Country fetched {country}")
        except ObjectDoesNotExist:
            country = Country.objects.create(
                name=country, short_name=country_short_name
            )
            logger.info(f"Country added {country}")

        try:
            state = State.objects.get(id=state, country=country)
            logger.info(f"state fetched {state}")
        except ObjectDoesNotExist as error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid state sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Incorrect state provided for country."
                    },
                }
            )

        community, c_created = get_or_create_db_object(
            Community, name=community, added_by=DataSource.USER_ADDED
        )

        logger.info(f"Community added {community},created {c_created}")

        return country, state, community

    @general_exception_handler
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)

        serializer = ManualAddLocationDetails(data=request.data)

        # if not serializer.is_valid():
        #     raise InvalidSerializerDataException(
        #         {
        #             KEY_MESSAGE: "Invalid data sent",
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: serializer.errors,
        #         }
        #     )

        validated_data = validate_serializer(serializer)
        logger.info(
            f"Validated data for create ManualSavePropertyViewSet : {validated_data}"
        )
        created_by = request.user

        country, state, community = self.add_country_state_and_community_details(
            validated_data
        )

        property_obj = create_db_object(
            Property,
            country=country,
            state=state,
            community=community,
            property_category=validated_data["property_category"],
            unit_number=validated_data["unit_number"],
            created_by_role=role_obj,
            created_by=created_by,
        )

        user_level_property_obj = create_db_object(
            UserLevelPropertyData,
            property=property_obj,
            created_by=created_by,
            created_by_role=role_obj,
        )

        if user_role == INVESTOR:
            investor_profile = get_investor_profile_object(request.user)
            property_obj.owner = investor_profile
            user_level_property_obj.user_hierarchy = PropertyHierarchy.OWNER
            property_obj.save()
            user_level_property_obj.save()
        elif user_role == AGENT:
            property_obj.owner_verified = False
            property_obj.agent_type = PropertyAgentType.SELECTIVE_AGENTS
            user_level_property_obj.user_hierarchy = PropertyHierarchy.AGENT
            property_obj.save()
            agent_profile = get_agent_profile_object(request.user)
            create_db_object(
                AgentAssociatedProperty,
                property=property_obj,
                agent_profile=agent_profile,
                is_associated=True,
                created_by_role=role_obj,
                created_by=created_by,
                action_status=UserRequestActions.ACCEPTED,
            )
            user_level_property_obj.save()
            # Recalculate property unlocking based on new subscription

        property_financials, created = get_or_create_db_object(
            PropertyFinancialDetails, property=property_obj
        )
        user_level_property_financial, created = get_or_create_db_object(
            UserLevelPropertyFinancialDetails,
            property_level_data=user_level_property_obj,
            created_by=request.user,
            created_by_role=role_obj,
        )
        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            property_financials.property_currency_code = currency_code
            user_level_property_financial.property_currency_code = currency_code
            if created:
                property_financials.created_by = created_by
                property_financials.created_by_role = role_obj
            else:
                property_financials.updated_by = request.user
                property_financials.updated_by_role = role_obj
                user_level_property_financial.updated_by = request.user
                user_level_property_financial.updated_by_role = role_obj
            property_financials.save()
            user_level_property_financial.save()

        is_completed = True
        draft_data = None
        if serializer.data.get("is_draft"):
            is_completed = False
            # draft_data = json.dumps(serializer.data)
            serializer.data.pop("is_draft")
            draft_data = serializer.data
        # Update PropertyCompletionState
        save_property_completion_state(
            property_obj,
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            is_completed,
            draft_data,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: Location Details"
        )

        property_serializer = PropertyIDSerializer(property_obj)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property created successfully",
                KEY_PAYLOAD: property_serializer.data,
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    def destroy(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )
        property_obj.delete()
        user_level_property_obj.delete()
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property discarded successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def update(self, request, *args, **kwargs):
        # try:
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )

        country, state, community = self.add_country_state_and_community_details(
            request.data
        )

        property_obj.country = country
        property_obj.state = state
        property_obj.community = community
        property_obj.unit_number = request.data["unit_number"]
        property_obj.updated_by = request.user

        (
            property_financials,
            created,
        ) = get_or_create_db_object(PropertyFinancialDetails, property=property_obj)

        (
            user_level_property_financial,
            user_level_property_financial_created,
        ) = get_or_create_db_object(
            UserLevelPropertyFinancialDetails,
            property_level_data=user_level_property_obj,
            created_by=request.user,
            created_by_role=role_obj,
        )

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            property_financials.property_currency_code = currency_code
            if created:
                property_financials.created_by = request.user
                property_financials.created_by_role = role_obj
            else:
                property_financials.updated_by = request.user
                property_financials.updated_by_role = role_obj
                user_level_property_financial.updated_by = request.user
                user_level_property_financial.updated_by_role = role_obj
            property_financials.save()
            user_level_property_financial.save()

        is_completed = True
        draft_data = None

        # Update PropertyCompletionState
        save_property_completion_state(
            property_obj,
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            is_completed,
            draft_data,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: Location Details"
        )

        property_serializer = PropertyIDSerializer(property_obj)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property updated successfully",
                KEY_PAYLOAD: property_serializer.data,
                KEY_ERROR: {},
            },
        )

        # except InvalidSerializerDataException as invalid_data:
        #     return Response(
        #         status=invalid_data.status_code, data=invalid_data.to_dict()
        #     )
        # except Exception as error:
        #     message = "Unknown error occurred"
        #     logger.error(f"Error in PropertyBasicDetailsViewSet - {message} - {error}")
        #     raise InternalServerException(
        #         {
        #             KEY_MESSAGE: message,
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: {KEY_ERROR_MESSAGE: error},
        #         }
        #     )


class CoOwnerPropertyRequestViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    @general_exception_handler
    def post(self, request, property_id):
        """
        co-owner requesting access to property
        """
        investor_profile = request.user.investorprofile
        property_obj = get_property_object(property_id)
        if (
            property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
            or not property_obj.owner_verified
            or property_obj.owner == investor_profile
        ):
            raise_invalid_data_exception("Cannot request access to this property")

        property_co_owners = PropertyCoOwner.objects.filter(
            co_owner=investor_profile, property=property_obj
        )

        if property_co_owners.filter(is_associated=True).exists():
            raise_invalid_data_exception("You are already associated to the Property")

        if property_co_owners.filter(
            action_status=UserRequestActions.PENDING,
            is_associated=False,
            is_request_expired=False,
        ).exists():
            raise_invalid_data_exception("The request is already sent")

        investor_role = get_investor_role_object()
        PropertyCoOwner.objects.create(
            co_owner=investor_profile,
            property=property_obj,
            request_type=CoOwnerRequestType.REQUEST_TO_OWNER,
            created_by=request.user,
            created_by_role=investor_role,
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Requested access successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class DirectAddPropertyToPortfolioViewSet(APIView):
    """
    API view to directly add an unverified property to agent's portfolio
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @general_exception_handler
    def post(self, request, property_id):
        """
        Directly add an unverified property to agent's portfolio

        Args:
            property_id: ID of the property to add

        Returns:
            Response with success/error message
        """
        # Get agent profile and role
        agent_profile = get_agent_profile_object(request.user)
        agent_role = get_agent_role_object()

        # Get property object
        property_obj = get_property_object(property_id)

        # Validate property can be added
        if property_obj.owner_verified:
            raise_invalid_data_exception(
                "Cannot add a verified property directly to portfolio"
            )

        # Check if agent is already associated
        if AgentAssociatedProperty.objects.filter(
            agent_profile=agent_profile,
            property=property_obj,
            is_associated=True,
            action_status=UserRequestActions.ACCEPTED,
            is_request_expired=False,
        ).exists():
            raise_invalid_data_exception("Property is already in your portfolio")

        # Create agent association
        agent_association = AgentAssociatedProperty.objects.create(
            agent_profile=agent_profile,
            property=property_obj,
            is_associated=True,
            created_by=request.user,
            created_by_role=agent_role,
            action_status=UserRequestActions.ACCEPTED,
        )
        create_user_level_property_data(
            property_obj=property_obj,
            user=agent_association.agent_profile.user,
            role=agent_role,
            user_hierarchy="Agent",
        )
        recalculate_unlocked_properties(agent_profile)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property added to portfolio successfully",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {},
            },
        )


class PropertyDetailView(APIView):
    """
    API view to get property information for property owners
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsPropertyOwner]

    @general_exception_handler
    def get(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)
        # Get property object
        base_filters = {
            "property_publish_status": PropertyPublishStatus.ADDED_TO_PORTFOLIO,
            "is_archived": False,
        }

        property_obj = Property.objects.filter(id=property_id, **base_filters).first()
        if not property_obj:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Property not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Property not found"},
                }
            )

        if (
            property_obj.property_publish_status
            != PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ) or (property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid property details"},
                }
            )
        user_level_filter = create_user_level_filter(
            property_obj, request.user, role_obj
        )
        user_level_property_data = UserLevelPropertyData.objects.filter(
            **user_level_filter
        ).first()

        # Serialize property data
        serializer = ClaimPropertyBasicDetailSerializer(user_level_property_data)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property details fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class PropertyViewSetV1(ModelViewSet):
    """
    Property ViewSet to create, update and delete property of property monitor
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    http_method_names = ["get", "post", "delete", "put"]

    def get_permissions(self):
        return build_property_creation_permission_classes(self.request, self.action)

    def get_property_object_or_404(self, pk):
        request_obj = get_db_object(
            app_name="properties",
            model_name=Property.__name__,
            single_field_value_dict={"pk": pk},
            not_found_text=f"Property object with ID {pk} does not exist",
        )
        return request_obj

    def get_user_level_property_object_or_404(
        self, property_obj, created_by, created_by_role
    ):
        request_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict={
                "property": property_obj,
                "created_by": created_by,
                "created_by_role": created_by_role,
            },
            not_found_text=f"UserLevelProperty object {property_obj} does not exist",
        )
        return request_obj

    @general_exception_handler
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        user_id = request.user

        serializer = PMAddLocationDetailsV1(data=request.data)
        validated_data = validation_utility.validate_serializer(serializer)
        logger.info(f"Validated data for create PropertyViewSetV1 : {validated_data}")

        country, state = property_helper.add_country_state_details(validated_data)
        property_category = validated_data.get("property_category")

        # Create property instance
        property_obj = create_db_object(
            Property,
            country=country,
            state=state,
            property_category=property_category,
            created_by_role=role_obj,
            created_by=user_id,
        )

        # Create user level property instance
        user_level_property_obj = create_db_object(
            UserLevelPropertyData,
            property=property_obj,
            created_by=user_id,
            created_by_role=role_obj,
        )

        if user_role == INVESTOR:
            investor_profile = get_investor_profile_object(request.user)
            property_obj.owner = investor_profile
            user_level_property_obj.user_hierarchy = PropertyHierarchy.OWNER
        elif user_role == AGENT:
            property_obj.owner_verified = False
            property_obj.agent_type = PropertyAgentType.SELECTIVE_AGENTS
            user_level_property_obj.user_hierarchy = PropertyHierarchy.AGENT
            agent_profile = get_agent_profile_object(request.user)
            create_db_object(
                AgentAssociatedProperty,
                property=property_obj,
                agent_profile=agent_profile,
                is_associated=True,
                created_by_role=role_obj,
                created_by=user_id,
                action_status=UserRequestActions.ACCEPTED,
            )

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )

        (
            user_level_property_financial,
            created,
        ) = property_utility.get_or_create_user_level_financial_obj()

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            user_level_property_financial.property_currency_code = currency_code
            user_level_property_financial.save()

        community, created = property_utility.get_or_create_community_obj(
            validated_data
        )
        property_obj.community = community
        property_obj.save()
        user_level_property_obj.save()

        property_utility.save_property_completion_state(
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            user_id,
            role_obj,
            True,
            None,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: PM Location Details"
        )

        return Response(
            {
                KEY_MESSAGE: "PM property created successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )

    @general_exception_handler
    @transaction.atomic
    def update(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)

        serializer = PMUpdateLocationDetailsV1(data=request.data)
        user_id = request.user

        validated_data = validation_utility.validate_serializer(serializer)
        logger.info(f"Validated data for update PropertyViewSet : {validated_data}")
        country, state = property_helper.add_country_state_details(validated_data)

        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, user_id, role_obj
        )

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )

        (
            user_level_property_financial,
            created,
        ) = property_utility.get_or_create_user_level_financial_obj()

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()

        if currency_data:
            currency_code = currency_data[0]
            user_level_property_financial.property_currency_code = currency_code
            user_level_property_financial.save()

        property_obj.country = country
        property_obj.state = state
        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj

        community, created = property_utility.get_or_create_community_obj(
            validated_data
        )
        property_obj.community = community

        property_obj.save()
        user_level_property_obj.save()

        # Update PropertyCompletionState for property location
        property_utility.save_property_completion_state(
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            user_id,
            role_obj,
            True,
            None,
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "PM property updated successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )


class ManualSavePropertyViewSetV1(ModelViewSet):
    """
    Property ViewSet to create, update and delete property for manual flow
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    http_method_names = ["get", "post", "delete", "put"]

    def get_permissions(self):
        return build_property_creation_permission_classes(self.request, self.action)

    def get_property_object_or_404(self, pk):
        request_obj = get_db_object(
            app_name="properties",
            model_name=Property.__name__,
            single_field_value_dict={"pk": pk},
            not_found_text=f"Property object with ID {pk} does not exist",
        )
        return request_obj

    def get_user_level_property_object_or_404(
        self, property_obj, created_by, created_by_role
    ):
        request_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict={
                "property": property_obj,
                "created_by": created_by,
                "created_by_role": created_by_role,
            },
            not_found_text=f"UserLevelProperty object {property_obj} does not exist",
        )

        return request_obj

    @general_exception_handler
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        serializer = ManualAddLocationDetailsV1(data=request.data)
        validated_data = validate_serializer(serializer)

        logger.info(
            f"Validated data for create ManualSavePropertyViewSet : {validated_data}"
        )
        user_id = request.user

        country, state = property_helper.add_country_state_details(validated_data)
        if country.name == INDIA:
            city = property_helper.add_city_details(validated_data, country, state)
        else:
            city = None
        community_name = validated_data.get("community")
        community, community_created = get_or_create_db_object(
            Community, name=community_name, added_by=DataSource.USER_ADDED
        )
        postal_code = validated_data.get("postal_code")

        logger.info(f"Manual community {community} added")
        property_category = validated_data.get("property_category")

        property_obj = create_db_object(
            Property,
            country=country,
            state=state,
            city=city,
            community=community,
            property_category=property_category,
            created_by_role=role_obj,
            created_by=user_id,
            postal_code=postal_code,
        )

        user_level_property_obj = create_db_object(
            UserLevelPropertyData,
            property=property_obj,
            created_by=user_id,
            created_by_role=role_obj,
        )

        if user_role == INVESTOR:
            investor_profile = get_investor_profile_object(request.user)
            property_obj.owner = investor_profile
            user_level_property_obj.user_hierarchy = PropertyHierarchy.OWNER
        elif user_role == AGENT:
            property_obj.owner_verified = False
            property_obj.agent_type = PropertyAgentType.SELECTIVE_AGENTS
            user_level_property_obj.user_hierarchy = PropertyHierarchy.AGENT
            agent_profile = get_agent_profile_object(request.user)
            create_db_object(
                AgentAssociatedProperty,
                property=property_obj,
                agent_profile=agent_profile,
                is_associated=True,
                created_by_role=role_obj,
                created_by=user_id,
                action_status=UserRequestActions.ACCEPTED,
            )

        property_obj.save()
        user_level_property_obj.save()

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )

        (
            user_level_property_financial,
            created,
        ) = property_utility.get_or_create_user_level_financial_obj()

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            user_level_property_financial.property_currency_code = currency_code
            user_level_property_financial.save()

        # Update PropertyCompletionState
        property_utility.save_property_completion_state(
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            True,
            None,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: Manual Property Location Details"
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Manual property created successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def update(self, request, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        serializer = ManualUpdateLocationDetailsV1(data=request.data)
        validated_data = validate_serializer(serializer)

        logger.info(
            f"Validated data for update ManualSavePropertyViewSet : {validated_data}"
        )
        user_id = request.user

        property_obj = self.get_property_object_or_404(self.kwargs.get("pk"))
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )

        country, state = property_helper.add_country_state_details(validated_data)
        if country.name == INDIA:
            city = property_helper.add_city_details(validated_data, country, state)
        else:
            city = None
        community_name = validated_data.get("community")
        community, community_created = get_or_create_db_object(
            Community, name=community_name, added_by=DataSource.USER_ADDED
        )
        postal_code = validated_data.get("postal_code")

        logger.info(f"Manual community {community.id} added")

        property_obj.country = country
        property_obj.state = state
        property_obj.community = community
        property_obj.city = city
        property_obj.postal_code = postal_code
        property_obj.updated_by = user_id
        property_obj.updated_by_role = role_obj

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )
        (
            user_level_property_financial,
            created,
        ) = property_utility.get_or_create_user_level_financial_obj()

        country_info = CountryInfo(country.name)
        currency_data = country_info.currencies()
        if currency_data:
            currency_code = currency_data[0]
            user_level_property_financial.property_currency_code = currency_code
            user_level_property_financial.save()

        property_obj.save()
        user_level_property_obj.save()

        # Update PropertyCompletionState
        property_utility.save_property_completion_state(
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.LOCATION_DETAILS,
            request.user,
            role_obj,
            True,
            False,
        )

        logger.info(
            "Data added in PropertyCompletionState, state completed: Manual Property Location Details"
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Manual property updated successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )


class PropertySaveUnitDetailsViewV1(APIView):
    """
    Property save unit details API view
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_property_creation_permission_classes(self.request, ["put"])

    def get_property_object_or_404(self, pk):
        request_obj = get_db_object(
            app_name="properties",
            model_name=Property.__name__,
            single_field_value_dict={"pk": pk},
            not_found_text=f"Property object with ID {pk} does not exist",
        )
        return request_obj

    def get_user_level_property_object_or_404(
        self, property_obj, created_by, created_by_role
    ):
        request_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict={
                "property": property_obj,
                "created_by": created_by,
                "created_by_role": created_by_role,
            },
            not_found_text=f"UserLevelProperty object {property_obj} does not exist",
        )

        return request_obj

    @general_exception_handler
    @transaction.atomic
    def put(self, request, property_id, *args, **kwargs):
        user_role, role_obj = fetch_role_obj_and_name(request)
        serializer = PropertySaveUnitDetailsV1Serializer(data=request.data)

        validated_data = validation_utility.validate_serializer(serializer)
        logger.info(
            f"Validated data for PUT PropertySaveUnitDetailsViewV1: {validated_data}"
        )
        address_id = validated_data.get("address_id")

        property_instance = (
            get_list_of_object_with_filters(
                app_name="properties",
                model_name=Property.__name__,
                single_field_value_dict={"property_monitor_address_id": address_id},
            )
            .exclude(id=property_id)
            .first()
        )
        continue_claim_flow = False
        if property_instance:
            continue_claim_flow = True
            if (
                property_instance.property_publish_status
                == PropertyPublishStatus.DRAFT.value
            ):
                property_instance.delete()
                continue_claim_flow = False

        if continue_claim_flow:
            logger.info(
                f"Property {property_instance} already exists with address id {address_id}"
            )
            property_id = PropertyIDSerializer(property_instance)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            claim_property_choices = get_claim_property_choices(
                property_instance, viewer_profile, role_obj
            )
            if claim_property_choices == ClaimPropertyChoices.EXISTS_IN_OWN_PORTFOLIO:
                user_level_filter = create_user_level_filter(
                    property_instance, request.user, role_obj
                )
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    **user_level_filter
                ).first()
            else:
                creator_level_property_data = UserLevelPropertyData.objects.filter(
                    property=property_instance,
                    created_by=property_instance.created_by,
                    created_by_role=property_instance.created_by_role,
                ).first()

            property_info = ClaimPropertyBasicDetailSerializer(
                creator_level_property_data
            ).data
            property_specifications = PropertySpecificationsSerializer(
                creator_level_property_data
            ).data

            logger.info(
                f"For property monitor address id {address_id}, "
                f"property specifications {property_specifications} and property info {property_info}"
            )

            email_template = None
            if (
                claim_property_choices == ClaimPropertyChoices.CLAIM_PROPERTY
                and user_role == INVESTOR
            ):
                template = EmailTemplate.objects.filter(name="claim_property").first()
                if template:
                    email_subject = template.subject.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }
            elif claim_property_choices == ClaimPropertyChoices.CONTACT_SUPPORT:
                template = EmailTemplate.objects.filter(
                    name="add_property_to_portfolio"
                ).first()
                if template:
                    email_subject = template.subject.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                    )
                    email_body = template.body.format(
                        role_name=role_obj.name,
                        unit_number=property_instance.unit_number,
                        building_street=property_info.get("building_street"),
                        name=viewer_profile.name,
                    )
                    email_template = {
                        "subject": email_subject,
                        "body": email_body,
                    }

            response = {
                "claim_property_choices": claim_property_choices,
                "property_info": property_info,
                "property_specifications": property_specifications,
                "email_template": email_template,
                "duplicate_found": True,
                "created_by": property_instance.created_by.id,
                "created_by_role": property_instance.created_by_role.name,
            }
            response.update(property_id.data)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Property already exists",
                    KEY_PAYLOAD: response,
                    KEY_ERROR: {},
                },
            )

        property_obj = self.get_property_object_or_404(property_id)
        user_level_property_obj = self.get_user_level_property_object_or_404(
            property_obj, request.user, role_obj
        )

        if property_obj.property_monitor_address_id != address_id:
            logger.info(
                f"Property with property monitor address id {address_id} does not exist"
            )

            property_verified_fields = get_list_of_object_with_filters(
                app_name="properties",
                model_name=PropertyVerifiedDataFields.__name__,
                single_field_value_dict={"property": property_obj},
            )
            property_verified_fields.delete()

            logger.info(f"Deleted verified fields for property {property_obj}")

            property_obj.number_of_bathrooms = None
            user_level_property_obj.number_of_bathrooms = None
            property_obj.save()
            user_level_property_obj.save()

            community, unit = property_helper.save_community_and_unit_data(
                validated_data=validated_data,
                property_obj=property_obj,
                user_level_property_obj=user_level_property_obj,
                user=request.user,
                role_obj=role_obj,
            )

            logger.info(
                f"Community {community} and Unit details {unit} are stored successfully"
            )

        property_info = ClaimPropertyBasicDetailSerializer(user_level_property_obj).data
        property_specifications = PropertySpecificationsSerializer(
            user_level_property_obj
        ).data

        property_serializer = PropertyIDSerializer(property_obj)
        response = {
            "claim_property_choices": None,
            "property_info": property_info,
            "property_specifications": property_specifications,
            "email_template": None,
            "duplicate_found": False,
            "created_by": property_obj.created_by.id,
            "created_by_role": property_obj.created_by_role.name,
        }
        response.update(property_serializer.data)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property unit details are saved successfully",
                KEY_PAYLOAD: response,
                KEY_ERROR: {},
            },
        )


class PropertyTransactionsListingsView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @staticmethod
    def convert_to_int(value):
        """
        Safely convert a value to integer.

        Args:
            value: The value to convert to integer

        Returns:
            int: The converted integer value
            bool: False if conversion fails
        """
        try:
            return int(value)
        except (ValueError, TypeError):
            return False

    @staticmethod
    def get_date_range(months):
        """
        Calculate a date range from current date going back specified number of months.

        Args:
            months (int): Number of months to go back from current date

        Returns:
            tuple: (current_date, past_date) both in 'YYYY-MM-DD' format
        """
        current_date = dt.date.today()

        # Calculate past date manually (handling year rollback)
        year = current_date.year - (months // 12)
        month = current_date.month - (months % 12)

        if month <= 0:  # Handle negative month values
            month += 12
            year -= 1

        past_date = current_date.replace(year=year, month=month)

        return current_date.strftime("%Y-%m-%d"), past_date.strftime("%Y-%m-%d")

    @general_exception_handler
    def get(self, request):
        """
        Handle GET requests for property transaction listings.

        Processes query parameters and fetches property transactions data based on
        specified filters. Supports filtering by location, property characteristics,
        price range, and more.

        Query Parameters:
            emirate (str): Name of the emirate (default: "Dubai")
            master_development (str): Name of the master development
            sub_loc_1 (str): Sub-location level 1
            sub_loc_2 (str): Sub-location level 2
            sub_loc_3 (str): Sub-location level 3
            sub_loc_4 (str): Sub-location level 4
            category (str): Transaction category ("sale" or "rent", default: "sale")
            duration (int): Time duration for transaction history
            property_types (str): Comma-separated list of property types
            min_area (int): Minimum area in sq ft
            max_area (int): Maximum area in sq ft
            number_of_bedrooms (int): Number of bedrooms
            min_price (int): Minimum price
            max_price (int): Maximum price
            contract_type (str): Type of contract (for rentals: "new" or "renewal")
            page (int): Page number for pagination (default: 1)
            page_size (int): Number of items per page (default: 15)

        Returns:
            Response: JSON response containing:
                - message: Success/failure message
                - payload: Serialized transaction data
                - error: Error details if any

        Raises:
            InvalidDataException: If validation fails for any parameters
        """
        logger.info("Received property transaction listings request")

        # removed checking for dubai property --->

        # user_role, role_obj = fetch_role_obj_and_name(request)
        # has_dubai_property = user_has_dubai_property(request.user, role_obj)
        # if not has_dubai_property:
        #     raise InvalidTokenException(
        #         {
        #             KEY_MESSAGE: "Invalid data sent",
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: {
        #                 KEY_ERROR_MESSAGE: "User does not have any properties in Dubai"
        #             },
        #         }
        #     )

        start_date = request.query_params.get("start_date", "")
        end_date = request.query_params.get("end_date", "")
        duration = request.query_params.get("duration", None)

        if start_date or end_date:
            try:
                if not start_date or not end_date:
                    logger.error(
                        "Both start_date and end_date are required if either is provided"
                    )
                    return raise_invalid_data_exception(
                        "Both start_date and end_date are required"
                    )

                start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

                if start_date > end_date:
                    logger.error("Start date cannot be greater than end date")
                    return raise_invalid_data_exception(
                        "Start date cannot be greater than end date"
                    )

            except ValueError:
                logger.error("Invalid date format provided")
                return raise_invalid_data_exception(
                    "Invalid date format. Use YYYY-MM-DD"
                )
        elif duration:
            duration = self.convert_to_int(duration)
            if duration is False:
                logger.error("Invalid duration value provided")
                return raise_invalid_data_exception("Invalid value for date duration")
            end_date, start_date = self.get_date_range(duration)
            logger.debug(
                f"Date range calculated - start_date: {start_date}, end_date: {end_date}"
            )

        if not start_date and end_date and duration:
            end_date, start_date = self.get_date_range(12)

        property_id = request.query_params.get("property_id")
        master_development = request.query_params.get("master_development", "")
        sub_loc_1 = request.query_params.get("sub_loc_1", "")
        sub_loc_2 = request.query_params.get("sub_loc_2", "")
        sub_loc_3 = request.query_params.get("sub_loc_3", "")
        sub_loc_4 = request.query_params.get("sub_loc_4", "")
        if property_id:
            logger.info(f"Fetching property details for property_id: {property_id}")
            property_obj = get_property_object(property_id)
            master_development = property_obj.community.name
            sub_loc_1 = (
                property_obj.community.sub_loc_1
                if property_obj.community.sub_loc_1
                else ""
            )
            sub_loc_2 = (
                property_obj.community.sub_loc_2
                if property_obj.community.sub_loc_2
                else ""
            )
            sub_loc_3 = (
                property_obj.community.sub_loc_3
                if property_obj.community.sub_loc_3
                else ""
            )
            sub_loc_4 = (
                property_obj.community.sub_loc_4
                if property_obj.community.sub_loc_4
                else ""
            )

        # if not master_development:
        #     master_development = settings.TRENDS_AND_TRANSACTIONS_DEFAULT_LOCATION
        #     logger.info(f"Using default master_development: {settings.TRENDS_AND_TRANSACTIONS_DEFAULT_LOCATION}")

        unit_type = request.query_params.get("unit_type", "sqft")
        # Validate area constraints
        min_area = request.query_params.get("min_area")
        max_area = request.query_params.get("max_area")
        if max_area:
            min_area = self.convert_to_int(min_area)
        if max_area:
            max_area = self.convert_to_int(max_area)
        if min_area and max_area:
            if min_area is False or max_area is False:
                return raise_invalid_data_exception(
                    "invalid value for minimum area or maximum area"
                )
            if min_area < 0 or max_area < 0:
                return raise_invalid_data_exception("area values cannot be negative")
            if min_area > max_area:
                return raise_invalid_data_exception(
                    "minimum area cannot be greater than maximum area"
                )
            # if unit_type == "sqm":
            #     # Convert sqft to sqm if unit_type is sqm
            #     min_area = round(min_area * 0.092903)
            #     max_area = round(max_area * 0.092903)

        # Validate number of bedrooms
        number_of_bedrooms = request.query_params.get("number_of_bedrooms", "")
        if number_of_bedrooms:
            number_of_bedrooms = self.convert_to_int(number_of_bedrooms)
            if number_of_bedrooms < 0:
                logger.error(f"Invalid number of bedrooms: {number_of_bedrooms}")
                return raise_invalid_data_exception(
                    "number of bedrooms cannot be negative"
                )
            if number_of_bedrooms == 0:
                number_of_bedrooms = "s"
            else:
                number_of_bedrooms = str(number_of_bedrooms)

        query_params = QUERY_PARAMS(
            emirate=request.query_params.get("emirate", "Dubai"),
            master_development=master_development,
            sub_loc_1=sub_loc_1,
            sub_loc_2=sub_loc_2,
            sub_loc_3=sub_loc_3,
            sub_loc_4=sub_loc_4,
            category=request.query_params.get("category", "sale"),
            property_types=request.query_params.get("property_types", "").split(","),
            min_area=min_area,
            max_area=max_area,
            number_of_bedrooms=number_of_bedrooms,
            min_price=request.query_params.get("min_price"),
            max_price=request.query_params.get("max_price"),
            contract_type=request.query_params.get("contract_type"),
            start_date=start_date,
            end_date=end_date,
            page=int(request.query_params.get("page", 1)),
            page_size=int(request.query_params.get("page_size", 15)),
        )

        logger.debug(f"Query parameters: {query_params}")

        if query_params.category not in ["sale", "rent"]:
            logger.error(f"Invalid category provided: {query_params.category}")
            return raise_invalid_data_exception("invalid category")

        if query_params.category == "sale" and query_params.contract_type:
            return raise_invalid_data_exception("contract_type is an invalid filter")

        if (
            query_params.contract_type
            and query_params.category == "rent"
            and query_params.contract_type not in ["new", "renewal"]
        ):
            logger.error("Contract type provided for sale category")
            return raise_invalid_data_exception("contract_type is an invalid filter")

        if (
            query_params.contract_type
            and query_params.category == "rent"
            and query_params.contract_type not in ["new", "renewal"]
        ):
            logger.error(
                f"Invalid contract type provided: {query_params.contract_type}"
            )
            return raise_invalid_data_exception("invalid contract_type")

        # Validate price constraints
        min_price = query_params.min_price
        max_price = query_params.max_price
        if query_params.min_price:
            min_price = self.convert_to_int(min_price)

        if query_params.max_price:
            max_price = self.convert_to_int(max_price)

        if query_params.category == "rent" and (min_price or max_price):
            logger.error("Price filters provided for rent category")
            return raise_invalid_data_exception("invalid filter min_price or max_price")
        if query_params.category == "rent" and (min_price or max_price):
            return raise_invalid_data_exception("invalid filter min_price or max_price")

        if min_price and max_price:
            try:
                if min_price < 0 or max_price < 0:
                    logger.error("Negative price values provided")
                    return raise_invalid_data_exception(
                        "price values cannot be negative"
                    )
                if min_price > max_price:
                    logger.error(
                        f"Invalid price range: min_price ({min_price}) > max_price ({max_price})"
                    )
                    return raise_invalid_data_exception(
                        "minimum price cannot be greater than maximum price"
                    )
            except ValueError:
                logger.error("Invalid price values provided")
                return raise_invalid_data_exception("invalid price values provided")

        # Initialize PropertyMonitorAPI
        logger.info("Initializing PropertyMonitorAPI")
        property_monitor = PropertyMonitorAPI()

        # Get property listings
        logger.info("Fetching property listings from PropertyMonitorAPI")
        response = property_monitor.get_property_listings(query_params)

        # Serialize the data
        logger.info("Serializing response data")
        serializer = PaginatedResponseSerializer(response)

        logger.info("Successfully processed property transaction listings request")
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Transactions fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class SavePropertySpecificationsV1(APIView):
    """
    API View to save residential property specifications
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        if self.request.method == "POST":
            return build_property_creation_permission_classes(self.request, ["post"])
        elif self.request.method in ["PUT", "GET"]:
            return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj} with user id {request.user} and "
            f"role {user_role}",
        )

        serializer = PropertySpecificationSerializerV1(
            user_level_property_obj,
            data=request.data,
            context={
                "property_obj": property_obj,
            },
        )

        validated_data = validation_utility.validate_serializer(serializer)
        logger.info(
            f"Validated data in POST SavePropertySpecificationsV1 is: {validated_data}"
        )

        updated_property, changes = serializer.save()

        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj

        image_index = property_id % 5
        if user_level_property_obj.property_type == PropertyType.APARTMENT:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                image_index
            ]
            property_obj.default_image = default_image
        elif user_level_property_obj.property_type == PropertyType.VILLA:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.VILLA)[image_index]
            property_obj.default_image = default_image
        elif user_level_property_obj.property_type == PropertyType.TOWNHOUSE:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.TOWNHOUSE)[
                image_index
            ]
            property_obj.default_image = default_image

        property_obj.save()
        user_level_property_obj.save()
        logger.info(f"Property {updated_property.id} updated with changes: {changes}")

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )

        # Update PropertyCompletionState
        property_utility.save_property_completion_state(
            DataSource.PROPERTY_MONITOR,
            PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            request.user,
            role_obj,
            True,
            None,
        )

        last_sub_location = ""
        master_development = ""
        if property_obj.community:
            master_development = property_obj.community.name
            for loc_no in range(5, 0, -1):
                if getattr(property_obj.community, f"sub_loc_{loc_no}", None):
                    last_sub_location = getattr(
                        property_obj.community, f"sub_loc_{loc_no}"
                    )

        property_monitor = PropertyMonitorAPI()
        volume_trend_data = property_monitor.get_sale_price_volume_trend(
            master_development,
            last_sub_location,
            user_level_property_obj.property_type,
        )

        if volume_trend_data:
            for price_data in volume_trend_data[::-1]:
                avg_price_sqft = price_data.get("avg_price_sqft")
                if avg_price_sqft:
                    valuation = round(
                        user_level_property_obj.total_area * avg_price_sqft
                    )
                    (
                        property_financials,
                        created,
                    ) = PropertyFinancialDetails.objects.get_or_create(
                        property=property_obj
                    )
                    (
                        user_level_property_financials,
                        created,
                    ) = UserLevelPropertyFinancialDetails.objects.get_or_create(
                        property_level_data=user_level_property_obj,
                    )
                    property_financials.valuation = valuation
                    user_level_property_financials.valuation = valuation
                    get_or_create_db_object(
                        PropertyVerifiedDataFields,
                        property=property_obj,
                        field_name="valuation",
                        value=str(valuation),
                        created_by=request.user,
                        created_by_role=role_obj,
                        field_type="int",
                    )

                    property_financials.valuation_data_source = (
                        DataSource.PROPERTY_MONITOR
                    )

                    property_financials.updated_by = request.user
                    property_financials.updated_by_role = role_obj
                    property_financials.save()
                    user_level_property_financials.save()
                    break

        logger.info(
            "Data saved in PropertyCompletionState, state completed: Property Specification"
        )
        message = "Property specifications are added successfully"

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def put(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj} with user id {request.user} and "
            f"role {user_role}",
        )

        serializer = EditPropertySpecificationSerializerV1(
            user_level_property_obj,
            data=request.data,
        )

        validated_data = validation_utility.validate_serializer(serializer)
        logger.info(
            f"Validated data in PUT SavePropertySpecificationsV1 is: {validated_data}"
        )

        updated_property, changes = serializer.save()

        user_level_property_obj.save()
        logger.info(f"Property {updated_property.id} updated with changes: {changes}")

        message = "Property specifications are updated successfully"

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def get(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj} with user id {request.user} and "
            f"role {user_role}",
        )
        serializer = PropertySpecificationsSerializer(user_level_property_obj)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Property specifications fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class ManualSavePropertySpecificationsV1(APIView):
    """
    API View to save manual property specifications
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        if self.request.method == "POST":
            return build_property_creation_permission_classes(self.request, ["post"])
        elif self.request.method in ["PUT", "GET"]:
            return build_permission_classes(self.request)

    @general_exception_handler
    @transaction.atomic
    def post(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj}",
        )

        property_category = property_obj.property_category

        if property_category == PropertyCategory.RESIDENTIAL:
            serializer = ManualResidentialPropertySpecificationSerializerV1(
                user_level_property_obj,
                data=request.data,
                context={
                    "property_obj": property_obj,
                },
            )
        else:
            serializer = ManualCommercialPropertySpecificationSerializerV1(
                user_level_property_obj,
                data=request.data,
                context={
                    "property_obj": property_obj,
                },
            )

        validated_data = validate_serializer(serializer)

        logger.info(
            f"Validated data for POST ManualSavePropertySpecificationsV1: {validated_data}"
        )

        updated_property, changes = serializer.save()

        logger.info(f"Property updated with changes: {changes}")

        property_obj.unit_number = validated_data.get("unit_number")
        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj

        # If earlier property is added from PM but now change to manual flow then removing PM data
        if property_obj.property_category == PropertyCategory.RESIDENTIAL:
            property_verified_fields = get_list_of_object_with_filters(
                app_name="properties",
                model_name=PropertyVerifiedDataFields.__name__,
                single_field_value_dict={"property": property_obj},
            )
            property_verified_fields.delete()

            logger.info(
                f"In manual save property specifications, deleted verified fields for property {property_obj}"
            )

            property_obj.property_monitor_address_id = None

            property_financial_obj = get_list_of_object_with_filters(
                app_name="properties",
                model_name=PropertyFinancialDetails.__name__,
                single_field_value_dict={"property": property_obj},
            )
            if property_financial_obj:
                property_financial_obj = property_financial_obj.first()
                property_financial_obj.annual_rent = None
                property_financial_obj.original_price = None
                property_financial_obj.valuation = None
                property_financial_obj.original_price_data_source = (
                    DataSource.USER_ADDED
                )
                property_financial_obj.valuation_data_source = DataSource.USER_ADDED

                user_level_financial_obj = get_list_of_object_with_filters(
                    app_name="properties",
                    model_name=UserLevelPropertyFinancialDetails.__name__,
                    single_field_value_dict={
                        "property_level_data": user_level_property_obj
                    },
                ).first()
                user_level_financial_obj.annual_rent = None
                user_level_financial_obj.original_price = None
                user_level_financial_obj.valuation = None

                user_level_financial_obj.save()
                property_financial_obj.save()

            # Delete all sales and rental history
            PropertySalesUnitHistory.objects.filter(property=property_obj).delete()
            PropertyRentalUnitHistory.objects.filter(property=property_obj).delete()

        image_index = property_id % 5
        if user_level_property_obj.property_type == PropertyType.APARTMENT:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                image_index
            ]
        elif user_level_property_obj.property_type == PropertyType.VILLA:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.VILLA)[image_index]
        elif user_level_property_obj.property_type == PropertyType.TOWNHOUSE:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.TOWNHOUSE)[
                image_index
            ]
        # TODO: Need to add other images of new commercial property type
        else:
            default_image = DEFAULT_PROPERTY_IMAGES.get(PropertyType.APARTMENT)[
                image_index
            ]

        property_obj.default_image = default_image
        property_obj.save()
        user_level_property_obj.save()

        logger.info(f"Property {updated_property.id} updated with changes: {changes}")

        property_utility = PropertyUtility(
            property_obj=property_obj, user_level_property_obj=user_level_property_obj
        )

        # Update PropertyCompletionState
        property_utility.save_property_completion_state(
            DataSource.USER_ADDED,
            PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            request.user,
            role_obj,
            True,
            None,
        )
        logger.info(
            "Data saved in PropertyCompletionState, state completed: Manual Property Specification"
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Manual property specifications are added successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    @transaction.atomic
    def put(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)
        user_level_filters = create_user_level_filter(
            property_obj, request.user, role_obj
        )

        user_level_property_obj = get_db_object(
            app_name="properties",
            model_name=UserLevelPropertyData.__name__,
            single_field_value_dict=user_level_filters,
            not_found_text=f"UserLevelPropertyData not found for {property_obj}",
        )

        property_category = property_obj.property_category

        if property_category == PropertyCategory.RESIDENTIAL:
            serializer = EditPropertySpecificationSerializerV1(
                user_level_property_obj,
                data=request.data,
            )
        else:
            serializer = EditManualCommercialPropertySpecificationSerializerV1(
                user_level_property_obj,
                data=request.data,
            )

        validated_data = validate_serializer(serializer)

        logger.info(
            f"Validated data for PUT ManualSavePropertySpecificationsV1: {validated_data}"
        )

        updated_property, changes = serializer.save()

        user_level_property_obj.save()

        logger.info(f"Property {updated_property.id} updated with changes: {changes}")

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Manual property specifications are updated successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )


class GenerateSimilarTransactionsNotifications(APIView):
    """
    API View to generate similar transactions notifications
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def post(self, request):
        property_ids = request.data.get("property_ids")
        if not property_ids:
            return raise_invalid_data_exception("property_ids is required")
        similar_transactions_notify_service = SimilarTransactionNotifier()
        similar_transactions_notify_service.trigger_notification(property_ids)
        # similar_transactions_notify_service.execute()
        # BRNUpdateService.execute()
        return Response(
            status=status.HTTP_200_OK,
            data={"message": "Notification triggered successfully"},
        )


class BRNUpdateTask(APIView):
    """
    API View to generate similar transactions notifications
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def post(self, request):
        from rezio.user.tasks import update_brn_details_from_dld

        update_brn_details_from_dld.delay()
        return Response(
            status=status.HTTP_200_OK,
            data={"message": "BRN update task triggered successfully"},
        )


class PropertyUpdateUnitNumberV1(APIView):
    """
    API View to update unit number of the property
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        return build_permission_classes(self.request)

    @general_exception_handler
    def put(self, request, property_id):
        user_role, role_obj = fetch_role_obj_and_name(request)

        property_obj = get_property_object(property_id)

        serializer = UpdateUnitNumberSerializerV1(
            instance=property_obj,
            data=request.data,
            context={
                "user": request.user,
                "role_obj": role_obj,
            },
        )
        validated_data = validation_utility.validate_serializer(serializer)

        logger.info(
            f"Validated data in PUT PropertyUpdateUnitNumberV1: {validated_data}"
        )

        property_obj.unit_number = validated_data.get("unit_number")
        property_obj.updated_by = request.user
        property_obj.updated_by_role = role_obj
        property_obj.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Unit number is updated successfully",
                KEY_PAYLOAD: {"property_id": property_obj.id},
                KEY_ERROR: {},
            },
        )
