from django.contrib import admin
from rezio.properties.models import *

# Register your models here.

admin.site.register(Country)
admin.site.register(State)
admin.site.register(City)
admin.site.register(Community)
admin.site.register(Property)
admin.site.register(PropertyAvailabilityAndStatus)
admin.site.register(PropertyCompletionState)
admin.site.register(AgentAssociatedProperty)
admin.site.register(PropertyRentalUnitHistory)
admin.site.register(PropertyFinancialDetails)
admin.site.register(PropertyFeatures)
admin.site.register(PropertySalesUnitHistory)
admin.site.register(PropertyUnitSections)
admin.site.register(UnitSectionMedia)
admin.site.register(PropertyVerifiedDataFields)
admin.site.register(UnregisteredOwner)
admin.site.register(ExchangeRates)
admin.site.register(PropertyCoOwner)
admin.site.register(UnregisteredCoOwner)
admin.site.register(HierarchyLevel)
admin.site.register(PropertyAttributes)
admin.site.register(PropertyFloorPlan)
admin.site.register(EmailTemplate)
admin.site.register(UserLevelPropertyData)
admin.site.register(UserLevelPropertyFinancialDetails)
admin.site.register(UserLevelPropertyFeatures)
admin.site.register(UserLevelPropertyAvailabilityAndStatus)
