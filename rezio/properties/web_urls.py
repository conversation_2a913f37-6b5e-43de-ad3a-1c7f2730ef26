from django.urls import path
from rezio.properties.views.web_view import property_details_view

urlpatterns = [
    path(
        "get_property_details/<int:property_id>/",
        property_details_view.WebPropertyDetailsView.as_view(),
        name="web-get-property-details",
    ),
    path(
        "get_property_financials/<int:property_id>",
        property_details_view.WebPropertyFinancialsView.as_view(),
        name="web-get-property-financials",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_details/<int:property_id>/",
        property_details_view.WebPropertyDetailsViewSet.as_view(),
        name="web-property-details",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_financial_details/<int:property_id>/",
        property_details_view.WebPropertyFinancialDetailsViewSet.as_view(),
        name="web-property-financial-details",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/property_basic_details/<int:property_id>/",
        property_details_view.WebPropertyBasicDetailsViewSet.as_view(),
        name="web-property-basic-details",
    ),
    path(
        "<str:viewed_user_id>/<str:viewed_user_role_name>/portfolio/",
        property_details_view.WebPropertyPortfolioViewSet.as_view(),
        name="web-user-portfolio",
    ),
]
