from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator
from django.db import models
from django.utils import timezone
from phonenumber_field.modelfields import <PERSON><PERSON>umberField

from rezio.properties.constants import MINIMUM_OWNERSHIP_PERCENTAGE
from rezio.properties.text_choices import (
    PropertyHierarchy,
    PropertySourceType,
    PropertyType,
    PropertyAvailabilityStatus,
    PropertyStatus,
    PropertyAreaUnit,
    PropertyCompletionStateChoices,
    PropertyPublishStatus,
    OwnerIntentForProperty,
    TenancyType,
    PropertyAgentType,
    UserRequestActions,
    RequestType,
    PropertyCategory,
    PropertyBuildingType,
    PreferredPaymentFrequency,
    PropertyAttributesCategory,
    PropertyExternalMediaType,
    CoOwnerRequestType,
    FurnishedChoices,
)
from rezio.user.models import Common, AgentProfile, Role, User
from rezio.user.models import InvestorProfile
from rezio.user.text_choices import AgentSubscriptionPlanChoices
from rezio.utils.custom_exceptions import ResourceNotFoundException
from rezio.utils.text_choices import DataSource, MediaType


class Country(models.Model):
    name = models.CharField(max_length=64, unique=True)
    short_name = models.CharField(max_length=12)
    phone_code = models.CharField(max_length=12)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class State(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    name = models.CharField(max_length=64)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class City(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=64)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class Area(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.CASCADE, null=True, blank=True)
    name = models.CharField(max_length=64)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class Community(models.Model):
    state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.CASCADE, null=True, blank=True)
    area = models.ForeignKey(Area, on_delete=models.CASCADE, null=True, blank=True)
    property_monitor_location_id = models.IntegerField(null=True, blank=True)
    name = models.CharField(max_length=128)
    sub_loc_1 = models.CharField(max_length=128, null=True, blank=True)
    sub_loc_2 = models.CharField(max_length=128, null=True, blank=True)
    sub_loc_3 = models.CharField(max_length=128, null=True, blank=True)
    sub_loc_4 = models.CharField(max_length=128, null=True, blank=True)
    sub_loc_5 = models.CharField(max_length=128, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    added_by = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.PROPERTY_MONITOR
    )

    def __str__(self):
        return self.name


# Custom QuerySet for Active Properties
class ActivePropertyManager(models.Manager):
    """Custom manager to exclude deleted properties by default and provide additional query methods."""

    def get_queryset(self):
        """
        Override the default queryset to exclude deleted properties.
        """
        return (
            super()
            .get_queryset()
            .exclude(
                id__in=DeletedProperty.objects.values_list("property_id", flat=True)
            )
        )

    def active(self):
        """
        Return the queryset of active properties.
        """
        return self.get_queryset()


class PropertyDeletionManager(models.Manager):
    def get_active_property_for_deletion(self, property_id):
        """
        Retrieve an active, archived property for deletion without causing outer joins.
        """
        try:
            return self.select_for_update().get(id=property_id, is_archived=True)
        except models.ObjectDoesNotExist:
            raise ResourceNotFoundException(
                {
                    "message": "Property not found or already deleted.",
                    "payload": {"property_id": property_id},
                    "error": {
                        "error_message": "The requested property could not be found."
                    },
                }
            )


class Property(Common):
    owner = models.ForeignKey(
        InvestorProfile, on_delete=models.SET_NULL, null=True, blank=True
    )
    unregistered_owner = models.ForeignKey(
        "properties.UnregisteredOwner", on_delete=models.SET_NULL, null=True, blank=True
    )
    owner_verified = models.BooleanField(default=True)
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.CASCADE, null=True, blank=True)
    area = models.ForeignKey(Area, on_delete=models.CASCADE, null=True, blank=True)
    community = models.ForeignKey(
        Community, on_delete=models.CASCADE, null=True, blank=True
    )
    unit_number = models.CharField(max_length=32, null=True, blank=True)
    building_number = models.CharField(max_length=32, null=True, blank=True)
    property_monitor_address_id = models.IntegerField(null=True, blank=True)
    property_type = models.CharField(
        max_length=32, choices=PropertyType.choices, null=True, blank=True
    )
    floor_number = models.CharField(max_length=32, null=True, blank=True)
    postal_code = models.IntegerField(null=True, blank=True)
    property_unit_type = models.CharField(
        max_length=32, choices=PropertyAreaUnit.choices, default=PropertyAreaUnit.SQFT
    )
    total_area = models.FloatField(null=True, blank=True)
    carpet_area = models.FloatField(null=True, blank=True)
    balcony_area = models.FloatField(null=True, blank=True)
    number_of_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_common_bathrooms = models.IntegerField(null=True, blank=True)
    number_of_attached_bathrooms = models.IntegerField(null=True, blank=True)
    number_of_powder_rooms = models.IntegerField(null=True, blank=True)
    parking_available = models.BooleanField(default=False)
    number_of_covered_parking = models.IntegerField(null=True, blank=True)
    number_of_open_parking = models.IntegerField(null=True, blank=True)
    parking_number = models.TextField(null=True, blank=True)
    property_publish_status = models.CharField(
        max_length=32,
        choices=PropertyPublishStatus.choices,
        default=PropertyPublishStatus.DRAFT,
    )
    owner_intent = models.CharField(
        max_length=32,
        choices=OwnerIntentForProperty.choices,
        default=OwnerIntentForProperty.NOT_FOR_SALE,
    )
    dewa_id = models.CharField(max_length=32, null=True, blank=True)
    user_unit_preference = models.CharField(max_length=8, default="sqft")
    tenancy_type = models.CharField(
        max_length=25, choices=TenancyType.choices, null=True, blank=True
    )
    tenancy_start_date = models.DateField(null=True, blank=True)
    tenancy_end_date = models.DateField(null=True, blank=True)
    lead_owner_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, default=100
    )
    agent_type = models.CharField(
        max_length=32,
        choices=PropertyAgentType.choices,
        default=PropertyAgentType.OPEN_TO_ALL,
    )
    number_of_master_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_other_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_maid_rooms = models.IntegerField(null=True, blank=True)
    number_of_study_rooms = models.IntegerField(null=True, blank=True)
    is_archived = models.BooleanField(default=False)
    default_image = models.CharField(max_length=512, null=True, blank=True)
    property_category = models.IntegerField(
        null=False,
        blank=False,
        default=PropertyCategory.RESIDENTIAL,
        choices=PropertyCategory.choices,
    )
    total_floors = models.IntegerField(null=True, blank=True)
    building_type = models.IntegerField(
        null=True, blank=True, choices=PropertyBuildingType.choices
    )
    number_of_bathrooms = models.IntegerField(null=True, blank=True)

    # Custom managers
    objects = ActivePropertyManager()  # Default manager excludes deleted properties
    all_objects = models.Manager()  # Manager to access all properties including deleted
    deletion_objects = (
        PropertyDeletionManager()
    )  # Manager for deletion-specific queries

    def __str__(self):
        return f"{self.id} - {self.unit_number}"

    def get_parking_number(self):
        if self.parking_number:
            try:
                return self.parking_number.split(",")
            except Exception as e:
                return [self.parking_number]
        return []

    def clear_fields(self, exclude: list = None):
        # fields to be excluded by default
        if not exclude:
            exclude = list()
        exclude.extend(
            [
                "id",
                "user",
                "role",
                "country",
                "created_ts",
                "updated_ts",
                "created_by",
                "updated_by",
                "owner",
                "created_by_role",
                "updated_by_role",
            ]
        )
        for field in self._meta.fields:
            if field.name not in exclude:  # Preserve required fields
                if field.has_default():
                    default = field.get_default()
                    if callable(default):
                        default = default()
                        setattr(self, field.name, default)
                else:
                    setattr(self, field.name, None)
        self.save()

    def get_total_bathroom_count(self):
        number_of_bathrooms = self.number_of_bathrooms or 0

        return number_of_bathrooms


class Requirements(Common):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    country = models.ForeignKey(
        Country, on_delete=models.CASCADE, null=True, blank=True
    )
    state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
    city = models.ForeignKey(City, on_delete=models.CASCADE, null=True, blank=True)
    area = models.ForeignKey(Area, on_delete=models.CASCADE, null=True, blank=True)
    community = models.ForeignKey(
        Community, on_delete=models.CASCADE, null=True, blank=True
    )
    property_type = models.CharField(
        max_length=32, choices=PropertyType.choices, null=True, blank=True
    )
    property_category = models.IntegerField(
        null=False,
        blank=False,
        default=PropertyCategory.RESIDENTIAL,
        choices=PropertyCategory.choices,
    )
    building_type = models.IntegerField(
        null=True, blank=True, choices=PropertyBuildingType.choices
    )
    floor_number = models.CharField(max_length=16, null=True, blank=True)
    total_floors = models.IntegerField(null=True, blank=True)  # Building height context
    total_area = models.FloatField(null=True, blank=True)
    carpet_area = models.FloatField(null=True, blank=True)
    number_of_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_bathrooms = models.IntegerField(null=True, blank=True)
    property_currency_code = models.CharField(
        max_length=32,
        null=True,
        blank=True,
        default=settings.DEFAULT_PROPERTY_CURRENCY_CODE,
    )
    annual_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    property_unit_type = models.CharField(max_length=8, null=True, blank=True)
    price_negotiable = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    meta = models.JSONField(default=dict, null=True, blank=True)
    intent = models.CharField(max_length=32, null=True, blank=True)
    source_ref = models.ForeignKey(
        "assistant.PropertyInquiriesMessages",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    def __str__(self):
        return f"{self.id} - {self.unit_number}"

    def get_total_bathroom_count(self):
        return self.number_of_bathrooms or 0


class PropertyAvailabilityAndStatus(Common):
    property = models.OneToOneField(Property, on_delete=models.CASCADE)
    # availability info
    status = models.CharField(
        max_length=32, choices=PropertyStatus.choices, null=True, blank=True
    )
    handover_date = models.CharField(max_length=32, null=True, blank=True)
    occupancy_status = models.CharField(
        max_length=32,
        choices=PropertyAvailabilityStatus.choices,
        null=True,
        blank=True,
        default=PropertyAvailabilityStatus.VACANT,
    )
    rent_contract_key = models.CharField(max_length=512, null=True, blank=True)
    rent_contract_file_name = models.CharField(max_length=128, null=True, blank=True)
    rent_contract_file_size = models.BigIntegerField(null=True, blank=True)
    rent_available_start_date = models.DateField(null=True, blank=True)

    # payment plan
    enable_payment_plan = models.BooleanField(default=False)
    during_construction = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True
    )
    on_handover = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True
    )
    enable_post_handover = models.BooleanField(default=False)
    post_handover_time_frame = models.IntegerField(null=True, blank=True)

    # ownership proof
    ownership_proof_key = models.CharField(max_length=512, null=True, blank=True)
    ownership_proof_file_name = models.CharField(max_length=128, null=True, blank=True)
    ownership_proof_file_size = models.BigIntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.property_id} - {self.property.unit_number}"

    def clear_fields(self, exclude):
        # fields to be excluded by default
        if not exclude:
            exclude = list()
        exclude.extend(
            [
                "id",
                "property",
                "created_ts",
                "updates_ts",
                "created_by",
                "updated_by",
                "created_by_role",
                "updated_by_role",
            ]
        )
        print(exclude)
        for field in self._meta.fields:
            if field.name not in exclude:  # Preserve required fields
                if field.has_default():
                    default = field.get_default()
                    if callable(default):
                        default = default()
                        setattr(self, field.name, default)
                else:
                    setattr(self, field.name, None)
        self.save()


class PropertyVerifiedDataFields(Common):
    """
    IN this model data will be populated only if data(any field) received from property monitor is changed by user
    """

    property = models.ForeignKey(
        Property,
        on_delete=models.CASCADE,
        related_name="property_monitor_verified_fields",
    )
    field_name = models.CharField(max_length=32)
    value = models.CharField(max_length=32, null=True)
    data_source = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.PROPERTY_MONITOR
    )
    field_type = models.CharField(max_length=10, default="str")

    def __str__(self):
        return f"{self.property_id} - {self.field_name}"


class PropertyCompletionState(Common):
    """
    This model helps to track Property state,
    is_completed will be true if user adds all the required fields and click on done
    Data will be saved in draft_json and is_completed will be false if user moves the property to draft
    """

    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="completion_states"
    )
    data_source = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.PROPERTY_MONITOR
    )
    # Field to store the current state of completion
    state = models.CharField(
        max_length=32, choices=PropertyCompletionStateChoices.choices
    )
    is_completed = models.BooleanField(default=False)
    draft_data_json = models.JSONField(null=True)

    def __str__(self):
        return f"Progress of {self.property.id}: {self.state}"


class PropertyFinancialDetails(Common):
    property = models.OneToOneField(Property, on_delete=models.CASCADE)
    property_currency_code = models.CharField(
        max_length=32,
        null=True,
        blank=True,
        default=settings.DEFAULT_PROPERTY_CURRENCY_CODE,
    )
    original_price = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    asking_price = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    valuation = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    annual_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    annual_service_charges = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    security_deposit = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    other_expenses = models.BigIntegerField(default=0)
    valuation_data_source = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.USER_ADDED
    )
    preferred_payment_frequency = models.IntegerField(
        null=True, blank=True, choices=PreferredPaymentFrequency.choices
    )
    price_negotiable = models.BooleanField(default=False)
    expected_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    expected_security_deposit = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    original_price_data_source = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.USER_ADDED
    )

    def __str__(self):
        return f"Financial details of property {self.property_id} - {self.property.unit_number}"

    def get_yearly_expected_rent(self):
        """
        Calculate yearly expected rent based on preferred payment frequency.
        Returns None if expected_rent is None.
        """
        if self.expected_rent is None:
            return None

        if self.preferred_payment_frequency == PreferredPaymentFrequency.YEARLY:
            return self.expected_rent
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.BI_YEARLY:
            return self.expected_rent * 2
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.QUARTERLY:
            return self.expected_rent * 4
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.MONTHLY:
            return self.expected_rent * 12
        else:
            return (
                self.expected_rent
            )  # Default to original value if frequency not specified


class PropertySalesUnitHistory(Common):
    """
    This model will be used to store property sales history data fetched from property model
    """

    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="sales_history"
    )
    evidence_date = models.DateField(null=True, blank=True)
    evidence = models.CharField(max_length=50, blank=True, null=True)
    total_sales_price = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    sales_price_sqft_unit = models.FloatField(blank=True, null=True)
    sales_price_sqm_unit = models.FloatField(blank=True, null=True)
    sale_recurrence = models.CharField(max_length=50, blank=True, null=True)
    sale_price_increase_percentage = models.FloatField(blank=True, null=True)
    is_upward = models.BooleanField(blank=True, null=True)

    def __str__(self):
        return f"{self.property} - {self.evidence_date} - {self.evidence}"


class PropertyRentalUnitHistory(Common):
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="rental_history"
    )
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    evidence = models.CharField(max_length=50, null=True, blank=True)
    total_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    rent_price_sqft_unit = models.FloatField(null=True, blank=True)
    rent_price_sqm_unit = models.FloatField(null=True, blank=True)
    rent_recurrence = models.CharField(max_length=50, null=True, blank=True)
    rent_increase_percentage = models.FloatField(blank=True, null=True)
    is_upward = models.BooleanField(blank=True, null=True)

    def __str__(self):
        return f"{self.property} - {self.start_date} to {self.end_date}"


class PropertyUnitSections(Common):
    property = models.ForeignKey(
        Property, related_name="sections", on_delete=models.CASCADE
    )
    section_type = models.CharField(max_length=50)
    attached_balcony = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.property} - {self.section_type}"


class UnitSectionMedia(Common):
    section = models.ForeignKey(
        PropertyUnitSections, related_name="media", on_delete=models.CASCADE
    )
    media_type = models.CharField(max_length=5, choices=MediaType.choices)
    media_file = models.CharField(max_length=512)
    order_no = models.IntegerField()
    thumbnail_file = models.CharField(max_length=512, null=True, blank=True)
    media_url = models.URLField(null=True, blank=True)
    thumbnail_url = models.URLField(null=True, blank=True)

    def __str__(self):
        return f"{self.section} - {self.media_type}"


class PropertyTag(models.Model):
    """
    Model to store property tags
    """

    name = models.CharField(max_length=50, null=False, blank=False, unique=True)


class PropertyFeatures(Common):
    """
    Model to store property features
    """

    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    branded_building = models.BooleanField(default=False)
    furnished = models.IntegerField(
        choices=FurnishedChoices.choices,
        default=FurnishedChoices.UNFURNISHED,
        null=True,
        blank=True
    )
    premium_view = models.BooleanField(default=False)
    is_restroom_available = models.BooleanField(default=False)
    no_of_shared_restrooms = models.IntegerField(null=True, blank=True)
    no_of_private_restrooms = models.IntegerField(null=True, blank=True)
    is_parking_available = models.BooleanField(default=False)
    public_parking = models.BooleanField(default=False)
    no_of_reserved_parking = models.IntegerField(null=True, blank=True)
    reserved_parking_number = models.TextField(null=True, blank=True)
    is_lift_available = models.BooleanField(default=False)
    common_lift = models.BooleanField(default=False)
    no_of_personal_lift = models.IntegerField(null=True, blank=True)
    security_available = models.BooleanField(default=False)
    water_storage_available = models.BooleanField(default=False)
    property_on_main_road = models.BooleanField(default=False)
    corner_property = models.BooleanField(default=False)
    tags = models.ManyToManyField(PropertyTag)

    def __str__(self):
        return f"Features for Property ID {self.property.id}"

    def get_tags(self):
        if self.tags.all().exists():
            return self.tags.values_list("name", flat=True)
        return []

    def get_reserved_parking_number(self):
        if self.reserved_parking_number:
            try:
                return self.reserved_parking_number.split(",")
            except Exception:
                return [self.reserved_parking_number]
        return []


class UnregisteredCoOwner(Common):
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=False)
    phone_number = PhoneNumberField(unique=False, blank=False, null=False)

    def __str__(self):
        return f"{self.name} (Unregistered) is a pending co-owner"


def validate_ownership_percentage(ownership_percentage):
    """
    Method to validate ownership percentage value
    """
    if float(ownership_percentage) < MINIMUM_OWNERSHIP_PERCENTAGE:
        raise ValidationError(
            f"Ensure this value is greater than or equal to {MINIMUM_OWNERSHIP_PERCENTAGE}"
        )


class PropertyCoOwner(Common):
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    co_owner = models.ForeignKey(
        InvestorProfile, null=True, blank=True, on_delete=models.CASCADE
    )
    unregistered_co_owner = models.ForeignKey(
        UnregisteredCoOwner, null=True, blank=True, on_delete=models.CASCADE
    )
    ownership_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[validate_ownership_percentage],
        null=True,
        blank=True,
    )
    is_associated = models.BooleanField(default=False)
    action_status = models.CharField(
        max_length=32,
        choices=UserRequestActions.choices,
        default=UserRequestActions.PENDING,
    )
    is_request_expired = models.BooleanField(default=False)
    request_type = models.CharField(
        max_length=32,
        choices=CoOwnerRequestType.choices,
        default=CoOwnerRequestType.CO_OWNER_INVITE,
    )

    def __str__(self):
        return (
            f"Registered {self.co_owner.user.primary_phone_number} - {self.property}"
            if self.co_owner
            else f"Un-registered{self.unregistered_co_owner.phone_number} - {self.property}"
        )


class AgentAssociatedProperty(Common):
    agent_profile = models.ForeignKey(AgentProfile, on_delete=models.CASCADE)
    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    is_associated = models.BooleanField(default=False)
    request_accepted = models.BooleanField(default=False)
    action_status = models.CharField(
        max_length=32,
        choices=UserRequestActions.choices,
        default=UserRequestActions.PENDING,
    )
    agent_contract_key = models.CharField(max_length=512, null=True, blank=True)
    agent_contract_file_name = models.CharField(max_length=128, null=True, blank=True)
    agent_contract_file_size = models.BigIntegerField(null=True, blank=True)
    request_agent_type = models.CharField(
        max_length=32, choices=PropertyAgentType.choices, null=True, blank=True
    )
    is_request_expired = models.BooleanField(default=False)
    request_type = models.CharField(
        max_length=32, choices=RequestType.choices, default=RequestType.AGENT_INVITE
    )

    def __str__(self):
        return f"{self.agent_profile.user.primary_phone_number} - {self.property}"


class UnregisteredOwner(Common):
    name = models.CharField(max_length=255)
    email = models.EmailField(null=True, blank=True)
    phone_number = PhoneNumberField(unique=False, blank=False, null=False)

    def __str__(self):
        return f"{self.name} (Unregistered) is a pending owner"


class ExchangeRates(models.Model):
    base_currency = models.CharField(max_length=12)
    target_currency = models.CharField(max_length=12)
    exchange_rate = models.FloatField()
    created_at = models.DateField(auto_now_add=True)

    def __str__(self):
        return f"{self.base_currency}/{self.target_currency} - {self.exchange_rate} - {self.created_at.date()}"


# DeletedProperty Model for Tracking Deletions
class DeletedProperty(models.Model):
    property = models.OneToOneField(
        Property, on_delete=models.CASCADE, related_name="deleted_entry"
    )
    deleted_by = models.ForeignKey(
        "user.User", on_delete=models.SET_NULL, null=True, blank=True
    )
    deleted_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Deleted Property ID: {self.property.id}"


class PropertyFloorPlan(Common):
    """
    Model to store property floor plan details
    """

    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    media_file_key = models.CharField(max_length=512, null=True, blank=True)
    media_file_name = models.CharField(max_length=128, null=True, blank=True)
    media_file_size = models.BigIntegerField(null=True, blank=True)
    order_no = models.IntegerField(null=True)
    media_file_content_type = models.CharField(max_length=50, null=True, blank=True)


class PropertyPaymentPlan(Common):
    """
    Model to store property payment plan details
    """

    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    media_file_key = models.CharField(max_length=512, null=True, blank=True)
    media_file_name = models.CharField(max_length=128, null=True, blank=True)
    media_file_size = models.BigIntegerField(null=True, blank=True)
    order_no = models.IntegerField(null=True)
    media_file_content_type = models.CharField(max_length=50, null=True, blank=True)


class PropertyExternalMedia(Common):
    """
    Model to store property external media and its type
    """

    property = models.ForeignKey(Property, on_delete=models.CASCADE)
    media_type = models.CharField(
        max_length=20,
        null=False,
        blank=False,
        choices=PropertyExternalMediaType.choices,
    )


class PropertyExternalMediaLink(Common):
    """
    Model to store property external media link
    """

    property_external_media = models.ForeignKey(
        PropertyExternalMedia, on_delete=models.CASCADE
    )
    url = models.URLField(null=False, blank=False)
    description = models.TextField(null=True, blank=True, max_length=320)


class PropertyExternalMediaSection(Common):
    """
    Model to store property external media document/image section details
    """

    property_external_media = models.ForeignKey(
        PropertyExternalMedia, on_delete=models.CASCADE
    )
    title = models.CharField(null=False, blank=False, max_length=100)


class PropertyExternalMediaDocument(Common):
    """
    Model to store property external media document/image files
    """

    property_external_media_section = models.ForeignKey(
        PropertyExternalMediaSection, on_delete=models.CASCADE
    )
    media_file_key = models.CharField(max_length=512, null=True, blank=True)
    media_file_name = models.CharField(max_length=128, null=True, blank=True)
    media_file_size = models.BigIntegerField(null=True, blank=True)
    order_no = models.IntegerField(null=True)
    media_file_content_type = models.CharField(max_length=50, null=True, blank=True)
    media_url = models.URLField(null=True, blank=True)


class HierarchyLevel(models.Model):
    """
    Model to store hierarchy levels
    """

    name = models.CharField(
        max_length=255, unique=True, choices=PropertyHierarchy.choices
    )
    level = models.PositiveIntegerField()
    # store json data like editable fields for a hierarchy etc.
    metadata = models.JSONField(null=True, blank=True)
    editable_attributes = models.JSONField(null=True, blank=True)
    viewed_attributes = models.JSONField(null=True, blank=True)

    def __str__(self):
        return self.name


class PropertyAttributes(models.Model):
    """
    Model to store property attributes
    """

    component_name = models.CharField(max_length=255, null=False, blank=False)
    attribute_name = models.CharField(max_length=255, null=False, blank=False)
    is_public = models.BooleanField(default=True, null=False)
    hierarchy_level = models.ForeignKey(
        HierarchyLevel,
        on_delete=models.CASCADE,
        null=False,
        related_name="property_attributes",
    )
    subscription_level = models.PositiveSmallIntegerField(
        choices=AgentSubscriptionPlanChoices.choices,
        default=AgentSubscriptionPlanChoices.PREMIUM,
        null=False,
    )
    attribute_category = models.PositiveSmallIntegerField(
        choices=PropertyAttributesCategory.choices, null=False
    )
    hidden_in_countries = models.ManyToManyField(
        "properties.Country",
        related_name="hidden_attributes",
        blank=True,
        help_text="Countries where this attribute should be hidden",
    )

    def __str__(self):
        return f"{self.attribute_name} - {self.hierarchy_level} - {self.is_public}"


class EmailTemplate(models.Model):
    """
    Model to store email templates
    """

    name = models.CharField(max_length=255)
    subject = models.CharField(max_length=255)
    body = models.TextField()

    def __str__(self):
        return self.name


# class PropertyInformation(Common):
#     owner = models.ForeignKey(
#         InvestorProfile, on_delete=models.SET_NULL, null=True, blank=True,
#         related_name="property_owner"
#     )
#     unregistered_owner = models.ForeignKey(
#         "properties.UnregisteredOwner", on_delete=models.SET_NULL, null=True, blank=True,
#         related_name="property_unregistered_owner"
#     )
#     owner_verified = models.BooleanField(default=True)
#     country = models.ForeignKey(Country, on_delete=models.CASCADE)
#     state = models.ForeignKey(State, on_delete=models.CASCADE, null=True, blank=True)
#     city = models.ForeignKey(City, on_delete=models.CASCADE, null=True, blank=True)
#     area = models.ForeignKey(Area, on_delete=models.CASCADE, null=True, blank=True)
#     community = models.ForeignKey(
#         Community, on_delete=models.CASCADE, null=True, blank=True
#     )
#     unit_number = models.CharField(max_length=32, null=True, blank=True)
#     building_number = models.CharField(max_length=32, null=True, blank=True)
#     property_monitor_address_id = models.IntegerField(null=True, blank=True)
#     postal_code = models.IntegerField(null=True, blank=True)
#     owner_intent = models.CharField(
#         max_length=32,
#         choices=OwnerIntentForProperty.choices,
#         null=True,
#         blank=True,
#     )


# class PropertyVerifiedData(Common):

#     property = models.ForeignKey(
#         PropertyInformation,
#         on_delete=models.CASCADE,
#         related_name="property_verified_data",
#     )
#     field_name = models.CharField(max_length=32)
#     value = models.CharField(max_length=32, null=True)
#     data_source = models.CharField(
#         max_length=32, choices=DataSource.choices
#     )

#     def __str__(self):
#         return f"{self.property_id} - {self.field_name}"


class UserLevelPropertyData(Common):
    """
    Model to store user level property details
    """

    # user = models.ForeignKey(User, on_delete=models.CASCADE)
    # role = models.ForeignKey(Role, on_delete=models.CASCADE)
    user_hierarchy = models.CharField(max_length=32, choices=PropertyHierarchy.choices)
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="property_user_level_data"
    )
    property_type = models.CharField(
        max_length=32, choices=PropertyType.choices, null=True, blank=True
    )
    floor_number = models.CharField(max_length=32, null=True, blank=True)
    property_unit_type = models.CharField(
        max_length=32, choices=PropertyAreaUnit.choices, default=PropertyAreaUnit.SQFT
    )
    total_area = models.FloatField(null=True, blank=True)
    carpet_area = models.FloatField(null=True, blank=True)
    balcony_area = models.FloatField(null=True, blank=True)
    number_of_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_common_bathrooms = models.IntegerField(null=True, blank=True)
    number_of_attached_bathrooms = models.IntegerField(null=True, blank=True)
    number_of_powder_rooms = models.IntegerField(null=True, blank=True)
    parking_available = models.BooleanField(default=False)
    number_of_covered_parking = models.IntegerField(null=True, blank=True)
    number_of_open_parking = models.IntegerField(null=True, blank=True)
    parking_number = models.TextField(null=True, blank=True)
    property_publish_status = models.CharField(
        max_length=32,
        choices=PropertyPublishStatus.choices,
        default=PropertyPublishStatus.DRAFT,
    )
    # owner_intent = models.CharField(
    #     max_length=32,
    #     choices=OwnerIntentForProperty.choices,
    #     default=OwnerIntentForProperty.NOT_FOR_SALE,
    # )
    dewa_id = models.CharField(max_length=32, null=True, blank=True)
    user_unit_preference = models.CharField(max_length=8, default="sqft")
    tenancy_type = models.CharField(
        max_length=25, choices=TenancyType.choices, null=True, blank=True
    )
    tenancy_start_date = models.DateField(null=True, blank=True)
    tenancy_end_date = models.DateField(null=True, blank=True)
    lead_owner_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, default=100
    )
    agent_type = models.CharField(
        max_length=32,
        choices=PropertyAgentType.choices,
        default=PropertyAgentType.OPEN_TO_ALL,
    )
    number_of_master_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_other_bedrooms = models.IntegerField(null=True, blank=True)
    number_of_maid_rooms = models.IntegerField(null=True, blank=True)
    number_of_study_rooms = models.IntegerField(null=True, blank=True)
    is_archived = models.BooleanField(default=False)
    # default_image = models.CharField(max_length=512, null=True, blank=True)
    total_floors = models.IntegerField(null=True, blank=True)
    building_type = models.IntegerField(
        null=True, blank=True, choices=PropertyBuildingType.choices
    )
    number_of_bathrooms = models.IntegerField(null=True, blank=True)
    meta = models.JSONField(default=dict, null=True, blank=True)
    source_ref = models.ForeignKey(
        "assistant.PropertyInquiriesMessages",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    source = models.CharField(
        max_length=25,
        choices=PropertySourceType.choices,
        default=PropertySourceType.IN_APP,
    )
    open_for_collaboration = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.property.id} - {self.property.unit_number} - {self.created_by} - {self.created_by_role}"

    def get_parking_number(self):
        if self.parking_number:
            try:
                return self.parking_number.split(",")
            except Exception as e:
                return [self.parking_number]
        return []

    def clear_fields(self, exclude: list = None):
        # fields to be excluded by default
        if not exclude:
            exclude = list()
        exclude.extend(
            [
                "id",
                "user",
                "role",
                "country",
                "created_ts",
                "updated_ts",
                "created_by",
                "updated_by",
                "owner",
                "created_by_role",
                "updated_by_role",
            ]
        )
        for field in self._meta.fields:
            if field.name not in exclude:  # Preserve required fields
                if field.has_default():
                    default = field.get_default()
                    if callable(default):
                        default = default()
                        setattr(self, field.name, default)
                else:
                    setattr(self, field.name, None)
        self.save()

    def get_total_bathroom_count(self):
        number_of_bathrooms = self.number_of_bathrooms or 0

        return number_of_bathrooms


class UserLevelPropertyAvailabilityAndStatus(Common):
    property_level_data = models.OneToOneField(
        UserLevelPropertyData,
        on_delete=models.CASCADE,
        related_name="property_user_level_availability_and_status",
    )
    # availability info
    status = models.CharField(
        max_length=32, choices=PropertyStatus.choices, null=True, blank=True
    )
    handover_date = models.CharField(max_length=32, null=True, blank=True)
    occupancy_status = models.CharField(
        max_length=32,
        choices=PropertyAvailabilityStatus.choices,
        null=True,
        blank=True,
        default=PropertyAvailabilityStatus.VACANT,
    )
    rent_contract_key = models.CharField(max_length=512, null=True, blank=True)
    rent_contract_file_name = models.CharField(max_length=128, null=True, blank=True)
    rent_contract_file_size = models.BigIntegerField(null=True, blank=True)
    rent_available_start_date = models.DateField(null=True, blank=True)

    # payment plan
    enable_payment_plan = models.BooleanField(default=False)
    during_construction = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True
    )
    on_handover = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True
    )
    enable_post_handover = models.BooleanField(default=False)
    post_handover_time_frame = models.IntegerField(null=True, blank=True)

    # ownership proof
    ownership_proof_key = models.CharField(max_length=512, null=True, blank=True)
    ownership_proof_file_name = models.CharField(max_length=128, null=True, blank=True)
    ownership_proof_file_size = models.BigIntegerField(null=True, blank=True)

    def __str__(self):
        return f"{self.property_level_data.property.id} - {self.property_level_data.property.unit_number} - {self.property_level_data.created_by} - {self.property_level_data.created_by_role}"


# class PropertyInformationCompletionState(Common):
#     """
#     This model helps to track Property state,
#     is_completed will be true if user adds all the required fields and click on done
#     Data will be saved in draft_json and is_completed will be false if user moves the property to draft
#     """

#     property_information = models.OneToOneField(PropertyInformation, on_delete=models.CASCADE, related_name="property_information_completion_state")
#     data_source = models.CharField(
#         max_length=32, choices=DataSource.choices, default=DataSource.PROPERTY_MONITOR
#     )
#     # Field to store the current state of completion
#     state = models.CharField(
#         max_length=32, choices=PropertyCompletionStateChoices.choices
#     )
#     is_completed = models.BooleanField(default=False)
#     draft_data_json = models.JSONField(null=True)

#     def __str__(self):
#         return f"Progress of {self.property.id}: {self.state}"


class UserLevelPropertyFinancialDetails(Common):
    property_level_data = models.OneToOneField(
        UserLevelPropertyData,
        on_delete=models.CASCADE,
        related_name="property_user_level_financial_details",
    )
    property_currency_code = models.CharField(
        max_length=32,
        null=True,
        blank=True,
        default=settings.DEFAULT_PROPERTY_CURRENCY_CODE,
    )
    original_price = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    asking_price = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    valuation = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    annual_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    annual_service_charges = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    security_deposit = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    other_expenses = models.BigIntegerField(default=0)
    preferred_payment_frequency = models.IntegerField(
        null=True, blank=True, choices=PreferredPaymentFrequency.choices
    )
    price_negotiable = models.BooleanField(default=False)
    expected_rent = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )
    expected_security_deposit = models.BigIntegerField(
        validators=[MaxValueValidator(settings.PRICE_MAX_LIMIT)], null=True, blank=True
    )

    def __str__(self):
        return f"{self.property_level_data.property.id} - {self.property_level_data.property.unit_number} - {self.property_level_data.created_by} - {self.property_level_data.created_by_role}"

    def get_yearly_expected_rent(self):
        """
        Calculate yearly expected rent based on preferred payment frequency.
        Returns None if expected_rent is None.
        """
        if self.expected_rent is None:
            return None

        if self.preferred_payment_frequency == PreferredPaymentFrequency.YEARLY:
            return self.expected_rent
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.BI_YEARLY:
            return self.expected_rent * 2
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.QUARTERLY:
            return self.expected_rent * 4
        elif self.preferred_payment_frequency == PreferredPaymentFrequency.MONTHLY:
            return self.expected_rent * 12
        else:
            return (
                self.expected_rent
            )  # Default to original value if frequency not specified


class UserLevelPropertyFeatures(Common):
    """
    Model to store property features
    """

    property_level_data = models.OneToOneField(
        UserLevelPropertyData,
        on_delete=models.CASCADE,
        related_name="property_user_level_features",
    )
    branded_building = models.BooleanField(default=False)
    furnished = models.IntegerField(
        choices=FurnishedChoices.choices,
        default=FurnishedChoices.UNFURNISHED,
        null=True,
        blank=True
    )
    premium_view = models.BooleanField(default=False)
    is_restroom_available = models.BooleanField(default=False)
    no_of_shared_restrooms = models.IntegerField(null=True, blank=True)
    no_of_private_restrooms = models.IntegerField(null=True, blank=True)
    is_parking_available = models.BooleanField(default=False)
    public_parking = models.BooleanField(default=False)
    no_of_reserved_parking = models.IntegerField(null=True, blank=True)
    reserved_parking_number = models.TextField(null=True, blank=True)
    is_lift_available = models.BooleanField(default=False)
    common_lift = models.BooleanField(default=False)
    no_of_personal_lift = models.IntegerField(null=True, blank=True)
    security_available = models.BooleanField(default=False)
    water_storage_available = models.BooleanField(default=False)
    property_on_main_road = models.BooleanField(default=False)
    corner_property = models.BooleanField(default=False)
    tags = models.ManyToManyField(PropertyTag)

    def __str__(self):
        return f"{self.property_level_data.property.id} - {self.property_level_data.property.unit_number} - {self.property_level_data.created_by} - {self.property_level_data.created_by_role}"

    def get_tags(self):
        if self.tags.all().exists():
            return self.tags.values_list("name", flat=True)
        return []

    def get_reserved_parking_number(self):
        if self.reserved_parking_number:
            try:
                return self.reserved_parking_number.split(",")
            except Exception:
                return [self.reserved_parking_number]
        return []
