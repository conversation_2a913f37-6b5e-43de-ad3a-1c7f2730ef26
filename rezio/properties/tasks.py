"""
Celery tasks for property scoring system.
These tasks run in the background to avoid blocking API responses.
"""

import logging
from typing import List, Optional

from celery import shared_task
from django.db import transaction

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def calculate_property_score_task(self, user_level_property_data_id: int):
    """
    Background task to calculate and update property score for a UserLevelPropertyData.
    
    Args:
        user_level_property_data_id (int): The ID of the UserLevelPropertyData to update
        
    Returns:
        dict: Task result with success status and score
    """
    try:
        from rezio.properties.services.dynamic_property_scoring_service import (
            calculate_and_update_user_level_property_score_dynamic,
        )
        
        logger.info(f"Starting property score calculation task for UserLevelPropertyData {user_level_property_data_id}")
        
        # Calculate and update the score
        success = calculate_and_update_user_level_property_score_dynamic(user_level_property_data_id)
        
        if success:
            logger.info(f"Successfully completed property score calculation for UserLevelPropertyData {user_level_property_data_id}")
            return {
                'success': True,
                'user_level_property_data_id': user_level_property_data_id,
                'message': 'Property score calculated successfully'
            }
        else:
            logger.error(f"Failed to calculate property score for UserLevelPropertyData {user_level_property_data_id}")
            return {
                'success': False,
                'user_level_property_data_id': user_level_property_data_id,
                'message': 'Property score calculation failed'
            }
            
    except Exception as exc:
        logger.error(f"Error in property score calculation task for UserLevelPropertyData {user_level_property_data_id}: {str(exc)}")
        
        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying property score calculation task for UserLevelPropertyData {user_level_property_data_id} (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc)
        else:
            logger.error(f"Max retries exceeded for property score calculation task for UserLevelPropertyData {user_level_property_data_id}")
            return {
                'success': False,
                'user_level_property_data_id': user_level_property_data_id,
                'message': f'Property score calculation failed after {self.max_retries} retries: {str(exc)}'
            }


@shared_task(bind=True, max_retries=2, default_retry_delay=120)
def bulk_calculate_property_scores_task(self, user_level_property_data_ids: Optional[List[int]] = None, batch_size: int = 50):
    """
    Background task to calculate property scores for multiple UserLevelPropertyData objects.
    
    Args:
        user_level_property_data_ids (list, optional): List of UserLevelPropertyData IDs to update
        batch_size (int): Number of objects to process in each batch
        
    Returns:
        dict: Task result with summary statistics
    """
    try:
        from rezio.properties.services.dynamic_property_scoring_service import DynamicPropertyScoringService
        from rezio.properties.models import UserLevelPropertyData
        
        logger.info(f"Starting bulk property score calculation task")
        
        # Get objects to process
        if user_level_property_data_ids:
            user_level_data_objects = UserLevelPropertyData.objects.filter(id__in=user_level_property_data_ids)
            logger.info(f"Processing {len(user_level_property_data_ids)} specific UserLevelPropertyData objects")
        else:
            user_level_data_objects = UserLevelPropertyData.objects.all()
            logger.info(f"Processing all UserLevelPropertyData objects")
        
        total_objects = user_level_data_objects.count()
        
        if total_objects == 0:
            return {
                'success': True,
                'message': 'No UserLevelPropertyData objects found to process',
                'total': 0,
                'updated': 0,
                'failed': 0
            }
        
        # Initialize scoring service
        scoring_service = DynamicPropertyScoringService()
        
        # Process in batches
        updated_count = 0
        failed_count = 0
        
        for i in range(0, total_objects, batch_size):
            batch_objects = user_level_data_objects[i:i + batch_size]
            
            logger.info(f"Processing batch {i//batch_size + 1} ({len(batch_objects)} objects)")
            
            for user_level_data in batch_objects:
                try:
                    with transaction.atomic():
                        new_score = scoring_service._calculate_score_for_user_level_data(user_level_data)
                        user_level_data.property_score = new_score
                        user_level_data.save(update_fields=['property_score'])
                        updated_count += 1
                        
                except Exception as e:
                    logger.error(f"Failed to update score for UserLevelPropertyData {user_level_data.id}: {str(e)}")
                    failed_count += 1
            
            # Log progress
            progress = ((i + len(batch_objects)) / total_objects) * 100
            logger.info(f"Bulk calculation progress: {progress:.1f}% ({updated_count + failed_count}/{total_objects})")
        
        logger.info(f"Bulk property score calculation completed: {updated_count} updated, {failed_count} failed")
        
        return {
            'success': True,
            'message': 'Bulk property score calculation completed',
            'total': total_objects,
            'updated': updated_count,
            'failed': failed_count
        }
        
    except Exception as exc:
        logger.error(f"Error in bulk property score calculation task: {str(exc)}")
        
        # Retry the task if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying bulk property score calculation task (attempt {self.request.retries + 1})")
            raise self.retry(exc=exc)
        else:
            logger.error(f"Max retries exceeded for bulk property score calculation task")
            return {
                'success': False,
                'message': f'Bulk property score calculation failed after {self.max_retries} retries: {str(exc)}',
                'total': 0,
                'updated': 0,
                'failed': 0
            }


@shared_task
def cleanup_failed_score_calculations():
    """
    Periodic task to identify and retry failed score calculations.
    This can be run as a periodic task to ensure data consistency.
    """
    try:
        from rezio.properties.models import UserLevelPropertyData
        
        logger.info("Starting cleanup of failed score calculations")
        
        # Find UserLevelPropertyData objects with null or zero scores
        objects_without_scores = UserLevelPropertyData.objects.filter(
            property_score__isnull=True
        ) | UserLevelPropertyData.objects.filter(property_score=0.0)
        
        count = objects_without_scores.count()
        
        if count == 0:
            logger.info("No UserLevelPropertyData objects found without scores")
            return {
                'success': True,
                'message': 'No objects found without scores',
                'queued_tasks': 0
            }
        
        # Queue individual tasks for each object
        queued_count = 0
        for user_level_data in objects_without_scores[:100]:  # Limit to 100 to avoid overwhelming the queue
            calculate_property_score_task.delay(user_level_data.id)
            queued_count += 1
        
        logger.info(f"Queued {queued_count} score calculation tasks for cleanup")
        
        return {
            'success': True,
            'message': f'Queued {queued_count} score calculation tasks for cleanup',
            'queued_tasks': queued_count,
            'total_without_scores': count
        }
        
    except Exception as exc:
        logger.error(f"Error in cleanup task: {str(exc)}")
        return {
            'success': False,
            'message': f'Cleanup task failed: {str(exc)}',
            'queued_tasks': 0
        }


# Task routing configuration (optional)
# You can add this to your Celery configuration to route these tasks to specific queues
TASK_ROUTES = {
    'rezio.properties.tasks.calculate_property_score_task': {'queue': 'property_scoring'},
    'rezio.properties.tasks.bulk_calculate_property_scores_task': {'queue': 'property_scoring_bulk'},
    'rezio.properties.tasks.cleanup_failed_score_calculations': {'queue': 'property_scoring_cleanup'},
}
