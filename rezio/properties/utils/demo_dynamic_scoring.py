"""
Demonstration script for the new dynamic scoring approach using ModelName.field_name format.
This shows the benefits of the new approach over hardcoded scoring methods.
"""

from rezio.properties.models import UserLevelPropertyData
from rezio.properties.services.dynamic_property_scoring_service import DynamicPropertyScoringService
from rezio.properties.utils.scoring_utils import get_user_level_property_score_breakdown
from rezio.properties.scoring_constants import get_weight_mapping
from rezio.properties.text_choices import PropertyCategory


def demonstrate_dynamic_scoring():
    """
    Demonstrate the dynamic scoring approach and its benefits.
    """
    print("=== Dynamic Property Scoring Demonstration ===\n")
    
    # Get a sample UserLevelPropertyData object
    user_level_data = UserLevelPropertyData.objects.first()
    
    if not user_level_data:
        print("No UserLevelPropertyData found. Please create some test data first.")
        return
    
    print(f"Testing with UserLevelPropertyData ID: {user_level_data.id}")
    print(f"Property ID: {user_level_data.property.id}")
    print(f"Property Category: {user_level_data.property.property_category}")
    print(f"Owner Intent: {user_level_data.property.owner_intent}")
    print("-" * 60)
    
    # Initialize dynamic scoring service
    scoring_service = DynamicPropertyScoringService()
    
    # Calculate score using dynamic approach
    score = scoring_service.calculate_user_level_property_score(user_level_data.id)
    print(f"Calculated Score: {score:.2f}")
    
    # Get detailed breakdown
    breakdown = get_user_level_property_score_breakdown(user_level_data.id)
    
    print(f"\nDetailed Breakdown:")
    print(f"Total Raw Score: {breakdown['total_raw_score']:.2f}")
    print(f"Max Possible Score: {breakdown['max_possible_score']}")
    print(f"Normalized Score: {breakdown['normalized_score']:.2f}")
    print(f"Current Stored Score: {breakdown['current_property_score']}")
    
    print(f"\nOwner Intent Logic:")
    print(f"Owner Intent: {breakdown['owner_intent']} ({breakdown['owner_intent_display']})")
    
    # Show field-by-field breakdown
    print(f"\nField-by-Field Breakdown:")
    print("-" * 80)
    print(f"{'Field Name':<50} {'Weight':<8} {'Score':<8} {'Applied':<8}")
    print("-" * 80)
    
    applied_count = 0
    total_applied_weight = 0
    
    for field_name, field_data in breakdown['field_breakdown'].items():
        weight = field_data['weight']
        score = field_data['score']
        applied = field_data['applied']
        
        if applied:
            applied_count += 1
            total_applied_weight += weight
        
        status = "✓" if applied else "✗"
        print(f"{field_name:<50} {weight:<8} {score:<8.1f} {status:<8}")
    
    print("-" * 80)
    print(f"Applied Fields: {applied_count}/{len(breakdown['field_breakdown'])}")
    print(f"Total Applied Weight: {total_applied_weight}")
    
    return breakdown


def show_weight_configuration():
    """
    Show the current weight configuration for both property categories.
    """
    print("\n=== Weight Configuration ===\n")
    
    # Show residential weights
    residential_weights = get_weight_mapping(PropertyCategory.RESIDENTIAL)
    print("Residential Property Weights:")
    print("-" * 60)
    
    models = {}
    for key, weight in residential_weights.items():
        model_name = key.split('.')[0]
        if model_name not in models:
            models[model_name] = []
        models[model_name].append((key, weight))
    
    for model_name, fields in models.items():
        print(f"\n{model_name}:")
        for field_key, weight in fields:
            field_name = field_key.split('.', 1)[1]
            print(f"  {field_name}: {weight}")
    
    print(f"\nTotal Residential Weight: {sum(residential_weights.values())}")
    
    # Show commercial weights
    commercial_weights = get_weight_mapping(PropertyCategory.COMMERCIAL)
    print(f"\nTotal Commercial Weight: {sum(commercial_weights.values())}")


def demonstrate_conditional_logic():
    """
    Demonstrate the conditional logic based on owner intent.
    """
    print("\n=== Conditional Logic Demonstration ===\n")
    
    user_level_data = UserLevelPropertyData.objects.first()
    if not user_level_data:
        print("No UserLevelPropertyData found.")
        return
    
    scoring_service = DynamicPropertyScoringService()
    
    # Show how asking_price and expected_rent are handled
    try:
        financial_details = user_level_data.property_user_level_financial_details
        asking_price = financial_details.asking_price
        expected_rent = financial_details.expected_rent
        
        print(f"Financial Details:")
        print(f"  Asking Price: {asking_price}")
        print(f"  Expected Rent: {expected_rent}")
        
        owner_intent = user_level_data.property.owner_intent
        print(f"\nOwner Intent: {owner_intent}")
        
        # Check which fields would be applied
        asking_price_key = "UserLevelPropertyFinancialDetails.asking_price"
        expected_rent_key = "UserLevelPropertyFinancialDetails.expected_rent"
        
        asking_price_skipped = scoring_service._should_skip_field_based_on_owner_intent(asking_price_key, owner_intent)
        expected_rent_skipped = scoring_service._should_skip_field_based_on_owner_intent(expected_rent_key, owner_intent)
        
        print(f"\nConditional Logic Results:")
        print(f"  Asking Price will be {'SKIPPED' if asking_price_skipped else 'INCLUDED'}")
        print(f"  Expected Rent will be {'SKIPPED' if expected_rent_skipped else 'INCLUDED'}")
        
    except Exception as e:
        print(f"Error accessing financial details: {e}")


def show_benefits_of_dynamic_approach():
    """
    Show the benefits of the dynamic approach over hardcoded methods.
    """
    print("\n=== Benefits of Dynamic Approach ===\n")
    
    benefits = [
        "1. Easy to Add New Fields:",
        "   - Just add 'ModelName.field_name': weight to scoring constants",
        "   - No need to modify scoring service code",
        "",
        "2. Maintainable Weight Configuration:",
        "   - All weights in one place (scoring_constants.py)",
        "   - Clear model.field naming convention",
        "   - Easy to understand and modify",
        "",
        "3. Automatic Field Access:",
        "   - Dynamic model instance retrieval",
        "   - Automatic field value extraction",
        "   - Handles different field types automatically",
        "",
        "4. Conditional Logic Support:",
        "   - Owner intent-based field inclusion/exclusion",
        "   - Easy to add more conditional rules",
        "",
        "5. Detailed Debugging:",
        "   - Field-by-field breakdown available",
        "   - Shows which fields are applied and why",
        "   - Error tracking for individual fields",
        "",
        "6. Consistent Approach:",
        "   - Same logic for all model types",
        "   - Standardized field value evaluation",
        "   - Unified scoring methodology"
    ]
    
    for benefit in benefits:
        print(benefit)


if __name__ == "__main__":
    print("To run the dynamic scoring demonstration:")
    print("python manage.py shell")
    print(">>> from rezio.properties.utils.demo_dynamic_scoring import demonstrate_dynamic_scoring")
    print(">>> demonstrate_dynamic_scoring()")
    print()
    print("Other available functions:")
    print(">>> show_weight_configuration()")
    print(">>> demonstrate_conditional_logic()")
    print(">>> show_benefits_of_dynamic_approach()")
