"""
Utility functions for property scoring system.
"""

from typing import Dict, List, Optional
from django.db.models import Q

from rezio.properties.models import UserLevelPropertyData
from rezio.properties.services.property_scoring_service import PropertyScoringService
from rezio.properties.scoring_constants import get_weight_mapping, get_max_score
from rezio.properties.text_choices import PropertyCategory


def get_user_level_property_score_breakdown(user_level_property_data_id: int) -> Dict:
    """
    Get a detailed breakdown of how a UserLevelPropertyData's score is calculated.

    Args:
        user_level_property_data_id (int): The ID of the UserLevelPropertyData

    Returns:
        dict: Detailed score breakdown
    """
    try:
        user_level_data = UserLevelPropertyData.objects.get(id=user_level_property_data_id)
        scoring_service = PropertyScoringService()

        property_category = user_level_data.property.property_category
        weight_mapping = get_weight_mapping(property_category)
        max_score = get_max_score(property_category)

        # Calculate individual scores
        breakdown = {
            'user_level_property_data_id': user_level_property_data_id,
            'property_id': user_level_data.property.id,
            'property_category': 'Residential' if property_category == PropertyCategory.RESIDENTIAL else 'Commercial',
            'max_possible_score': max_score,
            'score_breakdown': {
                'user_level_data': scoring_service._calculate_user_level_data_score(user_level_data, weight_mapping),
                'availability_status': scoring_service._calculate_availability_status_score(user_level_data, weight_mapping),
                'financial_details': scoring_service._calculate_financial_details_score(user_level_data, weight_mapping),
                'features': scoring_service._calculate_features_score(user_level_data, weight_mapping),
                'floor_plan': scoring_service._calculate_floor_plan_score(user_level_data.property, weight_mapping),
                'payment_plan': scoring_service._calculate_payment_plan_score(user_level_data.property, weight_mapping),
                'unit_sections': scoring_service._calculate_unit_sections_score(user_level_data.property, weight_mapping),
            }
        }

        # Calculate totals
        total_raw_score = sum(breakdown['score_breakdown'].values())
        normalized_score = (total_raw_score / max_score) * 100 if max_score > 0 else 0.0

        breakdown['total_raw_score'] = total_raw_score
        breakdown['normalized_score'] = min(100.0, max(0.0, normalized_score))
        breakdown['current_property_score'] = user_level_data.property_score

        return breakdown

    except UserLevelPropertyData.DoesNotExist:
        return {
            'error': f'UserLevelPropertyData with ID {user_level_property_data_id} does not exist',
            'user_level_property_data_id': user_level_property_data_id
        }
    except Exception as e:
        return {
            'error': f'Error calculating score breakdown: {str(e)}',
            'user_level_property_data_id': user_level_property_data_id
        }


def get_user_level_properties_by_score_range(min_score: float = 0.0, max_score: float = 100.0,
                                            property_category: Optional[int] = None) -> List[Dict]:
    """
    Get UserLevelPropertyData objects within a specific score range.

    Args:
        min_score (float): Minimum score (0-100)
        max_score (float): Maximum score (0-100)
        property_category (int, optional): Filter by property category

    Returns:
        list: List of UserLevelPropertyData objects with their scores
    """
    queryset = UserLevelPropertyData.objects.filter(
        property_score__gte=min_score,
        property_score__lte=max_score
    ).exclude(property_score__isnull=True)

    if property_category is not None:
        queryset = queryset.filter(property__property_category=property_category)

    return list(queryset.values(
        'id', 'property__id', 'property__unit_number', 'property__property_category', 'property_score'
    ).order_by('-property_score'))


def get_top_scored_user_level_properties(limit: int = 10, property_category: Optional[int] = None) -> List[Dict]:
    """
    Get the top scored UserLevelPropertyData objects.

    Args:
        limit (int): Number of UserLevelPropertyData objects to return
        property_category (int, optional): Filter by property category

    Returns:
        list: List of top scored UserLevelPropertyData objects
    """
    queryset = UserLevelPropertyData.objects.exclude(property_score__isnull=True)

    if property_category is not None:
        queryset = queryset.filter(property__property_category=property_category)

    return list(queryset.values(
        'id', 'property__id', 'property__unit_number', 'property__property_category', 'property_score'
    ).order_by('-property_score')[:limit])


def get_user_level_properties_without_scores() -> List[Dict]:
    """
    Get UserLevelPropertyData objects that don't have scores calculated yet.

    Returns:
        list: List of UserLevelPropertyData objects without scores
    """
    return list(UserLevelPropertyData.objects.filter(
        Q(property_score__isnull=True) | Q(property_score=0.0)
    ).values('id', 'property__id', 'property__unit_number', 'property__property_category'))


def calculate_user_level_property_score_statistics(property_category: Optional[int] = None) -> Dict:
    """
    Calculate statistics about UserLevelPropertyData scores.

    Args:
        property_category (int, optional): Filter by property category

    Returns:
        dict: Score statistics
    """
    from django.db.models import Avg, Max, Min, Count

    queryset = UserLevelPropertyData.objects.exclude(property_score__isnull=True)

    if property_category is not None:
        queryset = queryset.filter(property__property_category=property_category)

    stats = queryset.aggregate(
        total_user_level_properties=Count('id'),
        avg_score=Avg('property_score'),
        max_score=Max('property_score'),
        min_score=Min('property_score')
    )

    # Add category information
    if property_category is not None:
        stats['category'] = 'Residential' if property_category == PropertyCategory.RESIDENTIAL else 'Commercial'
    else:
        stats['category'] = 'All Categories'

    return stats


def validate_scoring_weights() -> Dict:
    """
    Validate that the scoring weights are properly configured.

    Returns:
        dict: Validation results
    """
    from rezio.properties.scoring_constants import (
        RESIDENTIAL_PROPERTY_SCORE_WEIGHTS,
        COMMERCIAL_PROPERTY_SCORE_WEIGHTS,
        MAX_RESIDENTIAL_SCORE,
        MAX_COMMERCIAL_SCORE
    )

    validation_results = {
        'residential': {
            'total_weights': len(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS),
            'max_score': MAX_RESIDENTIAL_SCORE,
            'calculated_max': sum(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.values()),
            'weights_match': MAX_RESIDENTIAL_SCORE == sum(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.values())
        },
        'commercial': {
            'total_weights': len(COMMERCIAL_PROPERTY_SCORE_WEIGHTS),
            'max_score': MAX_COMMERCIAL_SCORE,
            'calculated_max': sum(COMMERCIAL_PROPERTY_SCORE_WEIGHTS.values()),
            'weights_match': MAX_COMMERCIAL_SCORE == sum(COMMERCIAL_PROPERTY_SCORE_WEIGHTS.values())
        }
    }

    validation_results['overall_valid'] = (
        validation_results['residential']['weights_match'] and
        validation_results['commercial']['weights_match']
    )

    return validation_results


def recalculate_user_level_property_scores_for_category(property_category: int) -> Dict:
    """
    Recalculate scores for all UserLevelPropertyData objects in a specific category.

    Args:
        property_category (int): Property category to recalculate

    Returns:
        dict: Summary of the operation
    """
    user_level_data_objects = UserLevelPropertyData.objects.filter(property__property_category=property_category)
    user_level_property_data_ids = list(user_level_data_objects.values_list('id', flat=True))

    scoring_service = PropertyScoringService()
    result = scoring_service.bulk_update_user_level_property_scores(user_level_property_data_ids)

    category_name = 'Residential' if property_category == PropertyCategory.RESIDENTIAL else 'Commercial'
    result['category'] = category_name

    return result
