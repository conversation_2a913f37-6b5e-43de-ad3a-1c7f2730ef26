"""
Test script to verify that location fields (country, state, city, community) 
are properly included in the property scoring calculation.
"""

from rezio.properties.models import UserLevelPropertyData
from rezio.properties.services.dynamic_property_scoring_service import DynamicPropertyScoringService
from rezio.properties.utils.scoring_utils import get_user_level_property_score_breakdown
from rezio.properties.scoring_constants import get_weight_mapping
from rezio.properties.text_choices import PropertyCategory


def test_location_field_scoring():
    """
    Test that location fields are properly included in scoring calculation.
    """
    print("=== Testing Location Field Scoring ===\n")
    
    # Get a sample UserLevelPropertyData object
    user_level_data = UserLevelPropertyData.objects.first()
    
    if not user_level_data:
        print("No UserLevelPropertyData found. Please create some test data first.")
        return
    
    property_obj = user_level_data.property
    property_category = property_obj.property_category
    
    print(f"Testing with UserLevelPropertyData ID: {user_level_data.id}")
    print(f"Property ID: {property_obj.id}")
    print(f"Property Category: {'Residential' if property_category == PropertyCategory.RESIDENTIAL else 'Commercial'}")
    
    # Show current location data
    print(f"\nCurrent Location Data:")
    print(f"Country: {property_obj.country.name if property_obj.country else 'None'} (ID: {property_obj.country.id if property_obj.country else 'None'})")
    print(f"State: {property_obj.state.name if property_obj.state else 'None'} (ID: {property_obj.state.id if property_obj.state else 'None'})")
    print(f"City: {property_obj.city.name if property_obj.city else 'None'} (ID: {property_obj.city.id if property_obj.city else 'None'})")
    print(f"Community: {property_obj.community.name if property_obj.community else 'None'} (ID: {property_obj.community.id if property_obj.community else 'None'})")
    
    # Get weight mapping for this property category
    weight_mapping = get_weight_mapping(property_category)
    
    # Show location field weights
    print(f"\nLocation Field Weights for this Category:")
    location_fields = ['Property.country', 'Property.state', 'Property.city', 'Property.community']
    total_location_weight = 0
    
    for field in location_fields:
        weight = weight_mapping.get(field, 0)
        total_location_weight += weight
        print(f"{field}: {weight}")
    
    print(f"Total Location Weight: {total_location_weight}")
    
    # Calculate score and get breakdown
    scoring_service = DynamicPropertyScoringService()
    score = scoring_service.calculate_user_level_property_score(user_level_data.id)
    breakdown = get_user_level_property_score_breakdown(user_level_data.id)
    
    print(f"\nScoring Results:")
    print(f"Total Score: {score:.2f}")
    print(f"Total Raw Score: {breakdown['total_raw_score']:.2f}")
    print(f"Max Possible Score: {breakdown['max_possible_score']}")
    
    # Show location field contributions
    print(f"\nLocation Field Contributions:")
    location_score = 0
    
    for field in location_fields:
        if field in breakdown['field_breakdown']:
            field_data = breakdown['field_breakdown'][field]
            weight = field_data['weight']
            score_contrib = field_data['score']
            applied = field_data['applied']
            
            location_score += score_contrib
            status = "✓ Applied" if applied else "✗ Not Applied"
            print(f"{field}: Weight={weight}, Score={score_contrib}, {status}")
        else:
            print(f"{field}: Not found in breakdown")
    
    print(f"\nTotal Location Score Contribution: {location_score}")
    print(f"Location Score as % of Total: {(location_score / breakdown['total_raw_score'] * 100):.1f}%" if breakdown['total_raw_score'] > 0 else "N/A")
    
    return {
        'user_level_data_id': user_level_data.id,
        'total_score': score,
        'location_score': location_score,
        'location_fields_applied': sum(1 for field in location_fields if breakdown['field_breakdown'].get(field, {}).get('applied', False)),
        'breakdown': breakdown
    }


def compare_scores_with_and_without_location():
    """
    Compare scores with and without location fields to show their impact.
    """
    print("\n=== Comparing Scores With and Without Location Fields ===\n")
    
    user_level_data = UserLevelPropertyData.objects.first()
    if not user_level_data:
        print("No UserLevelPropertyData found.")
        return
    
    # Get original score with location fields
    scoring_service = DynamicPropertyScoringService()
    original_score = scoring_service.calculate_user_level_property_score(user_level_data.id)
    
    # Temporarily modify weight mapping to exclude location fields
    from rezio.properties.scoring_constants import RESIDENTIAL_PROPERTY_SCORE_WEIGHTS, COMMERCIAL_PROPERTY_SCORE_WEIGHTS
    
    property_category = user_level_data.property.property_category
    
    if property_category == PropertyCategory.RESIDENTIAL:
        original_weights = RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.copy()
    else:
        original_weights = COMMERCIAL_PROPERTY_SCORE_WEIGHTS.copy()
    
    # Create modified weights without location fields
    modified_weights = {k: v for k, v in original_weights.items() 
                      if not k.startswith('Property.')}
    
    # Calculate max scores
    original_max = sum(original_weights.values())
    modified_max = sum(modified_weights.values())
    
    # Calculate what the score would be without location fields
    # (This is a simplified calculation for demonstration)
    location_weight = sum(v for k, v in original_weights.items() if k.startswith('Property.'))
    
    print(f"Property Category: {'Residential' if property_category == PropertyCategory.RESIDENTIAL else 'Commercial'}")
    print(f"\nWeight Analysis:")
    print(f"Total Original Weights: {original_max}")
    print(f"Location Field Weights: {location_weight}")
    print(f"Non-Location Weights: {modified_max}")
    print(f"Location Weight Percentage: {(location_weight / original_max * 100):.1f}%")
    
    print(f"\nScore Analysis:")
    print(f"Score with Location Fields: {original_score:.2f}")
    
    # Get detailed breakdown to see actual location contribution
    breakdown = get_user_level_property_score_breakdown(user_level_data.id)
    location_fields = ['Property.country', 'Property.state', 'Property.city', 'Property.community']
    actual_location_score = sum(breakdown['field_breakdown'].get(field, {}).get('score', 0) for field in location_fields)
    
    estimated_score_without_location = ((breakdown['total_raw_score'] - actual_location_score) / modified_max) * 100 if modified_max > 0 else 0
    
    print(f"Estimated Score without Location: {estimated_score_without_location:.2f}")
    print(f"Location Fields Contribution: {actual_location_score:.2f} points")
    print(f"Impact of Location Fields: {(original_score - estimated_score_without_location):.2f} points")


def show_location_field_details():
    """
    Show detailed information about how location fields are handled.
    """
    print("\n=== Location Field Implementation Details ===\n")
    
    details = [
        "Location Field Scoring Implementation:",
        "",
        "1. Field Types and Weights:",
        "   - Property.country: Foreign key to Country model",
        "   - Property.state: Foreign key to State model", 
        "   - Property.city: Foreign key to City model",
        "   - Property.community: Foreign key to Community model",
        "",
        "2. Scoring Logic:",
        "   - If foreign key relationship exists (not None), full weight is applied",
        "   - If foreign key is None, no weight is applied",
        "   - Location hierarchy: Country → State → City → Community",
        "",
        "3. Weight Distribution (Residential vs Commercial):",
        "   Residential: Country(8), State(6), City(10), Community(12) = 36 total",
        "   Commercial: Country(10), State(8), City(12), Community(15) = 45 total",
        "",
        "4. Benefits:",
        "   - Properties in well-defined locations score higher",
        "   - Encourages complete location data entry",
        "   - Community-level specificity is most valuable",
        "   - Commercial properties get higher location weights",
        "",
        "5. Dynamic Access:",
        "   - Uses Property.field_name format in weight constants",
        "   - Automatically accesses property.country, property.state, etc.",
        "   - Handles None values gracefully",
        "   - No hardcoded logic needed for new location fields"
    ]
    
    for detail in details:
        print(detail)


if __name__ == "__main__":
    print("To test location field scoring:")
    print("python manage.py shell")
    print(">>> from rezio.properties.utils.test_location_scoring import test_location_field_scoring")
    print(">>> test_location_field_scoring()")
    print()
    print("Other available functions:")
    print(">>> compare_scores_with_and_without_location()")
    print(">>> show_location_field_details()")
