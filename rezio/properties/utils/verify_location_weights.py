"""
Verification script to show the impact of adding location fields to the scoring system.
"""

from rezio.properties.scoring_constants import (
    RESIDENTIAL_PROPERTY_SCORE_WEIGHTS,
    COMMERCIAL_PROPERTY_SCORE_WEIGHTS,
    MAX_RESIDENTIAL_SCORE,
    MAX_COMMERCIAL_SCORE
)


def verify_location_weights():
    """
    Verify that location fields are properly included in the weight calculations.
    """
    print("=== Location Fields Weight Verification ===\n")
    
    # Define location fields
    location_fields = [
        "Property.country",
        "Property.state", 
        "Property.city",
        "Property.community"
    ]
    
    # Check residential weights
    print("RESIDENTIAL PROPERTY WEIGHTS:")
    print("-" * 50)
    
    residential_location_weights = {}
    residential_total_location = 0
    
    for field in location_fields:
        weight = RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.get(field, 0)
        residential_location_weights[field] = weight
        residential_total_location += weight
        print(f"{field}: {weight}")
    
    print(f"\nTotal Residential Location Weight: {residential_total_location}")
    print(f"Total Residential Weight (All Fields): {MAX_RESIDENTIAL_SCORE}")
    print(f"Location Weight as % of Total: {(residential_total_location / MAX_RESIDENTIAL_SCORE * 100):.1f}%")
    
    # Check commercial weights
    print(f"\nCOMMERCIAL PROPERTY WEIGHTS:")
    print("-" * 50)
    
    commercial_location_weights = {}
    commercial_total_location = 0
    
    for field in location_fields:
        weight = COMMERCIAL_PROPERTY_SCORE_WEIGHTS.get(field, 0)
        commercial_location_weights[field] = weight
        commercial_total_location += weight
        print(f"{field}: {weight}")
    
    print(f"\nTotal Commercial Location Weight: {commercial_total_location}")
    print(f"Total Commercial Weight (All Fields): {MAX_COMMERCIAL_SCORE}")
    print(f"Location Weight as % of Total: {(commercial_total_location / MAX_COMMERCIAL_SCORE * 100):.1f}%")
    
    # Compare residential vs commercial
    print(f"\nCOMPARISON:")
    print("-" * 50)
    print(f"Residential Location Weight: {residential_total_location}")
    print(f"Commercial Location Weight: {commercial_total_location}")
    print(f"Difference: {commercial_total_location - residential_total_location} (Commercial gets {commercial_total_location - residential_total_location} more points)")
    
    # Show field-by-field comparison
    print(f"\nFIELD-BY-FIELD COMPARISON:")
    print("-" * 70)
    print(f"{'Field':<30} {'Residential':<12} {'Commercial':<12} {'Difference':<12}")
    print("-" * 70)
    
    for field in location_fields:
        res_weight = residential_location_weights[field]
        com_weight = commercial_location_weights[field]
        diff = com_weight - res_weight
        print(f"{field:<30} {res_weight:<12} {com_weight:<12} {diff:+<12}")
    
    return {
        'residential_location_total': residential_total_location,
        'commercial_location_total': commercial_total_location,
        'residential_total': MAX_RESIDENTIAL_SCORE,
        'commercial_total': MAX_COMMERCIAL_SCORE,
        'residential_location_weights': residential_location_weights,
        'commercial_location_weights': commercial_location_weights
    }


def show_weight_distribution():
    """
    Show the distribution of weights across different model categories.
    """
    print("\n=== Weight Distribution Analysis ===\n")
    
    # Analyze residential weights
    print("RESIDENTIAL WEIGHT DISTRIBUTION:")
    print("-" * 40)
    
    residential_by_model = {}
    for field, weight in RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.items():
        model_name = field.split('.')[0]
        if model_name not in residential_by_model:
            residential_by_model[model_name] = 0
        residential_by_model[model_name] += weight
    
    for model, total_weight in sorted(residential_by_model.items()):
        percentage = (total_weight / MAX_RESIDENTIAL_SCORE) * 100
        print(f"{model:<40} {total_weight:>6} ({percentage:>5.1f}%)")
    
    print(f"{'TOTAL':<40} {MAX_RESIDENTIAL_SCORE:>6} (100.0%)")
    
    # Analyze commercial weights
    print(f"\nCOMMERCIAL WEIGHT DISTRIBUTION:")
    print("-" * 40)
    
    commercial_by_model = {}
    for field, weight in COMMERCIAL_PROPERTY_SCORE_WEIGHTS.items():
        model_name = field.split('.')[0]
        if model_name not in commercial_by_model:
            commercial_by_model[model_name] = 0
        commercial_by_model[model_name] += weight
    
    for model, total_weight in sorted(commercial_by_model.items()):
        percentage = (total_weight / MAX_COMMERCIAL_SCORE) * 100
        print(f"{model:<40} {total_weight:>6} ({percentage:>5.1f}%)")
    
    print(f"{'TOTAL':<40} {MAX_COMMERCIAL_SCORE:>6} (100.0%)")


def validate_weight_consistency():
    """
    Validate that the weight configuration is consistent and complete.
    """
    print("\n=== Weight Configuration Validation ===\n")
    
    # Check that all location fields are present
    location_fields = ["Property.country", "Property.state", "Property.city", "Property.community"]
    
    missing_residential = [field for field in location_fields if field not in RESIDENTIAL_PROPERTY_SCORE_WEIGHTS]
    missing_commercial = [field for field in location_fields if field not in COMMERCIAL_PROPERTY_SCORE_WEIGHTS]
    
    if missing_residential:
        print(f"❌ Missing residential location fields: {missing_residential}")
    else:
        print("✅ All location fields present in residential weights")
    
    if missing_commercial:
        print(f"❌ Missing commercial location fields: {missing_commercial}")
    else:
        print("✅ All location fields present in commercial weights")
    
    # Check for zero weights
    zero_weight_residential = [field for field, weight in RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.items() if weight == 0]
    zero_weight_commercial = [field for field, weight in COMMERCIAL_PROPERTY_SCORE_WEIGHTS.items() if weight == 0]
    
    if zero_weight_residential:
        print(f"⚠️  Residential fields with zero weight: {zero_weight_residential}")
    else:
        print("✅ No residential fields with zero weight")
    
    if zero_weight_commercial:
        print(f"⚠️  Commercial fields with zero weight: {zero_weight_commercial}")
    else:
        print("✅ No commercial fields with zero weight")
    
    # Validate max score calculations
    calculated_residential_max = sum(RESIDENTIAL_PROPERTY_SCORE_WEIGHTS.values())
    calculated_commercial_max = sum(COMMERCIAL_PROPERTY_SCORE_WEIGHTS.values())
    
    if calculated_residential_max == MAX_RESIDENTIAL_SCORE:
        print("✅ Residential max score calculation is correct")
    else:
        print(f"❌ Residential max score mismatch: calculated={calculated_residential_max}, stored={MAX_RESIDENTIAL_SCORE}")
    
    if calculated_commercial_max == MAX_COMMERCIAL_SCORE:
        print("✅ Commercial max score calculation is correct")
    else:
        print(f"❌ Commercial max score mismatch: calculated={calculated_commercial_max}, stored={MAX_COMMERCIAL_SCORE}")


if __name__ == "__main__":
    print("To verify location weights:")
    print("python manage.py shell")
    print(">>> from rezio.properties.utils.verify_location_weights import verify_location_weights")
    print(">>> verify_location_weights()")
    print()
    print("Other available functions:")
    print(">>> show_weight_distribution()")
    print(">>> validate_weight_consistency()")
