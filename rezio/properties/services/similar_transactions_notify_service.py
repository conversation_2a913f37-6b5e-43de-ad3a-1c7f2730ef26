import logging
import time
import datetime
from typing import List, Dict, Any
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict

from django.core.cache import cache
from django.conf import settings
from django.db.models import QuerySet
from django.db import transaction

from rezio.notifications.models import NotificationType, Notification
from rezio.properties.constants import QUERY_PARAMS
from rezio.properties.models import (
    Property,
    PropertyPublishStatus,
    PropertyCoOwner,
    AgentAssociatedProperty,
    UserLevelPropertyData,
)
from rezio.properties.text_choices import OwnerIntentForProperty
from rezio.properties.utils import (
    build_property_address,
    get_exchange_rates,
    get_property_building_name,
)
from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI
from rezio.notifications.notification_handlers import NotificationHandler
from rezio.user.utils import get_agent_role_object, get_investor_role_object
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import general_exception_handler
from rezio.utils.rate_limiter import RateLimiter
from rezio.utils.metrics import MetricsTracker

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class SimilarTransactionNotifier:
    def __init__(self):
        self.property_monitor = PropertyMonitorAPI()
        self.notification_handler = NotificationHandler()
        self.agent_role = get_agent_role_object()
        self.investor_role = get_investor_role_object()
        self.batch_size = getattr(settings, "SIMILAR_TRANSACTIONS_BATCH_SIZE", 10)
        self.max_workers = getattr(settings, "SIMILAR_TRANSACTIONS_MAX_WORKERS", 5)
        self.rate_limiter = RateLimiter(
            "property_monitor",
            getattr(settings, "PROPERTY_MONITOR_MAX_REQUESTS", 5),
            getattr(settings, "PROPERTY_MONITOR_TIME_WINDOW", 1),
        )
        self.past_days_to_fetch_transactions = getattr(
            settings, "PAST_DAYS_TO_FETCH_TRANSACTIONS", 1
        )
        self.metrics = MetricsTracker("similar_transactions")

    def trigger_notification(self, property_ids):
        """Main execution method that orchestrates the notification process"""
        try:
            logger.info("Starting similar transactions notification process")
            user_level_data = (
                UserLevelPropertyData.objects.filter(
                    property__property_monitor_address_id__isnull=False,
                    property__property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                    property__is_archived=False,
                    property__id__in=property_ids,
                )
                .select_related(
                    "property",
                    "property__state",
                    "property__community",
                    "created_by",
                    "created_by_role",
                )
                .order_by("-created_ts")
            )
            logger.info(f"Found {len(user_level_data)} properties to process")

            for property_obj in user_level_data:
                try:
                    logger.info(f"Processing property ID: {property_obj.id}")
                    self._process_property_transactions(property_obj)
                    logger.info(
                        f"Successfully processed property ID: {property_obj.id} for {property_obj.created_by.id} - {property_obj.created_by_role.name}"
                    )
                except Exception as property_error:
                    logger.error(
                        f"Error processing property {property_obj.id} for {property_obj.created_by.id} - {property_obj.created_by_role.name}: {str(property_error)}",
                        exc_info=True,
                    )
                    continue

            logger.info(
                "Similar transactions notification process completed successfully"
            )
            return "Similar transactions notification process completed"

        except Exception as e:
            logger.error(
                f"Error in similar transactions notification process: {str(e)}",
                exc_info=True,
            )
            raise

    def execute(self):
        """Main execution method that orchestrates the notification process"""
        start = time.time()
        try:
            logger.info("Starting similar transactions notification process")
            properties = self._get_relevant_properties()
            logger.info(f"Found {len(properties)} properties to process")

            # Process properties in batches
            for batch_start in range(0, properties.count(), self.batch_size):
                batch_end = min(batch_start + self.batch_size, len(properties))
                batch = properties[batch_start:batch_end]

                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = {
                        executor.submit(
                            self._process_property_transactions, property_obj
                        ): property_obj
                        for property_obj in batch
                    }

                    for future in as_completed(futures):
                        property_obj = futures[future]
                        try:
                            future.result()
                            self.metrics.record_success()
                            logger.info(
                                f"Successfully processed property ID: {property_obj.id} for {property_obj.created_by.id} - {property_obj.created_by_role.name}"
                            )
                        except Exception as property_error:
                            self.metrics.record_failure()
                            logger.error(
                                f"Error processing property {property_obj.id} for {property_obj.created_by.id} - {property_obj.created_by_role.name}: {str(property_error)}",
                                exc_info=True,
                            )

            # Save metrics and log summary
            self.metrics.save_metrics()
            metrics_summary = self.metrics.get_metrics()
            logger.info(f"Process completed. Metrics: {metrics_summary}")

            end = time.time()
            logger.info(f"Total time taken: {end - start} seconds")
            return "Similar transactions notification process completed"

        except Exception as e:
            self.metrics.record_failure()
            logger.error(
                f"Error in similar transactions notification process: {str(e)}",
                exc_info=True,
            )
            raise

    def _get_relevant_properties(self) -> QuerySet:
        """Fetches user level property data that need similar transaction notifications"""
        logger.debug(
            "Fetching relevant user level property data for similar transactions"
        )
        user_level_data = (
            UserLevelPropertyData.objects.filter(
                property__property_monitor_address_id__isnull=False,
                property__property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                property__is_archived=False,
            )
            .select_related(
                "property",
                "property__state",
                "property__community",
                "created_by",
                "created_by_role",
            )
            .order_by("-created_ts")
        )

        logger.info(
            f"Found {user_level_data.count()} relevant user level property data records"
        )
        return user_level_data

    def _get_transaction_categories(self, property_obj):
        """Determines transaction categories based on owner intent"""
        logger.debug(f"Getting transaction categories for property {property_obj.id}")
        if property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT:
            return ["rent"]
        elif property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE:
            return ["sale"]
        return ["sale", "rent"]

    def _fetch_transactions(self, query_params):
        """Fetches transactions from Property Monitor API with retry logic"""
        logger.debug(f"Starting transaction fetch with params: {query_params}")
        transactions = []
        current_page = 1
        has_more_pages = True

        # Cache key for transactions
        cache_key = f"similar_transactions_{hash(str(query_params))}"
        cached_transactions = cache.get(cache_key)

        if cached_transactions:
            logger.debug("Using cached transactions")
            self.metrics.record_cache_hit()
            return cached_transactions

        self.metrics.record_cache_miss()

        while has_more_pages:
            query_params = query_params._replace(page=current_page)

            # Check rate limit
            if not self.rate_limiter.is_allowed("api"):
                wait_time = self.rate_limiter.get_wait_time("api")
                if wait_time:
                    logger.warning(f"Rate limit reached. Waiting {wait_time} seconds")
                    time.sleep(wait_time)

            for attempt in range(settings.SIMILAR_TRANSACTIONS_ATTEMPTS):
                try:
                    logger.debug(
                        f"Fetching transactions page {current_page}, attempt {attempt + 1}"
                    )
                    api_start_time = time.time()
                    response = self.property_monitor.get_property_listings(
                        query_params, fetch_similar_transactions=True
                    )
                    api_response_time = time.time() - api_start_time
                    self.metrics.record_api_call(api_response_time)

                    if not response or "data" not in response:
                        logger.warning("No data received from Property Monitor API")
                        break

                    page_transactions = response["data"]
                    if not page_transactions:
                        logger.debug("No more transactions to fetch")
                        has_more_pages = False
                        break

                    transactions.extend(page_transactions)
                    logger.debug(
                        f"Successfully fetched {len(page_transactions)} transactions"
                    )

                    if current_page >= response.get("total_pages", 1):
                        logger.debug("Reached last page of transactions")
                        has_more_pages = False
                        break

                    current_page += 1
                    break

                except Exception as e:
                    if attempt == settings.SIMILAR_TRANSACTIONS_ATTEMPTS - 1:
                        logger.error(
                            f"Failed to fetch transactions after {settings.SIMILAR_TRANSACTIONS_ATTEMPTS} attempts: {str(e)}",
                            exc_info=True,
                        )
                        has_more_pages = False
                        break
                    logger.warning(
                        f"Attempt {attempt + 1} failed, retrying after {settings.SIMILAR_TRANSACTIONS_WAIT_TIME} seconds"
                    )
                    time.sleep(settings.SIMILAR_TRANSACTIONS_WAIT_TIME)

        # Cache the transactions for 1 hour
        if transactions:
            cache.set(cache_key, transactions, timeout=3600)

        logger.info(f"Total transactions fetched: {len(transactions)}")
        return transactions

    def _get_recipients(self, property_obj):
        """Gets list of users to notify about similar transactions"""
        logger.debug(f"Getting recipients for property {property_obj.id}")
        recipients = []

        # Add owner if verified
        if (
            property_obj.owner_verified
            and property_obj.owner
            and property_obj.owner.user
        ):
            recipients.append(
                {"profile": property_obj.owner, "role": self.investor_role}
            )
            logger.debug(
                f"Added verified owner {property_obj.owner.user.id} to recipients"
            )

        # Add co-owners
        for co_owner in PropertyCoOwner.objects.filter(
            property=property_obj, is_associated=True
        ):
            if co_owner:
                recipients.append(
                    {"profile": co_owner.co_owner, "role": self.investor_role}
                )
                logger.debug(
                    f"Added co-owner {co_owner.co_owner.user.id} to recipients"
                )

        # Add associated agents
        for agent_assoc in AgentAssociatedProperty.objects.filter(
            property=property_obj,
            is_associated=True,
        ):
            recipients.append(
                {"profile": agent_assoc.agent_profile, "role": self.agent_role}
            )
            logger.debug(
                f"Added agent {agent_assoc.agent_profile.user.id} to recipients"
            )

        logger.info(f"Total recipients found: {len(recipients)}")
        return recipients

    def _format_price(self, amount, currency):
        """Formats price based on currency and amount"""
        logger.debug(f"Formatting price {amount} {currency}")
        if currency == "INR":
            if amount >= 10000000:
                return f"{amount/10000000:.2f}Cr"
            elif amount >= 100000:
                return f"{amount/100000:.2f}L"
            return f"{amount/1000:.2f}K"
        else:
            if amount >= 1000000:
                return f"{amount/1000000:.2f}M"
            return f"{amount/1000:.2f}K"

    def _get_property_filters(
        self, user_level_data, property_obj, start_date, end_date
    ):
        total_area = (
            round(user_level_data.total_area) if user_level_data.total_area else None
        )
        twenty_percent = round(total_area * 0.20) if total_area else 0
        number_of_bedrooms = user_level_data.number_of_bedrooms
        if not number_of_bedrooms is None:
            if number_of_bedrooms == 0:
                number_of_bedrooms = "s"
            else:
                number_of_bedrooms = str(number_of_bedrooms)
        else:
            number_of_bedrooms = ""
        filters = {
            "property_type": user_level_data.property_type,
            "number_of_bedrooms": number_of_bedrooms,
            "min_area": str(round(total_area - twenty_percent) if total_area else None),
            "max_area": str(round(total_area + twenty_percent) if total_area else None),
            "start_date": str(start_date),
            "end_date": str(end_date),
        }
        if property_obj.community.property_monitor_location_id:
            filters.update(
                {
                    "master_development": property_obj.community.name,
                    "sub_loc_1": property_obj.community.sub_loc_1,
                    "sub_loc_2": property_obj.community.sub_loc_2,
                    "sub_loc_3": property_obj.community.sub_loc_3,
                    "sub_loc_4": property_obj.community.sub_loc_4,
                    "building": get_property_building_name(property_obj),
                    "community_string": build_property_address(property_obj),
                }
            )
        else:
            filters.update(
                {
                    "master_development": "",
                    "sub_loc_1": "",
                    "sub_loc_2": "",
                    "sub_loc_3": "",
                    "sub_loc_4": "",
                    "building": "",
                    "community_string": "",
                }
            )
        return filters

    @staticmethod
    def get_address(obj: Property):

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community else None,
        ]

        return ", ".join(filter(None, address_parts))

    @general_exception_handler
    def _process_property_transactions(self, user_level_data):
        """Processes transactions for a user level property data and sends notifications"""
        logger.info(
            f"Processing transactions for user level data ID: {user_level_data.id}"
        )
        property_obj = user_level_data.property
        property_type = user_level_data.property_type
        bedrooms = user_level_data.number_of_bedrooms

        if not property_type or bedrooms is None:
            logger.warning(
                f"User level data {user_level_data.id} missing property type or bedrooms"
            )
            return

        if bedrooms == 0:
            bedrooms = "s"

        end_date = start_date = (
            datetime.date.today()
            - datetime.timedelta(days=self.past_days_to_fetch_transactions)
        ).strftime("%Y-%m-%d")
        logger.debug(f"Similar transactions date set to: {start_date}")

        categories = self._get_transaction_categories(property_obj)
        filters = self._get_property_filters(
            user_level_data, property_obj, start_date, end_date
        )
        notifications_to_process = []

        for category in categories:
            logger.debug(
                f"Processing {category} transactions for user level data {user_level_data.id}"
            )
            query_params = QUERY_PARAMS(
                emirate=property_obj.state.name,
                master_development=filters.get("master_development"),
                sub_loc_1=filters.get("sub_loc_1"),
                sub_loc_2=filters.get("sub_loc_2"),
                sub_loc_3=filters.get("sub_loc_3"),
                sub_loc_4=filters.get("sub_loc_4"),
                category=category,
                property_types=property_type,
                min_area=filters.get("min_area"),
                max_area=filters.get("max_area"),
                number_of_bedrooms=bedrooms,
                min_price="",
                max_price="",
                contract_type="",
                start_date=start_date,
                end_date=end_date,
                page=1,
                page_size=15,
            )

            transactions = self._fetch_transactions(query_params)

            if not transactions:
                logger.info(
                    f"No {category} transactions found for user level data {user_level_data.id}"
                )
                continue

            # Add category to transactions
            for transaction in transactions:
                transaction["category"] = category

                property_address = self.get_address(property_obj)
                logger.info(f"Processing transaction with address {transaction}")
                if transaction.get("address_id") and transaction["address_id"] not in [
                    "",
                    0,
                ]:
                    logger.info("Transaction address ID: %s", transaction["address_id"])
                    rezio_user_property = Property.objects.filter(
                        property_monitor_address_id=transaction["address_id"]
                    ).exists()
                    logger.info(
                        "Transaction for rezio_user_property: %s", rezio_user_property
                    )
                    # Get users to notify
                    users_to_notify = []
                    
                    # Always add the user from user level data
                    users_to_notify.append({
                        "user": user_level_data.created_by,
                        "role": user_level_data.created_by_role
                    })

                    # If user level data is from investor, add co-owners
                    if user_level_data.created_by_role.name == "Investor":
                        co_owners = PropertyCoOwner.objects.filter(
                            property=property_obj,
                            is_associated=True,
                            co_owner__isnull=False
                        ).select_related("co_owner", "co_owner__user")
                        
                        for co_owner in co_owners:
                            users_to_notify.append({
                                "user": co_owner.co_owner.user,
                                "role": self.investor_role
                            })

                    if rezio_user_property:
                        title = "Property Status Change"
                        if category == "sale":
                            notification_type = (
                                NotificationType.SIMILAR_PROPERTY_SOLD.value
                            )
                            message = f"Property {property_address} has just been Sold!"
                        else:
                            notification_type = (
                                NotificationType.SIMILAR_PROPERTY_RENTED.value
                            )
                            message = (
                                f"Property {property_address} has just been Rented!"
                            )

                        # Generate notifications for all users
                        for user_data in users_to_notify:
                            push_notification_data = {
                                "notification_category": "SIMILAR_PROPERTY SOLD/RENTED Update",
                                "priority_level": "HIGH",
                                "user_role": user_data["role"].name,
                                "user_id": user_data["user"].id,
                                "notification_type": notification_type,
                                "property_id": str(property_obj.id),
                            }
                            push_notification_data.update(filters)

                            # Prepare notification data
                            notification_data = {
                                "user": user_data["user"],
                                "role": user_data["role"],
                                "notification_type": notification_type,
                                "title": title,
                                "message": message,
                                "metadata": {
                                    "property_id": str(property_obj.id),
                                    "transaction": transaction,
                                    "property_type": property_type,
                                    "address": property_address,
                                },
                                "push_notification_data": push_notification_data,
                                "priority": "HIGH",
                                "related_property": property_obj,
                                "related_user": user_data["user"],
                                "related_user_role": user_data["role"],
                            }
                            logger.info(
                                "Adding notification for user: %s, Notification data: %s",
                                user_data["user"],
                                notification_data,
                            )
                            notifications_to_process.append(notification_data)
                    else:

                        try:
                            logger.debug(
                                f"Processing notification for user {user_level_data.created_by.id}"
                            )

                            # Generate notifications for all users
                            for user_data in users_to_notify:
                                # Get preferred currency based on user role
                                if user_data["role"].name == "Agent":
                                    preferred_currency = user_data["user"].agentprofile.preferred_currency_code
                                else:
                                    preferred_currency = user_data["user"].investorprofile.preferred_currency_code

                                # Get exchange rate
                                cache_key = f"exchange_rate_AED_{preferred_currency}_{datetime.datetime.today().date()}"
                                exchange_rate = cache.get(cache_key)

                                if not exchange_rate:
                                    logger.debug(
                                        f"Fetching exchange rate for AED to {preferred_currency}"
                                    )
                                    exchange_rate = get_exchange_rates(
                                        "AED", preferred_currency
                                    )
                                    cache.set(
                                        cache_key,
                                        exchange_rate,
                                        timeout=(24 - datetime.datetime.now().hour) * 3600,
                                    )

                                # Convert and format price
                                converted_price = (
                                    transaction["total_sales_price"] * exchange_rate
                                )
                                price_str = self._format_price(
                                    converted_price, preferred_currency
                                )

                                # Set notification content
                                title = (
                                    "A property like yours was sold"
                                    if category == "sale"
                                    else "A property like yours was rented"
                                )
                                message = (
                                    f"{'Studio' if bedrooms == 's' else bedrooms} BR in {property_address} just "
                                    f"{'sold' if category == 'sale' else 'rented'} for "
                                    f"{preferred_currency} {price_str}"
                                    f"{'/year' if category == 'rent' else ''}. "
                                    "See all similar transactions"
                                )
                                notification_type = (
                                    NotificationType.SIMILAR_TRANSACTION_UPDATE.value
                                )

                                filters.update({"category": category})
                                push_notification_data = {
                                    "notification_category": "SIMILAR_TRANSACTION_UPDATE",
                                    "priority_level": "HIGH",
                                    "user_role": user_data["role"].name,
                                    "user_id": user_data["user"].id,
                                    "notification_type": notification_type,
                                }
                                push_notification_data.update(filters)

                                # Prepare notification data
                                notification_data = {
                                    "user": user_data["user"],
                                    "role": user_data["role"],
                                    "notification_type": notification_type,
                                    "title": title,
                                    "message": message,
                                    "metadata": {
                                        "property_id": str(property_obj.id),
                                        "transaction": transaction,
                                        "property_type": property_type,
                                        "bedrooms": bedrooms,
                                        "filters": filters,
                                    },
                                    "push_notification_data": push_notification_data,
                                    "priority": "HIGH",
                                    "related_property": property_obj,
                                }
                                notifications_to_process.append(notification_data)

                        except Exception as notification_error:
                            logger.error(
                                f"Failed to prepare notification for user level data {user_level_data.id} to user {user_level_data.created_by.id}: {str(notification_error)}",
                                exc_info=True,
                            )

        # Process notifications in batches
        if notifications_to_process:
            batch_size = 50  # Process notifications in smaller batches
            for i in range(0, len(notifications_to_process), batch_size):
                batch = notifications_to_process[i : i + batch_size]
                for notification_data in batch:
                    try:
                        self.notification_handler._create_and_send_notification(
                            **notification_data
                        )
                        logger.info(
                            f"Successfully processed notification for user {notification_data['user'].id}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Failed to process notification for user {notification_data['user'].id}: {str(e)}",
                            exc_info=True,
                        )
