"""
Dynamic property scoring service that uses ModelName.field_name format for weights.
This approach is more maintainable and flexible than hardcoded scoring methods.
"""

import logging
from typing import Optional, Dict, Any

from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist
from django.apps import apps

from rezio.properties.models import (
    UserLevelPropertyData,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyFeatures,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    PropertyUnitSections,
)
from rezio.properties.scoring_constants import get_weight_mapping, get_max_score
from rezio.properties.text_choices import PropertyCategory, OwnerIntentForProperty

logger = logging.getLogger(__name__)


class DynamicPropertyScoringService:
    """Dynamic service class for calculating property scores using ModelName.field_name format."""

    def __init__(self):
        self.logger = logger

    def calculate_user_level_property_score(self, user_level_property_data_id: int) -> float:
        """
        Calculate the property score for a given UserLevelPropertyData.

        Args:
            user_level_property_data_id (int): The ID of the UserLevelPropertyData to score

        Returns:
            float: The calculated property score (0-100)
        """
        try:
            user_level_data = UserLevelPropertyData.objects.get(id=user_level_property_data_id)
            return self._calculate_score_for_user_level_data(user_level_data)
        except UserLevelPropertyData.DoesNotExist:
            self.logger.error(f"UserLevelPropertyData with ID {user_level_property_data_id} does not exist")
            return 0.0
        except Exception as e:
            self.logger.error(f"Error calculating score for UserLevelPropertyData {user_level_property_data_id}: {str(e)}")
            return 0.0

    def _calculate_score_for_user_level_data(self, user_level_data: UserLevelPropertyData) -> float:
        """
        Calculate the score for a UserLevelPropertyData object using dynamic field access.

        Args:
            user_level_data (UserLevelPropertyData): The UserLevelPropertyData object to score

        Returns:
            float: The calculated property score (0-100)
        """
        # Get property category from the related Property
        property_category = user_level_data.property.property_category
        owner_intent = user_level_data.property.owner_intent
        weight_mapping = get_weight_mapping(property_category)
        max_score = get_max_score(property_category)

        total_score = 0.0

        # Calculate scores dynamically using the weight mapping
        for weight_key, weight_value in weight_mapping.items():
            try:
                field_score = self._calculate_field_score(
                    user_level_data, weight_key, weight_value, owner_intent
                )
                total_score += field_score
            except Exception as e:
                self.logger.warning(f"Error calculating score for {weight_key}: {str(e)}")
                continue

        # Normalize score to 0-100 range
        normalized_score = (total_score / max_score) * 100 if max_score > 0 else 0.0

        return min(100.0, max(0.0, normalized_score))

    def _calculate_field_score(self, user_level_data: UserLevelPropertyData,
                              weight_key: str, weight_value: int, owner_intent: str) -> float:
        """
        Calculate score for a specific field using dynamic model access.

        Args:
            user_level_data: The UserLevelPropertyData object
            weight_key: The weight key in format "ModelName.field_name"
            weight_value: The weight value for this field
            owner_intent: The property's owner intent

        Returns:
            float: The calculated score for this field
        """
        if '.' not in weight_key:
            self.logger.warning(f"Invalid weight key format: {weight_key}")
            return 0.0

        model_name, field_name = weight_key.split('.', 1)

        # Apply conditional logic for financial fields based on owner intent
        if self._should_skip_field_based_on_owner_intent(weight_key, owner_intent):
            return 0.0

        # Get the model instance and field value
        model_instance = self._get_model_instance(user_level_data, model_name)
        if not model_instance:
            return 0.0

        field_value = self._get_field_value(model_instance, field_name)

        # Return weight if field has a truthy value
        if self._is_field_valuable(field_value, field_name):
            return weight_value

        return 0.0

    def _should_skip_field_based_on_owner_intent(self, weight_key: str, owner_intent: str) -> bool:
        """
        Determine if a field should be skipped based on owner intent.

        Args:
            weight_key: The weight key in format "ModelName.field_name"
            owner_intent: The property's owner intent

        Returns:
            bool: True if field should be skipped
        """
        # Apply conditional logic for asking_price and expected_rent
        if weight_key == "UserLevelPropertyFinancialDetails.asking_price":
            # Only include asking_price for AVAILABLE_FOR_SALE
            return owner_intent != OwnerIntentForProperty.AVAILABLE_FOR_SALE
        elif weight_key == "UserLevelPropertyFinancialDetails.expected_rent":
            # Only include expected_rent for AVAILABLE_FOR_RENT
            return owner_intent != OwnerIntentForProperty.AVAILABLE_FOR_RENT

        return False

    def _get_model_instance(self, user_level_data: UserLevelPropertyData, model_name: str):
        """
        Get the model instance based on the model name.

        Args:
            user_level_data: The UserLevelPropertyData object
            model_name: The name of the model

        Returns:
            Model instance or None
        """
        try:
            if model_name == "Property":
                return user_level_data.property
            elif model_name == "UserLevelPropertyData":
                return user_level_data
            elif model_name == "UserLevelPropertyAvailabilityAndStatus":
                return user_level_data.property_user_level_availability_and_status
            elif model_name == "UserLevelPropertyFinancialDetails":
                return user_level_data.property_user_level_financial_details
            elif model_name == "UserLevelPropertyFeatures":
                return user_level_data.property_user_level_features
            elif model_name == "PropertyFloorPlan":
                # Return True if any floor plans exist for this property
                return PropertyFloorPlan.objects.filter(property=user_level_data.property).first()
            elif model_name == "PropertyPaymentPlan":
                # Return True if any payment plans exist for this property
                return PropertyPaymentPlan.objects.filter(property=user_level_data.property).first()
            elif model_name == "PropertyUnitSections":
                # Return True if any unit sections exist for this property
                return PropertyUnitSections.objects.filter(property=user_level_data.property).first()
            else:
                self.logger.warning(f"Unknown model name: {model_name}")
                return None
        except ObjectDoesNotExist:
            return None
        except Exception as e:
            self.logger.error(f"Error getting model instance for {model_name}: {str(e)}")
            return None

    def _get_field_value(self, model_instance, field_name: str):
        """
        Get the field value from a model instance.

        Args:
            model_instance: The model instance
            field_name: The field name

        Returns:
            The field value or None
        """
        try:
            if hasattr(model_instance, field_name):
                return getattr(model_instance, field_name)
            else:
                self.logger.warning(f"Field {field_name} not found in {model_instance.__class__.__name__}")
                return None
        except Exception as e:
            self.logger.error(f"Error getting field value {field_name}: {str(e)}")
            return None

    def _is_field_valuable(self, field_value, field_name: str) -> bool:
        """
        Determine if a field value should contribute to the score.

        Args:
            field_value: The field value
            field_name: The field name

        Returns:
            bool: True if field should contribute to score
        """
        if field_value is None:
            return False

        # Handle different field types
        if isinstance(field_value, bool):
            return field_value
        elif isinstance(field_value, (int, float)):
            return field_value > 0
        elif isinstance(field_value, str):
            return bool(field_value.strip())
        elif hasattr(field_value, 'exists'):  # QuerySet or related manager
            return field_value.exists()
        elif hasattr(field_value, 'id'):  # Foreign key relationship (Model instance)
            return True  # If we have a related object, it contributes to score
        else:
            return bool(field_value)

    @transaction.atomic
    def update_user_level_property_score(self, user_level_property_data_id: int) -> bool:
        """
        Calculate and update the property score for a given UserLevelPropertyData.

        Args:
            user_level_property_data_id (int): The ID of the UserLevelPropertyData to update

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            user_level_data = UserLevelPropertyData.objects.select_for_update().get(id=user_level_property_data_id)
            new_score = self._calculate_score_for_user_level_data(user_level_data)

            user_level_data.property_score = new_score
            user_level_data.save(update_fields=['property_score'])

            self.logger.info(f"Updated property score for UserLevelPropertyData {user_level_property_data_id}: {new_score}")
            return True

        except UserLevelPropertyData.DoesNotExist:
            self.logger.error(f"UserLevelPropertyData with ID {user_level_property_data_id} does not exist")
            return False
        except Exception as e:
            self.logger.error(f"Error updating property score for UserLevelPropertyData {user_level_property_data_id}: {str(e)}")
            return False

    def bulk_update_user_level_property_scores(self, user_level_property_data_ids: list = None) -> Dict[str, int]:
        """
        Update property scores for multiple UserLevelPropertyData objects.

        Args:
            user_level_property_data_ids (list, optional): List of UserLevelPropertyData IDs to update. If None, updates all.

        Returns:
            dict: Summary of the update operation
        """
        if user_level_property_data_ids is None:
            user_level_data_objects = UserLevelPropertyData.objects.all()
        else:
            user_level_data_objects = UserLevelPropertyData.objects.filter(id__in=user_level_property_data_ids)

        updated_count = 0
        failed_count = 0

        for user_level_data in user_level_data_objects:
            try:
                new_score = self._calculate_score_for_user_level_data(user_level_data)
                user_level_data.property_score = new_score
                user_level_data.save(update_fields=['property_score'])
                updated_count += 1
            except Exception as e:
                self.logger.error(f"Failed to update score for UserLevelPropertyData {user_level_data.id}: {str(e)}")
                failed_count += 1

        self.logger.info(f"Bulk update completed: {updated_count} updated, {failed_count} failed")

        return {
            'updated': updated_count,
            'failed': failed_count,
            'total': updated_count + failed_count
        }

    def get_detailed_score_breakdown(self, user_level_data: UserLevelPropertyData) -> Dict:
        """
        Get a detailed breakdown of how the score is calculated for each field.

        Args:
            user_level_data: The UserLevelPropertyData object

        Returns:
            dict: Detailed breakdown with scores for each field
        """
        property_category = user_level_data.property.property_category
        owner_intent = user_level_data.property.owner_intent
        weight_mapping = get_weight_mapping(property_category)

        breakdown = {}
        total_score = 0.0

        for weight_key, weight_value in weight_mapping.items():
            try:
                field_score = self._calculate_field_score(
                    user_level_data, weight_key, weight_value, owner_intent
                )
                breakdown[weight_key] = {
                    'weight': weight_value,
                    'score': field_score,
                    'applied': field_score > 0
                }
                total_score += field_score
            except Exception as e:
                breakdown[weight_key] = {
                    'weight': weight_value,
                    'score': 0.0,
                    'applied': False,
                    'error': str(e)
                }

        return {
            'breakdown': breakdown,
            'total_raw_score': total_score,
            'max_possible_score': get_max_score(property_category),
            'normalized_score': (total_score / get_max_score(property_category)) * 100 if get_max_score(property_category) > 0 else 0.0
        }


# Convenience function for external use
def calculate_and_update_user_level_property_score_dynamic(user_level_property_data_id: int) -> bool:
    """
    Convenience function to calculate and update a UserLevelPropertyData's score using dynamic approach.

    Args:
        user_level_property_data_id (int): The ID of the UserLevelPropertyData to update

    Returns:
        bool: True if successful, False otherwise
    """
    service = DynamicPropertyScoringService()
    return service.update_user_level_property_score(user_level_property_data_id)
