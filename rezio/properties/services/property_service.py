import datetime
import logging
import traceback

from django.apps import apps
from django.conf import settings
from django.db import transaction
from django.db.models import <PERSON>r<PERSON>ield
from django.db.models.functions import Cast
from django.db.models.signals import post_save
from django.utils import timezone

from rezio.properties.models import (
    Property,
    DeletedProperty,
    Community,
    PropertyAvailabilityAndStatus,
    PropertyRentalUnitHistory,
    PropertySalesUnitHistory,
    PropertyUnitSections,
    UnitSectionMedia,
    AgentAssociatedProperty,
    PropertyCompletionState,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    PropertyVerifiedDataFields,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyData,
)
from rezio.properties.text_choices import (
    PropertyAgentType,
    PropertyAvailabilityStatus,
    PropertyMonitorSalesEvidence,
    UserRequestActions,
    OwnerIntentForProperty,
    PropertyPublishStatus,
)
from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import PRESIGNED_POST_STRUCTURES, KEY
from rezio.rezio.custom_error_codes import UNKNOWN_ERROR_IN_PM_DATA_PROCESS
from rezio.user.models import AgentProfile
from rezio.user.text_choices import AgentSubscriptionPlanChoices
from rezio.user.utils import (
    get_role_object,
    get_list_of_object_with_filters,
    get_object_with_filters,
    get_or_create_db_object,
    get_agent_role_object,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_ERROR_CODE,
)
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
    ResourceNotFoundException,
)
from rezio.utils.decorators import log_input_output
from rezio.utils.text_choices import DataSource

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def validate_serializer(serializer):
    if not serializer.is_valid():
        logger.error("Invalid data sent :" + str(serializer.errors))
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: serializer.errors,
            }
        )
    return serializer.validated_data


@log_input_output
def get_unit_details(validated_data):
    try:
        property_monitor = PropertyMonitorAPI()
        unit_details = property_monitor.get_unit_details(
            validated_data.get("address_id")
        )

        unit_details["unit_bua_sqft"] = (
            unit_details.get("unit_bua_sqft")
            if unit_details.get("unit_bua_sqft") != 0.0
            else None
        )
        unit_details["suite_area_sqft"] = (
            unit_details.get("suite_area_sqft")
            if unit_details.get("suite_area_sqft") != 0.0
            else None
        )
        unit_details["floor"] = (
            unit_details.get("floor") if unit_details.get("floor") != "" else None
        )

        if (
            unit_details["balcony"] == "Y"
            and unit_details.get("balcony_size_sqft") == 0.0
        ) or unit_details.get("balcony_size_sqft") == 0.0:
            unit_details["balcony_size_sqft"] = None

        if unit_details["property_type"] not in [
            "Apartment",
            "Villa",
            "Townhouse",
            "Residential Plot",
            "Residential Land",
        ]:
            unit_details["property_type"] = "Apartment"

        if unit_details["no_beds"] == "s":
            unit_details["no_beds"] = 0
        elif unit_details["no_beds"] == "" or unit_details["no_beds"] == "0":
            unit_details["no_beds"] = None

        unit_details["maid"] = (
            unit_details["maid"] if unit_details["maid"] != "" else None
        )
        unit_details["study"] = (
            unit_details["study"] if unit_details["study"] != "" else None
        )

        return unit_details
    except InvalidSerializerDataException:
        raise
    except Exception as error:
        logger.error(f"Error in get_unit_details - {error}")
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: "Error while processing unit details",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: UNKNOWN_ERROR_IN_PM_DATA_PROCESS.get("message"),
                    KEY_ERROR_CODE: UNKNOWN_ERROR_IN_PM_DATA_PROCESS.get("code"),
                },
            }
        )


def save_community_data(validated_data, property_obj):
    community, created = Community.objects.get_or_create(
        property_monitor_location_id=validated_data.get("location_id")
    )
    if created:
        community.name = validated_data.get("master_community")
        community.sub_loc_1 = validated_data.get("sub_loc_1")
        community.sub_loc_2 = validated_data.get("sub_loc_2")
        community.sub_loc_3 = validated_data.get("sub_loc_3")
        community.sub_loc_4 = validated_data.get("sub_loc_4")
        community.save()

    return community


@log_input_output
def update_rental_history(
    rental_data,
    property_obj,
    user,
    role_obj,
    property_financials,
    created,
    user_level_property_financial,
):
    from rezio.properties.serializers import PropertyRentalUnitHistorySerializer

    try:
        latest_rent_data_updated = False
        for each_rental_data in rental_data:
            rental_instance = PropertyRentalUnitHistory.objects.filter(
                property_id=property_obj.id,
                start_date=each_rental_data["start_date"],
                end_date=each_rental_data["end_date"],
            ).first()

            # each_rental_data['property_id'] = property_obj.id
            each_rental_data["rent_recurrence"] = each_rental_data["rent_sequence"]
            each_rental_data["updated-by"] = user

            if rental_instance:
                # If the record exists, update the existing instance
                rental_serializer = PropertyRentalUnitHistorySerializer(
                    rental_instance, data=each_rental_data
                )
            else:
                # If the record doesn't exist, create a new instance
                each_rental_data["created_by"] = user
                rental_serializer = PropertyRentalUnitHistorySerializer(
                    data=each_rental_data
                )

            if rental_serializer.is_valid():
                rental_serializer.save(property=property_obj)
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data received for rental information from Property Monitor",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: rental_serializer.errors},
                    }
                )
            current_date = datetime.date.today()
            start_date = datetime.datetime.strptime(
                each_rental_data["start_date"], "%Y-%m-%d"
            ).date()
            end_date = datetime.datetime.strptime(
                each_rental_data["end_date"], "%Y-%m-%d"
            ).date()
            if (start_date < current_date < end_date) and not latest_rent_data_updated:
                latest_rent_data_updated = True
                user_level_data = user_level_property_financial.property_level_data
                (
                    property_availability_status,
                    availability_status_created,
                ) = PropertyAvailabilityAndStatus.objects.get_or_create(
                    property=property_obj
                )
                (
                    user_levelproperty_availability_status,
                    user_level_availability_status_created,
                ) = UserLevelPropertyAvailabilityAndStatus.objects.get_or_create(
                    property_level_data=user_level_data
                )
                # save occupancy status
                property_availability_status.occupancy_status = (
                    PropertyAvailabilityStatus.RENTED
                )
                user_levelproperty_availability_status.occupancy_status = (
                    PropertyAvailabilityStatus.RENTED
                )

                # save rental data
                property_obj.tenancy_start_date = each_rental_data["start_date"]
                property_obj.tenancy_end_date = each_rental_data["end_date"]
                property_obj.tenancy_type = each_rental_data["rent_sequence"]
                property_obj.save()

                user_level_data.tenancy_start_date = each_rental_data["start_date"]
                user_level_data.tenancy_end_date = each_rental_data["end_date"]
                user_level_data.tenancy_type = each_rental_data["rent_sequence"]
                user_level_data.save()

                # save annual rent
                property_financials.annual_rent = each_rental_data["total_rent"]
                get_or_create_db_object(
                    PropertyVerifiedDataFields,
                    property=property_obj,
                    field_name="annual_rent",
                    value=str(each_rental_data["total_rent"]),
                    created_by_id=user,
                    created_by_role=role_obj,
                    field_type="float",
                )
                user_level_property_financial.annual_rent = each_rental_data[
                    "total_rent"
                ]
                if not created:
                    property_financials.updated_by_id = user
                    property_financials.updated_by_role = role_obj
                    user_level_property_financial.updated_by_id = user
                    user_level_property_financial.updated_by_role = role_obj
                else:
                    property_financials.created_by_id = user
                    property_financials.created_by_role = role_obj

                if not availability_status_created:
                    property_availability_status.updated_by_id = user
                    property_availability_status.updated_by_role = role_obj
                    user_levelproperty_availability_status.updated_by_id = user
                    user_levelproperty_availability_status.updated_by_role = role_obj
                else:
                    property_availability_status.created_by_id = user
                    property_availability_status.created_by_role = role_obj
                    user_levelproperty_availability_status.created_by_id = user
                    user_levelproperty_availability_status.created_by_role = role_obj
                property_financials.save()
                user_level_property_financial.save()
                property_availability_status.save()
                user_levelproperty_availability_status.save()

        # Once all data is stored, fetch all the rental history for this property
        rental_history = PropertyRentalUnitHistory.objects.filter(
            property_id=property_obj.id
        ).order_by("start_date")

        # Now, calculate the rent increase percentage and is_upward flag
        previous_rental_instance = None
        for rental_instance in rental_history:
            if previous_rental_instance:
                previous_rent = previous_rental_instance.total_rent
                current_rent = rental_instance.total_rent

                if previous_rent and current_rent:
                    rent_increase_percentage = (
                        (current_rent - previous_rent) / previous_rent
                    ) * 100
                    rental_instance.rent_increase_percentage = round(
                        rent_increase_percentage, 2
                    )

                    # Set is_upward flag based on whether the rent increased or decreased
                    rental_instance.is_upward = current_rent > previous_rent
                else:
                    rental_instance.rent_increase_percentage = None
                    rental_instance.is_upward = None

                # Save the updated rental instance with the calculated fields
                rental_instance.save()

            # Move to the next record for comparison
            previous_rental_instance = rental_instance

    except Exception as error:
        message = "Unknown error occurred in update_rental_history"
        logger.error(f"Error in update_rental_history - {message} - {error}")
        raise InternalServerException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


@log_input_output
def update_sales_history(
    sales_data,
    property_obj,
    user,
    role_obj,
    property_financials,
    created,
    user_level_property_financial,
):
    from rezio.properties.serializers import PropertySalesUnitHistorySerializer

    try:
        save_original_price = True
        for each_sales_data in sales_data:
            if (
                each_sales_data["evidence"]
                in [
                    PropertyMonitorSalesEvidence.OFF_PLAN_SALE.label,
                    PropertyMonitorSalesEvidence.TRANSFERRED_SALE.label,
                ]
                and save_original_price
            ):
                original_price = each_sales_data["total_sales_price"]
                if original_price:
                    property_financials.original_price = original_price
                    get_or_create_db_object(
                        PropertyVerifiedDataFields,
                        property=property_obj,
                        field_name="original_price",
                        value=str(original_price),
                        created_by_id=user,
                        created_by_role=role_obj,
                        field_type="float",
                    )
                    user_level_property_financial.original_price = original_price
                    property_financials.original_price_data_source = (
                        DataSource.PROPERTY_MONITOR
                    )

                    if not created:
                        property_financials.updated_by_id = user
                        user_level_property_financial.updated_by_id = user
                        property_financials.updated_by_role = role_obj
                        user_level_property_financial.updated_by_role = role_obj
                    else:
                        property_financials.created_by_id = user
                        property_financials.created_by_role = role_obj
                    property_financials.save()
                    user_level_property_financial.save()
                    save_original_price = False
            sales_instance = PropertySalesUnitHistory.objects.filter(
                property_id=property_obj.id,
                evidence_date=each_sales_data["evidence_date"],
            ).first()

            # each_sales_data['property'] = property_obj.id
            each_sales_data["created_by"] = user

            if sales_instance:
                # If the record exists, update the existing instance
                sales_serializer = PropertySalesUnitHistorySerializer(
                    sales_instance, data=each_sales_data
                )
            else:
                # If the record doesn't exist, create a new instance
                sales_serializer = PropertySalesUnitHistorySerializer(
                    data=each_sales_data
                )

            if sales_serializer.is_valid():
                sales_data = sales_serializer.save(property=property_obj)
                if not sales_instance:
                    sales_data.created_by_id = user
                    sales_data.created_by_role = role_obj
                else:
                    sales_data.updated_by_id = user
                    sales_data.updated_by_role = role_obj
                sales_data.save()
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data received for sales information from Property Monitor",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: sales_serializer.errors},
                    }
                )

        # Once all data is stored, fetch all the rental history for this property
        sales_history = PropertySalesUnitHistory.objects.filter(
            property_id=property_obj.id
        ).order_by("evidence_date")

        # Now, calculate the rent increase percentage and is_upward flag
        previous_sales_instance = None
        for sales_instance in sales_history:
            if previous_sales_instance:
                previous_sales = previous_sales_instance.total_sales_price
                current_sales = sales_instance.total_sales_price

                if previous_sales and current_sales:
                    rent_increase_percentage = (
                        (current_sales - previous_sales) / previous_sales
                    ) * 100
                    sales_instance.rent_increase_percentage = round(
                        rent_increase_percentage, 2
                    )

                    # Set is_upward flag based on whether the rent increased or decreased
                    sales_instance.is_upward = current_sales > previous_sales
                else:
                    sales_instance.rent_increase_percentage = None
                    sales_instance.is_upward = None

                # Save the updated rental instance with the calculated fields
                sales_instance.save()

            # Move to the next record for comparison
            previous_sales_instance = sales_instance

    except Exception as error:
        message = "Unknown error occurred in update_sales_history"
        logger.error(f"Error in update_sales_history - {message} - {error}")
        raise InternalServerException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


@log_input_output
def edit_section_medias(input_data, serializer, user):
    try:
        property_unit_section = PropertyUnitSections.objects.get(
            id=input_data["section_id"]
        )

        if property_unit_section:
            property_unit_section.section_type = serializer.data["section"]
            property_unit_section.attached_balcony = serializer.data["attached_balcony"]
            property_unit_section.updated_by = user

            property_unit_section.save()

        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data received of section id",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid SectionId"},
                }
            )

        return property_unit_section

    except Exception as error:
        message = "Unknown error occurred in edit_section_medias"
        logger.error(f"Error in edit_section_medias - {message} - {error}")
        raise InternalServerException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


def get_property_unit_section_object(property, section_id):
    """
    Get property unit section object

    Returns:
        Property: property unit section object

    Raises:
        ResourceNotFoundException: if property unit section does not exist
    """

    try:
        return PropertyUnitSections.objects.get(property=property, id=section_id)
    except PropertyUnitSections.DoesNotExist:
        message = f"PropertyUnitSections does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"Propertyunitsection object with ID {section_id} does not exist"
                },
            }
        )


@log_input_output
def delete_unit_section_media(section):
    """
    THis function will be used to delete all media connected to given section
    from db as well as S3
    """
    try:
        s3_client = S3Client()

        # Fetch and delete related media files from S3
        media_files = UnitSectionMedia.objects.filter(section=section)
        for media in media_files:
            s3_key = media.media_file  # Get the S3 key from the media_file field
            s3_client.delete_file(s3_key)

            if media.thumbnail_file:
                s3_client.delete_file(media.thumbnail_file)

        # After deleting files from S3, delete related media records
        media_files.delete()

        return True

    except Exception as error:
        raise InternalServerException(
            {
                KEY_MESSAGE: "Error in delete_unit_section_media",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


@log_input_output
def delete_unit_section_media_from_media_ids(media_id_list, user, role_obj):
    """
    This function will be used to delete all media connected to given section
    from db as well as S3

    :param media_id_list: List of media ids that needs to be deleted
    :param user: User id
    :param role_obj: Object of the role
    """
    try:
        if len(media_id_list):
            logger.info(f"This is deleted media list {media_id_list}")
            s3_client = S3Client()

            if role_obj.name == "Investor":
                filters = {
                    "created_by_role": role_obj,
                }
            else:
                filters = {
                    "created_by": user,
                    "created_by_role": role_obj,
                }
            media_list = get_list_of_object_with_filters(
                app_name="properties",
                model_name=UnitSectionMedia.__name__,
                single_field_value_dict=filters,
                multi_field_value_dict={"id": media_id_list},
            )

            for media in media_list:
                s3_key = media.media_file  # Get the S3 key from the media_file field
                s3_client.delete_file(s3_key)

                if media.thumbnail_file:
                    s3_client.delete_file(media.thumbnail_file)

            # After deleting files from S3, delete related media records
            media_list.delete()
            logger.info(
                f"The media files with IDs {media_id_list} have been deleted successfully."
            )

        return True

    except UnitSectionMedia.DoesNotExist:
        message = f"UnitSectionMedia does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"UnitSectionMedia object with ID {media_id_list} does not exist"
                },
            }
        )

    except Exception as error:
        raise InternalServerException(
            {
                KEY_MESSAGE: "Error in delete_unit_section_media_from_media_ids",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


@log_input_output
def map_order_with_media_in_db_and_assign_order(
    media_order_input, property_unit_section
):
    """
    This function will be used to delete all media connected to given section
    from db as well as S3
    """
    try:
        media_list = UnitSectionMedia.objects.filter(
            section_id=property_unit_section.id
        ).order_by("order_no")
        # use this line when testing from postman
        # media_order_input = media_order_input.strip('"')
        order_list = media_order_input.split(",")
        media_order = [int(item) for item in order_list]
        logger.info(f"this is new media order list {media_order}")

        existing_order_no = list()
        existing_media_new_sequence = dict()
        new_media_sequence = list()
        # Fetch and delete related media files from S3

        for media in media_list:
            logger.info(f"existing media details {media.id} and order {media.order_no}")

            existing_order_no.append(media.order_no)
            new_order_no_index = media_order.index(media.order_no) + 1
            existing_media_new_sequence[media.id] = new_order_no_index
            media_to_update = UnitSectionMedia.objects.get(id=media.id)
            media_to_update.order_no = new_order_no_index
            media_to_update.save()

        remaining_order_no = [
            order_rem for order_rem in media_order if order_rem not in existing_order_no
        ]
        logger.info(
            f"remaining order No {remaining_order_no} and existing order no {existing_order_no}"
        )

        if remaining_order_no:
            for each_order in remaining_order_no:
                order_index = media_order.index(each_order) + 1
                new_media_sequence.append(order_index)
        return new_media_sequence, existing_media_new_sequence

    except UnitSectionMedia.DoesNotExist:
        message = f"UnitSectionMedia does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"UnitSectionMedia object with ID  does not exist"
                },
            }
        )

    except Exception as error:
        traceback.print_exc()
        raise InternalServerException(
            {
                KEY_MESSAGE: "Error in map_order_with_media_in_db_and_assign_order",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: error},
            }
        )


def remove_floor_payment_plan_data_for_agent(property_id, agent_ids):
    agent_profiles = AgentProfile.objects.filter(id__in=agent_ids)
    agent_role = get_agent_role_object()
    for profile in agent_profiles:
        UserLevelPropertyData.objects.filter(
            property_id=property_id, created_by=profile.user, created_by_role=agent_role
        ).delete()
        floor_plans = PropertyFloorPlan.objects.filter(
            property_id=property_id, created_by=profile.user, created_by_role=agent_role
        )
        payment_plans = PropertyPaymentPlan.objects.filter(
            property_id=property_id, created_by=profile.user, created_by_role=agent_role
        )
        unit_sections = PropertyUnitSections.objects.filter(
            property_id=property_id, created_by=profile.user, created_by_role=agent_role
        )
        unit_media = UnitSectionMedia.objects.filter(
            section__in=unit_sections,
            created_by=profile.user,
            created_by_role=agent_role,
        )
        media_keys_in_unit_images = list(
            unit_media.filter(media_file__isnull=False).values_list(
                "media_file", flat=True
            )
        )
        thumbnail_keys_in_unit_images = list(
            unit_media.filter(thumbnail_file__isnull=False).values_list(
                "thumbnail_file", flat=True
            )
        )

        media_keys_in_floor_plans = list(
            floor_plans.values_list("media_file_key", flat=True)
        )
        media_keys_in_payment_plans = list(
            payment_plans.values_list("media_file_key", flat=True)
        )
        media_keys_to_delete = list(
            set(
                media_keys_in_floor_plans
                + media_keys_in_payment_plans
                + media_keys_in_unit_images
                + thumbnail_keys_in_unit_images
            )
        )
        s3_client = S3Client()
        s3_client.bulk_delete_files(media_keys_to_delete)

        floor_plans.delete()
        payment_plans.delete()
        unit_sections.delete()

        user_level_property_data = UserLevelPropertyData.objects.filter(
            property_id=property_id, created_by=profile.user, created_by_role=agent_role
        )
        if user_level_property_data:
            user_level_property_data.delete()


def handle_associated_agents(
    property_obj, agent_profiles, created_by, agent_type, validated_data, role_obj
):
    currently_associated_agents = AgentAssociatedProperty.objects.filter(
        property=property_obj
    )
    if property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
        (
            currently_associated_agents.filter(
                is_associated=False, action_status=UserRequestActions.PENDING
            ).update(
                updated_by=created_by, is_request_expired=True, updated_by_role=role_obj
            )
        )
        return True
    if agent_type == PropertyAgentType.OPEN_TO_ALL:
        remove_agents = list(
            currently_associated_agents.filter(is_associated=True).values_list(
                "id", flat=True
            )
        )
        removed_agents_ids = list(
            currently_associated_agents.filter(is_associated=True).values_list(
                "agent_profile", flat=True
            )
        )
        logger.info(f"remove_agents ********************** : {remove_agents}")
        currently_associated_agents.update(
            is_associated=False,
            is_request_expired=True,
            updated_by=created_by,
            updated_by_role=role_obj,
        )
        post_save.send(
            sender=AgentAssociatedProperty,
            instance=AgentAssociatedProperty.objects.filter(id__in=remove_agents),
            manual_trigger=True,
            action_taken_by=created_by,
        )
        remove_floor_payment_plan_data_for_agent(property_obj.id, removed_agents_ids)
        return True
    if agent_profiles:
        # Making all entries other than current agent type as expired
        agents_to_be_updated = currently_associated_agents.filter(
            is_request_expired=False
        ).exclude(request_agent_type=agent_type)
        (
            agents_to_be_updated.update(
                is_associated=False,
                updated_by=created_by,
                is_request_expired=True,
                updated_by_role=role_obj,
            )
        )
        logger.info(
            f"inside if agent_profiles ********************** : {agents_to_be_updated}"
        )
        post_save.send(
            sender=AgentAssociatedProperty,
            instance=agents_to_be_updated,
            manual_trigger=True,
            action_taken_by=created_by,
        )

        # Getting current agents of respective agent type
        current_agents = (
            currently_associated_agents.filter(
                request_agent_type=agent_type,
                is_request_expired=False,
            )
            .annotate(id_str=Cast("agent_profile_id", CharField()))
            .values_list("id_str", flat=True)
        )
        # Calculating difference between current and coming agents
        removed_agents = set(current_agents) - set(agent_profiles)
        if removed_agents:
            signal_data = list(
                AgentAssociatedProperty.objects.filter(
                    property=property_obj,
                    agent_profile_id__in=removed_agents,
                    is_request_expired=False,
                ).values_list("id", flat=True)
            )
            agents_to_be_removed = AgentAssociatedProperty.objects.filter(
                property=property_obj,
                agent_profile_id__in=removed_agents,
                is_request_expired=False,
            )
            (
                agents_to_be_removed.update(
                    is_associated=False,
                    updated_by=created_by,
                    is_request_expired=True,
                    updated_by_role=role_obj,
                )
            )
            logger.info(f"if remove_agents ********************** : {signal_data}")
            post_save.send(
                sender=AgentAssociatedProperty,
                instance=AgentAssociatedProperty.objects.filter(id__in=signal_data),
                manual_trigger=True,
                action_taken_by=created_by,
            )
            remove_floor_payment_plan_data_for_agent(property_obj.id, removed_agents)

        for agent in agent_profiles:
            if agent not in current_agents:
                agent_associated_property = AgentAssociatedProperty.objects.create(
                    property=property_obj,
                    agent_profile_id=agent,
                    request_agent_type=property_obj.agent_type,
                    created_by=created_by,
                    created_by_role=role_obj,
                )
                # recalculate_unlocked_properties(agent)
            else:
                agent_associated_property = AgentAssociatedProperty.objects.filter(
                    property=property_obj,
                    agent_profile_id=agent,
                    request_agent_type=property_obj.agent_type,
                    is_request_expired=False,
                ).first()

            # Upload contract if it's exclusive agent
            if agent_type == PropertyAgentType.EXCLUSIVE_AGENT and validated_data.get(
                "agent_contract"
            ):
                file_field = "agent_contract"
                file = validated_data.get("agent_contract")

                uploaded_document_key = PRESIGNED_POST_STRUCTURES.get(
                    file_field.upper(), {}
                ).get(KEY, "")
                uploaded_document_key = uploaded_document_key.format(
                    property_id=property_obj.id,
                    user_id=agent_associated_property.agent_profile.user_id,
                    filename=f"{agent_associated_property.agent_profile.user_id}_{file_field}",
                )

                s3_client = S3Client()
                s3_client.upload_file(file, uploaded_document_key)

                # Update model instance with file details
                agent_associated_property.agent_contract_key = uploaded_document_key
                agent_associated_property.agent_contract_file_name = file.name
                agent_associated_property.agent_contract_file_size = file.size
                agent_associated_property.save()

            elif agent_type == PropertyAgentType.EXCLUSIVE_AGENT and validated_data.get(
                "remove_exclusive_agent_contract", False
            ):
                if agent_associated_property.agent_contract_key:
                    s3_client = S3Client()
                    s3_client.delete_file(agent_associated_property.agent_contract_key)

                    agent_associated_property.agent_contract_key = None
                    agent_associated_property.agent_contract_file_name = None
                    agent_associated_property.agent_contract_file_size = None
                    agent_associated_property.save()

            agent_associated_property.save()

        return True


def fetch_role_obj_and_name(request):
    user_role = request.query_params.get("user_role")
    role_obj = get_role_object(user_role)
    return user_role, role_obj


@log_input_output
def save_property_completion_state(
    property_obj: object,
    data_source: str,
    state: str,
    user: object,
    role_obj: object,
    is_completed: bool,
    draft_data: object,
) -> None:
    completion_state, created = PropertyCompletionState.objects.get_or_create(
        property=property_obj, data_source=data_source, state=state
    )
    if created:
        completion_state.created_by = user
        completion_state.created_by_role = role_obj
    else:
        completion_state.updated_by = user
        completion_state.updated_by_role = role_obj

    completion_state.is_completed = is_completed
    completion_state.draft_data_json = draft_data
    completion_state.save()


@log_input_output
def delete_media_from_media_ids(
    media_id_list, app_name, db_model, single_field_value_dict
):
    """
    Method to delete already uploaded images

    :param media_id_list: List of media ids to delete it from database
    :param app_name: Name of the app
    :param db_model: Name of the db model
    :param single_field_value_dict: Dictionary which contains single value for fields

    :return Bool: True if all the given media files are deleted
    """
    if len(media_id_list):
        deleted_media = media_id_list.split(",")
        media_list_in = [int(item) for item in deleted_media]
        logger.info(f"This is deleted media list {media_list_in}")
        s3_client = S3Client()

        media_list = get_list_of_object_with_filters(
            app_name, db_model, single_field_value_dict, {"id": media_list_in}
        )

        # If no media found, then raise an error
        if not media_list:
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"{db_model} object with ID {media_list_in} does not exist"
                    },
                }
            )

        for media in media_list:
            s3_key = media.media_file_key  # Get the S3 key from the media_file field
            s3_client.delete_file(s3_key)

        # After deleting files from S3, delete related media records
        media_list.delete()
        logger.info(
            f"The media files with IDs {media_list_in} have been deleted successfully."
        )

    return True


def delete_property(property_id, user):
    """
    Soft-delete a property by creating a DeletedProperty entry.

    Parameters:
        property_id (int): The ID of the property to delete.
        user (User): The user performing the deletion.

    Returns:
        dict: A dictionary containing the deletion details.
    """
    try:
        with transaction.atomic():
            # Step 1: Fetch the property ensuring it is archived
            property_instance = Property.objects.select_for_update().get(
                id=property_id, is_archived=True
            )
            logger.info(
                f"Fetched Property ID {property_id} for deletion by User ID {user.id}."
            )

            # Step 3: Perform soft-delete by creating a DeletedProperty entry
            DeletedProperty.objects.create(
                property=property_instance, deleted_by=user, deleted_at=timezone.now()
            )
            logger.info(
                f"Property ID {property_id} soft-deleted by User ID {user.id} at {timezone.now()}."
            )

            # Step 4: Return success response using KEY_MESSAGE
            return {
                KEY_MESSAGE: "Property deleted successfully.",
                "property_id": property_id,
            }

    except Property.DoesNotExist:
        logger.error(f"Property ID {property_id} does not exist or is not archived.")
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"Property object with ID {property_id} does not exist or is not archived."
                },
            }
        )
    except InvalidSerializerDataException:
        # Re-raise to be handled by the view's exception handler
        raise
    except Exception as e:
        logger.error(
            f"Unexpected error deleting Property ID {property_id}: {str(e)}",
            exc_info=True,
        )
        raise InternalServerException(
            {
                KEY_MESSAGE: "An unexpected error occurred.",
                KEY_PAYLOAD: {"property_id": property_id},
                KEY_ERROR: {KEY_ERROR_MESSAGE: "Please try again later."},
            }
        )


@log_input_output
def map_media_order(
    media_order_input,
    app_name,
    db_model,
    single_field_value_dict=None,
    multi_field_value_dict=None,
    no_of_media_files=10,
):
    """
    Method to map the media order in the database with assign order

    :param media_order_input: Input media order
    :param app_name: Name of the app
    :param db_model: Name of the db model
    :param single_field_value_dict: Dictionary which contains single value for fields
    :param multi_field_value_dict: Dictionary which contains multiple values for fields
    :param no_of_media_files: Maximum number of media file

    :return
        new_media_sequence: Sequence of new media
        existing_media_new_sequence: New sequence of existing media
    """
    media_list = get_list_of_object_with_filters(
        app_name, db_model, single_field_value_dict, multi_field_value_dict
    )
    media_list = media_list.order_by("order_no")

    # use this line when testing from postman
    # media_order_input = media_order_input.strip('"')
    order_list = media_order_input.split(",")
    media_order = [int(item) for item in order_list]
    logger.info(f"this is new media order list {media_order}")

    existing_order_no = list()
    existing_media_new_sequence = dict()
    new_media_sequence = list()

    for media in media_list:
        logger.info(f"existing media details {media.id} and order {media.order_no}")

        existing_order_no.append(media.order_no)
        new_order_no_index = media_order.index(media.order_no) + 1
        existing_media_new_sequence[media.id] = new_order_no_index
        model_name = apps.get_model(app_name, db_model)
        media_to_update = get_object_with_filters(model_name, id=media.id)
        media_to_update.order_no = new_order_no_index
        media_to_update.save()

    remaining_order_no = [
        order_rem for order_rem in media_order if order_rem not in existing_order_no
    ]
    logger.info(
        f"remaining order No {remaining_order_no} and existing order no {existing_order_no}"
    )

    if (len(media_list) + len(remaining_order_no)) > no_of_media_files:
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: f"Max {no_of_media_files} images are allowed",
                KEY_PAYLOAD: {},
                KEY_ERROR: {
                    KEY_ERROR_MESSAGE: f"Max {no_of_media_files} images are allowed"
                },
            }
        )

    if remaining_order_no:
        for each_order in remaining_order_no:
            order_index = media_order.index(each_order) + 1
            new_media_sequence.append(order_index)
    return new_media_sequence, existing_media_new_sequence


def get_payment_plan_object(property):
    """
    Get payment plan object

    Returns:
        payment_plan: Payment plan object

    Raises:
        ResourceNotFoundException: if payment plan does not exist
    """

    try:
        return PropertyPaymentPlan.objects.get(property=property)
    except PropertyPaymentPlan.DoesNotExist:
        message = f"Payment plan document for {property} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_floor_plan_object(property):
    """
    Get floor plan object

    Returns:
        floor_plan: Floor plan object

    Raises:
        ResourceNotFoundException: if floor plan does not exist
    """

    try:
        return PropertyFloorPlan.objects.get(property=property)
    except PropertyFloorPlan.DoesNotExist:
        message = f"Floor plan document for {property} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def handle_new_property_for_agent(agent_profile, new_property):
    """
    If limit reached, property remains locked (not added to unlocked_properties)
    with country-specific limits
    """
    if agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC:
        # Get phone country code and convert to ISO country code
        phone_country_code = agent_profile.user.primary_phone_number.country_code
        iso_country_code = settings.PHONE_TO_ISO_COUNTRY_MAP.get(
            str(phone_country_code), "DEFAULT"
        )

        # Get country-specific limit or default
        allowed_count = settings.PROPERTY_COUNT_LIMITS.get(
            iso_country_code, settings.PROPERTY_COUNT_LIMITS["DEFAULT"]
        )
        logger.info(
            f"Property limit for agent {agent_profile.id}: "
            f"phone_code={phone_country_code}, "
            f"iso_code={iso_country_code}, "
            f"limit={allowed_count}"
        )
        if agent_profile.unlocked_properties.count() < allowed_count:
            agent_profile.unlocked_properties.add(new_property)
    else:
        agent_profile.unlocked_properties.add(new_property)

    agent_profile.save()


@log_input_output
def recalculate_unlocked_properties(agent_profile, basic_subscription=False):
    """
    Retrieve and update unlocked properties for the agent in a single query
    with country-specific limits
    """
    # Base query with combined filters for associations and properties
    association_query = AgentAssociatedProperty.objects.filter(
        agent_profile=agent_profile,
        action_status=UserRequestActions.ACCEPTED,
        is_associated=True,
        is_request_expired=False,
        property__property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
        property__is_archived=False,
    )

    associations = association_query.select_related("property").order_by("created_ts")
    property_count = association_query.count()

    if (
        property_count > 0
        and property_count % 5 == 0
        and agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC
    ):
        agent_profile.trial_premium_card = True
        agent_profile.save()

    # Determine allowed count based on subscription status and country
    logger.info(
        "agent profile subscription status is %s", agent_profile.subscription_status
    )
    logger.info("type of subscription is %s", type(agent_profile.subscription_status))
    allowed_count = None
    if (
        str(agent_profile.subscription_status) == AgentSubscriptionPlanChoices.BASIC
        or basic_subscription is True
    ):
        logger.info("inside IF")
        # Get phone country code and convert to ISO country code
        phone_country_code = agent_profile.user.primary_phone_number.country_code
        iso_country_code = settings.PHONE_TO_ISO_COUNTRY_MAP.get(
            str(phone_country_code), "DEFAULT"
        )

        # Get country-specific limit or default
        allowed_count = settings.PROPERTY_COUNT_LIMITS.get(
            iso_country_code, settings.PROPERTY_COUNT_LIMITS["DEFAULT"]
        )
        logger.info(
            f"Property limit for agent {agent_profile.id}: "
            f"phone_code={phone_country_code}, "
            f"iso_code={iso_country_code}, "
            f"limit={allowed_count}"
        )

    logger.info("associations count: %s", associations)
    # Apply subscription limit if needed
    if allowed_count is not None:
        associations = associations[:allowed_count]
    logger.info("associations count after limit: %s", associations)
    # Extract properties directly from the filtered associations
    properties = [assoc.property for assoc in associations]
    logger.info("properties count: %s", properties)
    # Update unlocked properties in a single operation
    agent_profile.unlocked_properties.set(properties)
    agent_profile.save()
