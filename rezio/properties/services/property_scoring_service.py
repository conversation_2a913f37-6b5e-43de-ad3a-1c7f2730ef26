"""
Property scoring service for calculating property scores based on weighted attributes.
"""

import logging
from typing import Optional, Dict, Any

from django.db import transaction
from django.core.exceptions import ObjectDoesNotExist

from rezio.properties.models import (
    Property,
    UserLevelPropertyData,
    UserLevelPropertyAvailabilityAndStatus,
    UserLevelPropertyFinancialDetails,
    UserLevelPropertyFeatures,
    PropertyFloorPlan,
    PropertyPaymentPlan,
    PropertyUnitSections,
)
from rezio.properties.scoring_constants import get_weight_mapping, get_max_score
from rezio.properties.text_choices import PropertyCategory

logger = logging.getLogger(__name__)


class PropertyScoringService:
    """Service class for calculating property scores based on weighted attributes."""

    def __init__(self):
        self.logger = logger

    def calculate_property_score(self, property_id: int) -> float:
        """
        Calculate the property score for a given property.

        Args:
            property_id (int): The ID of the property to score

        Returns:
            float: The calculated property score (0-100)
        """
        try:
            property_obj = Property.objects.get(id=property_id)
            return self._calculate_score_for_property(property_obj)
        except Property.DoesNotExist:
            self.logger.error(f"Property with ID {property_id} does not exist")
            return 0.0
        except Exception as e:
            self.logger.error(f"Error calculating score for property {property_id}: {str(e)}")
            return 0.0

    def _calculate_score_for_property(self, property_obj: Property) -> float:
        """
        Calculate the score for a property object.

        Args:
            property_obj (Property): The property object to score

        Returns:
            float: The calculated property score (0-100)
        """
        property_category = property_obj.property_category
        weight_mapping = get_weight_mapping(property_category)
        max_score = get_max_score(property_category)

        total_score = 0.0

        # Get user level property data
        user_level_data = self._get_user_level_property_data(property_obj)
        if not user_level_data:
            self.logger.warning(f"No user level data found for property {property_obj.id}")
            return 0.0

        # Calculate scores for each category
        total_score += self._calculate_user_level_data_score(user_level_data, weight_mapping)
        total_score += self._calculate_availability_status_score(user_level_data, weight_mapping)
        total_score += self._calculate_financial_details_score(user_level_data, weight_mapping)
        total_score += self._calculate_features_score(user_level_data, weight_mapping)
        total_score += self._calculate_floor_plan_score(property_obj, weight_mapping)
        total_score += self._calculate_payment_plan_score(property_obj, weight_mapping)
        total_score += self._calculate_unit_sections_score(property_obj, weight_mapping)

        # Normalize score to 0-100 range
        normalized_score = (total_score / max_score) * 100 if max_score > 0 else 0.0

        return min(100.0, max(0.0, normalized_score))

    def _get_user_level_property_data(self, property_obj: Property) -> Optional[UserLevelPropertyData]:
        """Get the most recent user level property data for a property."""
        try:
            return UserLevelPropertyData.objects.filter(
                property=property_obj
            ).order_by('-created_ts').first()
        except Exception as e:
            self.logger.error(f"Error getting user level data for property {property_obj.id}: {str(e)}")
            return None

    def _calculate_user_level_data_score(self, user_level_data: UserLevelPropertyData, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on user level property data fields."""
        score = 0.0

        # Property type
        if user_level_data.property_type and "user_level_data.property_type" in weight_mapping:
            score += weight_mapping["user_level_data.property_type"]

        # Area fields
        if user_level_data.total_area and "user_level_data.total_area" in weight_mapping:
            score += weight_mapping["user_level_data.total_area"]

        if user_level_data.carpet_area and "user_level_data.carpet_area" in weight_mapping:
            score += weight_mapping["user_level_data.carpet_area"]

        if user_level_data.balcony_area and "user_level_data.balcony_area" in weight_mapping:
            score += weight_mapping["user_level_data.balcony_area"]

        # Room counts
        if user_level_data.number_of_bedrooms and "user_level_data.number_of_bedrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_bedrooms"]

        if user_level_data.number_of_bathrooms and "user_level_data.number_of_bathrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_bathrooms"]

        if user_level_data.number_of_common_bathrooms and "user_level_data.number_of_common_bathrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_common_bathrooms"]

        if user_level_data.number_of_attached_bathrooms and "user_level_data.number_of_attached_bathrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_attached_bathrooms"]

        if user_level_data.number_of_powder_rooms and "user_level_data.number_of_powder_rooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_powder_rooms"]

        # Parking
        if user_level_data.parking_available and "user_level_data.parking_available" in weight_mapping:
            score += weight_mapping["user_level_data.parking_available"]

        if user_level_data.number_of_covered_parking and "user_level_data.number_of_covered_parking" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_covered_parking"]

        if user_level_data.number_of_open_parking and "user_level_data.number_of_open_parking" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_open_parking"]

        # Building details
        if user_level_data.floor_number and "user_level_data.floor_number" in weight_mapping:
            score += weight_mapping["user_level_data.floor_number"]

        if user_level_data.total_floors and "user_level_data.total_floors" in weight_mapping:
            score += weight_mapping["user_level_data.total_floors"]

        if user_level_data.building_type and "user_level_data.building_type" in weight_mapping:
            score += weight_mapping["user_level_data.building_type"]

        # Additional room types
        if user_level_data.number_of_master_bedrooms and "user_level_data.number_of_master_bedrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_master_bedrooms"]

        if user_level_data.number_of_other_bedrooms and "user_level_data.number_of_other_bedrooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_other_bedrooms"]

        if user_level_data.number_of_maid_rooms and "user_level_data.number_of_maid_rooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_maid_rooms"]

        if user_level_data.number_of_study_rooms and "user_level_data.number_of_study_rooms" in weight_mapping:
            score += weight_mapping["user_level_data.number_of_study_rooms"]

        return score

    def _calculate_availability_status_score(self, user_level_data: UserLevelPropertyData, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on availability and status fields."""
        score = 0.0

        try:
            availability_status = user_level_data.property_user_level_availability_and_status

            if availability_status.status and "availability_status.status" in weight_mapping:
                score += weight_mapping["availability_status.status"]

            if availability_status.occupancy_status and "availability_status.occupancy_status" in weight_mapping:
                score += weight_mapping["availability_status.occupancy_status"]

            if availability_status.handover_date and "availability_status.handover_date" in weight_mapping:
                score += weight_mapping["availability_status.handover_date"]

            if availability_status.rent_available_start_date and "availability_status.rent_available_start_date" in weight_mapping:
                score += weight_mapping["availability_status.rent_available_start_date"]

            if availability_status.enable_payment_plan and "availability_status.enable_payment_plan" in weight_mapping:
                score += weight_mapping["availability_status.enable_payment_plan"]

            if availability_status.during_construction and "availability_status.during_construction" in weight_mapping:
                score += weight_mapping["availability_status.during_construction"]

            if availability_status.on_handover and "availability_status.on_handover" in weight_mapping:
                score += weight_mapping["availability_status.on_handover"]

            if availability_status.enable_post_handover and "availability_status.enable_post_handover" in weight_mapping:
                score += weight_mapping["availability_status.enable_post_handover"]

            if availability_status.ownership_proof_key and "availability_status.ownership_proof_key" in weight_mapping:
                score += weight_mapping["availability_status.ownership_proof_key"]

        except ObjectDoesNotExist:
            # No availability status data exists
            pass
        except Exception as e:
            self.logger.error(f"Error calculating availability status score: {str(e)}")

        return score

    def _calculate_financial_details_score(self, user_level_data: UserLevelPropertyData, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on financial details fields."""
        score = 0.0

        try:
            financial_details = user_level_data.property_user_level_financial_details

            if financial_details.original_price and "financial_details.original_price" in weight_mapping:
                score += weight_mapping["financial_details.original_price"]

            if financial_details.asking_price and "financial_details.asking_price" in weight_mapping:
                score += weight_mapping["financial_details.asking_price"]

            if financial_details.valuation and "financial_details.valuation" in weight_mapping:
                score += weight_mapping["financial_details.valuation"]

            if financial_details.annual_rent and "financial_details.annual_rent" in weight_mapping:
                score += weight_mapping["financial_details.annual_rent"]

            if financial_details.annual_service_charges and "financial_details.annual_service_charges" in weight_mapping:
                score += weight_mapping["financial_details.annual_service_charges"]

            if financial_details.security_deposit and "financial_details.security_deposit" in weight_mapping:
                score += weight_mapping["financial_details.security_deposit"]

            if financial_details.preferred_payment_frequency and "financial_details.preferred_payment_frequency" in weight_mapping:
                score += weight_mapping["financial_details.preferred_payment_frequency"]

            if financial_details.expected_rent and "financial_details.expected_rent" in weight_mapping:
                score += weight_mapping["financial_details.expected_rent"]

            if financial_details.expected_security_deposit and "financial_details.expected_security_deposit" in weight_mapping:
                score += weight_mapping["financial_details.expected_security_deposit"]

        except ObjectDoesNotExist:
            # No financial details data exists
            pass
        except Exception as e:
            self.logger.error(f"Error calculating financial details score: {str(e)}")

        return score

    def _calculate_features_score(self, user_level_data: UserLevelPropertyData, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on property features fields."""
        score = 0.0

        try:
            features = user_level_data.property_user_level_features

            if features.branded_building and "features.branded_building" in weight_mapping:
                score += weight_mapping["features.branded_building"]

            if features.furnished and "features.furnished" in weight_mapping:
                score += weight_mapping["features.furnished"]

            if features.premium_view and "features.premium_view" in weight_mapping:
                score += weight_mapping["features.premium_view"]

            if features.is_restroom_available and "features.is_restroom_available" in weight_mapping:
                score += weight_mapping["features.is_restroom_available"]

            if features.no_of_shared_restrooms and "features.no_of_shared_restrooms" in weight_mapping:
                score += weight_mapping["features.no_of_shared_restrooms"]

            if features.no_of_private_restrooms and "features.no_of_private_restrooms" in weight_mapping:
                score += weight_mapping["features.no_of_private_restrooms"]

            if features.is_parking_available and "features.is_parking_available" in weight_mapping:
                score += weight_mapping["features.is_parking_available"]

            if features.public_parking and "features.public_parking" in weight_mapping:
                score += weight_mapping["features.public_parking"]

            if features.no_of_reserved_parking and "features.no_of_reserved_parking" in weight_mapping:
                score += weight_mapping["features.no_of_reserved_parking"]

            if features.is_lift_available and "features.is_lift_available" in weight_mapping:
                score += weight_mapping["features.is_lift_available"]

            if features.common_lift and "features.common_lift" in weight_mapping:
                score += weight_mapping["features.common_lift"]

            if features.no_of_personal_lift and "features.no_of_personal_lift" in weight_mapping:
                score += weight_mapping["features.no_of_personal_lift"]

            if features.security_available and "features.security_available" in weight_mapping:
                score += weight_mapping["features.security_available"]

            if features.water_storage_available and "features.water_storage_available" in weight_mapping:
                score += weight_mapping["features.water_storage_available"]

            if features.property_on_main_road and "features.property_on_main_road" in weight_mapping:
                score += weight_mapping["features.property_on_main_road"]

            if features.corner_property and "features.corner_property" in weight_mapping:
                score += weight_mapping["features.corner_property"]

            if features.tags.exists() and "features.tags" in weight_mapping:
                score += weight_mapping["features.tags"]

        except ObjectDoesNotExist:
            # No features data exists
            pass
        except Exception as e:
            self.logger.error(f"Error calculating features score: {str(e)}")

        return score

    def _calculate_floor_plan_score(self, property_obj: Property, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on floor plan availability."""
        score = 0.0

        try:
            floor_plans = PropertyFloorPlan.objects.filter(property=property_obj)
            if floor_plans.exists() and "floor_plan.media_file_key" in weight_mapping:
                score += weight_mapping["floor_plan.media_file_key"]
        except Exception as e:
            self.logger.error(f"Error calculating floor plan score: {str(e)}")

        return score

    def _calculate_payment_plan_score(self, property_obj: Property, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on payment plan availability."""
        score = 0.0

        try:
            payment_plans = PropertyPaymentPlan.objects.filter(property=property_obj)
            if payment_plans.exists() and "payment_plan.media_file_key" in weight_mapping:
                score += weight_mapping["payment_plan.media_file_key"]
        except Exception as e:
            self.logger.error(f"Error calculating payment plan score: {str(e)}")

        return score

    def _calculate_unit_sections_score(self, property_obj: Property, weight_mapping: Dict[str, int]) -> float:
        """Calculate score based on unit sections."""
        score = 0.0

        try:
            unit_sections = PropertyUnitSections.objects.filter(property=property_obj)

            if unit_sections.exists():
                if "unit_sections.count" in weight_mapping:
                    score += weight_mapping["unit_sections.count"]

                # Check if any section has specific attributes
                if unit_sections.filter(section_type__isnull=False).exists() and "unit_sections.section_type" in weight_mapping:
                    score += weight_mapping["unit_sections.section_type"]

                if unit_sections.filter(attached_balcony=True).exists() and "unit_sections.attached_balcony" in weight_mapping:
                    score += weight_mapping["unit_sections.attached_balcony"]

        except Exception as e:
            self.logger.error(f"Error calculating unit sections score: {str(e)}")

        return score

    @transaction.atomic
    def update_property_score(self, property_id: int) -> bool:
        """
        Calculate and update the property score for a given property.

        Args:
            property_id (int): The ID of the property to update

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            property_obj = Property.objects.select_for_update().get(id=property_id)
            new_score = self._calculate_score_for_property(property_obj)

            property_obj.property_score = new_score
            property_obj.save(update_fields=['property_score'])

            self.logger.info(f"Updated property score for property {property_id}: {new_score}")
            return True

        except Property.DoesNotExist:
            self.logger.error(f"Property with ID {property_id} does not exist")
            return False
        except Exception as e:
            self.logger.error(f"Error updating property score for property {property_id}: {str(e)}")
            return False

    def bulk_update_property_scores(self, property_ids: list = None) -> Dict[str, int]:
        """
        Update property scores for multiple properties.

        Args:
            property_ids (list, optional): List of property IDs to update. If None, updates all properties.

        Returns:
            dict: Summary of the update operation
        """
        if property_ids is None:
            properties = Property.objects.all()
        else:
            properties = Property.objects.filter(id__in=property_ids)

        updated_count = 0
        failed_count = 0

        for property_obj in properties:
            try:
                new_score = self._calculate_score_for_property(property_obj)
                property_obj.property_score = new_score
                property_obj.save(update_fields=['property_score'])
                updated_count += 1
            except Exception as e:
                self.logger.error(f"Failed to update score for property {property_obj.id}: {str(e)}")
                failed_count += 1

        self.logger.info(f"Bulk update completed: {updated_count} updated, {failed_count} failed")

        return {
            'updated': updated_count,
            'failed': failed_count,
            'total': updated_count + failed_count
        }


# Convenience function for external use
def calculate_and_update_property_score(property_id: int) -> bool:
    """
    Convenience function to calculate and update a property's score.

    Args:
        property_id (int): The ID of the property to update

    Returns:
        bool: True if successful, False otherwise
    """
    service = PropertyScoringService()
    return service.update_property_score(property_id)