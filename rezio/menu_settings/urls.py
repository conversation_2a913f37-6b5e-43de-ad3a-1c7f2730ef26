from django.urls import path, include, re_path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rezio.menu_settings.views.account_views import (
    AccountManagementViewSet,
    FAQViewSet,
    RatingViewSet,
)
from rezio.menu_settings.views.help_views import IssueViewSet, ContactSupportViewSet
from rezio.menu_settings.views.privacy_policy_views import PrivacyPolicyView
from rezio.menu_settings.views.profile_views import ProfileViewSet
from rezio.menu_settings.views.category_views import CategoryViewSet

# Initialize router and register viewsets
router = DefaultRouter()

# Registering viewsets with appropriate basenames
# router.register(
#     r'help_center/contact_support',
#     ContactSupportViewSet,
#     basename='menu-settings-help-center-contact-support'
# )
# router.register(
#     r'rate_us',
#     RatingViewSet,
#     basename='menu-settings-rate-us'
# )
# router.register(
#     r'faqs',
#     FAQViewSet,
#     basename='menu-settings-faqs'
# )
router.register(r"profiles", ProfileViewSet, basename="menu-settings-profiles")
# router.register(
#     r'account-management',
#     AccountManagementViewSet,
#     basename='menu-settings-account-management'
# )
# router.register(
#     r'categories',
#     CategoryViewSet,
#     basename='menu-settings-categories'
# )
# router.register(
#     r'issues',
#     IssueViewSet,
#     basename='menu-settings-issues'
# )

urlpatterns = [
    # Include all router URLs
    path("", include(router.urls)),
    # Privacy Policy endpoint
    path(
        "privacy_policy/",
        PrivacyPolicyView.as_view(),
        name="menu-settings-privacy-policy",
    ),
    # Profile-specific detail route
    re_path(
        r"^profiles/(?P<profile_id>\d+)/update/$",
        ProfileViewSet.as_view({"patch": "partial_update"}),
        name="menu-settings-profiles-update",
    ),
]
