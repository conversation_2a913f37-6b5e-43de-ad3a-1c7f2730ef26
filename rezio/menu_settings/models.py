from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from rezio.user.models import User, Role, Common, AgentProfile, InvestorProfile
from rezio.user.text_choices import OptionTypes


class UserTypes(models.TextChoices):
    """Text choices for user types"""

    INVESTOR = "investor", _("Investor")
    AGENT = "agent", _("Agent")
    ADMIN = "admin", _("Admin")


class IssueStatus(models.TextChoices):
    """Text choices for issue statuses"""

    OPEN = "OPEN", _("Open")
    IN_PROGRESS = "IN_PROGRESS", _("In Progress")
    RESOLVED = "RESOLVED", _("Resolved")
    CLOSED = "CLOSED", _("Closed")


class Issue(Common):
    title = models.CharField(max_length=255)
    description = models.TextField()
    reported_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="reported_issues"
    )
    status = models.CharField(
        max_length=20, choices=IssueStatus.choices, default=IssueStatus.OPEN
    )
    visible_to_roles = models.ManyToManyField(
        Role, related_name="visible_issues", blank=True
    )

    def __str__(self):
        return f"{self.title} - {self.reported_by}"


class IssueImage(models.Model):
    issue = models.ForeignKey(Issue, related_name="images", on_delete=models.CASCADE)
    image = models.ImageField(
        upload_to="issue_images/", null=True, blank=True
    )  # Temporarily nullable

    def __str__(self):
        return f"Image for issue {self.issue.id}"


class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    description = models.TextField(blank=True)
    accessible_to_roles = models.ManyToManyField(
        Role, related_name="accessible_categories"
    )

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Categories"


class FAQ(models.Model):
    category = models.ForeignKey(
        Category, on_delete=models.SET_NULL, null=True, related_name="faqs"
    )
    question = models.CharField(max_length=255)
    answer = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    visible_to_roles = models.ManyToManyField(Role, related_name="visible_faqs")

    def __str__(self):
        return self.question


class Rating(Common):
    agent = models.ForeignKey(
        "user.AgentProfile", on_delete=models.CASCADE, related_name="ratings", null=True
    )
    rated_by = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="given_ratings"
    )
    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)])
    comment = models.TextField(blank=True, null=True)
    user_type = models.CharField(max_length=20, choices=UserTypes.choices)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["agent", "rated_by"], name="unique_agent_rating"
            )
        ]

    def __str__(self):
        return f"Rating {self.rating} by {self.rated_by} for {self.agent}"


class ContactSupport(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    subject = models.CharField(max_length=255)
    message = models.TextField()
    contact_email = models.EmailField()
    created_at = models.DateTimeField(auto_now_add=True)
    assigned_to_role = models.ForeignKey(
        Role,
        on_delete=models.SET_NULL,
        null=True,
        related_name="assigned_support_requests",
    )

    def __str__(self):
        return f"Support request from {self.user} - {self.subject}"
