# menu_settings/serializers/issue_serializer.py

from rest_framework import serializers
from rezio.menu_settings.models import Issue, IssueImage, IssueStatus
from rezio.user.models import Role


class IssueImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = IssueImage
        fields = ["id", "image"]
        read_only_fields = ["id"]


class IssueSerializer(serializers.ModelSerializer):
    reported_by = serializers.PrimaryKeyRelatedField(read_only=True)
    images = IssueImageSerializer(many=True, read_only=True)
    visible_to_roles = serializers.SlugRelatedField(
        many=True, queryset=Role.objects.all(), slug_field="name", required=False
    )
    status = serializers.ChoiceField(
        choices=IssueStatus.choices, default=IssueStatus.OPEN
    )

    class Meta:
        model = Issue
        fields = [
            "id",
            "title",
            "description",
            "reported_by",
            "status",
            "visible_to_roles",
            "images",
        ]
        read_only_fields = ["id", "reported_by", "images"]

    def create(self, validated_data):
        roles = validated_data.pop("visible_to_roles", [])
        issue = Issue.objects.create(**validated_data)
        issue.visible_to_roles.set(roles)
        return issue

    def update(self, instance, validated_data):
        roles = validated_data.pop("visible_to_roles", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        if roles is not None:
            instance.visible_to_roles.set(roles)
        instance.save()
        return instance
