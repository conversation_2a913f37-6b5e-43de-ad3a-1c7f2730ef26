from rest_framework import serializers
from rezio.menu_settings.models import Rating, UserTypes
from rezio.user.models import AgentProfile


class RatingSerializer(serializers.ModelSerializer):
    agent = serializers.PrimaryKeyRelatedField(queryset=AgentProfile.objects.all())
    rated_by = serializers.PrimaryKeyRelatedField(read_only=True)
    user_type = serializers.ChoiceField(choices=UserTypes.choices)

    class Meta:
        model = Rating
        fields = ["id", "agent", "rated_by", "rating", "comment", "user_type"]
        read_only_fields = ["id", "rated_by"]

    def validate_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5.")
        return value

    def create(self, validated_data):
        return Rating.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.rating = validated_data.get("rating", instance.rating)
        instance.comment = validated_data.get("comment", instance.comment)
        instance.user_type = validated_data.get("user_type", instance.user_type)
        instance.save()
        return instance
