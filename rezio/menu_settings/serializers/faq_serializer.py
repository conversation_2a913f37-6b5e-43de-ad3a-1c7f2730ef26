from rest_framework import serializers
from rezio.menu_settings.models import FAQ, Category
from rezio.user.models import Role


class FAQSerializer(serializers.ModelSerializer):
    category = serializers.SlugRelatedField(
        queryset=Category.objects.all(),
        slug_field="slug",
        required=False,
        allow_null=True,
    )
    visible_to_roles = serializers.SlugRelatedField(
        many=True, queryset=Role.objects.all(), slug_field="name"
    )

    class Meta:
        model = FAQ
        fields = [
            "id",
            "category",
            "question",
            "answer",
            "created_at",
            "updated_at",
            "visible_to_roles",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]
