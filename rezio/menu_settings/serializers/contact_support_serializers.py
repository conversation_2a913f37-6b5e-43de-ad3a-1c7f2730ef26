from rest_framework import serializers
from rezio.menu_settings.models import ContactSupport
from rezio.user.models import Role


class ContactSupportSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    assigned_to_role = serializers.SlugRelatedField(
        queryset=Role.objects.all(), slug_field="name", required=False, allow_null=True
    )

    class Meta:
        model = ContactSupport
        fields = [
            "id",
            "user",
            "subject",
            "message",
            "contact_email",
            "created_at",
            "assigned_to_role",
        ]
        read_only_fields = ["id", "user", "created_at"]

    def create(self, validated_data):
        return ContactSupport.objects.create(**validated_data)

    def update(self, instance, validated_data):
        instance.subject = validated_data.get("subject", instance.subject)
        instance.message = validated_data.get("message", instance.message)
        instance.contact_email = validated_data.get(
            "contact_email", instance.contact_email
        )
        instance.assigned_to_role = validated_data.get(
            "assigned_to_role", instance.assigned_to_role
        )
        instance.save()
        return instance
