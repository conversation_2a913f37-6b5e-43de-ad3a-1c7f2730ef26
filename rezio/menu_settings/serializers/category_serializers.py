from rest_framework import serializers
from rezio.menu_settings.models import Category
from rezio.user.models import Role


class CategorySerializer(serializers.ModelSerializer):
    accessible_to_roles = serializers.SlugRelatedField(
        many=True, queryset=Role.objects.all(), slug_field="name"
    )

    class Meta:
        model = Category
        fields = ["id", "name", "slug", "description", "accessible_to_roles"]
        read_only_fields = ["id", "slug"]

    def create(self, validated_data):
        roles = validated_data.pop("accessible_to_roles", [])
        category = Category.objects.create(**validated_data)
        category.accessible_to_roles.set(roles)
        return category

    def update(self, instance, validated_data):
        roles = validated_data.pop("accessible_to_roles", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        if roles is not None:
            instance.accessible_to_roles.set(roles)
        instance.save()
        return instance
