from rezio.properties.utils import (
    get_s3_object,
    get_primary_phone_code,
    get_primary_number,
)
from rest_framework import serializers
from rezio.user.models import AgentProfile, InvestorProfile, MenuSettingsPrivacyPolicy
from rezio.user.serializers import PrivacyPolicySerializer, TermsAndConditionsSerializer
from rezio.user.utils import (
    get_menu_settings_privacy_policy_object,
    get_terms_and_condition_object,
)


class AgentProfileSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    primary_phone_number = serializers.CharField(
        source="user.primary_phone_number", read_only=True
    )
    secondary_phone_number = serializers.CharField(
        source="user.secondary_phone_number", read_only=True
    )
    profile_photo = serializers.SerializerMethodField(read_only=True)
    subscription_status = serializers.CharField(read_only=True)
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    secondary_phone_code = serializers.SerializerMethod<PERSON>ield(read_only=True)
    is_investor_profile_exist = serializers.SerializerMethodField(read_only=True)
    privacy_link_data = serializers.SerializerMethodField(read_only=True)
    terms_and_condition = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    secondary_number = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "id",
            "user",
            "agency",
            "name",
            "brn",
            "primary_phone_number",
            "secondary_phone_number",
            "license_start_date",
            "license_end_date",
            "created_ts",
            "updated_ts",
            "profile_photo",
            "subscription_status",
            "primary_phone_code",
            "secondary_phone_code",
            "is_investor_profile_exist",
            "privacy_link_data",
            "primary_number",
            "secondary_number",
            "terms_and_condition",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_secondary_phone_code(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_phone_code(instance.user.secondary_phone_number)

    def get_is_investor_profile_exist(self, instance):
        return InvestorProfile.objects.filter(user=instance.user).exists()

    def get_privacy_link_data(self, instance):
        privacy_object = get_menu_settings_privacy_policy_object()
        serializer = PrivacyPolicySerializer(privacy_object)
        return serializer.data

    def get_terms_and_condition(self, instance):
        terms_object = get_terms_and_condition_object()
        serializer = TermsAndConditionsSerializer(terms_object)
        return serializer.data

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)

    def get_secondary_number(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_number(instance.user.secondary_phone_number)


class InvestorProfileSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    primary_phone_number = serializers.CharField(
        source="user.primary_phone_number", read_only=True
    )
    secondary_phone_number = serializers.CharField(
        source="user.secondary_phone_number", read_only=True
    )
    profile_photo = serializers.SerializerMethodField(read_only=True)
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    secondary_phone_code = serializers.SerializerMethodField(read_only=True)
    is_agent_profile_exist = serializers.SerializerMethodField(read_only=True)
    privacy_link_data = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    secondary_number = serializers.SerializerMethodField(read_only=True)
    terms_and_condition = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            "id",
            "user",
            "name",
            "primary_phone_number",
            "secondary_phone_number",
            "investor_type",
            "created_ts",
            "updated_ts",
            "profile_photo",
            "primary_phone_code",
            "secondary_phone_code",
            "is_agent_profile_exist",
            "privacy_link_data",
            "primary_number",
            "secondary_number",
            "terms_and_condition",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_secondary_phone_code(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_phone_code(instance.user.secondary_phone_number)

    def get_is_agent_profile_exist(self, instance):
        return AgentProfile.objects.filter(user=instance.user).exists()

    def get_privacy_link_data(self, instance):
        privacy_object = get_menu_settings_privacy_policy_object()
        serializer = PrivacyPolicySerializer(privacy_object)
        return serializer.data

    def get_terms_and_condition(self, instance):
        terms_object = get_terms_and_condition_object()
        serializer = TermsAndConditionsSerializer(terms_object)
        return serializer.data

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)

    def get_secondary_number(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_number(instance.user.secondary_phone_number)
