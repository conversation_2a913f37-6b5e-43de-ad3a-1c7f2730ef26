from rest_framework import serializers
from rezio.menu_settings.models import Issue
from rezio.user.models import User
from rezio.menu_settings.models import ContactSupport


class IssueSerializer(serializers.ModelSerializer):
    user = (
        serializers.SerializerMethodField()
    )  # Use SerializerMethodField to format the phone number as a string
    image_url = serializers.URLField(
        allow_blank=True, required=False
    )  # Use URLField for the image URL

    class Meta:
        model = Issue
        fields = "__all__"

    def get_user(self, obj):
        # Convert the user's primary_phone_number (PhoneNumberField) to string
        return str(obj.user.primary_phone_number)

    def validate(self, data):
        if not data.get("category"):
            raise serializers.ValidationError("Category is required")
        if not data.get("description"):
            raise serializers.ValidationError("Description is required")
        return data


class ContactSupportSerializer(serializers.ModelSerializer):
    user = serializers.ReadOnlyField(source="user.primary_phone_number")

    class Meta:
        model = ContactSupport
        fields = ["user", "subject", "message", "contact_email", "created_at"]

    def validate(self, data):
        if not data.get("subject"):
            raise serializers.ValidationError("Subject is required")
        if not data.get("message"):
            raise serializers.ValidationError("Message is required")
        if not data.get("contact_email"):
            raise serializers.ValidationError("Contact email is required")
        return data
