# Generated by Django 4.1.7 on 2024-11-20 15:22

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0033_alter_agentprofile_working_type"),
        ("menu_settings", "0001_initial"),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name="rating",
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name="issue",
            name="user_type",
        ),
        migrations.RemoveField(
            model_name="issueimage",
            name="image_url",
        ),
        migrations.AddField(
            model_name="issue",
            name="visible_to_roles",
            field=models.ManyToManyField(
                blank=True, related_name="visible_issues", to="user.role"
            ),
        ),
        migrations.AddField(
            model_name="issueimage",
            name="image",
            field=models.ImageField(blank=True, null=True, upload_to="issue_images/"),
        ),
        migrations.AddConstraint(
            model_name="rating",
            constraint=models.UniqueConstraint(
                fields=("agent", "rated_by"), name="unique_agent_rating"
            ),
        ),
    ]
