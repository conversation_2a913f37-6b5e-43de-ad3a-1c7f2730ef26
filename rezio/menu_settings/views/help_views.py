from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.core.files.storage import default_storage
from django.db.models import Q
from rest_framework.decorators import action
from rezio.menu_settings.models import Issue, ContactSupport, IssueImage
from rezio.menu_settings.serializers.issue_serializer import (
    IssueSerializer,
    IssueImageSerializer,
)
from rezio.menu_settings.serializers.contact_support_serializers import (
    ContactSupportSerializer,
)
from rezio.utils.exception_handler import custom_exception_handler
import logging

logger = logging.getLogger(__name__)


class IssueViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user-reported issues.
    Allows authenticated users to report, view, and manage issues.
    """

    serializer_class = IssueSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Retrieve issues visible to the authenticated user.
        Includes issues reported by the user or visible to their roles.
        """
        user = self.request.user
        user_roles = user.roles.all()
        return Issue.objects.filter(
            Q(reported_by=user) | Q(visible_to_roles__in=user_roles)
        ).distinct()

    def perform_create(self, serializer):
        """
        Save the reported issue and associate any uploaded images with it.
        """
        try:
            issue = serializer.save(reported_by=self.request.user)
            images = self.request.FILES.getlist("images")
            for image_file in images:
                image_path = default_storage.save(image_file.name, image_file)
                image_url = default_storage.url(image_path)
                IssueImage.objects.create(issue=issue, image=image_url)
        except Exception as e:
            logger.error(f"Error creating issue: {e}")
            raise e

    def list(self, request, *args, **kwargs):
        """
        Retrieve a list of issues visible to the user.
        Returns an empty state message if no issues are found.
        """
        try:
            queryset = self.get_queryset()
            if queryset.exists():
                return super().list(request, *args, **kwargs)
            return Response(
                {"message": "No issues reported yet.", "data": [], "error": {}},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return custom_exception_handler(e)


class ContactSupportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing support contact requests.
    Allows users to create and view their contact support requests.
    Admins and Support Agents can view all requests.
    """

    serializer_class = ContactSupportSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Retrieve support contact requests based on the user's role.
        Admins and Support Agents can view all requests,
        while regular users can only see their own requests.
        """
        user = self.request.user
        if user.roles.filter(Q(name="Admin") | Q(name="SupportAgent")).exists():
            return ContactSupport.objects.all()
        return ContactSupport.objects.filter(user=user)

    def perform_create(self, serializer):
        """
        Save a new support contact request linked to the authenticated user.
        """
        try:
            serializer.save(user=self.request.user)
        except Exception as e:
            logger.error(f"Error creating contact support request: {e}")
            raise e
