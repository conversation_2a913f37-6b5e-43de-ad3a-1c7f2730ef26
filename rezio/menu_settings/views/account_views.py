from django.contrib.auth import logout
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from rezio.menu_settings.models import FAQ, Rating
from rezio.menu_settings.serializers.faq_serializer import FAQSerializer
from rezio.menu_settings.serializers.profile_serializers import (
    AgentProfileSerializer,
    InvestorProfileSerializer,
)
from rezio.menu_settings.serializers.ratings_serializers import RatingSerializer
from rezio.user.authentication import J<PERSON>TAuthentication, FirebaseAuthentication
from rezio.user.models import AgentProfile, InvestorProfile, Role
from rezio.utils.exception_handler import custom_exception_handler


class AccountManagementViewSet(viewsets.ViewSet):
    """
    ViewSet for managing account-related tasks, such as retrieving profiles,
    logging out, and switching accounts.
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def list(self, request):
        """
        Retrieve available profiles for the authenticated user.
        """
        try:
            user = request.user
            investor_profile = InvestorProfile.objects.filter(user=user).first()
            agent_profile = AgentProfile.objects.filter(user=user).first()

            data = {
                "current_role": (
                    user.roles.first().name if user.roles.exists() else None
                ),
                "available_profiles": {
                    "investor_profile": {
                        "exists": bool(investor_profile),
                        "details": (
                            InvestorProfileSerializer(investor_profile).data
                            if investor_profile
                            else None
                        ),
                    },
                    "agent_profile": {
                        "exists": bool(agent_profile),
                        "details": (
                            AgentProfileSerializer(agent_profile).data
                            if agent_profile
                            else None
                        ),
                    },
                },
            }

            return Response(
                {
                    "message": "Profile information retrieved successfully",
                    "data": data,
                    "error": {},
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return custom_exception_handler(e)

    def destroy(self, request):
        """
        Log out the user and invalidate their session.
        """
        try:
            logout(request)
            return Response(
                {"message": "Successfully logged out", "data": {}, "error": {}},
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return custom_exception_handler(e)

    @action(detail=False, methods=["patch"], url_path="switch_account")
    def switch_account(self, request):
        """
        Switch user accounts between Agent and Investor roles.
        """
        try:
            new_role = request.data.get("new_role")
            if new_role not in ["Agent", "Investor"]:
                return Response(
                    {
                        "message": "Invalid role provided.",
                        "data": {},
                        "error": {"detail": "Role must be 'Agent' or 'Investor'."},
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user = request.user
            user.roles.clear()
            role = Role.objects.get(name=new_role)
            user.roles.add(role)

            if new_role == "Agent":
                AgentProfile.objects.get_or_create(user=user)
                InvestorProfile.objects.filter(user=user).delete()
            elif new_role == "Investor":
                InvestorProfile.objects.get_or_create(user=user)
                AgentProfile.objects.filter(user=user).delete()

            return Response(
                {"message": "Account switched successfully.", "data": {}, "error": {}},
                status=status.HTTP_200_OK,
            )
        except Role.DoesNotExist:
            return Response(
                {
                    "message": "Role does not exist.",
                    "data": {},
                    "error": {"detail": "Specified role was not found."},
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return custom_exception_handler(e)


class FAQViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for retrieving Frequently Asked Questions (FAQs).
    """

    queryset = FAQ.objects.all()
    serializer_class = FAQSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_queryset(self):
        """
        Filter FAQs based on the user's roles.
        """
        user = self.request.user
        user_roles = user.roles.all()
        return self.queryset.filter(visible_to_roles__in=user_roles).distinct()

    def list(self, request, *args, **kwargs):
        """
        List FAQs visible to the requesting user.
        """
        try:
            queryset = self.get_queryset()
            if not queryset.exists():
                return Response(
                    {"message": "No FAQs available.", "data": [], "error": {}},
                    status=status.HTTP_200_OK,
                )
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {
                    "message": "FAQs retrieved successfully",
                    "data": serializer.data,
                    "error": {},
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return custom_exception_handler(e)


class RatingViewSet(viewsets.ViewSet):
    """
    ViewSet for managing user ratings, including listing, creating,
    and updating ratings.
    """

    permission_classes = [IsAuthenticated]
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def list(self, request):
        """
        Retrieve ratings submitted by the authenticated user.
        """
        try:
            user = request.user
            ratings = Rating.objects.filter(rated_by=user)
            serializer = RatingSerializer(ratings, many=True)
            return Response(
                {
                    "message": "Ratings retrieved successfully",
                    "data": serializer.data,
                    "error": {},
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return custom_exception_handler(e)

    def create(self, request):
        """
        Submit or update a rating for the authenticated user.
        """
        try:
            user = request.user
            serializer = RatingSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save(rated_by=user)
                return Response(
                    {
                        "message": "Rating submitted successfully",
                        "data": serializer.data,
                        "error": {},
                    },
                    status=status.HTTP_201_CREATED,
                )
            return Response(
                {"message": "Invalid data", "data": {}, "error": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return custom_exception_handler(e)

    @action(
        detail=False, methods=["patch"], url_path="update_rating/(?P<agent_id>[^/.]+)"
    )
    def update_rating(self, request, agent_id=None):
        """
        Update an existing rating for a specific agent.
        """
        try:
            user = request.user
            rating = Rating.objects.filter(rated_by=user, agent__id=agent_id).first()
            if not rating:
                return Response(
                    {
                        "message": "Rating not found.",
                        "data": {},
                        "error": {"detail": "You have not rated this agent yet."},
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = RatingSerializer(rating, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {
                        "message": "Rating updated successfully",
                        "data": serializer.data,
                        "error": {},
                    },
                    status=status.HTTP_200_OK,
                )
            return Response(
                {"message": "Invalid data", "data": {}, "error": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return custom_exception_handler(e)
