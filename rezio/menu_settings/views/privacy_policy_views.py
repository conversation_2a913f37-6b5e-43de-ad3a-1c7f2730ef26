import boto3
from botocore.exceptions import NoCredentials<PERSON>rror, ClientError
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from rest_framework import status
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)


class PrivacyPolicyView(APIView):
    """
    APIView to retrieve the privacy policy file's pre-signed URL.
    This view uses AWS S3 to generate a temporary URL for the privacy policy PDF.
    The URL is cached for subsequent requests to optimize performance.
    """

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Handles GET requests to retrieve the privacy policy file's URL.

        Returns:
            - 200: Pre-signed URL for the privacy policy.
            - 404: If the privacy policy file is not found on S3.
            - 500: For internal server errors, such as missing AWS credentials.
        """
        cache_key = f"privacy_policy_url_{request.user.id}"
        url = cache.get(cache_key)

        if not url:
            try:
                # Initialize the S3 client
                s3 = boto3.client(
                    "s3",
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                    region_name=settings.AWS_S3_REGION_NAME,
                )

                # Retrieve settings or use defaults
                policy_file_key = getattr(
                    settings,
                    "PRIVACY_POLICY_S3_KEY",
                    "privacy_policy/privacy_policy.pdf",
                )
                url_expiration = getattr(
                    settings, "PRIVACY_POLICY_URL_EXPIRATION", 3600
                )

                # Generate the pre-signed URL
                url = s3.generate_presigned_url(
                    ClientMethod="get_object",
                    Params={
                        "Bucket": settings.AWS_STORAGE_BUCKET_NAME,
                        "Key": policy_file_key,
                    },
                    ExpiresIn=url_expiration,
                )

                # Cache the generated URL
                cache.set(cache_key, url, timeout=url_expiration)

            except NoCredentialsError:
                logger.error("AWS credentials not provided")
                return Response(
                    {"detail": "Internal server error: AWS credentials missing"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            except ClientError as e:
                error_code = e.response.get("Error", {}).get("Code", None)
                if error_code == "NoSuchKey":
                    logger.error("Privacy policy file not found in S3")
                    return Response(
                        {"detail": "Privacy policy file not found"},
                        status=status.HTTP_404_NOT_FOUND,
                    )
                else:
                    logger.error(f"AWS ClientError occurred: {e}")
                    return Response(
                        {"detail": "Internal server error"},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    )
            except Exception as e:
                logger.error(f"Unexpected error occurred: {e}")
                return Response(
                    {"detail": "An unexpected error occurred"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        # Return the cached or newly generated URL
        return Response({"url": url}, status=status.HTTP_200_OK)
