import logging

from rest_framework import viewsets, status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response

from rezio.menu_settings.serializers.profile_serializers import (
    AgentProfileSerializer,
    InvestorProfileSerializer,
)
from rezio.user.authentication import JWTAuthentication, FirebaseAuthentication
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.models import Agent<PERSON><PERSON><PERSON><PERSON>, InvestorProfile
from rezio.user.permissions import IsAuthenticatedAgent, IsAuthenticatedInvestor
from rezio.user.utils import (
    get_role_object,
    get_agent_profile_object,
    get_investor_profile_object,
)
from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    DJANGO_LOGGER_NAME,
    KEY_ERROR_MESSAGE,
)
from rezio.utils.custom_exceptions import (
    ResourceNotFoundException,
    InvalidSerializerDataException,
    InternalServerException,
)
from rezio.utils.exception_handler import custom_exception_handler

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class ProfileViewSet(viewsets.ViewSet):
    """
    A ViewSet for managing Agent and Investor profiles.
    This view allows users to retrieve and update their profile information.
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        permission_classes = []
        role_name = self.request.query_params.get("user_role", None)
        role = get_role_object(role_name)
        if role.name == AGENT:
            permission_classes = [IsAuthenticatedAgent]
        elif role.name == INVESTOR:
            permission_classes = [IsAuthenticatedInvestor]
        else:
            raise PermissionDenied(detail="Role not found")

        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get_serializer_class_and_profile(self, user):
        """
        Determine the appropriate serializer class and profile instance based on the user's role.

        Args:
            user: The currently authenticated user.

        Returns:
            tuple: Serializer class and profile instance.

        Raises:
            ResourceNotFoundException: If the user does not have a valid profile.
        """
        if user.roles.filter(name="Agent").exists():
            profile = AgentProfile.objects.filter(user=user).first()
            serializer_class = AgentProfileSerializer
        elif user.roles.filter(name="Investor").exists():
            profile = InvestorProfile.objects.filter(user=user).first()
            serializer_class = InvestorProfileSerializer
        else:
            profile = None
            serializer_class = None
        return serializer_class, profile

    def list(self, request):
        """
        Retrieve the user's profile information.

        Returns:
            Response: Serialized profile data or a 404 error if no profile is found.
        """
        try:
            user_role = request.query_params.get("user_role")
            if user_role == AGENT:
                profile = get_agent_profile_object(request.user)
                serializer = AgentProfileSerializer(instance=profile)
            elif user_role == INVESTOR:
                profile = get_investor_profile_object(request.user)
                serializer = InvestorProfileSerializer(instance=profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: f"{user_role} data fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in ProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def update(self, request, pk=None):
        """
        Update the user's profile information.

        Args:
            request: The HTTP request containing updated profile data.
            pk: Primary key (not used).

        Returns:
            Response: Updated profile data or validation errors.
        """
        try:
            user = request.user
            serializer_class, profile = self.get_serializer_class_and_profile(user)
            if not profile:
                raise ResourceNotFoundException("Profile not found")

            serializer = serializer_class(profile, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                raise InvalidSerializerDataException(serializer.errors)
        except ResourceNotFoundException as e:
            return Response({"detail": str(e)}, status=status.HTTP_404_NOT_FOUND)
        except InvalidSerializerDataException as e:
            return Response(
                {"detail": e.message, "errors": e.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def partial_update(self, request, pk=None):
        """
        Partially update the user's profile information.

        Args:
            request: The HTTP request containing updated profile data.
            pk: Primary key (not used).

        Returns:
            Response: Partially updated profile data or validation errors.
        """
        try:
            user = request.user
            serializer_class, profile = self.get_serializer_class_and_profile(user)
            if not profile:
                raise ResourceNotFoundException("Profile not found")

            serializer = serializer_class(profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data, status=status.HTTP_200_OK)
            else:
                raise InvalidSerializerDataException(serializer.errors)
        except ResourceNotFoundException as e:
            return Response({"detail": str(e)}, status=status.HTTP_404_NOT_FOUND)
        except InvalidSerializerDataException as e:
            return Response(
                {"detail": e.message, "errors": e.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})
