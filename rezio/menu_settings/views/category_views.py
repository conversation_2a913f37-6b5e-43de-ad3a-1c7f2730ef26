from rest_framework import viewsets, status
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdmin<PERSON>ser
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rezio.menu_settings.models import Category
from rezio.menu_settings.serializers.category_serializers import CategorySerializer
from rezio.utils.exception_handler import custom_exception_handler


class CategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing categories.
    - <PERSON><PERSON> can create, update, delete categories.
    - Authenticated users can view/list categories.
    """

    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    pagination_class = PageNumberPagination
    lookup_field = "slug"

    def get_permissions(self):
        """
        Returns the appropriate permissions based on the action.
        """
        if self.action in ["create", "update", "partial_update", "destroy"]:
            # Ensure the user is both authenticated and an admin for these actions.
            return [permission() for permission in [IsAuthenticated, IsAdminUser]]
        # For list and retrieve actions, only authentication is required.
        return [IsAuthenticated()]

    def create(self, request, *args, **kwargs):
        """
        Create a new category. Admin only.
        """
        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def list(self, request, *args, **kwargs):
        """
        List categories. Accessible to all authenticated users.
        """
        try:
            return super().list(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific category by slug.
        """
        try:
            return super().retrieve(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def update(self, request, *args, **kwargs):
        """
        Update category details. Admin only.
        """
        try:
            return super().update(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def partial_update(self, request, *args, **kwargs):
        """
        Partially update a category. Admin only.
        """
        try:
            return super().partial_update(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})

    def destroy(self, request, *args, **kwargs):
        """
        Delete a category. Admin only.
        """
        try:
            return super().destroy(request, *args, **kwargs)
        except Exception as e:
            return custom_exception_handler(e, context={"view": self})
