# Generated by Django 4.1.7 on 2025-01-02 14:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0045_investorprofile_profile_public_view"),
        ("analytics", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvestorProfileView",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("viewed_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField()),
                (
                    "investor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user.investorprofile",
                    ),
                ),
            ],
        ),
    ]
