from django.db import models

from rezio.user.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, InvestorProfile


class AgentP<PERSON><PERSON>le<PERSON>ie<PERSON>(models.Model):
    """
    Model for analytics of agent profile view
    """

    agent = models.ForeignKey(
        AgentProfile, on_delete=models.CASCADE, null=True
    )  # Changed to null=True temporarily

    viewed_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=False)

    def __str__(self):
        return {
            "agent": self.agent,
            "ip_address": self.ip_address,
            "viewed_at": self.viewed_at,
        }


class InvestorProfileView(models.Model):
    """
    Model for analytics of investor profile view
    """

    investor = models.ForeignKey(InvestorProfile, on_delete=models.CASCADE)

    viewed_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=False)

    def __str__(self):
        return {
            "investor": self.investor,
            "ip_address": self.ip_address,
            "viewed_at": self.viewed_at,
        }
