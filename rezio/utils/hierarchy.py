import logging
from typing import Optional

from django.conf import settings
from django.db.models.query_utils import Q

from rezio.properties.constants import (
    EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES,
    LAND_PLOT_HIDDEN_ATTRIBUTES,
    OFFICE_CO_WORKING_HIDDEN_ATTRIBUTES,
    SHOP_SHOW_ROOM_SCHOOL_RETAIL_HIDDEN_ATTRIBUTES,
    GODOWN_INDUSTRIAL_SHED_FACTORY_HIDDEN_ATTRIBUTES,
    INDUSTRIAL_BUILDING_HIDDEN_ATTRIBUTES,
    HOSPITAL_HIDDEN_ATTRIBUTES,
)
from rezio.properties.models import (
    Property,
    PropertyVerifiedDataFields,
    UserLevelPropertyData,
    UserLevelPropertyFeatures,
)
from rezio.properties.models import (
    PropertyCoOwner,
    AgentAssociatedProperty,
    PropertyAttributes,
    HierarchyLevel,
)
from rezio.properties.serializers.property_hierarchy_serializer import (
    LocationDetailsAttributesSerializer,
    PropertySpecificationsAttributesSerializer,
    BedroomDataSerializer,
    BathroomDataSerializer,
    ParkingDataSerializer,
    PropertyUnitFeaturesAttributesSerializer,
    FloorPlanAttributesSerializer,
    OtherInformationAttributesSerializer,
    PropertyOwnerDetailsSerializer,
    PropertyAgentsDetailsSerializer,
    RestroomDataSerializer,
    LiftDataSerializer,
    CommercialParkingDataSerializer,
    ScrollableComponentPropertySpecificationsAttributesSerializer,
    PropertyUnitImagesSerializer,
    InvestorDetailsSerializer,
    PropertyAvailabilityAndStatusAttributesSerializer,
    IncomeFromPropertyAttributesSerializer,
    PreviousTransactionsAttributesSerializer,
    InvestorCardDetailsSerializer,
    AgentCardDetailsSerializer,
    UpperComponentLocationDetailsAttributesSerializer,
)
from rezio.properties.text_choices import (
    PropertyAttributesCategory,
    OwnerIntentForProperty,
    UserRequestActions,
    UserViewCTA,
    PropertyAvailabilityStatus,
    PropertyAgentType,
    PropertyCompletionStateChoices,
    PropertyCategory,
    PropertyType,
)
from rezio.properties.utils import (
    get_co_owner_list,
    build_return_values_for_attributes,
    get_exchange_rates,
    get_property_gross_yield,
    get_property_net_yield,
    get_property_gains_data,
    build_owner_information_action_button,
    get_property_completion_state_object,
    get_property_trends_filters,
)
from rezio.property_integrations.services.property_monitor import PropertyMonitorAPI
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import get_profile_object_by_role
from rezio.user.models import User, Role, UserPropertySharingControlAttributes
from rezio.user.utils import get_list_of_object_with_filters
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    KEY_PAYLOAD,
)
from rezio.utils.custom_exceptions import InvalidSerializerDataException

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class Hierarchy:
    def __init__(
        self,
        viewer: Optional[User],
        viewer_role: Optional[Role],
        property_obj: Property,
        self_view: bool,
        viewed_user: User,
        viewed_user_role: Role,
        is_common_view: bool = False,
    ):
        self.viewer = viewer
        self.viewer_role = viewer_role
        self.self_view = self_view
        self.property_obj = property_obj
        self.viewer_level_property = UserLevelPropertyData.objects.filter(
            property=property_obj
        )
        self.viewed_user = viewed_user
        self.viewed_user_role = viewed_user_role
        self.is_common_view = is_common_view
        self.property_agents = AgentAssociatedProperty.objects.filter(
            (Q(action_status=UserRequestActions.PENDING) & Q(is_associated=False))
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True)),
            property=self.property_obj,
            is_request_expired=False,
        )
        self.property_co_owners = PropertyCoOwner.objects.filter(
            property=self.property_obj
        )
        self.viewer_profile = (
            self.get_user_profile_object(viewer, viewer_role)
            if viewer and viewer_role
            else None
        )
        self.viewed_profile = (
            self.viewer_profile
            if self.self_view
            else self.get_user_profile_object(viewed_user, viewed_user_role)
        )
        self.property_max_hierarchy = self.get_property_max_hierarchy()
        self.viewed_user_hierarchy_level = self.get_user_relation_to_property(
            self.viewed_profile, self.viewed_user_role, viewed_user_hierarchy=True
        )
        self.viewer_hierarchy_level = self.get_user_relation_to_property(
            self.viewer_profile, self.viewer_role
        )
        self.non_display_fields = self.get_non_display_fields()
        self.viewer_edit_access = self.get_edit_rights()
        self.is_viewer_associated_to_property = self.get_is_viewer_associated()
        self.property_data_source_obj = get_property_completion_state_object(
            self.property_obj.id,
            state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
        )
        self.viewer_viewable_attributes = self.get_final_viewable_attributes()
        self.viewer_editable_attributes = self.get_final_editable_attributes()
        self.property_verified_fields_and_values = (
            self.get_property_verified_fields_and_values()
        )

    @staticmethod
    def get_user_profile_object(user: User, role: Role):
        return get_profile_object_by_role(user, role)

    def get_edit_rights(self):
        if self.self_view and self.viewer_hierarchy_level.name in ["Owner", "Agent"]:
            return True
        else:
            return False

    def get_is_viewer_associated(self):
        if self.is_common_view:
            return False
        if self.viewer_role and self.viewer_role.name == AGENT:
            if self.property_agents.filter(
                agent_profile=self.viewer_profile, is_associated=True
            ).exists():
                return True
        elif self.viewer_role and self.viewer_role.name == INVESTOR:
            if (
                self.property_obj.owner_verified
                and self.property_obj.owner == self.viewer_profile
            ):
                return True
            elif self.property_co_owners.filter(
                co_owner=self.viewer_profile, is_associated=True
            ).exists():
                return True
        return False

    @staticmethod
    def get_hierarchy_level(name):
        hierarchy = HierarchyLevel.objects.filter(name=name).first()
        if not hierarchy:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid data sent"},
                }
            )
        return hierarchy

    def get_user_relation_to_property(
        self, profile, role: Role, viewed_user_hierarchy=False
    ):
        if (
            (not viewed_user_hierarchy and not self.self_view)
            or not profile
            or not role
        ):
            return self.get_hierarchy_level("Public")
        if role.name == INVESTOR:
            if viewed_user_hierarchy:
                self.viewer_level_property = self.viewer_level_property.filter(
                    created_by=self.property_obj.owner.user, created_by_role=role
                ).first()
            if not self.viewer_level_property:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid Property Details"},
                    }
                )
            if self.property_obj.owner_verified and self.property_obj.owner == profile:
                return self.get_hierarchy_level("Owner")
            property_co_owner = self.property_co_owners.filter(
                co_owner=profile, is_associated=True
            )
            if property_co_owner.exists():
                return self.get_hierarchy_level("Co-owner")
        if role.name == AGENT:
            if viewed_user_hierarchy:
                self.viewer_level_property = self.viewer_level_property.filter(
                    created_by=profile.user, created_by_role=role
                ).first()
            if not self.viewer_level_property:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid Property Details"},
                    }
                )
            property_associated_agent = self.property_agents.filter(
                agent_profile=profile, is_associated=True
            )
            if property_associated_agent.exists():
                return self.get_hierarchy_level("Agent")
        if self.self_view or viewed_user_hierarchy:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid Property Details"},
                }
            )
        return self.get_hierarchy_level("Public")

    def get_property_max_hierarchy(self):
        if self.property_obj.owner_verified and self.property_obj.owner:
            return self.get_hierarchy_level("Owner")
        if self.property_co_owners.filter(is_associated=True).exists():
            return self.get_hierarchy_level("Co-owner")
        if self.property_agents.exists():
            return self.get_hierarchy_level("Agent")
        raise InvalidSerializerDataException(
            {
                KEY_MESSAGE: "Invalid data sent",
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid Property Details"},
            }
        )

    def get_non_display_fields(self):
        viewer_hierarchy_level = self.viewer_hierarchy_level.level
        non_display_fields = PropertyAttributes.objects.filter(
            ~Q(
                attribute_category__in=[
                    self.property_obj.property_category,
                    PropertyAttributesCategory.BOTH.value,
                ]
            )
            | Q(is_public=False, hierarchy_level__level__lte=viewer_hierarchy_level)
        )
        if viewer_hierarchy_level == 1:
            non_display_fields.exclude(hierarchy_level__level=1)
        return non_display_fields

    def get_component_level_non_display_fields(self, component_name: str):
        non_display_fields = list(
            (
                self.non_display_fields.filter(
                    component_name=component_name
                ).values_list("attribute_name", flat=True)
            )
        )
        return non_display_fields

    @staticmethod
    def build_display_fields(display_fields_status: dict, non_display_fields: list):
        for non_display_field in non_display_fields:
            display_fields_status[non_display_field] = False
        return display_fields_status

    def viewer_viewable_attributes(self) -> list:
        attributes = self.viewer_hierarchy_level.viewed_attributes
        return attributes

    def viewer_editable_attributes(self) -> list:
        attributes = self.viewer_hierarchy_level.editable_attributes
        return attributes

    def get_attributes_to_be_skipped(self) -> list:
        by_property_category = PropertyAttributes.objects.filter(
            ~Q(
                attribute_category__in=[
                    self.property_obj.property_category,
                    PropertyAttributesCategory.BOTH.value,
                ]
            )
        ).values_list("attribute_name", flat=True)
        return by_property_category

    def get_share_control_attributes(self) -> list:
        private_fields = (
            get_list_of_object_with_filters(
                app_name="user",
                model_name=UserPropertySharingControlAttributes.__name__,
                single_field_value_dict={
                    "created_by": self.viewed_user,
                    "created_by_role": self.viewed_user_role,
                    "property_category": self.property_obj.property_category,
                },
            )
            .values_list("private_fields", flat=True)
            .first()
        )
        logger.info(f"List of private fields attributes: {private_fields}")
        return private_fields

    def get_land_plot_property_hidden_attributes(self) -> list:
        if (
            self.viewer_level_property.property_type
            in EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES
        ):
            return LAND_PLOT_HIDDEN_ATTRIBUTES
        return []

    def get_commercial_property_hidden_attributes(self) -> list:
        property_type = self.viewer_level_property.property_type
        if property_type in [PropertyType.OFFICE_SPACE, PropertyType.CO_WORKING]:
            return OFFICE_CO_WORKING_HIDDEN_ATTRIBUTES
        elif property_type in [
            PropertyType.SHOP,
            PropertyType.SHOWROOM,
            PropertyType.SCHOOL,
            PropertyType.RETAIL_SPACE,
        ]:
            return SHOP_SHOW_ROOM_SCHOOL_RETAIL_HIDDEN_ATTRIBUTES
        elif property_type in [
            PropertyType.GODOWN_WAREHOUSE,
            PropertyType.INDUSTRIAL_SHED,
            PropertyType.FACTORY,
        ]:
            return GODOWN_INDUSTRIAL_SHED_FACTORY_HIDDEN_ATTRIBUTES
        elif property_type in [PropertyType.INDUSTRIAL_BUILDING]:
            return INDUSTRIAL_BUILDING_HIDDEN_ATTRIBUTES
        elif property_type in [PropertyType.HOSPITAL_CLINIC]:
            return HOSPITAL_HIDDEN_ATTRIBUTES
        return []

    def get_final_viewable_attributes(self):
        attributes = self.viewer_viewable_attributes()
        attributes_to_be_skipped = self.get_attributes_to_be_skipped()

        # Get attributes hidden for this property's country
        country_hidden_attributes = []
        if self.property_obj.country:
            country_hidden_attributes = list(
                PropertyAttributes.objects.filter(
                    hidden_in_countries=self.property_obj.country
                ).values_list("attribute_name", flat=True)
            )
            logger.info(
                f"Attributes hidden for country {self.property_obj.country}: {country_hidden_attributes}"
            )

        share_control_attributes = []
        if not self.self_view:
            share_control_attributes = self.get_share_control_attributes() or []

        # Special condition for Residential/Commercial Land/Plot
        # Only show total_area specification for these property types
        land_plot_hidden_attributes = self.get_land_plot_property_hidden_attributes()
        logger.info(
            f"Property type {self.viewer_level_property.property_type} - hiding specification attributes except total_area: "
            f"{land_plot_hidden_attributes}"
        )

        # Special condition for commercial type except Hotel, Guest House and S.C.O Plot
        commercial_property_hidden_attributes = []
        if (
            self.viewer_level_property.property.property_category
            == PropertyCategory.COMMERCIAL
            and self.viewer_level_property.property_type
            not in EXPLORE_VISIBILITY_CONSTRAINT_LAND_TYPES
        ):
            commercial_property_hidden_attributes = (
                self.get_commercial_property_hidden_attributes()
            )
            logger.info(
                f"Property type {self.viewer_level_property.property_type} - hiding specification attributes for commercial "
                f"properties: {commercial_property_hidden_attributes}"
            )

        final_attributes = list(
            set(attributes)
            - set(attributes_to_be_skipped)
            - set(share_control_attributes)
            - set(country_hidden_attributes)
            - set(land_plot_hidden_attributes)
            - set(commercial_property_hidden_attributes)
        )
        logger.info(f"List of final viewable attributes: {final_attributes}")
        return final_attributes

    def get_non_editable_attributes(self) -> list:
        property_verified_attributes = (
            PropertyVerifiedDataFields.objects.filter(
                property=self.property_obj,
            )
            .distinct("field_name")
            .values_list("field_name", flat=True)
        )
        return property_verified_attributes

    def get_final_editable_attributes(self):
        attributes = self.viewer_editable_attributes()
        non_editable_attributes = self.get_non_editable_attributes()
        attributes_to_be_skipped = self.get_attributes_to_be_skipped()
        final_attributes = list(
            set(attributes)
            - set(non_editable_attributes)
            - set(attributes_to_be_skipped)
        )
        return final_attributes

    def get_property_verified_fields_and_values(self):
        property_verified_fields = PropertyVerifiedDataFields.objects.filter(
            property=self.property_obj,
        ).values_list("field_name", "value")
        property_verified_fields_dict = dict(property_verified_fields)
        return property_verified_fields_dict

    def build_property_details_response(self):
        property_details_response = {
            "location_details": self.build_location_details_object(),
            "property_specifications": self.build_property_specifications_object(),
            "property_unit_features": self.build_property_unit_features_object(),
            "floor_plan_details": self.build_floor_plan_details_object(),
            "other_information": self.build_other_information_object(),
            "owner_information": self.build_owner_information_object(),
            "agent_information": self.build_agent_information_object(),
            "cta_component": self.build_cta_component(),
            "property_information": self.build_property_information_component(),
            "similar_transactions": self.build_similar_transactions_component(),
        }
        return property_details_response

    def build_property_financial_details_response(self):
        property_financial_details = {
            "property_availability_and_status": self.build_availability_and_status_object(),
            "income_from_property": self.build_income_from_property_object(),
            "gains_component": self.build_gains_component_object(),
            "lease_conditions": self.build_lease_conditions_object(),
            "previous_transactions": self.build_previous_transactions_object(),
            "cta_component": self.build_cta_component(),
            "property_information": self.build_property_information_component(),
        }
        return property_financial_details

    def build_basic_details_response(self):
        user_share_control = UserPropertySharingControlAttributes.share_control_exists(
            user=self.viewed_user,
            role=self.viewed_user_role,
            property_category=self.property_obj.property_category,
        )

        property_basic_details = {
            "location_details_upper_component": self.build_location_details_upper_component_object(),
            "price_details": self.build_price_details_object(),
            "scrollable_component": self.build_scrollable_component_object(),
            "unit_images_component": self.build_unit_images_component_object(),
            "profile_card": self.build_profile_card_object(),
            "property_intent": self.build_property_intent_component(),
            "property_information": self.build_property_information_component(),
            "share_icon": (
                False
                if self.property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE
                or self.is_common_view
                else True
            ),
            "privacy_sharing_control": (
                True
                if self.self_view
                and not user_share_control
                and not self.is_common_view
                and self.property_obj.owner_intent
                != OwnerIntentForProperty.NOT_FOR_SALE
                else False
            ),
        }
        return property_basic_details

    @staticmethod
    def get_is_component_editable(component_data: dict) -> bool:
        is_editable = any(
            component_data[key]["is_editable"]
            for key in component_data
            if component_data[key] and "is_editable" in component_data[key]
        )
        return is_editable

    @staticmethod
    def get_is_component_visibility(component_data: dict) -> bool:
        is_visible = any(
            component_data[key]["is_visible"]
            for key in component_data
            if component_data[key] and "is_visible" in component_data[key]
        )
        return is_visible

    def build_location_details_object(self):
        logger.info(
            f"Building location details object for property {self.property_obj.id}"
        )

        location_details_attributes = LocationDetailsAttributesSerializer(
            self.property_obj,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "viewed_user": self.viewed_user,
                "viewed_user_role": self.viewed_user_role,
                "self_view": self.self_view,
                "is_common_view": self.is_common_view,
            },
        ).data

        location_details = {
            "component_is_editable": self.get_is_component_editable(
                location_details_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                location_details_attributes
            ),
        }
        location_details.update(location_details_attributes)
        return location_details

    def build_property_specifications_object(self):
        logger.info(
            f"Building property specifications object for property {self.property_obj.id}"
        )

        bedroom_data = (
            BedroomDataSerializer(self.viewer_level_property).data
            if "bedroom_data" in self.viewer_viewable_attributes
            else None
        )

        bedroom_data = build_return_values_for_attributes(
            bedroom_data,
            "number_of_bedrooms",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
        )

        bathroom_data = (
            BathroomDataSerializer(self.viewer_level_property).data
            if "bathroom_data" in self.viewer_viewable_attributes
            else None
        )

        bathroom_data = build_return_values_for_attributes(
            bathroom_data,
            "bathroom_data",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
        )

        parking_data = (
            ParkingDataSerializer(self.viewer_level_property).data
            if "residential_parking_data" in self.viewer_viewable_attributes
            else None
        )

        parking_data = build_return_values_for_attributes(
            parking_data,
            "parking_number",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
        )

        property_specifications_attributes = PropertySpecificationsAttributesSerializer(
            self.viewer_level_property,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "is_common_view": self.is_common_view,
            },
        ).data
        property_specifications_attributes.update({"bedroom_data": bedroom_data})
        property_specifications_attributes.update({"bathroom_data": bathroom_data})
        property_specifications_attributes.update({"parking_data": parking_data})
        property_specifications_attributes["data_source"] = (
            self.property_data_source_obj.data_source
        )
        property_specifications = {
            "component_is_editable": self.get_is_component_editable(
                property_specifications_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                property_specifications_attributes
            ),
        }
        property_specifications.update(property_specifications_attributes)
        return property_specifications

    def build_property_unit_features_object(self):
        logger.info(
            f"Building property unit features object for property {self.property_obj.id}"
        )

        property_features = UserLevelPropertyFeatures.objects.filter(
            property_level_data=self.viewer_level_property
        ).first()

        make_invisible = False if property_features else True

        is_restroom_available = None
        is_parking_available = None
        is_lift_available = None
        if property_features:
            is_restroom_available = property_features.is_restroom_available
            is_parking_available = property_features.is_parking_available
            is_lift_available = property_features.is_lift_available

        restroom_data = (
            RestroomDataSerializer(property_features).data
            if "restroom_data" in self.viewer_viewable_attributes
            and is_restroom_available
            else None
        )

        restroom_data = build_return_values_for_attributes(
            restroom_data,
            "restroom_data",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.is_common_view,
        )

        lift_data = (
            LiftDataSerializer(property_features).data
            if "lift_data" in self.viewer_viewable_attributes and is_lift_available
            else None
        )

        lift_data = build_return_values_for_attributes(
            lift_data,
            "lift_data",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.is_common_view,
        )

        parking_data = (
            CommercialParkingDataSerializer(property_features).data
            if "parking_data" in self.viewer_viewable_attributes
            and is_parking_available
            else None
        )

        parking_data = build_return_values_for_attributes(
            parking_data,
            "commercial_parking_data",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=make_invisible,
            is_common_view=self.is_common_view,
        )

        property_unit_features_attributes = PropertyUnitFeaturesAttributesSerializer(
            property_features if property_features else {},
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "make_invisible": make_invisible,
                "is_common_view": self.is_common_view,
            },
        ).data
        property_unit_features_attributes.update({"restroom_data": restroom_data})
        property_unit_features_attributes.update({"lift_data": lift_data})
        property_unit_features_attributes.update({"parking_data": parking_data})

        property_unit_features = {
            "component_is_editable": self.get_is_component_editable(
                property_unit_features_attributes
            ),
            "component_is_visible": (
                (
                    self.is_common_view
                    and self.get_is_component_visibility(
                        property_unit_features_attributes
                    )
                )
                or (
                    not self.is_common_view
                    and self.viewer_hierarchy_level.level in [1, 2]
                )
            ),
        }
        property_unit_features.update(property_unit_features_attributes)
        return property_unit_features

    def build_floor_plan_details_object(self):
        logger.info(
            f"Building floor plan details object for property {self.property_obj.id}"
        )

        floor_plan_attributes = FloorPlanAttributesSerializer(
            self.property_obj,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "viewed_user": self.viewed_user,
                "viewed_user_role": self.viewed_user_role,
                "self_view": self.self_view,
                "is_common_view": self.is_common_view,
            },
        ).data

        floor_plan = {
            "component_is_editable": self.get_is_component_editable(
                floor_plan_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                floor_plan_attributes
            ),
        }
        floor_plan.update(floor_plan_attributes)
        return floor_plan

    def build_other_information_object(self):
        logger.info(
            f"Building other information object for property {self.property_obj.id}"
        )

        other_information_attributes = OtherInformationAttributesSerializer(
            self.property_obj,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "is_common_view": self.is_common_view,
            },
        ).data

        other_information = {
            "component_is_editable": self.get_is_component_editable(
                other_information_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                other_information_attributes
            ),
        }
        other_information.update(other_information_attributes)
        return other_information

    def build_owner_information_object(self):
        logger.info(
            f"Building owner information object for property {self.property_obj.id}"
        )
        viewer_hierarchy_level = self.viewer_hierarchy_level.level
        owner_details = None
        co_owner_details = list()
        if not self.is_common_view:
            if (
                self.self_view
                and viewer_hierarchy_level in [1, 2]
                and self.property_obj.owner_verified
            ):
                owner_details = (
                    PropertyOwnerDetailsSerializer(self.property_obj).data
                    if self.property_obj.owner
                    else None
                )

                co_owner_details = get_co_owner_list(
                    self.property_obj, self.property_co_owners
                )
            elif (
                self.viewed_user_role.name == INVESTOR
                and self.property_obj.owner_verified
                and self.viewer_role
                and self.viewer_role.name == AGENT
            ):
                owner_details = InvestorDetailsSerializer(self.viewed_profile).data

            if self.self_view and viewer_hierarchy_level in [1]:
                owner_verified = self.property_obj.owner_verified
                action_button = build_owner_information_action_button(
                    self.viewer_role,
                    owner_verified,
                    self.property_co_owners,
                    self.viewer_profile,
                )
                if action_button is not None:
                    action_button = build_return_values_for_attributes(
                        action_button,
                        "action_button",
                        self.viewer_viewable_attributes,
                        self.viewer_editable_attributes,
                        self.property_verified_fields_and_values,
                    )
                else:
                    action_button = {
                        "value": None,
                        "is_visible": False,
                        "is_editable": False,
                    }
                owner_verified = build_return_values_for_attributes(
                    owner_verified,
                    "owner_verified",
                    self.viewer_viewable_attributes,
                    self.viewer_editable_attributes,
                    self.property_verified_fields_and_values,
                )
            else:
                action_button = {
                    "value": None,
                    "is_visible": False,
                    "is_editable": False,
                }
                owner_verified = {
                    "value": None,
                    "is_visible": False,
                    "is_editable": False,
                }
        else:
            action_button = {
                "value": None,
                "is_visible": False,
                "is_editable": False,
            }
            owner_verified = {
                "value": None,
                "is_visible": False,
                "is_editable": False,
            }

        owner_details = build_return_values_for_attributes(
            owner_details,
            "owner_details",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=False if owner_details else True,
        )
        co_owner_details = build_return_values_for_attributes(
            co_owner_details,
            "co_owner_details",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=False if co_owner_details else True,
        )

        owner_information_attributes = dict()
        owner_information_attributes.update({"owner_details": owner_details})
        owner_information_attributes.update({"co_owner_details": co_owner_details})
        owner_information_attributes.update({"action_button": action_button})
        owner_information_attributes.update({"owner_verified": owner_verified})

        owner_information = {
            "component_is_editable": self.get_is_component_editable(
                owner_information_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                owner_information_attributes
            ),
        }
        owner_information.update(owner_information_attributes)
        return owner_information

    def build_agent_information_object(self):
        logger.info(
            f"Building agent information object for property {self.property_obj.id}"
        )
        viewer_hierarchy_level = self.viewer_hierarchy_level.level
        property_agents = list()
        make_invisible = False
        if self.self_view and self.property_agents.exists():
            if viewer_hierarchy_level == 2:
                property_agents = self.property_agents.filter(
                    agent_profile=self.viewed_profile, is_associated=True
                )
            elif (
                viewer_hierarchy_level == 1
                and self.property_obj.owner_intent
                != OwnerIntentForProperty.NOT_FOR_SALE
            ):
                property_agents = self.property_agents
        elif viewer_hierarchy_level == 4:
            if self.viewed_user_role.name == AGENT:
                property_agents = self.property_agents.filter(
                    agent_profile=self.viewed_profile, is_associated=True
                )
            elif (
                self.viewed_user_role.name == INVESTOR
                and self.viewer_role
                and self.viewer_role.name == AGENT
                and self.property_obj.agent_type == PropertyAgentType.EXCLUSIVE_AGENT
            ):
                property_agents = self.property_agents.filter(is_associated=True)
                make_invisible = True
            elif (
                self.viewed_user_role.name == INVESTOR
                and self.viewer_role
                and self.viewer_role.name == INVESTOR
            ):
                property_agents = self.property_agents.filter(is_associated=True)
        agent_details = PropertyAgentsDetailsSerializer(
            property_agents,
            context={"self_view": self.self_view, "viewer_role": self.viewer_role},
            many=True,
        ).data

        agent_type = None
        if (
            agent_details
            and self.property_obj.owner_intent
            != OwnerIntentForProperty.NOT_FOR_SALE.value
        ):
            agent_type = int(self.property_obj.agent_type)

        if not agent_details:
            make_invisible = True

        agent_details = build_return_values_for_attributes(
            agent_details,
            "agent_details",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=make_invisible,
        )

        agent_type = build_return_values_for_attributes(
            agent_type,
            "agent_type",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=False if agent_type is not None else make_invisible,
            keep_value=True,
        )

        agent_information_attributes = dict()
        agent_information_attributes.update({"agent_details": agent_details})
        agent_information_attributes.update({"agent_type": agent_type})

        agent_information = {
            "component_is_editable": self.get_is_component_editable(
                agent_information_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                agent_information_attributes
            ),
        }
        agent_information.update(agent_information_attributes)
        return agent_information

    def build_price_details_object(self):
        logger.info(
            f"Building price details object for property {self.property_obj.id}"
        )

        financial_details = (
            self.viewer_level_property.property_user_level_financial_details
        )
        property_currency_code = financial_details.property_currency_code
        preferred_currency_code = (
            self.viewer_profile.preferred_currency_code if self.viewer_profile else None
        )
        exchange_rate = 1
        is_valuation = False
        if (property_currency_code and preferred_currency_code) and (
            property_currency_code != preferred_currency_code
        ):
            exchange_rate = get_exchange_rates(
                property_currency_code, preferred_currency_code
            )

        preferred_payment_frequency = None
        category = None
        if self.property_obj.owner_intent == OwnerIntentForProperty.NOT_FOR_SALE:
            display_price_in_property_currency = financial_details.valuation
            is_valuation = True
        elif (
            self.property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
        ):
            display_price_in_property_currency = financial_details.expected_rent
            preferred_payment_frequency = financial_details.preferred_payment_frequency
            category = "rent"
        else:
            display_price_in_property_currency = financial_details.asking_price
            category = "sale"

        display_price_in_preferred_currency = round(
            exchange_rate * display_price_in_property_currency, 3
        )

        tentative_commission_in_property_currency = None
        tentative_commission_in_preferred_currency = None
        if (
            self.property_obj.owner_intent
            in [
                OwnerIntentForProperty.AVAILABLE_FOR_SALE,
                OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            ]
            and self.self_view
            and self.viewer_role.name == AGENT
        ):
            commission_percentage = float(self.viewed_profile.commission_percentage)
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_SALE
            ):
                tentative_commission_in_property_currency = round(
                    financial_details.asking_price * commission_percentage / 100, 3
                )
                tentative_commission_in_preferred_currency = round(
                    exchange_rate * tentative_commission_in_property_currency, 3
                )
            if (
                self.property_obj.owner_intent
                == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            ):
                monthly_expected_rent = financial_details.expected_rent
                tentative_commission_in_property_currency = round(
                    monthly_expected_rent
                    * settings.AGENT_COMMISSION_PERCENTAGE_FOR_EXPECTED_RENT
                    / 100,
                    3,
                )
                tentative_commission_in_preferred_currency = round(
                    exchange_rate * tentative_commission_in_property_currency, 3
                )

        last_sub_location = ""
        master_development = ""
        if self.property_obj.community:
            master_development = self.property_obj.community.name
            for loc_no in range(5, 0, -1):
                if getattr(self.property_obj.community, f"sub_loc_{loc_no}", None):
                    last_sub_location = getattr(
                        self.property_obj.community, f"sub_loc_{loc_no}"
                    )

        average_price_comparison = None
        if (
            category
            and self.property_obj.property_category == PropertyCategory.RESIDENTIAL
        ):
            try:
                property_monitor = PropertyMonitorAPI()
                volume_trend_data = property_monitor.get_sale_price_volume_trend(
                    master_development,
                    last_sub_location,
                    self.viewer_level_property.property_type,
                    category=category,
                    last_x_months=1,
                )
                if volume_trend_data:
                    for price_data in volume_trend_data:
                        avg_price_sqft = price_data.get("avg_price_sqft")
                        if avg_price_sqft:
                            current_valuation = (
                                self.viewer_level_property.total_area * avg_price_sqft
                            )
                            average_price_comparison = round(
                                (display_price_in_property_currency - current_valuation)
                                / current_valuation
                                * 100,
                                2,
                            )
                        break
            except Exception as error:
                logger.error(f"Error while fetching average price comparison: {error}")

        property_currency_code = build_return_values_for_attributes(
            property_currency_code,
            "property_currency_code",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
        )
        preferred_currency_code = build_return_values_for_attributes(
            preferred_currency_code,
            "preferred_currency_code",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
        )
        exchange_rate = build_return_values_for_attributes(
            round(exchange_rate, 3),
            "exchange_rate",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
        )
        display_price_in_property_currency = build_return_values_for_attributes(
            display_price_in_property_currency,
            "display_price_in_property_currency",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
            is_value_exists=True if display_price_in_property_currency else False,
        )
        display_price_in_preferred_currency = build_return_values_for_attributes(
            display_price_in_preferred_currency,
            "display_price_in_preferred_currency",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
            is_value_exists=True if display_price_in_preferred_currency else False,
        )
        is_valuation = build_return_values_for_attributes(
            is_valuation,
            "is_valuation",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            is_common_view=self.is_common_view,
            is_value_exists=True if is_valuation else False,
        )
        preferred_payment_frequency = build_return_values_for_attributes(
            preferred_payment_frequency,
            "preferred_payment_frequency",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=False if preferred_payment_frequency is not None else True,
            make_non_editable=True,
            is_common_view=self.is_common_view,
            is_value_exists=True if preferred_payment_frequency else False,
        )

        tentative_commission_in_property_currency = build_return_values_for_attributes(
            tentative_commission_in_property_currency,
            "tentative_commission_in_property_currency",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=(
                False if tentative_commission_in_property_currency is not None else True
            ),
            make_non_editable=True,
            is_common_view=self.is_common_view,
            is_value_exists=(
                True if tentative_commission_in_property_currency else False
            ),
        )

        tentative_commission_in_preferred_currency = build_return_values_for_attributes(
            tentative_commission_in_preferred_currency,
            "tentative_commission_in_preferred_currency",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=(
                False
                if tentative_commission_in_preferred_currency is not None
                else True
            ),
            make_non_editable=True,
            is_common_view=self.is_common_view,
            is_value_exists=(
                True if tentative_commission_in_preferred_currency else False
            ),
        )
        average_price_comparison = build_return_values_for_attributes(
            average_price_comparison,
            "average_price_comparison",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=(False if average_price_comparison is not None else True),
            make_non_editable=True,
            is_common_view=self.is_common_view,
            is_value_exists=True if average_price_comparison else False,
        )

        price_details = {"component_is_editable": False, "component_is_visible": True}
        price_details.update({"property_currency_code": property_currency_code})
        price_details.update({"preferred_currency_code": preferred_currency_code})
        price_details.update({"exchange_rate": exchange_rate})
        price_details.update(
            {"display_price_in_property_currency": display_price_in_property_currency}
        )
        price_details.update(
            {"display_price_in_preferred_currency": display_price_in_preferred_currency}
        )
        price_details.update({"is_valuation": is_valuation})
        price_details.update(
            {"preferred_payment_frequency": preferred_payment_frequency}
        )
        price_details.update(
            {
                "tentative_commission_in_property_currency": tentative_commission_in_property_currency
            }
        )
        price_details.update(
            {
                "tentative_commission_in_preferred_currency": tentative_commission_in_preferred_currency
            }
        )
        price_details.update({"average_price_comparison": average_price_comparison})
        return price_details

    def build_scrollable_component_object(self):
        logger.info(
            f"Building scrollable component object for property {self.property_obj.id}"
        )

        property_specifications_attributes = ScrollableComponentPropertySpecificationsAttributesSerializer(
            self.viewer_level_property,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "is_common_view": self.is_common_view,
            },
        ).data

        property_specifications = {
            "component_is_editable": self.get_is_component_editable(
                property_specifications_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                property_specifications_attributes
            ),
        }
        property_specifications.update(property_specifications_attributes)
        return property_specifications

    def build_unit_images_component_object(self):
        logger.info(
            f"Building unit images component object for property {self.property_obj.id}"
        )
        unit_images_attributes = PropertyUnitImagesSerializer(
            self.property_obj,
            context={
                "created_by": self.viewed_user,
                "created_by_role": self.viewed_user_role,
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
            },
        ).data

        unit_images = {"component_is_editable": False, "component_is_visible": True}
        unit_images.update(unit_images_attributes)
        return unit_images

    def build_availability_and_status_object(self):
        logger.info(
            f"Building availability and status object for property {self.property_obj.id}"
        )

        user_level_availability_and_status = (
            self.viewer_level_property.property_user_level_availability_and_status
        )
        property_availability_and_status_attributes = PropertyAvailabilityAndStatusAttributesSerializer(
            user_level_availability_and_status,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "viewed_user": self.viewed_user,
                "viewed_user_role": self.viewed_user_role,
                "self_view": self.self_view,
                "is_common_view": self.is_common_view,
            },
        ).data

        property_availability_and_status = {
            "component_is_editable": self.get_is_component_editable(
                property_availability_and_status_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                property_availability_and_status_attributes
            ),
        }
        property_availability_and_status.update(
            property_availability_and_status_attributes
        )
        return property_availability_and_status

    def build_income_from_property_object(self):
        logger.info(
            f"Building income from property object for property {self.property_obj.id}"
        )
        income_from_property_attributes = {}

        if (
            self.property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            and not self.self_view
        ):
            income_from_property_attributes["occupancy_status"] = None
            income_from_property_attributes["tenancy_type"] = None
            income_from_property_attributes["start_date"] = None
            income_from_property_attributes["end_date"] = None
            income_from_property_attributes["rent_contract"] = None
            income_from_property_attributes["annual_service_charge_data"] = None
            income_from_property_attributes["annual_rent_data"] = None
            income_from_property_attributes["security_deposit_data"] = None
            income_from_property_attributes["gross_yield"] = None
            income_from_property_attributes["net_yield"] = None
            income_from_property_attributes["property_currency_code"] = None
            income_from_property_attributes["preferred_currency_code"] = None
            income_from_property = {
                "component_is_editable": False,
                "component_is_visible": False,
            }

        else:
            if self.property_verified_fields_and_values.get("occupancy_status", None):
                occupancy_status = self.property_verified_fields_and_values.get(
                    "occupancy_status"
                )
            else:
                occupancy_status = (
                    self.viewer_level_property.property_user_level_availability_and_status.occupancy_status
                )
            income_from_property_attributes = IncomeFromPropertyAttributesSerializer(
                self.viewer_level_property,
                context={
                    "viewable_attributes": self.viewer_viewable_attributes,
                    "editable_attributes": self.viewer_editable_attributes,
                    "property_verified_fields_and_values": self.property_verified_fields_and_values,
                    "occupancy_status": int(occupancy_status),
                    "is_common_view": self.is_common_view,
                },
            ).data
            property_financial_details = (
                self.viewer_level_property.property_user_level_financial_details
            )
            property_currency_code = property_financial_details.property_currency_code
            preferred_currency_code = (
                self.viewer_profile.preferred_currency_code
                if self.viewer_profile
                else None
            )
            exchange_rate = 1
            if (property_currency_code and preferred_currency_code) and (
                property_currency_code != preferred_currency_code
            ):
                exchange_rate = get_exchange_rates(
                    property_currency_code, preferred_currency_code
                )

            annual_service_charge = property_financial_details.annual_service_charges
            annual_service_charge_data = {
                "annual_service_charge_in_property_currency": annual_service_charge,
                "annual_service_charge_in_preferred_currency": (
                    round(annual_service_charge * exchange_rate, 3)
                    if annual_service_charge and preferred_currency_code
                    else None
                ),
            }
            annual_service_charge_data = build_return_values_for_attributes(
                annual_service_charge_data,
                "annual_service_charge",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if annual_service_charge else False,
            )
            income_from_property_attributes.update(
                {"annual_service_charge_data": annual_service_charge_data}
            )

            annual_rent_data = None
            security_deposit_data = None
            keep_value = True
            make_invisible = True
            gross_yield = None
            net_yield = None
            if occupancy_status == PropertyAvailabilityStatus.RENTED.value:
                make_invisible = False
                gross_yield = get_property_gross_yield(property_financial_details)
                net_yield = get_property_net_yield(property_financial_details)

            annual_rent = property_financial_details.annual_rent
            annual_rent_data = {
                "annual_rent_charge_in_property_currency": annual_rent,
                "annual_rent_charge_in_preferred_currency": (
                    round(annual_rent * exchange_rate, 3)
                    if annual_rent and preferred_currency_code and keep_value
                    else None
                ),
            }

            security_deposit = property_financial_details.security_deposit
            security_deposit_data = {
                "security_deposit_charge_in_property_currency": security_deposit,
                "security_deposit_charge_in_preferred_currency": (
                    round(security_deposit * exchange_rate, 3)
                    if security_deposit and preferred_currency_code and keep_value
                    else None
                ),
            }

            annual_rent_data = build_return_values_for_attributes(
                annual_rent_data,
                "annual_rent",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                keep_value=keep_value,
                make_invisible=make_invisible,
                is_common_view=self.is_common_view,
                is_value_exists=True if annual_rent else False,
            )

            income_from_property_attributes.update(
                {"annual_rent_data": annual_rent_data}
            )

            security_deposit_data = build_return_values_for_attributes(
                security_deposit_data,
                "security_deposit",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                keep_value=keep_value,
                make_invisible=make_invisible,
                is_common_view=self.is_common_view,
                is_value_exists=True if security_deposit else False,
            )
            income_from_property_attributes.update(
                {"security_deposit_data": security_deposit_data}
            )

            gross_yield = build_return_values_for_attributes(
                gross_yield,
                "gross_yield",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )
            income_from_property_attributes.update({"gross_yield": gross_yield})

            net_yield = build_return_values_for_attributes(
                net_yield,
                "net_yield",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if net_yield else False,
            )
            income_from_property_attributes.update({"net_yield": net_yield})

            property_currency_code = build_return_values_for_attributes(
                property_currency_code,
                "property_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if property_currency_code else False,
            )

            preferred_currency_code = build_return_values_for_attributes(
                preferred_currency_code,
                "preferred_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )

            income_from_property = {
                "component_is_editable": self.get_is_component_editable(
                    income_from_property_attributes
                ),
                "component_is_visible": self.get_is_component_visibility(
                    income_from_property_attributes
                ),
            }
            income_from_property_attributes.update(
                {"property_currency_code": property_currency_code}
            )
            income_from_property_attributes.update(
                {"preferred_currency_code": preferred_currency_code}
            )
        income_from_property.update(income_from_property_attributes)
        return income_from_property

    def build_gains_component_object(self):
        logger.info(
            f"Building gains component object for property {self.property_obj.id}"
        )

        # property_availability_and_status = self.property_obj.propertyavailabilityandstatus
        gains_component_attributes = {}
        property_financial_details = (
            self.viewer_level_property.property_user_level_financial_details
        )

        property_currency_code = property_financial_details.property_currency_code
        preferred_currency_code = (
            self.viewer_profile.preferred_currency_code if self.viewer_profile else None
        )
        exchange_rate = 1
        if (property_currency_code and preferred_currency_code) and (
            property_currency_code != preferred_currency_code
        ):
            exchange_rate = get_exchange_rates(
                property_currency_code, preferred_currency_code
            )

        gains_component_attributes["asking_price_data"] = None
        gains_component_attributes["original_price_data"] = None
        gains_component_attributes["valuation_data"] = None
        gains_component_attributes["gains_data"] = None
        gains_component_attributes["property_currency_code"] = None
        gains_component_attributes["preferred_currency_code"] = None

        if self.property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_SALE:
            asking_price = property_financial_details.asking_price
            asking_price_data = {
                "asking_price_in_property_currency": asking_price,
                "asking_price_in_preferred_currency": (
                    round(asking_price * exchange_rate, 3) if asking_price else None
                ),
            }
            asking_price_data = build_return_values_for_attributes(
                asking_price_data,
                "asking_price",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if asking_price else False,
            )
            gains_component_attributes.update({"asking_price_data": asking_price_data})

            original_price = property_financial_details.original_price
            original_price_data = {
                "original_price_charge_in_property_currency": original_price,
                "original_price_charge_in_preferred_currency": (
                    round(original_price * exchange_rate, 3)
                    if original_price and preferred_currency_code
                    else None
                ),
            }
            original_price_data = build_return_values_for_attributes(
                original_price_data,
                "original_price",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if original_price else False,
            )
            gains_component_attributes.update(
                {"original_price_data": original_price_data}
            )

            valuation = property_financial_details.valuation
            valuation_data = {
                "valuation_charge_in_property_currency": valuation,
                "valuation_charge_in_preferred_currency": (
                    round(valuation * exchange_rate, 3)
                    if valuation and preferred_currency_code
                    else None
                ),
            }
            valuation_data = build_return_values_for_attributes(
                valuation_data,
                "valuation",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if valuation else False,
            )
            gains_component_attributes.update({"valuation_data": valuation_data})

            gain, gain_percentage = get_property_gains_data(property_financial_details)

            gains_data = {
                "gain_in_property_currency": gain,
                "gain_in_preferred_currency": (
                    round(gain * exchange_rate, 3)
                    if gain and preferred_currency_code
                    else None
                ),
                "gain_percentage": gain_percentage,
            }
            gains_data = build_return_values_for_attributes(
                gains_data,
                "gains_data",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if gain else False,
            )
            gains_component_attributes.update({"gains_data": gains_data})

            property_currency_code = build_return_values_for_attributes(
                property_currency_code,
                "property_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )

            preferred_currency_code = build_return_values_for_attributes(
                preferred_currency_code,
                "preferred_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )

            gains_component_attributes.update(
                {
                    "component_is_editable": self.get_is_component_editable(
                        gains_component_attributes
                    )
                }
            )

            gains_component_attributes.update(
                {
                    "component_is_visible": self.get_is_component_visibility(
                        gains_component_attributes
                    )
                }
            )

            gains_component_attributes.update(
                {"property_currency_code": property_currency_code}
            )

            gains_component_attributes.update(
                {"preferred_currency_code": preferred_currency_code}
            )

        else:
            gains_component_attributes["component_is_editable"] = False
            gains_component_attributes["component_is_visible"] = False

        return gains_component_attributes

    def build_lease_conditions_object(self):
        logger.info(
            f"Building lease conditions object for property {self.property_obj.id}"
        )

        lease_conditions_attributes = {}
        property_financial_details = (
            self.viewer_level_property.property_user_level_financial_details
        )
        property_availability_and_status = (
            self.viewer_level_property.property_user_level_availability_and_status
        )
        property_currency_code = property_financial_details.property_currency_code
        preferred_currency_code = (
            self.viewer_profile.preferred_currency_code if self.viewer_profile else None
        )
        exchange_rate = 1
        if (property_currency_code and preferred_currency_code) and (
            property_currency_code != preferred_currency_code
        ):
            exchange_rate = get_exchange_rates(
                property_currency_code, preferred_currency_code
            )

        lease_conditions_attributes["annual_rent_data"] = None
        lease_conditions_attributes["security_deposit_data"] = None
        lease_conditions_attributes["preferred_payment_frequency"] = None
        lease_conditions_attributes["price_negotiable"] = None
        lease_conditions_attributes["available_from"] = None
        lease_conditions_attributes["property_currency_code"] = None
        lease_conditions_attributes["preferred_currency_code"] = None

        if self.property_obj.owner_intent == OwnerIntentForProperty.AVAILABLE_FOR_RENT:
            annual_rent = property_financial_details.expected_rent
            annual_rent_data = {
                "annual_rent_charge_in_property_currency": annual_rent,
                "annual_rent_charge_in_preferred_currency": (
                    round(annual_rent * exchange_rate, 3) if annual_rent else None
                ),
            }
            annual_rent_data = build_return_values_for_attributes(
                annual_rent_data,
                "expected_annual_rent",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if annual_rent else False,
            )
            lease_conditions_attributes.update({"annual_rent_data": annual_rent_data})

            security_deposit = property_financial_details.expected_security_deposit
            security_deposit_data = {
                "security_deposit_charge_in_property_currency": security_deposit,
                "security_deposit_charge_in_preferred_currency": (
                    round(security_deposit * exchange_rate, 3)
                    if security_deposit and preferred_currency_code
                    else None
                ),
            }
            security_deposit_data = build_return_values_for_attributes(
                security_deposit_data,
                "expected_security_deposit",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if security_deposit else False,
            )
            lease_conditions_attributes.update(
                {"security_deposit_data": security_deposit_data}
            )

            make_non_editable = False
            if (
                self.self_view
                and self.viewer_role == AGENT
                and self.property_obj.owner_verified
            ):
                make_non_editable = True

            preferred_payment_frequency = (
                property_financial_details.preferred_payment_frequency
            )
            preferred_payment_frequency = build_return_values_for_attributes(
                preferred_payment_frequency,
                "preferred_payment_frequency",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                make_non_editable=make_non_editable,
                is_common_view=self.is_common_view,
                is_value_exists=True if preferred_payment_frequency else False,
            )
            lease_conditions_attributes.update(
                {"preferred_payment_frequency": preferred_payment_frequency}
            )

            price_negotiable = property_financial_details.price_negotiable
            price_negotiable = build_return_values_for_attributes(
                price_negotiable,
                "price_negotiable",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                is_common_view=self.is_common_view,
                is_value_exists=True if price_negotiable else False,
            )
            lease_conditions_attributes.update({"price_negotiable": price_negotiable})

            available_from = property_availability_and_status.rent_available_start_date
            available_from = build_return_values_for_attributes(
                available_from,
                "available_from",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
                make_non_editable=make_non_editable,
                is_common_view=self.is_common_view,
                is_value_exists=True if available_from else False,
            )
            lease_conditions_attributes.update({"available_from": available_from})

            property_currency_code = build_return_values_for_attributes(
                property_currency_code,
                "property_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )

            preferred_currency_code = build_return_values_for_attributes(
                preferred_currency_code,
                "preferred_currency_code",
                self.viewer_viewable_attributes,
                self.viewer_editable_attributes,
                self.property_verified_fields_and_values,
            )

            lease_conditions_attributes.update(
                {
                    "component_is_editable": self.get_is_component_editable(
                        lease_conditions_attributes
                    )
                }
            )

            lease_conditions_attributes.update(
                {
                    "component_is_visible": self.get_is_component_visibility(
                        lease_conditions_attributes
                    )
                }
            )
            lease_conditions_attributes.update(
                {"property_currency_code": property_currency_code}
            )
            lease_conditions_attributes.update(
                {"preferred_currency_code": preferred_currency_code}
            )

        else:
            lease_conditions_attributes["component_is_editable"] = False
            lease_conditions_attributes["component_is_visible"] = False

        return lease_conditions_attributes

    def build_previous_transactions_object(self):
        logger.info(
            f"Building previous transactions object for property {self.property_obj.id}"
        )

        previous_transactions_attributes = PreviousTransactionsAttributesSerializer(
            self.property_obj,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
            },
        ).data

        property_financial_details = (
            self.viewer_level_property.property_user_level_financial_details
        )
        property_currency_code = property_financial_details.property_currency_code
        property_currency_code = build_return_values_for_attributes(
            property_currency_code,
            "property_currency_code",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
        )
        previous_transactions = {
            "component_is_editable": self.get_is_component_editable(
                previous_transactions_attributes
            ),
            "component_is_visible": self.get_is_component_visibility(
                previous_transactions_attributes
            ),
        }
        previous_transactions.update(previous_transactions_attributes)
        previous_transactions.update({"property_currency_code": property_currency_code})
        return previous_transactions

    def build_profile_card_object(self):
        logger.info(f"Building profile card object for property {self.property_obj.id}")
        if self.viewed_user_role.name == INVESTOR:
            data = InvestorCardDetailsSerializer(
                self.viewed_profile, context={"self_view": self.self_view}
            ).data
        else:
            data = AgentCardDetailsSerializer(
                self.viewed_profile,
                context={
                    "self_view": self.self_view,
                    "property_obj": self.property_obj,
                },
            ).data

        value = {
            "value": data,
            "is_editable": False,
            "is_visible": True,
        }

        profile_card = {"component_is_editable": False, "component_is_visible": True}
        profile_card.update({"profile_card_data": value})
        return profile_card

    def build_cta_component(self):
        logger.info(
            f"Building cta component object for property {self.property_obj.id}"
        )
        cta = None
        if not self.is_common_view:
            viewer_hierarchy_level = self.viewer_hierarchy_level.level
            if viewer_hierarchy_level == 4:
                investor_agent = (
                    self.viewed_user_role.name == INVESTOR
                    and self.viewer_role
                    and self.viewer_role.name == AGENT
                )
                agent_investor = (
                    self.viewed_user_role.name == AGENT
                    and self.viewer_role
                    and self.viewer_role.name == INVESTOR
                )
                both_investors = (
                    self.viewed_user_role.name == INVESTOR
                    and self.viewer_role
                    and self.viewer_role.name == INVESTOR
                )
                both_agents = (
                    self.viewed_user_role.name == AGENT
                    and self.viewer_role
                    and self.viewer_role.name == AGENT
                )

                if both_investors and not self.is_viewer_associated_to_property:
                    cta = (
                        UserViewCTA.CONTACT_AGENT.value
                        if self.property_agents.filter(is_associated=True).exists()
                        else UserViewCTA.RECOMMEND_PROPERTY_TO_AGENT.value
                    )

                elif both_agents:
                    cta = (
                        UserViewCTA.CONTACT_AGENT.value
                        if not self.is_viewer_associated_to_property
                        else UserViewCTA.PROPERTY_AGENT.value
                    )

                elif agent_investor and not self.is_viewer_associated_to_property:
                    cta = UserViewCTA.CONTACT_AGENT.value

                elif investor_agent:
                    cta = (
                        UserViewCTA.PROPERTY_AGENT.value
                        if self.is_viewer_associated_to_property
                        else (
                            UserViewCTA.PENDING_REQUEST.value
                            if self.property_agents.filter(
                                agent_profile=self.viewer_profile,
                                is_associated=False,
                                action_status=UserRequestActions.PENDING,
                                is_request_expired=False,
                            ).exists()
                            else (
                                UserViewCTA.REQUEST_ACCESS.value
                                if self.property_obj.agent_type
                                != PropertyAgentType.EXCLUSIVE_AGENT
                                else (
                                    UserViewCTA.CONTACT_AGENT.value
                                    if self.property_agents.filter(
                                        is_associated=True
                                    ).exists()
                                    and self.property_obj.agent_type
                                    == PropertyAgentType.EXCLUSIVE_AGENT
                                    else None
                                )
                            )
                        )
                    )

        attribute_value = {
            "value": cta,
            "is_editable": False,
            "is_visible": True if cta is not None else False,
        }

        build_cta_component = {
            "component_is_editable": False,
            "component_is_visible": attribute_value["is_visible"],
        }
        build_cta_component.update({"cta": attribute_value})
        return build_cta_component

    def build_location_details_upper_component_object(self):
        logger.info(
            f"Building location details upper component object for property {self.property_obj.id}"
        )

        location_details_upper_component_attributes = UpperComponentLocationDetailsAttributesSerializer(
            self.property_obj,
            context={
                "viewable_attributes": self.viewer_viewable_attributes,
                "editable_attributes": self.viewer_editable_attributes,
                "property_verified_fields_and_values": self.property_verified_fields_and_values,
                "is_common_view": self.is_common_view,
            },
        ).data

        location_details_upper_component = {
            "component_is_editable": False,
            "component_is_visible": True,
        }
        location_details_upper_component.update(
            location_details_upper_component_attributes
        )
        return location_details_upper_component

    def build_property_intent_component(self):
        logger.info(
            f"Building property intent component for property {self.property_obj.id}"
        )
        # intent = self.viewer_level_property.owner_intent

        intent = build_return_values_for_attributes(
            self.property_obj.owner_intent,
            "intent",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_non_editable=True,
        )
        availability_and_status = (
            self.viewer_level_property.property_user_level_availability_and_status
        )
        available_from = (
            availability_and_status.rent_available_start_date
            if self.property_obj.owner_intent
            == OwnerIntentForProperty.AVAILABLE_FOR_RENT
            else None
        )
        available_from = build_return_values_for_attributes(
            available_from,
            "available_from",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=True if not available_from else False,
            make_non_editable=True,
        )

        distressed_deal = None
        if self.property_obj.owner_intent not in [
            OwnerIntentForProperty.AVAILABLE_FOR_RENT,
            OwnerIntentForProperty.NOT_FOR_SALE,
        ]:
            financial_details = self.property_obj.propertyfinancialdetails
            distressed_deal = False
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                distressed_deal = True
        distressed_deal = build_return_values_for_attributes(
            distressed_deal,
            "distressed_deal",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_invisible=True if not available_from else False,
            make_non_editable=True,
        )
        furnished = False
        premium_view = False
        branded_building = False
        property_features = UserLevelPropertyFeatures.objects.filter(
            property_level_data=self.viewer_level_property
        ).first()
        if property_features:
            furnished = property_features.furnished
            premium_view = property_features.premium_view
            branded_building = property_features.branded_building
        furnished = build_return_values_for_attributes(
            furnished,
            "furnished",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_non_editable=True,
        )
        premium_view = build_return_values_for_attributes(
            premium_view,
            "premium_view",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_non_editable=True,
        )
        branded_building = build_return_values_for_attributes(
            branded_building,
            "branded_building",
            self.viewer_viewable_attributes,
            self.viewer_editable_attributes,
            self.property_verified_fields_and_values,
            make_non_editable=True,
        )

        property_intent = {"component_is_editable": False, "component_is_visible": True}
        property_intent.update({"intent": intent})
        property_intent.update({"available_from": available_from})
        property_intent.update({"distressed_deal": distressed_deal})
        property_intent.update({"furnished": furnished})
        property_intent.update({"premium_view": premium_view})
        property_intent.update({"branded_building": branded_building})
        return property_intent

    def build_property_information_component(self):
        logger.info(
            f"Building property information component for property {self.property_obj.id}"
        )
        property_category = self.property_obj.property_category
        attribute_value = {
            "value": property_category,
            "is_editable": False,
            "is_visible": False,
        }
        property_id = self.property_obj.id
        property_id = {"value": property_id, "is_editable": False, "is_visible": False}
        property_information = {
            "component_is_editable": False,
            "component_is_visible": False,
        }
        property_information.update({"property_category": attribute_value})
        property_information.update({"id": property_id})
        return property_information

    def build_similar_transactions_component(self):
        logger.info(
            f"Building similar transactions component for property {self.property_obj.id}"
        )
        redirect = {
            "value": True,
            "is_editable": False,
            "is_visible": True,
        }
        intent = {
            "value": self.property_obj.owner_intent,
            "is_editable": False,
            "is_visible": False,
        }
        if not self.is_common_view:
            filters = get_property_trends_filters(
                self.property_obj, self.viewer_level_property
            )
        else:
            filters = None

        if self.is_common_view or not self.property_obj.property_monitor_address_id:
            component_is_visible = False
        else:
            component_is_visible = True
        similar_transactions = {
            "component_is_editable": False,
            "component_is_visible": component_is_visible,
        }
        similar_transactions.update({"redirect": redirect})
        similar_transactions.update({"intent": intent})
        similar_transactions.update({"filters": filters})
        return similar_transactions
