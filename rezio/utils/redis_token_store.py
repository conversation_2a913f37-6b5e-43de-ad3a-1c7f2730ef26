import logging
import time

import jwt
from django.conf import settings
from django.core.cache import cache

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class RedisTokenStore:
    """
    Utility class for managing JWT tokens in Redis
    """

    # Prefix for blacklisted tokens
    BLACKLIST_PREFIX = "token_blacklist:"

    @classmethod
    def blacklist_token(cls, token, expires_delta=None):
        """
        Add a token to the blacklist

        Args:
            token: The JWT token to blacklist
            expires_delta: Optional override for token expiration

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Decode the token to get expiration time and jti
            decoded_token = jwt.decode(
                token,
                settings.SIMPLE_JWT["SIGNING_KEY"],
                algorithms=[settings.SIMPLE_JWT["ALGORITHM"]],
            )

            # Get token identifier and expiration
            jti = decoded_token.get("jti")
            exp = decoded_token.get("exp")

            if not jti or not exp:
                return False

            # Calculate TTL in seconds (how long until token expires)
            current_time = int(time.time())
            ttl = max(0, exp - current_time)

            # Store in Redis with expiration
            cache_key = f"{cls.BLACKLIST_PREFIX}{jti}"
            cache.set(cache_key, "1", timeout=ttl)

            return True
        except Exception as e:
            logger.error(f"Error blacklisting token: {str(e)}")
            return False

    @classmethod
    def is_blacklisted(cls, token):
        """
        Check if a token is blacklisted

        Args:
            token: The JWT token to check

        Returns:
            bool: True if blacklisted, False otherwise
        """
        try:
            # Decode the token to get the jti
            decoded_token = jwt.decode(
                token,
                settings.SIMPLE_JWT["SIGNING_KEY"],
                algorithms=[settings.SIMPLE_JWT["ALGORITHM"]],
            )

            jti = decoded_token.get("jti")
            if not jti:
                return False

            # Check if token is in blacklist
            cache_key = f"{cls.BLACKLIST_PREFIX}{jti}"
            return cache.get(cache_key) is not None
        except Exception as e:
            # If token is invalid or expired, consider it blacklisted
            logger.info(f"Token is blacklisted: {e}")
            return True
