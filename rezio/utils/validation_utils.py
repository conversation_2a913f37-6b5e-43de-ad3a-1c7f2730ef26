import logging

import phonenumbers
import pycountry
from phonenumber_field.phonenumber import PhoneNumber
from phonenumbers.phonenumberutil import NumberParseException

from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
)
from rezio.utils.custom_exceptions import InvalidSerializerDataException

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def validate_phone_number(phone_number, region=None):
    """
    It takes a phone number, formats it, and checks if it's a valid phone number

    Parameters:
        phone_number(PhoneNumber): The phone number to validate
        region(str): The region to which phone number belongs default is None
    Returns:
        A tuple of three values
            1. The phone number object
            2. A boolean value indicating whether the phone number is valid or not
            3. An error message
    """
    formatted_phone_number = phone_number.as_e164
    error_message = ""

    try:
        parsed_phone_number = phonenumbers.parse(formatted_phone_number, region)
    except NumberParseException:
        error_message = "Invalid phone number provided without specifying region"
        logger.warning(f"Invalid phone number: {formatted_phone_number}")
        return None, True, error_message
    except Exception as error:
        error_message = f"An unexpected error occurred: {str(error)}"
        logger.error(f"Unexpected error: {error}")
        return None, True, error_message

    if not phonenumbers.is_possible_number(parsed_phone_number):
        error_message = "Given phone number may not be a valid one"
        return None, True, error_message

    return phone_number, False, error_message


def validate_emirates_id_value(emirates_id):
    if not emirates_id.isdigit() or len(emirates_id) != 15:
        return False
    return True


def validate_alphanumeric_string(value):
    if len(value) < 6 or len(value) > 9 or not value.isalnum():
        return False
    return True


def validate_currency_code(currency_code: str) -> bool:
    if not pycountry.currencies.get(alpha_3=currency_code):
        return False
    return True


class ValidationUtility:
    @staticmethod
    def validate_serializer(serializer):
        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )
        return serializer.validated_data


validation_utility = ValidationUtility()
