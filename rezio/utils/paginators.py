from django.conf import settings
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR
from rezio.utils.decorators import log_input_output


class StandardResultsSetPagination(PageNumberPagination):
    """Custom pagination class"""

    page_size = settings.PAGE_SIZE
    page_size_query_param = settings.PAGE_SIZE_QUERY_PARAM
    max_page_size = settings.MAX_PAGE_SIZE

    def get_paginated_response(self, data):
        next_page_number = None
        if self.page.has_next():
            next_page_number = self.page.next_page_number()

        previous_page_number = None
        if self.page.has_previous():
            previous_page_number = self.page.previous_page_number()

        return Response(
            {
                KEY_MESSAGE: "Data fetched successfully",
                KEY_PAYLOAD: {
                    "count": self.page.paginator.count,
                    "next": next_page_number,
                    "previous": previous_page_number,
                    "total_pages": self.page.paginator.num_pages,
                    "current_page": self.page.number,
                    "page_size": self.page_size,
                    "results": data,
                },
                KEY_ERROR: {},
            }
        )
