def get_required_field_serializer_error_messages(field_name):
    """
    Returns custom serializer error messages for required field

    Parameters:
        field_name(str): The name of the field that is required
    Returns:
        error_messages dictionary
    """

    return {
        "required": f"{field_name} is required",
        "blank": f"{field_name} is required",
        "null": f"{field_name} is required",
    }
