from enum import Enum

from django.db import models
from django.utils.translation import gettext_lazy as _


class Gender(models.TextChoices):
    """Text choices for gender"""

    MALE = "Male", _("Male")
    FEMALE = "Female", _("Female")
    PREFER_NOT_TO_SAY = "Prefer not to say", _("Prefer not to say")


class DataSource(models.TextChoices):
    """Text choices for gender"""

    SYSTEM_DEFAULT = "SYSTEM_DEFAULT", _("SYSTEM_DEFAULT")
    DLD = "DLD", _("DLD")
    USER_ADDED = "USER_ADDED", _("USER_ADDED")
    PROPERTY_MONITOR = "PROPERTY_MONITOR", _("PROPERTY_MONITOR")
    REZIO_VERIFIED = "REZIO_VERIFIED", _("REZIO_VERIFIED")


class Pronouns(models.TextChoices):
    """Text choices for gender"""

    MALE = "0", _("He/Him/His")
    FEMALE = "1", _("She/Her/Hers")


class MediaType(models.TextChoices):
    """Text choices for Media type"""

    IMAGE = "IMAGE", _("image")
    VIDEO = "VIDEO", _("video")


class SectionChoices(models.TextChoices):
    MASTER_BEDROOM = "master_bedroom", _("Master bed room")
    OTHER_BEDROOM = "other_bedroom", _("Other bed room")
    KITCHEN = "kitchen", _("Kitchen")
    POWDER_ROOM = "powmentder_room", _("Powder Room")
    BATHROOM = "bathroom", _("Bathroom")
    OTHER = "other", _("Other")


class CurrencyCode(models.TextChoices):
    """Text choices for gender"""

    AED = "AED", _("aed")
    INR = "INR", _("inr")


class AgentWorkingType(models.TextChoices):
    """
    Type of agent working
    """

    WITH_AN_AGENCY = 0, _("With an agency")
    AS_A_FREELANCER = 1, _("As a freelancer")


class AgentLicenseVerificationStatus(models.TextChoices):
    """
    Status of agent verification
    """

    ADD_LICENCE = 0, _("Add License")
    LICENCE_VERIFICATION_PENDING = 1, _("License Verification Pending")
    LICENCE_VERIFICATION_FAILED = 2, _("License Verification Failed")
    LICENSE_VERIFIED = 3, _("License Verified")


class PreferenceCategory(models.TextChoices):
    """Text choices for Preference category"""

    TRANSACTION_EXPERIENCE = "Transaction Experience", _("Transaction Experience")
    PURPOSE = "Purpose", _("Purpose")
    INVESTOR_PROFILE_SUMMARY = "Investor Profile Summary", _("Investor Profile Summary")


class CustomMessageType(Enum):
    AI_STATUS_HEADER = "ai_status_header"
    DATE_HEADER = "date_header"
    SYSTEM_MESSAGE = "system_message"
    PREDEFINED_QUESTIONS = "predefined_questions"
    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"
