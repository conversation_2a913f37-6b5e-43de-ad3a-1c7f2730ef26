import time
from typing import Dict, Any
from django.core.cache import cache
from django.conf import settings

class MetricsTracker:
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.metrics: Dict[str, Any] = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'api_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0,
            'api_response_times': [],
        }
        self.start_time = time.time()

    def record_api_call(self, response_time: float):
        self.metrics['api_calls'] += 1
        self.metrics['api_response_times'].append(response_time)

    def record_cache_hit(self):
        self.metrics['cache_hits'] += 1

    def record_cache_miss(self):
        self.metrics['cache_misses'] += 1

    def record_success(self):
        self.metrics['successful'] += 1
        self.metrics['total_processed'] += 1

    def record_failure(self):
        self.metrics['failed'] += 1
        self.metrics['total_processed'] += 1

    def get_metrics(self) -> Dict[str, Any]:
        self.metrics['total_time'] = time.time() - self.start_time
        if self.metrics['api_response_times']:
            self.metrics['avg_api_response_time'] = sum(self.metrics['api_response_times']) / len(self.metrics['api_response_times'])
        else:
            self.metrics['avg_api_response_time'] = 0
        
        if self.metrics['api_calls'] > 0:
            self.metrics['cache_hit_rate'] = self.metrics['cache_hits'] / self.metrics['api_calls']
        else:
            self.metrics['cache_hit_rate'] = 0

        return self.metrics

    def save_metrics(self):
        """Save metrics to cache for historical tracking"""
        cache_key = f"metrics_{self.service_name}_{int(time.time())}"
        cache.set(cache_key, self.get_metrics(), timeout=86400)  # Store for 24 hours 