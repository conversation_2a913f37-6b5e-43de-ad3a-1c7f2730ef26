import time
from typing import Optional
from django.core.cache import cache
from django.conf import settings


class RateLimiter:
    """
    Rate limiter implementation using Django's cache backend.
    Limits number of requests within a specified time window.

    Example usage for 5 requests per second:
        rate_limiter = RateLimiter("my_api", max_requests=5, time_window=1)

    For no rate limiting, set max_requests=0 or time_window=0
    """

    def __init__(self, key_prefix: str, max_requests: int = 5, time_window: int = 1):
        """
        Initialize rate limiter with configuration parameters.

        Args:
            key_prefix: Prefix for cache keys to avoid collisions
            max_requests: Maximum number of requests allowed in time window (default: 5). Set to 0 for no limit.
            time_window: Time window in seconds to track requests (default: 1 second). Set to 0 for no limit.
        """
        self.key_prefix = key_prefix
        self.max_requests = max_requests  # 5 requests, 0 for no limit
        self.time_window = time_window  # 1 second window, 0 for no limit

    def _get_cache_key(self, identifier: str) -> str:
        """Generate unique cache key for the rate limit counter"""
        return f"{self.key_prefix}_{identifier}"

    def is_allowed(self, identifier: str) -> bool:
        """
        Check if request is allowed based on rate limit.

        Args:
            identifier: Unique identifier for the request (e.g. API endpoint, user ID)

        Returns:
            bool: True if request is allowed, False if rate limit exceeded
        """
        # Always allow if no rate limit set
        if self.max_requests == 0 or self.time_window == 0:
            return True

        cache_key = self._get_cache_key(identifier)
        current_time = time.time()

        # Get existing request data from cache or initialize new window
        request_data = cache.get(cache_key, {"count": 0, "window_start": current_time})

        # Reset counter if time window has expired
        if current_time - request_data["window_start"] > self.time_window:
            request_data = {"count": 0, "window_start": current_time}

        # Return False if rate limit exceeded
        if request_data["count"] >= self.max_requests:
            return False

        # Increment counter and update cache
        request_data["count"] += 1
        cache.set(cache_key, request_data, self.time_window)
        return True

    def get_wait_time(self, identifier: str) -> Optional[float]:
        """
        Get remaining time until rate limit window resets.

        Args:
            identifier: Unique identifier for the request

        Returns:
            float: Seconds until rate limit resets, or None if not rate limited or no limit set
        """
        # Return None if no rate limit set
        if self.max_requests == 0 or self.time_window == 0:
            return None

        cache_key = self._get_cache_key(identifier)
        request_data = cache.get(cache_key)

        if not request_data:
            return None

        current_time = time.time()
        window_end = request_data["window_start"] + self.time_window

        # Return time until window reset if rate limited
        if current_time < window_end and request_data["count"] >= self.max_requests:
            return window_end - current_time

        return None
