import logging
import traceback
from functools import wraps
from django.db import connection

from rest_framework.response import Response

from rezio.utils.constants import DJ<PERSON><PERSON>O_LOGGER_NAME
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidTokenException,
    ResourceNotFoundException,
    InvalidSerializerDataException,
    InternalServerException,
    TwilioException,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def log_input_output(func):
    """
    This decorator logs the input and output of the given function.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(
            "Function %s called with args: %s, kwargs: %s", func.__name__, args, kwargs
        )

        # Call the original function and get the result
        result = func(*args, **kwargs)

        # Log the response if it's a JSONResponse or something that can be represented
        try:
            logger.info("Function %s returned: %s", func.__name__, result)
        except Exception as e:
            logger.warning("Could not log response content: %s", e)

        return result

    return wrapper


def log_input(func):
    """
    This decorator logs the input of the given function.
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        request = args[1]  # `request` should be the second argument in view functions

        # Log request method and path
        logger.info("Request Method: %s, Path: %s", request.method, request.path)

        # Log GET parameters
        if request.method == "GET":
            logger.info("GET Params: %s", request.GET)

        # Log POST/PUT data
        elif request.method in ["POST", "PUT"]:
            try:
                # Attempt to read the request body
                body = request.body.decode("utf-8")
                # logger.info("Request Body: %s", json.loads(body))
            except Exception as e:
                logger.warning("Could not read request body: %s", e)

        # logger.info(
        #     "Function %s called with args: %s, kwargs: %s", func.__name__, args, kwargs
        # )

        return func(*args, **kwargs)

    return wrapper


def general_exception_handler(func):
    """
    Decorator to handle general exceptions
    """

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidTokenException as invalid_token:
            return Response(
                status=invalid_token.status_code, data=invalid_token.to_dict()
            )
        except TwilioException as twilio_exception:
            return Response(
                status=twilio_exception.status_code, data=twilio_exception.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"{self.__class__.__name__} - {func.__name__} - {message} - {error}"
            )
            traceback.print_exc()
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    return wrapper


def close_db_connection(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        finally:
            connection.close()

    return wrapper
