from rezio.utils.constants import KEY_MESSAGE
from rest_framework.views import exception_handler


def custom_exception_handler(exc, context):
    """Custom exception handler"""

    response = exception_handler(exc, context)

    # * Change detail key to message
    if response is not None and "detail" in response.data:
        # if response.status_code != status.HTTP_401_UNAUTHORIZED:
        response.data[KEY_MESSAGE] = response.data["detail"]
        response.data.pop("detail")

    return response
