from rest_framework import status
from rest_framework.exceptions import APIException


class ResourceNotFoundException(APIException):
    """Custom API exception for resource not found"""

    status_code = status.HTTP_404_NOT_FOUND
    default_detail = "Requested resource does not exist"
    default_code = "resource_does_not_exist"

    def __init__(self, detail):
        self.detail = detail

    def to_dict(self):
        return self.detail


class InvalidSerializerDataException(APIException):
    """Custom API exception for Bad serializer data"""

    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = "Invalid data sent"
    default_code = "invalid_data_sent"

    def __init__(self, detail):
        self.detail = detail

    def to_dict(self):
        return self.detail


class InvalidIDTokenException(APIException):
    """Custom API exception invalid firebase user token"""

    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = "Invalid id token"
    default_code = "invalid_id_token"


class InternalServerException(APIException):
    """Custom API exception for internal errors"""

    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    default_detail = "Internal Server Error"
    default_code = "internal_server_error"


class InvalidTokenException(APIException):
    """Custom API exception for invalid token"""

    status_code = status.HTTP_401_UNAUTHORIZED
    default_detail = "User is unauthorised"
    default_code = "unauthorised_user"

    def __init__(self, detail):
        self.detail = detail

    def to_dict(self):
        return self.detail


class TwilioException(APIException):
    """Custom API exception for Twilio exception"""

    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    default_detail = "Error from twilio"
    default_code = "error_from_twilio"

    def __init__(self, detail):
        self.detail = detail

    def to_dict(self):
        return self.detail
