from django.db import transaction
from django.contrib.auth.base_user import BaseUserManager


class UserManager(BaseUserManager):
    def _create_user(
        self, primary_phone_number, email=None, password=None, **extra_fields
    ):
        """
        Create and save a user with the given mobile number.
        """
        if not primary_phone_number:
            raise ValueError("The Phone Number field must be set")
        email = self.normalize_email(email) if email else None
        with transaction.atomic():
            user = self.model(
                primary_phone_number=primary_phone_number, email=email, **extra_fields
            )
            user.set_password(password)
            user.save(using=self._db)
        return user

    def create_superuser(
        self, primary_phone_number, email=None, password=None, **extra_fields
    ):
        """
        Create and save a superuser with the given phone number, email, and password.
        """
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_user(primary_phone_number, email, password, **extra_fields)

    def create_user(
        self, primary_phone_number, email=None, password=None, **extra_fields
    ):
        """
        Creates new user with given phone number

        Returns:
            user
        """

        return self._create_user(primary_phone_number, email, password, **extra_fields)
