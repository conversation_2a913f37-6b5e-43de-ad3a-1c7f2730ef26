from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models import Q, UniqueConstraint
from phonenumber_field.modelfields import PhoneNumberField

from rezio.properties.text_choices import PropertyCategory
from rezio.user.managers import UserManager
from rezio.user.text_choices import (
    OptionTypes,
    UserDataSource,
    AgentOnboardingStatus,
    AgentSubscriptionPlanChoices,
)
from rezio.utils.text_choices import (
    Gender,
    DataSource,
    Pronouns,
    AgentWorkingType,
    AgentLicenseVerificationStatus,
    PreferenceCategory,
)


class Common(models.Model):
    """
    A common model that will have created, updated timestamps, and created by, updated by fields.
    """

    created_ts = models.DateTimeField(auto_now_add=True)
    updated_ts = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(
        "user.User",
        on_delete=models.SET_NULL,
        related_name="%(class)s_updated_by",
        null=True,
        blank=True,
    )
    updated_by_role = models.ForeignKey(
        "user.Role",
        on_delete=models.SET_NULL,
        related_name="%(class)s_created_by_role",
        null=True,
        blank=True,
    )
    created_by = models.ForeignKey(
        "user.User",
        on_delete=models.SET_NULL,
        related_name="%(class)s_created_by_role",
        null=True,
        blank=True,
    )
    created_by_role = models.ForeignKey(
        "user.Role",
        on_delete=models.SET_NULL,
        related_name="%(class)s_created_by",
        null=True,
        blank=True,
    )

    class Meta:
        abstract = True


class Role(models.Model):
    """
    A model that will store all the different roles a user can have
    """

    name = models.CharField(max_length=128)

    def __str__(self):
        return self.name


class User(AbstractUser, Common):
    """
    Model to store basic user details
    """

    # ID field that can store both Firebase IDs and UUIDs
    id = models.CharField(max_length=255, primary_key=True, editable=False)

    email = models.EmailField(unique=True, blank=True, null=True)

    # Primary phone number of the user with which the user will log in
    primary_phone_number = PhoneNumberField(unique=True, blank=False, null=False)

    # Secondary phone number of the user
    secondary_phone_number = PhoneNumberField(blank=True, null=True)

    # Multiple roles of user
    roles = models.ManyToManyField(Role, verbose_name="Role Name")

    # User active inactive flag
    is_active = models.BooleanField(default=True)

    emirates_id = models.CharField(max_length=32, null=True, blank=True)
    emirates_id_image_key = models.CharField(max_length=512, null=True, blank=True)
    emirates_id_image_name = models.CharField(max_length=128, null=True, blank=True)
    emirates_id_image_size = models.BigIntegerField(null=True, blank=True)

    passport_number = models.CharField(max_length=32, null=True, blank=True)
    passport_image_key = models.CharField(max_length=512, null=True, blank=True)
    passport_image_name = models.CharField(max_length=128, null=True, blank=True)
    passport_image_size = models.BigIntegerField(null=True, blank=True)

    username = None

    USERNAME_FIELD = "primary_phone_number"

    objects = UserManager()

    def __str__(self):
        return str(self.primary_phone_number)


"""
    Models associated with Investor
"""


class InvestorType(models.Model):
    """
    A model that will store all the different investor types
    """

    name = models.CharField(max_length=128)

    def __str__(self):
        return self.name


class InvestorProfile(Common):
    """
    A model that will store investor details
    """

    user = models.OneToOneField(User, on_delete=models.RESTRICT)
    name = models.CharField(max_length=128)
    gender = models.CharField(
        max_length=128, choices=Gender.choices, null=True, blank=True
    )
    investor_type = models.ForeignKey(
        InvestorType, on_delete=models.SET_NULL, null=True, blank=True
    )
    onboarding_completed = models.BooleanField(default=False)

    # Email of user (used for recovery)
    email = models.EmailField(unique=True, blank=True, null=True)

    # Email verification flag
    email_verified = models.BooleanField(default=False)

    pronouns = models.CharField(
        max_length=128, choices=Pronouns.choices, null=True, blank=True
    )
    profile_photo_key = models.CharField(max_length=512, null=True, blank=True)
    profile_photo_name = models.CharField(max_length=128, null=True, blank=True)
    profile_photo_size = models.BigIntegerField(null=True, blank=True)
    cover_photo_key = models.CharField(max_length=512, null=True, blank=True)
    cover_photo_name = models.CharField(max_length=512, null=True, blank=True)
    cover_photo_size = models.BigIntegerField(null=True, blank=True)
    followers = models.ManyToManyField(
        "user.Follow", related_name="investor_followers", blank=True
    )
    following = models.ManyToManyField(
        "user.Follow", related_name="investor_following", blank=True
    )
    preferred_currency_code = models.CharField(
        max_length=32, null=True, blank=True, default=settings.DEFAULT_CURRENCY_CODE
    )

    # social links
    twitter_link = models.URLField(max_length=512, null=True, blank=True)
    instagram_link = models.URLField(max_length=512, null=True, blank=True)
    linkedin_link = models.URLField(max_length=512, null=True, blank=True)
    facebook_link = models.URLField(max_length=512, null=True, blank=True)
    other_links = models.JSONField(null=True, blank=True)

    profile_public_view = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.primary_phone_number} - {self.name}"


"""
    Models associated with Agent
"""


class Agency(Common):
    """
    A model that will store all the agency names
    """

    name = models.CharField(max_length=128)
    data_source = models.CharField(
        max_length=32, choices=DataSource.choices, default=DataSource.SYSTEM_DEFAULT
    )
    country_code = models.CharField(max_length=10, null=True, blank=True, default="AE")

    def __str__(self):
        return self.name


class AgentProfile(Common):
    """
    A model that will store agent details
    """

    user = models.OneToOneField(User, on_delete=models.RESTRICT)
    agency = models.ForeignKey(Agency, on_delete=models.SET_NULL, null=True)
    name = models.CharField(max_length=128)
    gender = models.CharField(
        max_length=128, choices=Gender.choices, null=True, blank=True
    )
    brn = models.CharField(max_length=50, null=True)
    license_start_date = models.DateTimeField(null=True, blank=True)
    license_end_date = models.DateTimeField(null=True, blank=True)
    onboarding_completed = models.BooleanField(default=False)

    # Email of user (used for recovery)
    email = models.EmailField(unique=True, blank=True, null=True)
    # Email verification flag
    email_verified = models.BooleanField(default=False)

    pronouns = models.CharField(
        max_length=128, choices=Pronouns.choices, null=True, blank=True
    )
    profile_photo_key = models.CharField(max_length=512, null=True, blank=True)
    profile_photo_name = models.CharField(max_length=128, null=True, blank=True)
    profile_photo_size = models.BigIntegerField(null=True, blank=True)
    cover_photo_key = models.CharField(max_length=512, null=True, blank=True)
    cover_photo_name = models.CharField(max_length=512, null=True, blank=True)
    cover_photo_size = models.BigIntegerField(null=True, blank=True)
    bio = models.TextField(null=True, blank=True)
    commission_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, default=2
    )
    followers = models.ManyToManyField(
        "user.Follow", related_name="agent_followers", blank=True
    )
    following = models.ManyToManyField(
        "user.Follow", related_name="agent_following", blank=True
    )
    preferred_currency_code = models.CharField(
        max_length=32, null=True, blank=True, default=settings.DEFAULT_CURRENCY_CODE
    )

    # social links
    twitter_link = models.URLField(max_length=512, null=True, blank=True)
    instagram_link = models.URLField(max_length=512, null=True, blank=True)
    linkedin_link = models.URLField(max_length=512, null=True, blank=True)
    facebook_link = models.URLField(max_length=512, null=True, blank=True)
    other_links = models.JSONField(null=True, blank=True)

    license_country_code = models.CharField(max_length=10, null=True, blank=True)
    license_country = models.CharField(max_length=50, null=True, blank=True)
    working_type = models.CharField(
        max_length=50,
        choices=AgentWorkingType.choices,
        null=True,
        blank=True,
        default=AgentWorkingType.WITH_AN_AGENCY,
    )
    license_verification_status = models.CharField(
        max_length=50,
        choices=AgentLicenseVerificationStatus.choices,
        default=AgentLicenseVerificationStatus.ADD_LICENCE,
    )

    license_file_key = models.CharField(max_length=512, null=True, blank=True)
    license_file_name = models.CharField(max_length=512, null=True, blank=True)
    license_file_size = models.BigIntegerField(null=True, blank=True)
    data_source = models.CharField(
        max_length=32, choices=UserDataSource.choices, null=True
    )
    subscription_status = models.CharField(
        max_length=50,
        choices=AgentSubscriptionPlanChoices.choices,
        default=AgentSubscriptionPlanChoices.BASIC,
    )
    unlocked_properties = models.ManyToManyField(
        "properties.Property", blank=True, related_name="unlocked_by_agents"
    )
    trial_premium_card = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.primary_phone_number} - {self.name}"

    def has_social_links(self):
        # Check if any of the social links fields have a value
        return bool(
            self.twitter_link
            or self.instagram_link
            or self.linkedin_link
            or self.facebook_link
            or self.other_links
        )


class Agent(Common):
    """
    A model that will store default agent information fetched from XLS
    """

    name = models.CharField(max_length=128)
    agency = models.ForeignKey(Agency, on_delete=models.SET_NULL, null=True)
    email = models.EmailField(unique=False, blank=True, null=True)
    brn = models.CharField(max_length=50)
    phone = PhoneNumberField(blank=True, null=True)
    fax = models.CharField(max_length=128, null=True, blank=True)
    gender = models.CharField(
        max_length=128, choices=Gender.choices, null=True, blank=True
    )
    license_start_date = models.DateTimeField(null=True, blank=True)
    license_end_date = models.DateTimeField(null=True, blank=True)
    real_estate_number = models.IntegerField(null=True, blank=True)
    license_country_code = models.CharField(
        max_length=10, null=True, blank=True, default="AE"
    )
    agent_onboarding_status = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        default=AgentOnboardingStatus.NOT_ONBOARDED,
    )
    profile_photo_url = models.CharField(max_length=512, null=True, blank=True)

    def __str__(self):
        return f"{self.name} - {self.agency}"


class AgentInvitations(Common):
    """
    A model that will store agent invitations details
    """

    agent = models.ForeignKey(Agent, on_delete=models.SET_NULL, null=True)
    message_id = models.CharField(max_length=100, null=True)


"""
    Onboarding Questions/Answer Models
"""


class OnboardingQuestion(models.Model):
    """
    A model that will all the onboarding questions
    """

    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    investor_type = models.ForeignKey(
        InvestorType, on_delete=models.CASCADE, null=True, blank=True
    )
    question_text = models.TextField()
    option_type = models.CharField(max_length=128, choices=OptionTypes.choices)
    options = models.JSONField()
    is_mandatory = models.BooleanField(default=False)
    is_main_preference = models.BooleanField(default=False, null=True)
    display_title = models.CharField(max_length=128)
    preference_order = models.IntegerField(default=0, null=True, blank=True)
    preference_icon_url = models.CharField(max_length=512, null=True, blank=True)
    illustration_url = models.CharField(max_length=512, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    display_in_onboarding = models.BooleanField(default=True)
    preference_category = models.CharField(
        max_length=128,
        choices=PreferenceCategory.choices,
        null=True,
        default=PreferenceCategory.INVESTOR_PROFILE_SUMMARY,
    )

    def __str__(self):
        return f"{self.id} - {self.role.name} - {self.display_title}"


class OnboardingAnswer(models.Model):
    """
    A model that will answer of user for onboarding questions
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    onboarding_question = models.ForeignKey(
        OnboardingQuestion, on_delete=models.CASCADE
    )
    is_skipped = models.BooleanField(default=False)
    answer = models.JSONField(null=True, blank=True)

    def __str__(self):
        return f"{self.id} - {self.user.primary_phone_number}"


class TermsAndConditions(models.Model):
    terms_url = models.URLField()
    version = models.CharField(max_length=128)
    created_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.version}"


class UserAgreements(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    terms_and_conditions = models.ForeignKey(
        TermsAndConditions, on_delete=models.CASCADE
    )
    agreed_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.terms_and_conditions.version} - {self.user.primary_phone_number}"


class Follow(models.Model):
    from_user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="follower"
    )
    from_user_role = models.ForeignKey(
        "Role", on_delete=models.CASCADE, related_name="from_user_role"
    )
    to_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="followed")
    to_user_role = models.ForeignKey(
        "Role", on_delete=models.CASCADE, related_name="followed_user_role"
    )
    followed_at = models.DateTimeField(auto_now_add=True)
    unfollowed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=["from_user", "from_user_role", "to_user", "to_user_role"],
                condition=Q(unfollowed_at__isnull=True),
                name="unique_active_follow",
            )
        ]

    def __str__(self):
        return f"{self.from_user} - {self.from_user_role} follows {self.to_user} - {self.to_user_role}"


class MenuSettingsPrivacyPolicy(models.Model):
    """
    Model to store privacy policy URL
    """

    privacy_url = models.URLField()
    version = models.CharField(max_length=128)
    created_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.version}"


class UserPrivacyPolicyAgreements(models.Model):
    """
    Model to capture the privacy policy data for the user
    """

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    privacy_policy = models.ForeignKey(
        MenuSettingsPrivacyPolicy, on_delete=models.CASCADE
    )
    agreed_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.privacy_policy.version} - {self.user.primary_phone_number}"


class UserPropertySharingControlAttributes(Common):
    """
    Model to store user property sharing control attributes
    """

    private_fields = models.JSONField(null=True, blank=True)
    property_category = models.IntegerField(
        null=False,
        blank=False,
        choices=PropertyCategory.choices,
    )

    def __str__(self):
        return f"{self.user.primary_phone_number} - {self.private_fields}"

    @staticmethod
    def share_control_exists(user, role, property_category):
        return UserPropertySharingControlAttributes.objects.filter(
            created_by=user, created_by_role=role, property_category=property_category
        ).exists()
