import logging
from celery import shared_task
from datetime import date

from rezio.user.services.brn_update_service import BRNUpdateService
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.text_choices import AgentLicenseVerificationStatus
from django.apps import apps

logger = logging.getLogger(DJANGO_LOGGER_NAME)


@shared_task
def check_agent_license_expiry():
    """
    Task to check if the agent's license is expired or not
    """
    try:
        agent_model = apps.get_model("user", "AgentProfile")
        agent_model.objects.filter(license_end_date__lt=date.today()).update(
            license_verification_status=AgentLicenseVerificationStatus.ADD_LICENCE
        )
    except Exception as error:
        message = "Error occurred from check agent license expiry task"
        logger.error(f"{message} - {error}")


@shared_task(
    name="update_brn_details_from_dld",
    queue="default",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
)
def update_brn_details_from_dld():
    """
    Task fetch and update BRN details from DLD
    """
    result = BRNUpdateService.execute()
    return result
