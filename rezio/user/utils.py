import logging
import re

from django.apps import apps
from django.db.models import Q
from rest_framework import serializers

from rezio.user.constants import AGENT, INVESTOR, BUYER_AND_SELLER
from rezio.user.models import (
    Role,
    InvestorType,
    InvestorProfile,
    AgentProfile,
    User,
    Follow,
    Agent,
    MenuSettingsPrivacyPolicy,
    TermsAndConditions,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
)
from rezio.utils.custom_exceptions import ResourceNotFoundException

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def get_agent_role_object():
    """
    Get role object

    Returns:
        Role: role object

    Raises:
        ResourceNotFoundException: if role does not exist
    """

    try:
        return Role.objects.get(name=AGENT)
    except Role.DoesNotExist:
        message = f"Role {AGENT} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_investor_role_object():
    """
    Get role object

    Returns:
        Role: role object

    Raises:
        ResourceNotFoundException: if role does not exist
    """

    try:
        return Role.objects.get(name=INVESTOR)
    except Role.DoesNotExist:
        message = f"Role {INVESTOR} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_default_investor_type_object():
    """
    Get InvestorType object

    Returns:
        InvestorType: investor type object

    Raises:
        ResourceNotFoundException: if investor type does not exist
    """

    try:
        return InvestorType.objects.get(name=BUYER_AND_SELLER)
    except InvestorType.DoesNotExist:
        message = f"Investor type {BUYER_AND_SELLER} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_investor_profile_object(user):
    """
    Get InvestorProfile object

    Returns:
        InvestorProfile: investor profile object

    Raises:
        ResourceNotFoundException: if investor profile does not exist
    """

    try:
        return InvestorProfile.objects.get(user=user)
    except InvestorProfile.DoesNotExist:
        message = f"Investor profile for {user} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_agent_profile_object(user):
    """
    Get AgentProfile object

    Returns:
        AgentProfile: agent profile object

    Raises:
        ResourceNotFoundException: if agent profile does not exist
    """

    try:
        logger.info(f"user is {user}")
        return AgentProfile.objects.get(user=user)
    except AgentProfile.DoesNotExist:
        message = f"Agent profile for {user} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def contains_special_characters(name, regex="^[a-zA-Zà-ÿÀ-Ÿä-öø-ÿÄ-ÖØ-ß .’'‘-]+$"):
    pattern = re.compile(r"{}".format(regex))
    # Search for the pattern in the name
    return bool(pattern.search(name))


def get_role_object(role_name):
    """
    Get role object

    Returns:
        Role: role object

    Raises:
        ResourceNotFoundException: if role does not exist
    """

    try:
        return Role.objects.get(name=role_name)
    except Role.DoesNotExist:
        message = f"Role {role_name} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def capitalize_first_letters(sentence):
    return " ".join(word.capitalize() for word in sentence.split())


def get_user_object(user_id):
    """
    Get user object

    Returns:
        User: user object

    Raises:
        ResourceNotFoundException: if user does not exist
    """

    try:
        return User.objects.get(id=user_id)
    except User.DoesNotExist:
        message = f"User {user_id} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_follow_object(from_user, from_user_role, to_user, to_user_role):
    """
    Get follow object

    Returns:
        Follow: follow object

    Raises:
        ResourceNotFoundException: if follow does not exist
    """

    try:
        return Follow.objects.get(
            from_user=from_user,
            from_user_role=from_user_role,
            to_user=to_user,
            to_user_role=to_user_role,
            unfollowed_at__isnull=True,
        )
    except Follow.DoesNotExist:
        message = f"Follow object for {from_user.id} to {to_user.id} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def url_validator(link):
    pattern = re.compile(
        r"^(http:\/\/www\.|https:\/\/www\.|http:\/\/|https:\/\/)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$"
    )
    # Search for the pattern in the name
    if not bool(pattern.search(link)):
        raise serializers.ValidationError("Invalid link provided")
    return link


def emirates_id_validator(emirates_id: str):
    if not emirates_id.startswith("784") or len(emirates_id) != 15:
        raise serializers.ValidationError("Invalid Emirates ID format")

    return emirates_id


def passport_validator(passport: str):
    if len(passport) > 15 or not passport.isalnum():
        raise serializers.ValidationError("Invalid Passport number format.")
    return passport


def get_object_with_filters(model_name, *args, **kwargs):
    """
    Method to get database object based on provided model_name and passed filter

    :param model_name: Name of the database model
    :return db_object: Database object based on the provided filter on the given model
    """
    try:
        return model_name.objects.filter(**kwargs).first()
    except model_name.DoesNotExist:
        pass


def get_db_agent_object(phone_number):
    """
    Get db agent object

    Returns:
        Agent: agent object

    Raises:
        ResourceNotFoundException: if agent does not exist
    """

    try:
        return Agent.objects.get(phone=phone_number)
    except Agent.DoesNotExist:
        message = f"Agent with {phone_number} does not exist"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_menu_settings_privacy_policy_object():
    try:
        return MenuSettingsPrivacyPolicy.objects.latest("created_at")
    except MenuSettingsPrivacyPolicy.DoesNotExist:
        message = "Privacy URL not found"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_terms_and_condition_object():
    try:
        return TermsAndConditions.objects.latest("created_at")
    except TermsAndConditions.DoesNotExist:
        message = "Terms and conditions URL not found"
        logger.error(message)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: message,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            }
        )


def get_list_of_object_with_filters(
    app_name, model_name, single_field_value_dict=None, multi_field_value_dict=None
):
    """
    Method to get database list of objects based on provided model_name and passed filter

    :param app_name: Name of the app
    :param model_name: Name of the database model
    :param single_field_value_dict: Dictionary which contains single value for fields
    :param multi_field_value_dict: Dictionary which contains multiple values for fields
    :return db_object: Database object based on the provided filter on the given model
    """
    try:
        model_name = apps.get_model(app_name, model_name)
        filter_fields = {}
        if single_field_value_dict:
            for single_field, single_value in single_field_value_dict.items():
                filter_fields[single_field] = single_value
        if multi_field_value_dict:
            for multi_field, multi_value in multi_field_value_dict.items():
                filter_fields[f"{multi_field}__in"] = multi_value
        return model_name.objects.filter(Q(**filter_fields))
    except model_name.DoesNotExist:
        pass


def create_db_object(db_model, **fields):
    """
    Method to insert data into the database
    
    :param db_model: Name of the db model
    :return instance: Created db object
    """ ""
    instance = db_model.objects.create(**fields)
    return instance


def get_db_object(
    app_name,
    model_name,
    single_field_value_dict=None,
    multi_field_value_dict=None,
    not_found_text="Does not exist",
):
    """
    Method to get db object

    :param app_name: Name of the app
    :param model_name: Name of the database model
    :param single_field_value_dict: Dictionary which contains single value for fields
    :param multi_field_value_dict: Dictionary which contains multiple values for fields
    :param not_found_text: Text to show when object is not found
    :return db_object: Database object based on the provided filter on the given model
    """
    try:
        model_name = apps.get_model(app_name, model_name)
        filter_fields = {}
        if single_field_value_dict:
            for single_field, single_value in single_field_value_dict.items():
                filter_fields[single_field] = single_value
        if multi_field_value_dict:
            for multi_field, multi_value in multi_field_value_dict.items():
                filter_fields[f"{multi_field}__in"] = multi_value
        return model_name.objects.get(Q(**filter_fields))
    except model_name.DoesNotExist:
        logger.error(not_found_text)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: not_found_text,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: not_found_text},
            }
        )


def delete_db_object(
    app_name,
    model_name,
    single_field_value_dict=None,
    multi_field_value_dict=None,
    not_found_text="Does not exist",
):
    """
    Method to get delete the db object

    :param app_name: Name of the app
    :param model_name: Name of the database model
    :param single_field_value_dict: Dictionary which contains single value for fields
    :param multi_field_value_dict: Dictionary which contains multiple values for fields
    :param not_found_text: Text to show when object is not found
    :return db_object: Database object based on the provided filter on the given model
    """
    try:
        model_name = apps.get_model(app_name, model_name)
        filter_fields = {}
        if single_field_value_dict:
            for single_field, single_value in single_field_value_dict.items():
                filter_fields[single_field] = single_value
        if multi_field_value_dict:
            for multi_field, multi_value in multi_field_value_dict.items():
                filter_fields[f"{multi_field}__in"] = multi_value

        instance = model_name.objects.get(Q(**filter_fields))
        instance.delete()
    except model_name.DoesNotExist:
        logger.error(not_found_text)
        raise ResourceNotFoundException(
            {
                KEY_MESSAGE: not_found_text,
                KEY_PAYLOAD: {},
                KEY_ERROR: {KEY_ERROR_MESSAGE: not_found_text},
            }
        )


def get_or_create_db_object(db_model, **fields):
    """
    Method to insert or get data from the database

    :param db_model: Name of the db model
    :return
        instance: Created db object
        created: True if the object is created else False
    """
    instance, created = db_model.objects.get_or_create(**fields)
    return instance, created
