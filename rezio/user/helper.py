import logging
import mimetypes
import re
from typing import Optional, Union

import fitz
import phonenumbers
import pycountry
from django.conf import settings
from django.db import transaction
from django.db.models import QuerySet
from phonenumbers import geocoder
from rest_framework import serializers
from rest_framework.exceptions import PermissionDenied
from rest_framework.request import Request

from rezio.analytics.models import AgentProfileView, InvestorProfileView
from rezio.properties.constants import EICAR_STRING
from rezio.properties.models import PropertyCoOwner, UnregisteredCoOwner
from rezio.properties.services.property_service import recalculate_unlocked_properties
from rezio.user.constants import AGENT, INVESTOR, SELLER
from rezio.user.models import (
    InvestorProfile,
    OnboardingQuestion,
    OnboardingAnswer,
    User,
    AgentProfile,
    Role,
)
from rezio.user.permissions import IsAuthenticatedAgent, IsAuthenticatedInvestor
from rezio.user.text_choices import OptionTypes, AgentSubscriptionPlanChoices
from rezio.user.utils import (
    get_agent_profile_object,
    get_investor_profile_object,
    get_role_object,
    get_investor_role_object,
    get_default_investor_type_object,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
)
from rezio.utils.decorators import log_input_output

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def raise_invalid_data_exception(message):
    """
    Raises invalid data exception

    Raises:
        InvalidSerializerDataException: For invalid data passed in request
    """
    raise InvalidSerializerDataException(
        {
            KEY_MESSAGE: "Invalid data sent",
            KEY_PAYLOAD: {},
            KEY_ERROR: {KEY_ERROR_MESSAGE: message},
        }
    )


@log_input_output
def get_profile_object_by_role(user: User, role: Role):
    if role.name == AGENT:
        return get_agent_profile_object(user)
    elif role.name == INVESTOR:
        return get_investor_profile_object(user)
    else:
        raise_invalid_data_exception("Role not found")


@log_input_output
def check_image_validity(image, allow_pdf=None):
    # Define your size limit (in bytes)
    limit = settings.FILE_SIZE_LIMIT_IN_MB * 1024 * 1024
    if image.size > limit:
        raise serializers.ValidationError(
            f"Image size should not exceed {settings.FILE_SIZE_LIMIT_IN_MB} MB"
        )
    elif image.size == 0:
        raise serializers.ValidationError(
            f"The selected file is either empty or in an unsupported format. Please upload a valid file."
        )
    # Get the MIME type of the file
    mime_type, _ = mimetypes.guess_type(image.name)
    # Define allowed MIME types
    allowed_types = ["image/png", "image/jpeg", "image/jpg", "image/heic"]
    if allow_pdf:
        allowed_types.append("application/pdf")

    if mime_type not in allowed_types:
        raise serializers.ValidationError(f"File type '{mime_type}' is not supported")

    return image


def check_file_validity(file):
    # Define your size limit (in bytes)
    limit = settings.FILE_SIZE_LIMIT_IN_MB * 1024 * 1024
    if file.size > limit:
        raise serializers.ValidationError(
            f"File size should not exceed {settings.FILE_SIZE_LIMIT_IN_MB} MB"
        )
    elif file.size == 0:
        raise serializers.ValidationError(f"Blank file cannot be uploaded")

    content = file.read().strip()
    if not content:
        raise serializers.ValidationError("Cannot upload a blank file")

    # Get the MIME type of the file
    mime_type, _ = mimetypes.guess_type(file.name)
    # Define allowed MIME types
    allowed_types = ["application/pdf"]

    if mime_type not in allowed_types:
        raise serializers.ValidationError(f"File type '{mime_type}' is not supported")

    try:
        file.seek(0)
        reader = fitz.open(stream=file.read(), filetype="pdf")
        if reader.is_encrypted:
            raise serializers.ValidationError("The PDF file is password protected")

        is_blank = True
        for page_no in range(len(reader)):
            page = reader.load_page(page_no)

            text = page.get_text("text")
            logger.info(f"Text in file {text}")

            image_list = page.get_images(full=True)

            if re.search(re.escape(EICAR_STRING), text):
                raise serializers.ValidationError("The PDF file is corrupted")

            # Check if the page contains any non-whitespace text or image
            if (text and not text.strip() == "") or image_list:
                is_blank = False
                break
        if is_blank:
            raise serializers.ValidationError("Cannot upload a blank PDF file")
    except serializers.ValidationError as error:
        raise error
    except Exception as error:
        raise serializers.ValidationError(f"Could not read the PDF file. {str(error)}")

    return file


def get_client_ip(request):
    """
    Method to get client IP address

    :param request: Request object
    :return ip: IP address of the client
    """

    # Check for X-Forwarded-For header if the app is behind a proxy/load balancer
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        # X-Forwarded-For may contain multiple IPs, so we take the first one
        ip = x_forwarded_for.split(",")[0]
    else:
        # Use REMOTE_ADDR if no proxy is involved
        ip = request.META.get("REMOTE_ADDR")

    return ip


@log_input_output
def add_agent_profile_view(request, agent_profile):
    """
    Method to add analytics data for agent profile view
    """
    try:
        ip_address = get_client_ip(request)

        with transaction.atomic():
            agent_profile_view_instance = AgentProfileView(
                agent_id=agent_profile.id, ip_address=ip_address
            )

            # Database operation that might fail
            agent_profile_view_instance.save()
    except Exception as error:
        logger.exception(
            "Error occurred while inserting agent profile view analytics data: ", error
        )


def build_profile_permission_classes(request: Request):
    # Define your default permissions
    role_name = request.query_params.get("user_role", None)
    role = get_role_object(role_name)
    if role.name == AGENT:
        permission_classes = [IsAuthenticatedAgent]
    elif role.name == INVESTOR:
        permission_classes = [IsAuthenticatedInvestor]
    else:
        raise PermissionDenied(detail="Role not found")
    # Return the appropriate permission classes
    return [permission() for permission in permission_classes]


def add_unregistered_co_owner_as_registered(investor_profile: InvestorProfile):
    # add unregistered investor as registered when user signs up with the same phone number
    unregistered_co_owner = UnregisteredCoOwner.objects.filter(
        phone_number=investor_profile.user.primary_phone_number
    )

    if unregistered_co_owner.exists():
        property_co_owners = PropertyCoOwner.objects.filter(
            unregistered_co_owner__in=unregistered_co_owner
        )
        for co_owner in property_co_owners:
            co_owner.unregistered_co_owner = None
            co_owner.co_owner = investor_profile
            co_owner.save()

        unregistered_co_owner.delete()
        return True
    return False


def get_country_and_currency(phone_number: str):
    try:
        parsed_number = phonenumbers.parse(phone_number)
        country_code = phonenumbers.region_code_for_number(parsed_number)

        # Get country name
        country = pycountry.countries.get(alpha_2=country_code)
        if not country:
            return False
        country_name = country.name

        # Get currency (assuming ISO 4217 standard)
        currency = pycountry.currencies.get(numeric=country.numeric)
        if not currency:
            return False
        currency_code = currency.alpha_3

        return currency_code
    except Exception as error:
        logger.error(f"Error getting currency code from phone number: {error}")
        return False


def get_investor_budget(user: User, investor_profile: InvestorProfile) -> Optional[str]:
    investor_type = investor_profile.investor_type
    if investor_type and not investor_type.name == SELLER:
        budget_investor_question = (
            OnboardingQuestion.objects.filter(
                investor_type=investor_profile.investor_type,
                option_type=OptionTypes.DROPDOWN,
                is_active=True,
            )
            .order_by("preference_order")
            .first()
        )
        answer = OnboardingAnswer.objects.filter(
            onboarding_question=budget_investor_question, user=user
        )
        if answer.exists():
            answer = answer.first()
            if answer.answer:
                answered_value = answer.answer.get("answer_options", {}).get(
                    "option_text", None
                )
                return answered_value
    return None


def get_investor_is_active(user: User, investor_profile: InvestorProfile) -> bool:
    active_investor_question = OnboardingQuestion.objects.filter(
        investor_type=investor_profile.investor_type,
        option_type=OptionTypes.SLIDER,
        is_active=True,
    ).first()
    answer = OnboardingAnswer.objects.filter(
        onboarding_question=active_investor_question, user=user
    )
    if answer.exists():
        answer = answer.first()
        if answer.answer:
            answered_value = answer.answer.get("answer_options", {}).get(
                "answered_value", None
            )
            if answered_value and answered_value == 5:
                return True
    return False


def get_country_name_from_profile(
    profile: Optional[Union[InvestorProfile, AgentProfile]],
) -> Optional[str]:
    try:
        primary_phone_number = profile.user.primary_phone_number
        country = geocoder.description_for_number(primary_phone_number, "en")
        return country
    except Exception as error:
        logger.error(
            f"Error while getting country name for Investor {profile.user.id} - {error}"
        )
        return None


def add_investor_profile_analytics(
    request: Request, investor_profile: InvestorProfile
) -> bool:
    """
    Method to add analytics data for investor profile view
    """
    try:
        ip_address = get_client_ip(request)

        with transaction.atomic():
            investor_profile_view = InvestorProfileView(
                investor=investor_profile, ip_address=ip_address
            )

            # Database operation that might fail
            investor_profile_view.save()
            return True
    except Exception as error:
        logger.exception(
            "Error occurred while inserting investor profile view analytics data: ",
            error,
        )
        return False


def get_investor_preferences(
    investor_profile: InvestorProfile,
) -> QuerySet[OnboardingQuestion]:
    investor_role = get_investor_role_object()
    investor_type = investor_profile.investor_type

    if not investor_type:
        # get default investor type to show default question list
        investor_type = get_default_investor_type_object()

    # get common question for investor
    common_investor_preference = OnboardingQuestion.objects.filter(
        role=investor_role,
        investor_type__isnull=True,
        is_main_preference__isnull=False,
        is_active=True,
    )
    # get rest questions based on investor type

    investor_type_preferences = OnboardingQuestion.objects.filter(
        investor_type=investor_type, is_main_preference__isnull=False, is_active=True
    )
    investor_preferences = common_investor_preference | investor_type_preferences
    investor_preferences = investor_preferences.order_by("preference_order")

    return investor_preferences


def get_onboarding_questions_with_answer(
    user: User, question: OnboardingQuestion
) -> dict:
    options = question.options
    answer_object = OnboardingAnswer.objects.filter(
        onboarding_question=question, user=user
    )
    if answer_object.exists() and not answer_object.first().is_skipped:
        answer = answer_object.first().answer
        try:
            """
            populate answers based upon option types
            """
            if question.option_type == OptionTypes.RADIO and answer.get(
                "answer_options", None
            ):
                options["answer_options"] = [
                    (
                        answer["answer_options"]
                        if data["id"] == answer["answer_options"]["id"]
                        else data
                    )
                    for data in options.get("answer_options")
                ]

            elif question.option_type == OptionTypes.CHECKBOX and answer.get(
                "answer_options", None
            ):
                for i, data in enumerate(options.get("answer_options")):
                    for answer_data in answer["answer_options"]:
                        if data["id"] == answer_data["id"]:
                            options["answer_options"][i] = answer_data
                            break  # Stop inner loop after first match

                if answer.get("others_data", None):
                    options["others_data"]["additional_text"] = answer.get(
                        "others_data"
                    ).get("additional_text")

            elif question.option_type == OptionTypes.SLIDER and answer.get(
                "answer_options", None
            ):
                options["answer_options"]["answered_value"] = answer["answer_options"][
                    "answered_value"
                ]

            elif question.option_type == OptionTypes.DROPDOWN and answer.get(
                "answer_options", None
            ):
                options["answer_options"] = [
                    (
                        answer["answer_options"]
                        if data["id"] == answer["answer_options"]["id"]
                        else data
                    )
                    for data in options.get("answer_options")
                ]

            elif question.option_type == OptionTypes.SEARCH_LOCATION and answer.get(
                "selected_locations", None
            ):
                options["selected_locations"] = answer["selected_locations"]

            elif question.option_type == OptionTypes.CURRENCY_FIELD and answer.get(
                "options_data", None
            ):
                for i, data in enumerate(options.get("options_data")):
                    for answer_data in answer["options_data"]:
                        if data["id"] == answer_data["id"]:
                            options["options_data"][i]["answer_text"] = answer_data[
                                "answer_text"
                            ]
                            break  # Stop inner loop after first match
        except Exception as error:
            message = "Unexpected error occurred"
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: [],
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Onboarding Question - {message} - {error}"
                    },
                }
            )
    return options


class UserHelper:
    @staticmethod
    @log_input_output
    def handle_subscription(user_id, agent_profile, subscription_type):
        """
        Handle premium subscription event
        """
        try:
            basic_subscription = False
            if subscription_type == AgentSubscriptionPlanChoices.BASIC:
                agent_profile.subscription_status = AgentSubscriptionPlanChoices.BASIC
                basic_subscription = True
            elif subscription_type == AgentSubscriptionPlanChoices.PREMIUM:
                agent_profile.subscription_status = AgentSubscriptionPlanChoices.PREMIUM

            agent_profile.updated_by_id = user_id
            agent_profile.save()

            # Recalculate property unlocking based on new subscription
            recalculate_unlocked_properties(
                agent_profile, basic_subscription=basic_subscription
            )

        except Exception as e:
            logger.error(f"Error occurred in handle subscription: {str(e)}")


user_helper = UserHelper()
