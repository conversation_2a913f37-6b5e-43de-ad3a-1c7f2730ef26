from django.db import models
from django.utils.translation import gettext_lazy as _


class OptionTypes(models.TextChoices):
    """Text choices for gender"""

    RADIO = "radio", _("Radio")
    CHECKBOX = "checkbox", _("Checkbox")
    SLIDER = "slider", _("Slider")
    DROPDOWN = "dropdown", _("Dropdown")
    SEARCH_BAR = "search_bar", _("Search Bar")
    SEARCH_LOCATION = "search_location", _("Search Location")
    CURRENCY_FIELD = "currency_text_field", _("Currency Text Field")
    TEXTAREA = "textarea", _("Textarea")


class UserDataSource(models.TextChoices):
    """Text choices for user data source"""

    LICENSE_DATA_BASE = "LICENSE_DATA_BASE", _("LICENSE_DATA_BASE")
    MANUALLY_ADDED = "MANUALLY_ADDED", _("MANUALLY_ADDED")


class AgentOnboardingStatus(models.TextChoices):
    """Text choices for agent onboarding status"""

    NOT_ONBOARDED = "NOT_ONBOARDED", _("NOT_ONBOARDED")
    ONBOARDED = "ONBOARDED", _("ONBOARDED")


class AgentInvitationStatus(models.TextChoices):
    """Text choices for agent invitation status"""

    ALREADY_PRESENT = 0, _("ALREADY_PRESENT")
    INVITE_TO_REZIO = 1, _("INVITE_TO_REZIO")
    INVITED = 2, _("INVITED")


class AgentSubscriptionPlanChoices(models.TextChoices):
    """
    Text choices for agent subscription plan
    """

    BASIC = 0, _("BASIC")
    PREMIUM = 1, _("PREMIUM")


class AgentSubscriptionStatusChoices(models.TextChoices):
    ACTIVE = "ACTIVE", "Active"
    CANCELLED = "CANCELLED", "Cancelled"
    EXPIRED = "EXPIRED", "Expired"
