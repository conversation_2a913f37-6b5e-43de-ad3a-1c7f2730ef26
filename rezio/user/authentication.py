import logging

import jwt
from django.conf import settings
from firebase_admin import auth
from rest_framework.authentication import BaseAuthentication
from rest_framework_simplejwt.authentication import (
    JWTAuthentication as SimpleJWTAuthentication,
)

from rezio.rezio.custom_error_codes import AUTHENTICATION_FAILED
from rezio.rezio.settings import REVENUECAT_WEBHOOK_KEY
from rezio.user.models import User
from rezio.utils.constants import DJANGO_LOGGER_NAME, KEY_ERROR_CODE
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import InvalidIDTokenException, InvalidTokenException
from rezio.utils.decorators import log_input_output
from rezio.utils.redis_token_store import RedisTokenStore

logger = logging.getLogger(DJANGO_LOGGER_NAME)

from rezio.middlewares.logging_middleware import local_storage


class FirebaseAuthentication(BaseAuthentication):
    """
    Custom authentication using Firebase
    """

    def authenticate(self, request):
        # retrieving firebase user token from the headers
        firebase_token = request.META.get("HTTP_AUTHORIZATION")
        logger.info(f"User Token: {firebase_token}")

        try:
            # calling firebase admin function to verify the user token
            decoded_token = auth.verify_id_token(firebase_token, check_revoked=True)
            phone_number = decoded_token.get("phone_number", None)
            if phone_number:
                user, created = User.objects.get_or_create(
                    primary_phone_number=decoded_token.get("phone_number"),
                    id=decoded_token.get("uid"),
                )
                local_storage.user_id = decoded_token.get("uid")
                if user:
                    return user, None
                else:
                    return None
            else:
                message = "Invalid firebase token"
                logger.warning(message)
                # pass
                # raise InvalidIDTokenException(
                #     {
                #         KEY_MESSAGE: message,
                #         KEY_PAYLOAD: {},
                #         KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                #     }
                # )
        except auth.InvalidIdTokenError:
            message = "Invalid firebase token"
            logger.warning(message)
            # raise InvalidIDTokenException(
            #     {
            #         KEY_MESSAGE: message,
            #         KEY_PAYLOAD: {},
            #         KEY_ERROR: {KEY_ERROR_MESSAGE: message},
            #     }
            # )
        except Exception as error:
            message = "Unexpected error occurred"
            logger.warning(f"{message} - {error}")
            # raise InvalidIDTokenException(
            #     {
            #         KEY_MESSAGE: message,
            #         KEY_PAYLOAD: {},
            #         KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
            #     }
            # )


class CustomFirebaseAuthentication(BaseAuthentication):
    """
    Custom authentication using Firebase
    """

    def authenticate(self, request):
        # retrieving firebase user token from the headers
        firebase_token = request.META.get("HTTP_AUTHORIZATION")
        try:
            # calling firebase admin function to verify the user token
            decoded_token = auth.verify_id_token(firebase_token, check_revoked=True)
            phone_number = decoded_token.get("phone_number", None)
            if phone_number:
                user, created = User.objects.get_or_create(
                    primary_phone_number=decoded_token.get("phone_number")
                )
                if user:
                    return user, None
                else:
                    return None
            else:
                message = "Invalid firebase token"
                logger.warning(message)
                raise InvalidIDTokenException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )
        except auth.InvalidIdTokenError:
            message = "Invalid firebase token"
            logger.warning(message)
            raise InvalidIDTokenException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        except Exception as error:
            message = "Unexpected error occurred"
            logger.warning(f"{message} - {error}")
            raise InvalidIDTokenException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class JWTTokenAuthentication(BaseAuthentication):
    def authenticate(self, request):
        x_rezio_token = request.headers.get("X_REZIO_TOKEN")
        if not x_rezio_token:
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: "X-REZIO-TOKEN is missing",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                }
            )

        try:
            decoded_token = jwt.decode(
                x_rezio_token, settings.PUBLIC_KEY, algorithms=["RS256"]
            )

            if (
                decoded_token.get("username") != settings.AI_USER_NAME
                or decoded_token.get("secret_key") != settings.AI_SECRET_KEY
            ):
                raise InvalidTokenException(
                    {
                        KEY_MESSAGE: "X-REZIO-TOKEN is invalid",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {},
                    }
                )

        except jwt.ExpiredSignatureError:
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: "X-REZIO-TOKEN is expired",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                }
            )
        except jwt.InvalidSignatureError:
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: "X-REZIO-TOKEN's signature is invalid",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                }
            )
        except jwt.DecodeError:
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: "Failed to decode X-REZIO-TOKEN",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                }
            )
        except jwt.PyJWTError as error:
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: f"Error occurred while decoding token - {error}",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                }
            )


class JWTAuthentication(SimpleJWTAuthentication):
    """
    Custom authentication using JWT tokens with SimpleJWT
    """

    def authenticate(self, request):
        # Check if the Authorization header exists
        header = self.get_header(request)
        if header is None:
            return None

        # Extract the raw token from the header
        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        try:
            # Validate the token
            validated_token = self.get_validated_token(raw_token)

            # Check if token is blacklisted in Redis
            if RedisTokenStore.is_blacklisted(raw_token):
                raise InvalidTokenException(
                    {
                        KEY_MESSAGE: "Token has been blacklisted",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Token has been blacklisted"},
                    }
                )

            # Get the user from the validated token
            user = self.get_user(validated_token)

            # Store user ID in local_storage for logging
            if user is not None:
                local_storage.user_id = user.id

            return (user, validated_token) if user is not None else None
        except Exception as e:
            # Log the error and raise exception
            logger.error(f"Authentication error: {str(e)}")
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: f"Authentication Failed:",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: AUTHENTICATION_FAILED.get("message"),
                        KEY_ERROR_CODE: AUTHENTICATION_FAILED.get("code"),
                    },
                }
            )

    def get_header(self, request):
        """
        Extract the header from the request.
        """
        header = request.META.get("HTTP_AUTHORIZATION")
        if not header:
            # Also check for Authorization without HTTP_ prefix (for direct API calls)
            header = request.META.get("Authorization")
        return header

    def get_raw_token(self, header):
        """
        Extract the raw token from the header.
        Overridden to handle tokens without 'Bearer' prefix.
        """
        # If header contains a space, assume it has a prefix (like 'Bearer')
        if " " in header:
            return super().get_raw_token(header)

        # If no space, assume the entire header is the token
        return header


class RevenueCatAuthentication(BaseAuthentication):
    """
    Authentication class for RevenueCat webhooks
    """

    @log_input_output
    def authenticate(self, request):
        try:
            authorization_key = request.META.get("HTTP_AUTHORIZATION")

            if not authorization_key:
                logger.error("No Authorization header in RevenueCat webhook request")
                raise InvalidIDTokenException(
                    {
                        KEY_MESSAGE: "Authorization header is missing",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Authorization header is missing"
                        },
                    }
                )

            if authorization_key != REVENUECAT_WEBHOOK_KEY:
                logger.error(
                    "Invalid Authorization header in RevenueCat webhook request"
                )
                raise InvalidIDTokenException(
                    {
                        KEY_MESSAGE: "Invalid Authorization header",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid Authorization header"},
                    }
                )

        except InvalidIDTokenException:
            raise
        except Exception as e:
            # Log the error and raise exception
            logger.error(f"Authentication error in RevenueCat webhook: {str(e)}")
            raise InvalidTokenException(
                {
                    KEY_MESSAGE: "RevenueCat Authentication Failed:",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "RevenueCat Authentication Failed",
                    },
                }
            )
