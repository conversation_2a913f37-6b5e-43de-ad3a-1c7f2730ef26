import logging

from rest_framework_simplejwt.tokens import RefreshToken

from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.redis_token_store import RedisTokenStore

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class AuthService:
    """
    Service for authentication-related operations
    """

    @staticmethod
    def blacklist_specific_tokens(request):
        """
        Blacklist tokens for a user to log them out

        Args:
            request: The request object containing user and authorization header

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get the access token from the Authorization header
            auth_header = request.META.get("HTTP_AUTHORIZATION")
            if auth_header:
                # Extract the token
                access_token = (
                    auth_header.split(" ")[0]
                    if " " not in auth_header
                    else auth_header.split(" ")[1]
                )

                RedisTokenStore.blacklist_token(access_token)

            # Get refresh token from request data if available
            refresh_token = request.data.get("refresh_token") or request.data.get(
                "refresh"
            )
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()

            return True
        except Exception as e:
            logger.error(f"Error blacklisting tokens: {str(e)}")
            return False
