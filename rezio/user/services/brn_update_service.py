import logging
import requests
import time
import re
import phonenumbers
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from requests.exceptions import RequestException
from phonenumbers import NumberParseException
from concurrent.futures import ThreadPoolExecutor, as_completed

from rezio.rezio.aws import S3Client
from rezio.rezio.constants import DD_MMM_YYYY, KEY, PRESIGNED_POST_STRUCTURES, PROFILE_PHOTO
from rezio.user.constants import AGENT
from rezio.user.models import Agent, Agency, AgentProfile
from django.db import transaction
from django.utils import timezone
from django.conf import settings
from rezio.user.utils import get_agent_role_object
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.text_choices import DataSource
from rezio.user.text_choices import AgentOnboardingStatus
from rezio.utils.metrics import MetricsTracker
from rezio.utils.rate_limiter import RateLimiter
from rezio.notifications.notification_handlers import Notification<PERSON>and<PERSON>
from rezio.notifications.models import NotificationType

logger = logging.getLogger(DJANGO_LOGGER_NAME)

class BRNUpdateService:
    """Service to update BRN details from Dubai Land Department API"""
    
    BASE_URL = "https://gateway.dubailand.gov.ae/brokers/"
    MAX_RETRIES = 3
    INITIAL_RETRY_DELAY = 4  # seconds
    BATCH_SIZE = getattr(settings, "BRN_UPDATE_BATCH_SIZE", 100)  # Number of records to process in each batch
    MAX_WORKERS = getattr(settings, "BRN_UPDATE_MAX_WORKERS", 5)  # Maximum number of concurrent workers
    agent_role = get_agent_role_object()
    
    def __init__(self):
        self.metrics = MetricsTracker("brn_update")
        self.rate_limiter = RateLimiter(
            "dubai_land_department",
            getattr(settings, "DLD_MAX_REQUESTS", 0),
            getattr(settings, "DLD_TIME_WINDOW", 0),
        )
        self.notification_handler = NotificationHandler()

    def _fetch_brn_data(self, page_index: int, page_size: int = 20) -> Dict:
        """
        Fetch BRN data from Dubai Land Department API with retry logic
        """
        retries = 0
        while retries < self.MAX_RETRIES:
            try:
                # Check rate limit
                if not self.rate_limiter.is_allowed("api"):
                    wait_time = self.rate_limiter.get_wait_time("api")
                    if wait_time:
                        logger.warning(f"Rate limit reached. Waiting {wait_time} seconds")
                        time.sleep(wait_time)

                params = {
                    "sortCriteria": 2,
                    "pageIndex": page_index,
                    "pageSize": page_size,
                    "consumer-id": settings.BRN_DATA_CONSUMER_KEY
                }
                
                api_start_time = time.time()
                response = requests.get(
                    self.BASE_URL,
                    params=params
                )
                api_response_time = time.time() - api_start_time
                self.metrics.record_api_call(api_response_time)
                
                response.raise_for_status()
                return response.json()
                
            except RequestException as e:
                retries += 1
                if retries == self.MAX_RETRIES:
                    logger.error(f"Failed to fetch BRN data after {self.MAX_RETRIES} attempts: {str(e)}")
                    self.metrics.record_failure()
                    raise
                
                # Calculate exponential backoff delay: 4s, 8s, 16s
                delay = self.INITIAL_RETRY_DELAY * (2 ** (retries - 1))
                logger.warning(f"Retry {retries}/{self.MAX_RETRIES} after {delay} seconds. Error: {str(e)}")
                time.sleep(delay)

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to timezone-aware datetime object"""
        try:
            if date_str:
                naive_datetime = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S")
                return timezone.make_aware(naive_datetime, timezone.get_default_timezone())
            return None
        except ValueError as e:
            logger.error(f"Error parsing date {date_str}: {str(e)}")
            return None

    def _format_phone_number(self, phone: str) -> Optional[str]:
        """Format phone number to E.164 format"""
        if not phone:
            return None

        phone = re.sub(r"[^\d+]", "", str(phone))
        if phone.startswith("971|"):
            phone = phone.replace("971|", "+971")
        elif phone.startswith("971") and not phone.startswith("+"):
            phone = "+" + phone
        elif phone.startswith("0"):
            phone = phone.replace("0", "+971", 1)
        elif not phone.startswith("+"):
            phone = "+" + phone
        phone = re.sub(r"[^\d+]", "", phone)
        if len(phone) < 8 or not phone.startswith("+971"):
            logger.warning(f"Invalid phone number format: {phone}")
            return None

        try:
            parsed_phone_number = phonenumbers.parse(phone, None)
        except NumberParseException:
            logger.warning(f"Invalid phone number: {phone}")
            return None
        except Exception as error:
            logger.error(f"Unexpected error: {error}")
            return None
        if not phonenumbers.is_possible_number(parsed_phone_number):
            return None

        return phone

    def _validate_brn_data(self, brn_data: Dict) -> Tuple[List[str], Optional[str]]:
        """Validate BRN data and return list of issues and formatted phone number"""
        issues = []
        formatted_phone = None
        
        # Check required fields
        required_fields = [
            "CardHolderNameEn",
            "CardIssueDate", 
            "CardExpiryDate",
            "CardHolderEmail",
            "CardHolderMobile",
            "LicenseNumber",
            "OfficeNameEn",
            "CardHolderPhoto"
        ]
        
        for field in required_fields:
            if not brn_data.get(field):
                issues.append(f"Missing {field}")
        
        # Validate phone number
        if brn_data.get("CardHolderMobile"):
            formatted_phone = self._format_phone_number(brn_data["CardHolderMobile"])
            if not formatted_phone:
                issues.append("Invalid phone number")
        
        return issues, formatted_phone

    def _process_batch(self, brn_data_list: List[Dict]) -> None:
        """Process a batch of BRN data records"""
        max_retries = 3
        retry_delay = 1  # seconds
        
        for retry in range(max_retries):
            try:
                with transaction.atomic():
                    total_processed = 0
                    skipped_records = 0
                    
                    for brn_data in brn_data_list:
                        # Validate data
                        issues, formatted_phone = self._validate_brn_data(brn_data)
                        if issues:
                            logger.warning(f"Skipping BRN {brn_data.get('CardNumber')} due to issues: {', '.join(issues)}")
                            skipped_records += 1
                            self.metrics.record_failure()
                            continue

                        # Get or create agency
                        agency_name = brn_data["OfficeNameEn"]
                        agency, created = Agency.objects.get_or_create(
                            name=agency_name,
                            defaults={"data_source": DataSource.PROPERTY_MONITOR}
                        )
                        if created:
                            logger.info(f"Created new Agency: {agency.name}")
                        else:
                            logger.info(f"Found existing Agency: {agency.name}")

                        # Prepare agent data
                        license_start_date = self._parse_date(brn_data["CardIssueDate"])
                        license_end_date = self._parse_date(brn_data["CardExpiryDate"])
                        
                        # Update agent
                        agent = Agent.objects.filter(
                            brn=brn_data["CardNumber"],
                            license_country_code="AE"
                        ).first()

                        if agent:
                            logger.info(f"Found existing Agent with BRN {agent.brn}")
                            # Check if any fields have changed
                            old_values = {
                                'name': agent.name,
                                'agency_id': agent.agency_id,
                                'email': agent.email,
                                'phone': agent.phone,
                                'license_start_date': agent.license_start_date,
                                'license_end_date': agent.license_end_date,
                                'real_estate_number': agent.real_estate_number,
                                'profile_photo_url': agent.profile_photo_url
                            }

                            new_values = {
                                'name': brn_data["CardHolderNameEn"],
                                'agency_id': agency.id,
                                'email': brn_data["CardHolderEmail"],
                                'phone': formatted_phone,
                                'license_start_date': license_start_date,
                                'license_end_date': license_end_date,
                                'real_estate_number': brn_data.get("RealEstateNumber"),
                                'profile_photo_url': brn_data.get("CardHolderPhoto")
                            }

                            has_changes = any(old_values[key] != new_values[key] for key in old_values)

                            if has_changes:
                                # Log changes
                                changes = {
                                    key: {'old': old_values[key], 'new': new_values[key]}
                                    for key in old_values
                                    if old_values[key] != new_values[key]
                                }
                                logger.info(f"Updating Agent BRN {agent.brn} with changes: {changes}")

                                # Update existing agent
                                agent.name = new_values['name']
                                agent.agency = agency
                                agent.email = new_values['email']
                                agent.phone = new_values['phone']
                                agent.license_start_date = new_values['license_start_date']
                                agent.license_end_date = new_values['license_end_date']
                                agent.real_estate_number = new_values['real_estate_number']
                                agent.profile_photo_url = new_values['profile_photo_url']
                                agent.save()
                                created = False
                        else:
                            # Create new agent
                            agent = Agent.objects.create(
                                brn=brn_data["LicenseNumber"],
                                license_country_code="AE",
                                name=brn_data["CardHolderNameEn"],
                                agency=agency,
                                email=brn_data["CardHolderEmail"],
                                phone=formatted_phone,
                                license_start_date=license_start_date,
                                license_end_date=license_end_date,
                                real_estate_number=brn_data.get("RealEstateNumber"),
                                profile_photo_url=brn_data.get("CardHolderPhoto")
                            )
                            created = True

                        if created:
                            logger.info(f"Created new Agent: {agent.name} with BRN: {agent.brn}")
                        else:
                            logger.info(f"Updated Agent: {agent.name} with BRN: {agent.brn}")

                        # Update AgentProfile if agent is onboarded
                        if agent.agent_onboarding_status == AgentOnboardingStatus.ONBOARDED:
                            try:
                                agent_profile = AgentProfile.objects.get(brn=agent.brn)
                                
                                # Check if profile photo needs to be updated
                                if agent.profile_photo_url and not agent_profile.profile_photo_key:
                                    try:
                                        s3_client = S3Client()
                                        # Generate a unique key for the profile photo
                                        profile_photo_key = PRESIGNED_POST_STRUCTURES.get(
                                            PROFILE_PHOTO, {}
                                        ).get(KEY, "")
                                        profile_photo_key = profile_photo_key.format(
                                            user_id=agent_profile.user.id,
                                            role_name=AGENT,
                                            filename=f"{agent_profile.user.id}_{datetime.now().strftime(DD_MMM_YYYY)}_{AGENT}_{PROFILE_PHOTO}",
                                        )
                                        # Download and upload the photo to S3
                                        upload_result = s3_client.download_and_upload_file(
                                            url=agent.profile_photo_url,
                                            s3_key=profile_photo_key,
                                        )
                                        
                                        # Update agent profile with the new photo details
                                        agent_profile.profile_photo_key = upload_result['key']
                                        agent_profile.profile_photo_size = upload_result['size']
                                        logger.info(f"Updated profile photo for AgentProfile: {agent_profile.name}")
                                        
                                        # Send notification to user about profile photo update
                                        try:
                                            self.notification_handler._create_and_send_notification(
                                                user=agent_profile.user,
                                                role=self.agent_role,
                                                notification_type=NotificationType.PROFILE_PHOTO_UPDATE.value,
                                                title="Profile Update",
                                                message="Your profile picture has been updated. Click here to view your profile.",
                                                priority="MEDIUM",
                                                metadata={
                                                    "notification_category": "PROFILE",
                                                    "action_required": False,
                                                },
                                                push_notification_data={
                                                    "notification_category": "PROFILE",
                                                    "priority_level": "HIGH",
                                                    "notification_type": NotificationType.PROFILE_PHOTO_UPDATE.value,
                                                },
                                                related_user=agent_profile.user,
                                                related_user_role=self.agent_role,
                                            )
                                            logger.info(f"Sent profile photo update notification to user {agent_profile.user.id}")
                                        except Exception as notification_error:
                                            logger.error(f"Failed to send profile photo update notification: {str(notification_error)}")
                                    
                                    except Exception as photo_error:
                                        logger.error(f"Error updating profile photo for BRN {agent.brn}: {str(photo_error)}")
                                
                                # Update license dates if changed
                                if (agent_profile.license_start_date != license_start_date or 
                                    agent_profile.license_end_date != license_end_date):
                                    agent_profile.license_start_date = license_start_date
                                    agent_profile.license_end_date = license_end_date
                                    logger.info(f"Updated license dates for onboarded AgentProfile: {agent_profile.name}")
                                else:
                                    logger.debug(f"No changes in license dates for AgentProfile: {agent_profile.name}")
                                
                                agent_profile.save()
                                
                            except AgentProfile.DoesNotExist:
                                logger.warning(f"No AgentProfile found for onboarded agent with BRN: {agent.brn}")
                            except Exception as e:
                                logger.error(f"Error updating AgentProfile for BRN {agent.brn}: {str(e)}")
                                self.metrics.record_failure()

                        total_processed += 1
                        self.metrics.record_success()

                    logger.info(f"Processed batch: {total_processed} records processed, {skipped_records} records skipped")
                    return  # Successfully processed the batch

            except Exception as e:
                if "deadlock detected" in str(e) and retry < max_retries - 1:
                    wait_time = retry_delay * (2 ** retry)  # Exponential backoff
                    logger.warning(f"Deadlock detected, retrying in {wait_time} seconds (attempt {retry + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
                else:
                    logger.error(f"Error processing batch: {str(e)}")
                    self.metrics.record_failure()
                    raise

    def update_brn_details(self) -> None:
        """
        Main method to fetch and update BRN details
        """
        start_time = time.time()
        try:
            logger.info("Starting BRN update process")
            page_size = 50
            total_processed = 0
            
            # First, get total count to determine number of pages
            initial_response = self._fetch_brn_data(0, page_size)
            total_records = initial_response.get("TotalRowsCount", 0)
            total_pages = (total_records + page_size - 1) // page_size  # Ceiling division
            
            logger.info(f"Total records to process: {total_records}, Total pages: {total_pages}")
            
            # Process first page's data
            if initial_response.get("Response"):
                try:
                    self._process_batch(initial_response["Response"])
                    total_processed += len(initial_response["Response"])
                    logger.info(f"Successfully processed first page with {len(initial_response['Response'])} records")
                except Exception as batch_error:
                    logger.error(
                        f"Error processing first page: {str(batch_error)}",
                        exc_info=True
                    )
            
            # Fetch and process remaining pages concurrently
            with ThreadPoolExecutor(max_workers=self.MAX_WORKERS) as executor:
                # Create futures for remaining pages
                futures = {
                    executor.submit(self._fetch_and_process_page, page_index, page_size): page_index
                    for page_index in range(1, total_pages)
                }
                
                # Process results as they complete
                for future in as_completed(futures):
                    page_index = futures[future]
                    try:
                        processed_count = future.result()
                        total_processed += processed_count
                        logger.info(f"Successfully processed page {page_index} with {processed_count} records")
                    except Exception as page_error:
                        logger.error(
                            f"Error processing page {page_index}: {str(page_error)}",
                            exc_info=True
                        )
            
            # Save metrics and log summary
            self.metrics.save_metrics()
            metrics_summary = self.metrics.get_metrics()
            logger.info(f"BRN update completed. Total records processed: {total_processed}")
            logger.info(f"Metrics: {metrics_summary}")
            logger.info(f"Total time taken: {time.time() - start_time} seconds")
            
        except Exception as e:
            self.metrics.record_failure()
            logger.error(f"Error in BRN update process: {str(e)}")
            raise

    def _fetch_and_process_page(self, page_index: int, page_size: int) -> int:
        """
        Fetch and process a single page of BRN data
        Returns the number of records processed
        """
        try:
            response_data = self._fetch_brn_data(page_index, page_size)
            if not response_data.get("Response"):
                return 0
                
            self._process_batch(response_data["Response"])
            return len(response_data["Response"])
            
        except Exception as e:
            logger.error(f"Error in _fetch_and_process_page for page {page_index}: {str(e)}")
            self.metrics.record_failure()
            raise

    @classmethod
    def execute(cls) -> str:
        """
        Execute the BRN update service
        """
        try:
            service = cls()
            service.update_brn_details()
            logger.info("BRN update service executed successfully")
            return "BRN update service executed successfully"
        except Exception as e:
            logger.error(f"Failed to execute BRN update service: {str(e)}")
            return "Failed to execute BRN update service executed"