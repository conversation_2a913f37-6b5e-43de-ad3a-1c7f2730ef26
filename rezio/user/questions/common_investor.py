from rezio.user.constants import BUYER_AND_SELLER, <PERSON>UY<PERSON>, <PERSON><PERSON><PERSON>


def get_common_investor_question(investor_type_model):
    common_question = [
        {
            "question_text": "Are you here to",
            "option_type": "radio",
            "is_mandatory": False,
            "is_main_preference": True,
            "preference_order": 1,
            "preference_icon_url": "https://rezio-static-files.s3.me-central-1.amazonaws.com/onboarding-questions-preferences-icons/investor-preferences-icons/investor_primary_purpose_preference_icon.svg",
            "illustration_url": "https://rezio-static-files.s3.me-central-1.amazonaws.com/onboarding-questions-illustrations/investor-illustrations/investor_type_illustration.svg",
            "display_title": "Primary Purpose",
            "answer_options": [
                {
                    "id": 1,
                    "default_selected": True,
                    "option_text": "Buy and sell properties",
                    "icon_url": "https://rezio-static-files.s3.me-central-1.amazonaws.com/onboarding-questions-icons/investor-icons/buy_and_sell.svg",
                    "user_selected": False,
                    "investor_type_id": investor_type_model.objects.get(
                        name=BUYER_AND_SELLER
                    ).id,
                },
                {
                    "id": 2,
                    "default_selected": False,
                    "option_text": "Only buy properties",
                    "icon_url": "https://rezio-static-files.s3.me-central-1.amazonaws.com/onboarding-questions-icons/investor-icons/buy_only.svg",
                    "user_selected": False,
                    "investor_type_id": investor_type_model.objects.get(name=BUYER).id,
                },
                {
                    "id": 3,
                    "default_selected": False,
                    "option_text": "Only sell properties",
                    "icon_url": "https://rezio-static-files.s3.me-central-1.amazonaws.com/onboarding-questions-icons/investor-icons/sell_only.svg",
                    "user_selected": False,
                    "investor_type_id": investor_type_model.objects.get(name=SELLER).id,
                },
            ],
        }
    ]
    return common_question
