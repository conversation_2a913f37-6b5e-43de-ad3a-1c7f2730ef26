import logging
from rezio.user.constants import (
    QUESTION_TEXT_KEY,
    OPTION_TYPE_KEY,
    MANDATORY_KEY,
    AGENT,
    INVESTOR,
    <PERSON><PERSON>YER,
    <PERSON>LLER,
    BUYER_AND_SELLER,
    REQUIRED_KEYS,
    PREFERENCE_KEY,
    DISPLAY_TITLE_KEY,
    PREFERENCE_ORDER,
    PREFERENCE_ICON_URL,
    ILLUSTRATION_URL,
)
from rezio.user.questions import (
    agent_questions,
    buyer_and_seller,
    buy_only,
    sell_only,
    common_investor,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class CreateQuestion:
    def __init__(
        self,
        role_model,
        investor_type_model,
        question_model,
        role_name,
        questions_data,
        investor_type_name=None,
    ):
        self.role_name = role_name
        self.questions_data = questions_data
        self.investor_type_name = investor_type_name
        self.role_model = role_model
        self.investor_type_model = investor_type_model
        self.question_model = question_model

    def get_role(self):
        try:
            return self.role_model.objects.get(name__exact=self.role_name)
        except self.role_model.DoesNotExist:
            return None

    def get_investor_type(self):
        try:
            return self.investor_type_model.objects.get(
                name__exact=self.investor_type_name
            )
        except self.investor_type_model.DoesNotExist:
            return None

    @staticmethod
    def check_missing_keys(keys, dictionary):
        missing_keys = [key for key in keys if key not in dictionary]
        return missing_keys

    def create_questions(self):
        try:
            role = self.get_role()
            if not role:
                logger.warning(f"Role name {self.role_name} not found")
                return False

            if self.investor_type_name:
                investor_type = self.get_investor_type()
                if not investor_type:
                    logger.warning(f"Investor type {self.role_name} not found")
                    return False
            else:
                investor_type = None

            question_object_list = list()
            for question_data in self.questions_data:
                if not self.check_missing_keys(REQUIRED_KEYS, question_data):
                    question_text = question_data.pop(QUESTION_TEXT_KEY)
                    option_type = question_data.pop(OPTION_TYPE_KEY)
                    is_mandatory = question_data.pop(MANDATORY_KEY)
                    is_main_preference = question_data.pop(PREFERENCE_KEY)
                    display_title = question_data.pop(DISPLAY_TITLE_KEY)
                    preference_order = question_data.pop(PREFERENCE_ORDER)
                    preference_icon_url = question_data.pop(PREFERENCE_ICON_URL)
                    illustration_url = question_data.pop(ILLUSTRATION_URL)
                    options = question_data
                    question_object = self.question_model(
                        role=role,
                        investor_type=investor_type,
                        question_text=question_text,
                        option_type=option_type,
                        is_mandatory=is_mandatory,
                        options=options,
                        is_main_preference=is_main_preference,
                        display_title=display_title,
                        preference_order=preference_order,
                        preference_icon_url=preference_icon_url,
                        illustration_url=illustration_url,
                    )

                    question_object_list.append(question_object)
                else:
                    return False

            return question_object_list

        except Exception as error:
            print(self.questions_data)
            logger.error(f"An error occurred - {error}")
            return False


def create_questions_for_roles(role_model, investor_model, question_model):
    # create question for agent role
    create_agent_questions = CreateQuestion(
        role_model,
        investor_model,
        question_model,
        AGENT,
        agent_questions.agent_questions_list,
    )
    agent_questions_list = create_agent_questions.create_questions()

    # create common question for investor role
    create_investor_common_questions = CreateQuestion(
        role_model,
        investor_model,
        question_model,
        INVESTOR,
        common_investor.get_common_investor_question(investor_model),
    )
    common_investor_questions_list = create_investor_common_questions.create_questions()

    # create question for investor role who is buyer & seller
    create_investor_buyer_and_seller_questions = CreateQuestion(
        role_model,
        investor_model,
        question_model,
        INVESTOR,
        buyer_and_seller.buyer_and_seller_questions_list,
        BUYER_AND_SELLER,
    )
    investor_buyer_and_seller_questions_list = (
        create_investor_buyer_and_seller_questions.create_questions()
    )

    # create question for investor role who is buyer
    create_investor_buyer_questions = CreateQuestion(
        role_model,
        investor_model,
        question_model,
        INVESTOR,
        buy_only.buy_only_questions_list,
        BUYER,
    )
    investor_buyer_questions_list = create_investor_buyer_questions.create_questions()

    # create question for investor role who is seller
    create_investor_buyer_and_seller_questions = CreateQuestion(
        role_model,
        investor_model,
        question_model,
        INVESTOR,
        sell_only.sell_only_questions_list,
        SELLER,
    )
    investor_seller_questions_list = (
        create_investor_buyer_and_seller_questions.create_questions()
    )

    if (
        agent_questions_list
        and common_investor_questions_list
        and investor_buyer_and_seller_questions_list
        and investor_buyer_questions_list
        and investor_seller_questions_list
    ):
        all_question_list = (
            agent_questions_list
            + common_investor_questions_list
            + investor_buyer_and_seller_questions_list
            + investor_buyer_questions_list
            + investor_seller_questions_list
        )
        logger.info(f"Saved questions data successfully")
        question_model.objects.bulk_create(all_question_list)
    else:
        logger.error(f"Unable to save the questions data")
        return False
