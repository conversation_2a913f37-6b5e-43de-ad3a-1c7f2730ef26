import logging
import re
from datetime import datetime
from io import BytesIO

import pandas as pd
import pytz
import requests
from django.db import migrations
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger("django")

S3_URL = "https://rezio-static-files.s3.me-central-1.amazonaws.com/agent_data/broker_data_updated.xlsx"


def convert_to_aware_datetime(naive_datetime_str):
    """Converts a naive datetime string or pd.Timestamp to a timezone-aware datetime object."""
    try:
        if isinstance(naive_datetime_str, pd.Timestamp):
            naive_datetime = naive_datetime_str.to_pydatetime()
        elif isinstance(naive_datetime_str, str):
            naive_datetime = datetime.fromisoformat(naive_datetime_str)
        elif isinstance(naive_datetime_str, datetime):
            naive_datetime = naive_datetime_str
        else:
            logger.error(f"Unsupported datetime format: {naive_datetime_str}")
            return None

        return timezone.make_aware(naive_datetime, timezone.get_default_timezone())
    except Exception as error:
        logger.error(f"Error converting datetime '{naive_datetime_str}': {error}")
        return None


def clean_agency_name(name):
    """Removes Arabic characters, trims extra spaces, removes trailing commas, and normalizes agency names."""
    if pd.isna(name):
        return name
    name = re.sub(r"[\u0600-\u06FF]+", "", name).strip()  # Remove Arabic characters
    name = name.rstrip(",")  # Remove trailing commas
    name = name.upper()  # Normalize case
    name = re.sub(r"\(\s*L\.L\.C\s*\)", "(L.L.C)", name)  # Standardize L.L.C format
    return name


def clean_phone_number(phone):
    """Cleans and formats phone numbers to UAE E.164 format, ensuring no numbers exceed 13 digits."""
    if pd.isnull(phone):
        return None
    phone = re.sub(r"[^\d+]", "", str(phone))

    if phone.startswith("971|"):
        phone = phone.replace("971|", "+971")
    elif phone.startswith("971") and not phone.startswith("+"):
        phone = "+" + phone
    elif phone.startswith("0"):
        phone = "+971" + phone[1:]
    elif not phone.startswith("+971"):
        phone = "+971" + phone.lstrip("+")

    phone = re.sub(r"\++", "+", phone)

    if len(phone) > 13:
        logger.warning(f"Phone number too long (more than 13 digits): {phone}")
        return None

    return phone


def clean_broker_data(url):
    """Downloads, validates, and cleans broker data."""
    try:
        response = requests.get(url)
        response.raise_for_status()
        df = pd.read_excel(BytesIO(response.content), dtype=str)
        logger.info("✅ Successfully downloaded broker data from S3.")

        # Check expected columns
        expected_columns = [
            "Name English",
            "Name Arabic",
            "Broker Number",
            "Office Name English",
            "Office Name Arabic",
            "Email",
            "Phone Number",
            "license_start_date",
            "license_end_date",
            "real_estate_number",
            "fax",
        ]
        missing_columns = [col for col in expected_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"❌ Missing required columns: {missing_columns}")
            raise KeyError(f"Missing required columns: {missing_columns}")

        # Remove duplicates based on 'Broker Number'
        initial_count = len(df)
        df.drop_duplicates(subset=["Broker Number"], keep="last", inplace=True)
        logger.info(f"✅ Removed {initial_count - len(df)} duplicate brokers.")

        # Normalize BRN format
        df["Broker Number"] = df["Broker Number"].apply(
            lambda x: (
                str(int(float(x)))
                if pd.notnull(x) and re.match(r"^\d+(\.0+)?$", str(x))
                else "INVALID_BRN"
            )
        )
        logger.info("✅ Broker Number (BRN) formatted correctly.")

        # Clean phone numbers
        df["Phone Number"] = df["Phone Number"].apply(clean_phone_number)
        logger.info("✅ Phone numbers formatted.")

        # Clean agency names
        df["Office Name English"] = df["Office Name English"].apply(clean_agency_name)
        logger.info("✅ Agency names cleaned.")

        # Convert dates to timezone-aware datetime objects
        for col in ["license_start_date", "license_end_date"]:
            df[col] = (
                pd.to_datetime(df[col], errors="coerce")
                .dt.tz_localize(pytz.UTC)
                .dt.tz_convert(None)
            )
        logger.info("✅ Dates converted to UTC.")

        # Validate emails
        email_pattern = re.compile(r"^[\w\.-]+@[\w\.-]+\.\w+$")
        df["Email"] = df["Email"].apply(
            lambda x: x if email_pattern.match(str(x)) else "<EMAIL>"
        )

        return df
    except Exception as e:
        logger.error(f"❌ Error in cleaning broker data: {e}")
        raise


def replace_broker_data(apps, schema_editor):
    """Replaces broker data in the database with cleaned data from S3."""
    try:
        Agent = apps.get_model("user", "Agent")
        Agency = apps.get_model("user", "Agency")

        df = clean_broker_data(S3_URL)
        total_records = len(df)
        processed_records = 0

        with transaction.atomic():
            for index, row in df.iterrows():
                if row["Broker Number"] == "INVALID_BRN" or not row["Phone Number"]:
                    logger.warning(
                        f"⚠️ Skipping row {index} due to invalid BRN or missing phone number."
                    )
                    continue

                agency_name = row["Office Name English"]
                if pd.isna(agency_name):
                    logger.warning(
                        f"⚠️ Skipping row {index} due to missing agency name."
                    )
                    continue

                # Get or create the agency
                agency, created = Agency.objects.get_or_create(name=agency_name)
                if created:
                    logger.info(f"✅ Created new Agency: {agency.name}")

                # Get or create the agent
                agent_defaults = {
                    "name": row["Name English"],
                    "agency": agency,
                    "phone": row["Phone Number"],
                    "email": row["Email"] if pd.notnull(row["Email"]) else "",
                    "license_start_date": row["license_start_date"],
                    "license_end_date": row["license_end_date"],
                    "real_estate_number": row["real_estate_number"],
                }

                # Set fax only if it is missing
                if row["fax"]:
                    agent_defaults["fax"] = row["fax"]

                agent, created = Agent.objects.update_or_create(
                    brn=row["Broker Number"], defaults=agent_defaults
                )

                if created:
                    logger.info(
                        f"✅ Created new Agent: {agent.name} (BRN: {agent.brn})"
                    )
                else:
                    logger.info(f"🔄 Updated Agent: {agent.name} (BRN: {agent.brn})")

                processed_records += 1
                if processed_records % 100 == 0:
                    logger.info(
                        f"🔄 Processed {processed_records}/{total_records} records."
                    )

        logger.info(f"🎉 Migration completed! Processed {processed_records} records.")

    except Exception as error:
        import traceback

        traceback.print_exc()
        logger.error(f"❌ Error in broker data migration: {error}")


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0049_agentprofile_unlocked_properties"),
    ]
