# Generated by Django 4.1.7 on 2024-11-25 05:20
import logging
from django.db import migrations
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def remove_duplicate_phone_numbers(apps, schema_editor):
    db_agents = apps.get_model('user', 'Agent')
    seen_phones = set()
    deleted_phones = set()
    # Iterate through all entries in the model
    for agent in db_agents.objects.all():
        if agent.phone in seen_phones:
            # If phone number is already seen, delete the entry
            deleted_phones.add(agent.phone)
        else:
            # Otherwise, add the phone number to the seen set
            seen_phones.add(agent.phone)

    deleted_phones = list(deleted_phones)
    db_agents.objects.filter(phone__in=deleted_phones).delete()



class Migration(migrations.Migration):
    dependencies = [
        ('user', '0034_agent_agent_onboarding_status_agentinvitations'),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_phone_numbers)
    ]
