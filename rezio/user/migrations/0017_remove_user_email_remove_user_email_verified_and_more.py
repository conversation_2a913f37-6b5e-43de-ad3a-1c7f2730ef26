# Generated by Django 4.1.7 on 2024-09-11 10:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0016_agent_data_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="user",
            name="email_verified",
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="bio",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="email",
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="profile_photo_key",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=512, null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="agentprofile",
            name="pronouns",
            field=models.CharField(
                blank=True,
                choices=[("0", "He/Him/His"), ("1", "She/Her/Hers")],
                max_length=128,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="email",
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="profile_photo_key",
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="pronouns",
            field=models.CharField(
                blank=True,
                choices=[("0", "He/Him/His"), ("1", "She/Her/Hers")],
                max_length=128,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="emirates_id",
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="emirates_id_image_key",
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="passport_image_key",
            field=models.CharField(blank=True, max_length=512, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="passport_number",
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.CreateModel(
            name="Follow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("followed_at", models.DateTimeField(auto_now_add=True)),
                ("unfollowed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "from_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="follower",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "from_user_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="from_user_role",
                        to="user.role",
                    ),
                ),
                (
                    "to_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="followed",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "to_user_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="followed_user_role",
                        to="user.role",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="followers",
            field=models.ManyToManyField(
                related_name="agent_followers", to="user.follow"
            ),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="following",
            field=models.ManyToManyField(
                related_name="agent_following", to="user.follow"
            ),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="followers",
            field=models.ManyToManyField(
                related_name="investor_followers", to="user.follow"
            ),
        ),
        migrations.AddField(
            model_name="investorprofile",
            name="following",
            field=models.ManyToManyField(
                related_name="investor_following", to="user.follow"
            ),
        ),
    ]
