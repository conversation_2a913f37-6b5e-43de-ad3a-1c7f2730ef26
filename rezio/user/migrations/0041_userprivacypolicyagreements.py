# Generated by Django 4.1.7 on 2024-12-03 12:43

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0040_alter_agentprofile_subscription_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPrivacyPolicyAgreements",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("agreed_at", models.DateTimeField(auto_now=True)),
                (
                    "privacy_policy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="user.menusettingsprivacypolicy",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
