# Generated by Django 4.1.7 on 2025-02-11 19:00

import logging
import re
from datetime import datetime
from io import BytesIO

import pandas as pd
import phonenumbers
import requests
from django.db import migrations
from django.db import transaction
from django.utils import timezone
from phonenumbers import NumberParseException

logger = logging.getLogger("django")


def convert_to_aware_datetime(naive_datetime_str):
    """
    Converts a naive datetime string or pd.Timestamp to a timezone-aware datetime object
    using Django's default timezone.
    """
    try:
        if isinstance(naive_datetime_str, pd.Timestamp):
            naive_datetime = naive_datetime_str.to_pydatetime()
        elif isinstance(naive_datetime_str, str):
            naive_datetime = datetime.fromisoformat(naive_datetime_str)
        elif isinstance(naive_datetime_str, datetime):
            naive_datetime = naive_datetime_str
        else:
            logger.error(f"Unsupported datetime format: {naive_datetime_str}")
            return None

        return timezone.make_aware(naive_datetime, timezone.get_default_timezone())
    except Exception as error:
        logger.error(f"Error converting datetime '{naive_datetime_str}': {error}")
        return None


def clean_broker_data(url):
    """
    Downloads and cleans broker data from the specified S3 URL.
    Ensures that all required columns are present and cleans data accordingly.
    """
    try:
        response = requests.get(url)
        response.raise_for_status()
        excel_data = BytesIO(response.content)
        df = pd.read_excel(excel_data, dtype={"Phone Number": str})
        logger.info("Successfully downloaded and read the Excel file from S3.")
        logger.info(f"Excel columns: {df.columns.tolist()}")
    except Exception as e:
        logger.error(f"Failed to download or read Excel file from S3: {e}")
        raise

    # Define the expected columns based on cleaned data
    expected_columns = [
        "Name English",
        "Name Arabic",
        "Broker Number",
        "Office Name English",
        "Office Name Arabic",
        "Email",
        "Phone Number",
        "license_start_date",
        "license_end_date",
        "real_estate_number",
        "fax",
    ]

    # Check for missing columns
    missing_columns = [col for col in expected_columns if col not in df.columns]
    if missing_columns:
        logger.error(f"Missing required columns: {missing_columns}")
        raise KeyError(f"Missing required columns: {missing_columns}")
    else:
        logger.info("All required columns are present.")

    # Force 'Broker Number' to be a consistent string to avoid float formatting issues
    df["Broker Number"] = df["Broker Number"].apply(
        lambda x: (
            str(int(x))
            if pd.notnull(x) and isinstance(x, (int, float)) and float(x).is_integer()
            else (
                str(x).rstrip(".0")
                if pd.notnull(x) and str(x).endswith(".0")
                else str(x)
            )
        )
    )
    logger.info("Formatted 'Broker Number' column to string.")

    # Remove duplicates based on 'Broker Number'
    initial_count = df.shape[0]
    df.drop_duplicates(subset=["Broker Number"], inplace=True)
    duplicates_removed = initial_count - df.shape[0]
    logger.info(
        f"Removed {duplicates_removed} duplicate records based on 'Broker Number'."
    )

    # Clean phone numbers
    def format_phone_number(phone):
        if pd.isnull(phone):
            return None

        phone = re.sub(r"[^\d+]", "", str(phone))
        if phone.startswith("971|"):
            phone = phone.replace("971|", "+971")
        elif phone.startswith("971") and not phone.startswith("+"):
            phone = "+" + phone
        elif phone.startswith("0"):
            phone = phone.replace("0", "+971", 1)
        elif not phone.startswith("+"):
            phone = "+" + phone
        phone = re.sub(r"[^\d+]", "", phone)
        if len(phone) < 8 or not phone.startswith("+971"):
            logger.warning(f"Invalid phone number format: {phone}")
            return None

        try:
            parsed_phone_number = phonenumbers.parse(phone, None)
        except NumberParseException:
            logger.warning(f"Invalid phone number: {phone}")
            return None
        except Exception as error:
            logger.error(f"Unexpected error: {error}")
            return None
        if not phonenumbers.is_possible_number(parsed_phone_number):
            return None

        return phone

    df["Phone Number"] = df["Phone Number"].astype(str).apply(format_phone_number)
    logger.info("Cleaned phone numbers.")

    # Convert dates to timezone-aware datetime objects
    date_columns = ["license_start_date", "license_end_date"]
    for col in date_columns:
        if col in df.columns:
            df[col] = df[col].apply(convert_to_aware_datetime)
            logger.info(f"Converted '{col}' to timezone-aware datetime objects.")

    # Ensure 'fax' column is present and clean
    if "fax" not in df.columns:
        df["fax"] = df["Email"]
        logger.info("Mapped 'fax' to 'Email' as 'fax' column was not present.")
    else:
        logger.info("'fax' column exists and has been retained.")

    # Handle 'real_estate_number' - ensure it's integer or None
    if "real_estate_number" in df.columns:
        df["real_estate_number"] = pd.to_numeric(
            df["real_estate_number"], errors="coerce"
        ).astype("Int64")
        logger.info("Converted 'real_estate_number' to numeric format.")

    # Initialize 'Issues' column (optional for further validation)
    df["Issues"] = ""

    # Flag missing essential fields
    essential_fields = [
        "Name English",
        "Name Arabic",
        "Broker Number",
        "Office Name English",
        "Office Name Arabic",
        "Email",
        "Phone Number",
    ]
    for field in essential_fields:
        df["Issues"] += (
            df[field]
            .isna()
            .apply(lambda x: f"Missing {field}; " if x else "")
            .astype(str)
        )

    # Flag invalid phone numbers
    df["Issues"] += df["Phone Number"].apply(
        lambda x: "Invalid Phone Number; " if x is None else ""
    )

    # Replace empty 'Issues' with NaN
    df["Issues"] = df["Issues"].replace("", pd.NA)

    logger.info(f"Flagged {df['Issues'].notna().sum()} records with issues.")

    return df


def replace_broker_data(apps, schema_editor):
    """
    Replaces broker data by importing from the provided cleaned Excel file on S3.
    """
    try:
        Agent = apps.get_model("user", "Agent")
        Agency = apps.get_model("user", "Agency")

        s3_url = "https://rezio-static-files.s3.me-central-1.amazonaws.com/agent_data/updated_broker_data_final_sheet.xlsx"

        df = clean_broker_data(s3_url)

        total_records = df.shape[0]
        processed_records = 0

        with transaction.atomic():
            for index, row in df.iterrows():
                if pd.notna(row["Issues"]):
                    logger.warning(
                        f"Skipping row {index} due to issues: {row['Issues']}"
                    )
                    continue

                if not row["Phone Number"]:
                    logger.warning(
                        f"Skipping row {index} due to missing or invalid phone number."
                    )
                    continue

                office_name_en = row["Office Name English"]
                office_name_ar = row["Office Name Arabic"]

                if pd.isna(office_name_en) or pd.isna(office_name_ar):
                    logger.warning(f"Skipping row {index} due to missing office names.")
                    continue

                agency, created = Agency.objects.get_or_create(name=office_name_en)
                if created:
                    logger.info(f"Created new Agency: {agency.name}")
                else:
                    logger.info(f"Found existing Agency: {agency.name}")

                try:
                    agent = Agent.objects.get(
                        brn=row["Broker Number"],
                        license_country_code="AE",
                    )
                    logger.info(
                        f"Found existing Agent: {agent.name} with BRN: {agent.brn}"
                    )
                except Agent.DoesNotExist:
                    agent = None

                agent_defaults = {
                    "name": row["Name English"],
                    "agency": agency,
                    "phone": row["Phone Number"],
                    "email": row["Email"] if pd.notnull(row["Email"]) else "",
                    "license_start_date": (
                        None
                        if pd.isna(row["license_start_date"])
                        else row["license_start_date"]
                    ),
                    "license_end_date": (
                        None
                        if pd.isna(row["license_end_date"])
                        else row["license_end_date"]
                    ),
                    "real_estate_number": (
                        row["real_estate_number"]
                        if pd.notnull(row["real_estate_number"])
                        else None
                    ),
                }

                if agent and (not agent.fax):
                    agent_defaults["fax"] = row["fax"]
                    logger.info(f"Updating 'fax' for Agent BRN: {agent.brn}")
                elif not agent:
                    agent_defaults["fax"] = row["fax"]

                agent, created = Agent.objects.update_or_create(
                    brn=row["Broker Number"],
                    license_country_code="AE",
                    defaults=agent_defaults,
                )
                if created:
                    logger.info(
                        f"Created new Agent: {agent.name} with BRN: {agent.brn}"
                    )
                else:
                    logger.info(f"Updated Agent: {agent.name} with BRN: {agent.brn}")

                processed_records += 1
                if processed_records % 100 == 0:
                    logger.info(
                        f"Processed {processed_records}/{total_records} records."
                    )

        logger.info(
            f"Broker data replacement migration completed successfully. Processed {processed_records} records."
        )

    except Exception as error:
        import traceback

        traceback.print_exc()
        logger.error(f"Error during broker data replacement migration: {error}")


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0052_add_agent_unlocked_properties"),
    ]

    operations = [
        migrations.RunPython(replace_broker_data),
    ]
