# Generated by Django 4.1.7 on 2025-02-07 03:57

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("properties", "0057_alter_property_tenancy_type"),
        ("user", "0048_alter_agent_email_alter_agent_phone"),
    ]

    operations = [
        migrations.AddField(
            model_name="agentprofile",
            name="unlocked_properties",
            field=models.ManyToManyField(
                blank=True, related_name="unlocked_by_agents", to="properties.property"
            ),
        ),
    ]
