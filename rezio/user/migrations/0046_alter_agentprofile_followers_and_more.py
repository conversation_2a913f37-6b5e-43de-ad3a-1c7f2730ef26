# Generated by Django 4.1.7 on 2025-01-07 08:28

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0045_investorprofile_profile_public_view"),
    ]

    operations = [
        migrations.AlterField(
            model_name="agentprofile",
            name="followers",
            field=models.ManyToManyField(
                blank=True, related_name="agent_followers", to="user.follow"
            ),
        ),
        migrations.AlterField(
            model_name="agentprofile",
            name="following",
            field=models.ManyToManyField(
                blank=True, related_name="agent_following", to="user.follow"
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="investorprofile",
            name="followers",
            field=models.ManyToManyField(
                blank=True, related_name="investor_followers", to="user.follow"
            ),
        ),
        migrations.AlterField(
            model_name="investorprofile",
            name="following",
            field=models.ManyToManyField(
                blank=True, related_name="investor_following", to="user.follow"
            ),
        ),
        migrations.AddConstraint(
            model_name="follow",
            constraint=models.UniqueConstraint(
                condition=models.Q(("unfollowed_at__isnull", True)),
                fields=("from_user", "from_user_role", "to_user", "to_user_role"),
                name="unique_active_follow",
            ),
        ),
    ]
