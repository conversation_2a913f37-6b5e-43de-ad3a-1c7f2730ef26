# Generated by Django 4.1.7 on 2024-11-15 09:33

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0031_alter_agentprofile_brn"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="agentprofile",
            name="verification_status",
        ),
        migrations.AddField(
            model_name="agency",
            name="country_code",
            field=models.CharField(blank=True, default="AE", max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="data_source",
            field=models.CharField(
                choices=[
                    ("LICENSE_DATA_BASE", "LICENSE_DATA_BASE"),
                    ("MANUALLY_ADDED", "MANUALLY_ADDED"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="agentprofile",
            name="license_verification_status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("0", "Add License"),
                    ("1", "License Verification Pending"),
                    ("2", "License Verification Failed"),
                    ("3", "License Verified"),
                ],
                default="0",
                max_length=50,
            ),
        ),
    ]
