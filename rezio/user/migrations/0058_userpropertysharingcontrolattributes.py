# Generated by Django 4.1.7 on 2025-04-12 11:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0057_agent_profile_photo_url'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPropertySharingControlAttributes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_ts', models.DateTimeField(auto_now_add=True)),
                ('updated_ts', models.DateTimeField(auto_now=True)),
                ('private_fields', models.JSONField(blank=True, null=True)),
                ('property_category', models.IntegerField(choices=[(0, 'Residential'), (1, 'Commercial')])),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by_role', to=settings.AUTH_USER_MODEL)),
                ('created_by_role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by', to='user.role')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated_by', to=settings.AUTH_USER_MODEL)),
                ('updated_by_role', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created_by_role', to='user.role')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
