# Generated by Django 4.1.7 on 2024-08-20 08:59
import logging
import math
import traceback
import pandas as pd
from datetime import datetime
from django.db import migrations, models, transaction
from django.utils import timezone
import phonenumber_field.modelfields
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.text_choices import Gender

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def load_broker_data(apps, schema_editor):
    try:
        Agent = apps.get_model("user", "Agent")
        Agency = apps.get_model("user", "Agency")

        df_male = pd.read_excel(
            "https://rezio-static-files.s3.me-central-1.amazonaws.com/male_brokers_data.xlsx"
        )
        df_female = pd.read_excel(
            "https://rezio-static-files.s3.me-central-1.amazonaws.com/female_brokers_data.xlsx"
        )

        for df in [df_male, df_female]:

            def format_phone_number(phone):
                if phone in [0, 1] or (isinstance(phone, float) and math.isnan(phone)):
                    return None

                if isinstance(phone, float):
                    phone = str(int(phone))

                if phone:
                    phone = str(phone)
                    if len(phone) < 8:
                        return None
                    else:
                        phone = "".join(filter(str.isdigit, phone))
                        if not phone.startswith("971"):
                            phone = "+971" + phone
                        else:
                            phone = "+" + phone
                return phone

            def map_gender(gender_type_id):
                if gender_type_id == 0:
                    return Gender.MALE
                elif gender_type_id == 1:
                    return Gender.FEMALE
                return None

            def convert_to_aware_datetime(naive_datetime_str):
                try:
                    naive_datetime = datetime.fromisoformat(naive_datetime_str)
                    return timezone.make_aware(
                        naive_datetime, timezone.get_default_timezone()
                    )
                except Exception as error:
                    logger.error(error)
                    return None

            df["Phone"] = df["PHONE"].apply(format_phone_number)
            df["Gender"] = df["GENDER_TYPE_ID"].apply(map_gender)

            df["LICENSE_START_DATE"] = df["LICENSE_START_DATE"].apply(
                lambda x: convert_to_aware_datetime(x) if pd.notnull(x) else None
            )
            df["LICENSE_END_DATE"] = df["LICENSE_END_DATE"].apply(
                lambda x: convert_to_aware_datetime(x) if pd.notnull(x) else None
            )

            for index, row in df.iterrows():
                with transaction.atomic():
                    if row["Phone"]:
                        agency, created = Agency.objects.get_or_create(
                            name=row["REAL_ESTATE_EN"]
                        )
                        Agent.objects.create(
                            name=row["BROKER_EN"],
                            agency=agency,
                            brn=row["BROKER_NUMBER"],
                            phone=str(row["Phone"]),
                            fax=row["FAX"],
                            gender=row["Gender"],
                            license_start_date=row["LICENSE_START_DATE"]
                            if pd.notnull(row["LICENSE_START_DATE"])
                            else None,
                            license_end_date=row["LICENSE_END_DATE"]
                            if pd.notnull(row["LICENSE_END_DATE"])
                            else None,
                            real_estate_number=row["REAL_ESTATE_NUMBER"]
                            if pd.notnull(row["REAL_ESTATE_NUMBER"])
                            else None,
                        )

        logger.info("Migration executed successfully to add default broker data in db")

    except Exception as error:
        traceback.print_exc()
        logger.error(error)


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0015_alter_agency_created_by_alter_agency_updated_by_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="agent",
            name="fax",
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AddField(
            model_name="agent",
            name="gender",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Male", "Male"),
                    ("Female", "Female"),
                    ("Prefer not to say", "Prefer not to say"),
                ],
                max_length=128,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="agent",
            name="license_end_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="agent",
            name="license_start_date",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="agent",
            name="real_estate_number",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.RemoveField(
            model_name="agent",
            name="email",
        ),
        migrations.AlterField(
            model_name="agent",
            name="phone",
            field=phonenumber_field.modelfields.PhoneNumberField(
                blank=True, max_length=128, null=True, region=None
            ),
        ),
        migrations.RunPython(load_broker_data),
    ]
