# Generated by Django 4.1.7 on 2025-02-10 08:00

import logging
from django.db import migrations
from django.conf import settings

from rezio.properties.text_choices import UserRequestActions, PropertyPublishStatus
from rezio.user.text_choices import AgentSubscriptionPlanChoices
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def agent_unlocked_properties(apps, schema_editor):
    """
    Retrieve and update unlocked properties for agent profiles
    """
    AgentProfile = apps.get_model("user", "AgentProfile")
    AgentAssociatedProperty = apps.get_model("properties", "AgentAssociatedProperty")

    agent_profiles = AgentProfile.objects.all()
    for agent_profile in agent_profiles:
        logger.info(
            f"Calculating unlocked properties for: {agent_profile.name} - {agent_profile.user.primary_phone_number}"
        )
        # recalculate_unlocked_properties(agent_profile)
        # Base query with combined filters for associations and properties
        associations = (
            AgentAssociatedProperty.objects.filter(
                agent_profile=agent_profile,
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                is_request_expired=False,
                property__property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                property__is_archived=False,
            )
            .select_related("property")
            .order_by("created_ts")
        )

        # Determine allowed count based on subscription status
        allowed_count = None
        if agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC:
            allowed_count = settings.BASIC_PLAN_PROPERTIES_COUNT

        # Apply subscription limit if needed
        if allowed_count is not None:
            associations = associations[:allowed_count]

        # Extract properties directly from the filtered associations
        properties = [assoc.property for assoc in associations]

        # Update unlocked properties in a single operation
        agent_profile.unlocked_properties.set(properties)
        agent_profile.save()
        logger.info("everything completed .")


class Migration(migrations.Migration):
    dependencies = [
        ("user", "0053_broker_data_fix"),
    ]

    operations = [
        migrations.RunPython(agent_unlocked_properties),
    ]
