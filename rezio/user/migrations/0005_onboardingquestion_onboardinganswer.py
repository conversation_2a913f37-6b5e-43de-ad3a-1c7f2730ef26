# Generated by Django 4.1.7 on 2024-07-11 13:30

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
from rezio.user.questions.generate_questions import create_questions_for_roles


def create_question_migration(apps, schema_editor):
    Role = apps.get_model('user', 'Role')
    InvestorType = apps.get_model('user', 'InvestorType')
    OnboardingQuestion = apps.get_model('user', 'OnboardingQuestion')

    create_questions_for_roles(Role, InvestorType, OnboardingQuestion)

class Migration(migrations.Migration):

    dependencies = [
        ('user', '0004_remove_agentprofile_agency_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OnboardingQuestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('question_text', models.TextField()),
                ('option_type', models.CharField(choices=[('radio', 'Radio'), ('checkbox', 'Checkbox'), ('slider', 'Slider'), ('dropdown', 'Dropdown'), ('search_bar', 'Search Bar'), ('search_location', 'Search Location'), ('currency_text_field', 'Currency Text Field'), ('textarea', 'Textarea')], max_length=128)),
                ('options', models.JSONField()),
                ('is_mandatory', models.BooleanField(default=False)),
                ('is_main_preference', models.BooleanField(null=True, default=False)),
                ('display_title', models.CharField(max_length=128)),
                ('preference_order', models.IntegerField(null=True, default=0)),
                ('preference_icon_url', models.CharField(null=True, blank=True, max_length=512)),
                ('illustration_url', models.CharField(null=True, blank=True, max_length=512)),
                ('is_active', models.BooleanField(default=True)),
                ('investor_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='user.investortype')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.role')),
            ],
        ),
        migrations.CreateModel(
            name='OnboardingAnswer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('answer', models.JSONField()),
                ('onboarding_question', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.onboardingquestion')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.RunPython(create_question_migration)
    ]
