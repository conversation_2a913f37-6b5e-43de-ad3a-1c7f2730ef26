from django.urls import path
from rezio.user.views.web_view import agent, investor

urlpatterns = [
    path(
        "view_agent_profile/<str:user_id>/",
        agent.WebViewAgentProfileViewSet.as_view(),
        name="web-view-agent-profile",
    ),
    path(
        "agent_portfolio/<str:user_id>/",
        agent.WebAgentPropertyListAPIView.as_view(),
        name="web-agent-portfolio",
    ),
    path(
        "view_investor_profile/<str:user_id>/",
        investor.WebViewInvestorProfileViewSet.as_view(),
        name="web-view-investor-profile",
    ),
    path(
        "investor_portfolio/<str:user_id>/",
        investor.WebInvestorPortfolioViewSet.as_view(),
        name="web-investor-portfolio",
    ),
    path(
        "investor_preferences/<str:user_id>/",
        investor.WebViewInvestorPreferencesViewSet.as_view(),
        name="web-investor-preferences",
    ),
]
