import mimetypes
from datetime import datetime
from functools import partial

from phonenumber_field.phonenumber import PhoneNumber
from phonenumber_field.serializerfields import PhoneNumber<PERSON>ield
from rest_framework import serializers

from rezio.properties.helper import is_valid_image_or_video
from rezio.properties.text_choices import PropertyCategory
from rezio.properties.utils import get_s3_object
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    PRESIGNED_POST_STRUCTURES,
    KEY,
    DD_MMM_YYYY,
    NAME,
    SIZE,
)
from rezio.user.constants import (
    SLIDER_MINIMUM_VALUE,
    SLIDER_MAXIMUM_VALUE,
    SLIDER_DEFAULT_VALUE,
    CURRENCY_MINIMUM_VALUE,
    CURRENCY_MAXIMUM_VALUE,
    AGENT,
    INVESTOR,
)
from rezio.user.helper import (
    check_image_validity,
    check_file_validity,
    get_onboarding_questions_with_answer,
)
from rezio.user.models import (
    <PERSON>,
    User,
    OnboardingQuestion,
    OnboardingAnswer,
    TermsAndConditions,
    Follow,
    MenuSettingsPrivacyPolicy,
)
from rezio.user.text_choices import OptionTypes
from rezio.user.utils import (
    contains_special_characters,
    url_validator,
    emirates_id_validator,
    passport_validator,
)
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
)
from rezio.utils.error_utils import (
    get_required_field_serializer_error_messages,
)
from rezio.utils.validation_utils import (
    validate_phone_number,
)


class UserLoginSerializer(serializers.Serializer):
    """
    Serializer for user login/signup
    """

    phone_number = PhoneNumberField(
        error_messages={
            **get_required_field_serializer_error_messages("Phone number"),
            "invalid": "Given phone number is invalid",
        }
    )

    role_name = serializers.CharField(
        error_messages=get_required_field_serializer_error_messages("Role name"),
    )

    def validate_role_name(self, role_name):
        if not Role.objects.filter(name=role_name).exists():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"Given user role {role_name} is invalid",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Given user role {role_name} is invalid"
                    },
                }
            )

        return role_name

    def validate_phone_number(self, phone_number):
        phone_number, is_error, error_message = validate_phone_number(phone_number)
        if is_error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: error_message,
                }
            )

        return phone_number

    class Meta:
        fields = ["phone_number", "role_name"]

        extra_kwargs = {
            "phone_number": {"required": True},
            "role_name": {"required": True},
        }


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User

        fields = ["id", "primary_phone_number"]


class OnboardingQuestionSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()
    is_answered = serializers.SerializerMethodField()
    is_skipped = serializers.SerializerMethodField()

    class Meta:
        model = OnboardingQuestion
        fields = [
            "id",
            "question_text",
            "option_type",
            "is_mandatory",
            "is_answered",
            "preference_icon_url",
            "illustration_url",
            "is_skipped",
            "is_main_preference",
            "display_title",
            "options",
            "preference_category",
        ]

    def get_is_answered(self, instance):
        user = self.context.get("user", None)
        if user:
            return OnboardingAnswer.objects.filter(
                onboarding_question=instance, user=user, is_skipped=False
            ).exists()
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"Request not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"Request not found"},
                }
            )

    def get_is_skipped(self, instance):
        user = self.context.get("user", None)
        if user:
            onboarding_answer = OnboardingAnswer.objects.filter(
                onboarding_question=instance, user=user
            )
            if onboarding_answer.exists():
                return onboarding_answer.first().is_skipped
            else:
                return False
        else:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"Request not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"Request not found"},
                }
            )

    def get_options(self, instance):
        user = self.context.get("user", None)
        options = get_onboarding_questions_with_answer(user, instance)
        return options


class CommonAnswerSerializer(serializers.Serializer):
    answer_options = serializers.JSONField(required=True)

    class Meta:
        fields = ["answer_options"]


class CommonAnswerJsonSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    user_selected = serializers.BooleanField(required=True)
    default_selected = serializers.BooleanField(allow_null=True)
    icon_url = serializers.CharField(allow_null=True)
    option_text = serializers.CharField(required=True)

    class Meta:
        fields = ["id", "user_selected", "default_selected", "icon_url", "option_text"]

    def validate(self, data):
        if not data.get("user_selected"):
            raise serializers.ValidationError("Option must be user selected")

        return data


class LocationAnswerSerializer(serializers.Serializer):
    selected_locations = serializers.JSONField(required=True)

    class Meta:
        fields = ["selected_locations"]


class LocationAnswerJsonSerializer(serializers.Serializer):
    location_name = serializers.CharField(required=True)

    class Meta:
        fields = ["location_name"]


class SliderAnswerJsonSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    max_value = serializers.IntegerField(required=True)
    min_value = serializers.IntegerField(required=True)
    answered_value = serializers.IntegerField(required=True)
    default_selected = serializers.IntegerField(required=True)

    class Meta:
        fields = ["id", "max_value", "min_value", "answered_value", "default_selected"]

    def validate(self, data):
        if data.get("min_value") != SLIDER_MINIMUM_VALUE:
            raise serializers.ValidationError("Incorrect minimum value")
        if data.get("max_value") != SLIDER_MAXIMUM_VALUE:
            raise serializers.ValidationError("Incorrect maximum value")
        if data.get("default_selected") != SLIDER_DEFAULT_VALUE:
            raise serializers.ValidationError("Incorrect default value")
        if data.get("answered_value") not in range(
            SLIDER_MINIMUM_VALUE, SLIDER_MAXIMUM_VALUE + 1
        ):
            raise serializers.ValidationError("Incorrect answer value")

        return data


class CurrencyAnswerSerializer(serializers.Serializer):
    options_data = serializers.JSONField(required=True)

    class Meta:
        fields = ["options_data"]


class CurrencyAnswerJsonSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    default = serializers.IntegerField(required=True)
    hint_text = serializers.CharField(required=True)
    label_text = serializers.CharField(required=True)
    answer_text = serializers.IntegerField(required=True, allow_null=True)

    class Meta:
        fields = ["id", "default", "hint_text", "label_text", "answer_text"]


class OnboardingAnswerSerializer(serializers.ModelSerializer):
    onboarding_question_id = serializers.PrimaryKeyRelatedField(
        queryset=OnboardingQuestion.objects.all(),
        source="onboarding_question",
    )
    answer = serializers.JSONField(allow_null=True)

    class Meta:
        model = OnboardingAnswer
        fields = ["id", "onboarding_question_id", "is_skipped", "answer", "user_id"]

        extra_kwargs = {
            "answer": {"required": True},
            "is_skipped": {"required": True},
        }

    def validate(self, data):
        onboarding_question = self.context.get("onboarding_question")
        answer = data.get("answer")
        if onboarding_question.is_mandatory and not answer:
            message = "Answer to this question is mandatory"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if answer and data.get("is_skipped"):
            message = "Cannot skip a question that has answer data"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if answer:
            option_type_validations = {
                OptionTypes.RADIO: dict,
                OptionTypes.CHECKBOX: list,
                OptionTypes.SLIDER: dict,
                OptionTypes.DROPDOWN: dict,
                OptionTypes.SEARCH_LOCATION: list,
                OptionTypes.CURRENCY_FIELD: list,
            }

            option_type = onboarding_question.option_type
            expected_type = option_type_validations.get(option_type)
            message = None

            if option_type in [
                OptionTypes.RADIO,
                OptionTypes.CHECKBOX,
                OptionTypes.DROPDOWN,
                OptionTypes.SLIDER,
            ]:
                answer_options = CommonAnswerSerializer(data=answer)
                if not answer_options.is_valid():
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid answer data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: answer_options.errors,
                        }
                    )
                answer_options = answer_options.data
                answer_options = answer_options.get("answer_options")
                if isinstance(answer_options, expected_type):
                    if option_type in [
                        OptionTypes.RADIO,
                        OptionTypes.CHECKBOX,
                        OptionTypes.DROPDOWN,
                    ]:
                        many = False
                        if option_type == OptionTypes.CHECKBOX:
                            many = True
                        answer_options_json = CommonAnswerJsonSerializer(
                            data=answer_options, many=many
                        )
                    elif option_type == OptionTypes.SLIDER:
                        answer_options_json = SliderAnswerJsonSerializer(
                            data=answer_options
                        )
                    else:
                        raise serializers.ValidationError("Invalid answer data")

                    if not answer_options_json.is_valid():
                        raise InvalidSerializerDataException(
                            {
                                KEY_MESSAGE: "Invalid answer data sent",
                                KEY_PAYLOAD: {},
                                KEY_ERROR: answer_options_json.errors,
                            }
                        )
                else:
                    message = "Invalid answer data"

            elif option_type == OptionTypes.SEARCH_LOCATION:
                selected_locations = LocationAnswerSerializer(data=answer)
                if not selected_locations.is_valid():
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid answer data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: selected_locations.errors,
                        }
                    )
                selected_locations = selected_locations.data
                selected_locations = selected_locations.get("selected_locations")
                if isinstance(selected_locations, expected_type):
                    selected_locations_json = LocationAnswerJsonSerializer(
                        data=selected_locations, many=True
                    )
                    if not selected_locations_json.is_valid():
                        raise InvalidSerializerDataException(
                            {
                                KEY_MESSAGE: "Invalid answer data sent",
                                KEY_PAYLOAD: {},
                                KEY_ERROR: selected_locations_json.errors,
                            }
                        )
                else:
                    message = "Invalid answer data"

            elif option_type == OptionTypes.CURRENCY_FIELD:
                options_data = CurrencyAnswerSerializer(data=answer)
                if not options_data.is_valid():
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "Invalid answer data sent",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: options_data.errors,
                        }
                    )
                options_data = options_data.data
                options_data = options_data.get("options_data")
                if isinstance(options_data, expected_type):
                    options_data_json = CurrencyAnswerJsonSerializer(
                        data=options_data, many=True
                    )
                    if not options_data_json.is_valid():
                        raise InvalidSerializerDataException(
                            {
                                KEY_MESSAGE: "Invalid answer data sent",
                                KEY_PAYLOAD: {},
                                KEY_ERROR: options_data_json.errors,
                            }
                        )
                    answered_minimum_value = None
                    answered_maximum_value = None

                    # validation for answer for currency field
                    for currency_data in options_data_json.data:
                        if currency_data.get("id") == 1:
                            minimum_required_value = currency_data.get("default")
                            answered_minimum_value = currency_data.get("answer_text")
                            if minimum_required_value != CURRENCY_MINIMUM_VALUE:
                                raise serializers.ValidationError(
                                    "Incorrect minimum default value"
                                )

                        if currency_data.get("id") == 2:
                            maximum_required_value = currency_data.get("default")
                            answered_maximum_value = currency_data.get("answer_text")
                            if maximum_required_value != CURRENCY_MAXIMUM_VALUE:
                                raise serializers.ValidationError(
                                    "Incorrect maximum  default value"
                                )

                    if answered_minimum_value and answered_maximum_value:
                        if answered_minimum_value > answered_maximum_value:
                            raise serializers.ValidationError(
                                "Please enter valid prices"
                            )
                        elif answered_minimum_value == answered_maximum_value:
                            raise serializers.ValidationError(
                                "Please enter valid prices"
                            )
                    elif (
                        answered_minimum_value is None
                        and answered_maximum_value is None
                    ):
                        pass
                    else:
                        raise serializers.ValidationError("Please enter valid prices")
                else:
                    message = "Invalid answer data"

            else:
                message = "Invalid answer data"

            if message:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

        return data


class TermsAndConditionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TermsAndConditions

        fields = ["id", "terms_url", "version"]


class PrivacyPolicySerializer(serializers.ModelSerializer):
    class Meta:
        model = MenuSettingsPrivacyPolicy
        fields = ["id", "privacy_url", "version"]


class CombinedTermsPolicySerializer(serializers.Serializer):
    terms_and_condition = serializers.SerializerMethodField(read_only=True)
    privacy_policy = serializers.SerializerMethodField(read_only=True)

    def get_terms_and_condition(self, instance):
        terms_and_condition = self.context.get("terms_and_conditions")
        serializer = TermsAndConditionsSerializer(terms_and_condition)
        return serializer.data

    def get_privacy_policy(self, instance):
        privacy_policy = self.context.get("privacy_policy")
        serializer = PrivacyPolicySerializer(privacy_policy)
        return serializer.data


class UserProfileImageUploadSerializer(serializers.Serializer):
    role = serializers.CharField(required=True)
    profile_photo = serializers.FileField(required=False)
    cover_photo = serializers.FileField(required=False)

    class Meta:
        fields = ["role", "profile_photo", "cover_photo"]

    def validate(self, data):
        # Ensure only one file field is provided
        provided_files = [field for field in data if field != "role"]

        if len(provided_files) == 0:
            raise serializers.ValidationError(
                "No file provided. Please upload one file."
            )
        elif len(provided_files) > 1:
            raise serializers.ValidationError(
                "Only one file can be uploaded at a time."
            )

        return data

    def validate_profile_photo(self, profile_photo):
        return check_image_validity(profile_photo)

    def validate_cover_photo(self, cover_photo):
        return check_image_validity(cover_photo)

    def update(self, instance, validated_data):
        # Get the field that was provided
        role = validated_data.pop("role")
        file_field = list(validated_data.keys())[0]
        file = validated_data[file_field]
        uploaded_document_key = PRESIGNED_POST_STRUCTURES.get(
            file_field.upper(), {}
        ).get(KEY, "")
        uploaded_document_key = uploaded_document_key.format(
            user_id=instance.user.id,
            role_name=role,
            filename=f"{instance.user.id}_{datetime.now().strftime(DD_MMM_YYYY)}_{role}_{file_field}",
        )
        s3_client = S3Client()
        s3_client.upload_file(file, uploaded_document_key)

        file_key_field = f"{file_field}_{KEY}".lower()
        file_name_field = f"{file_field}_{NAME}"
        file_size_field = f"{file_field}_{SIZE}"
        # Update the model instance with the new file
        setattr(instance, file_key_field, uploaded_document_key)
        setattr(instance, file_name_field, file.name)
        setattr(instance, file_size_field, file.size)
        instance.save()

        return instance


class UserProfileImageDeleteSerializer(serializers.Serializer):
    """
    Remove profile/cover image serializer
    """

    role = serializers.CharField(required=True)
    profile_photo = serializers.BooleanField(required=False)
    cover_photo = serializers.BooleanField(required=False)

    class Meta:
        fields = ["role", "profile_photo", "cover_photo"]

    def validate(self, data):
        profile_photo = data.get("profile_photo", None)
        cover_photo = data.get("cover_photo", None)

        # Ensure at least one photo field is provided
        if not profile_photo and not cover_photo:
            raise serializers.ValidationError("Photo type is not provided")
        # Ensure only one file field is provided
        elif profile_photo and cover_photo:
            raise serializers.ValidationError("Only one photo can be deleted at a time")
        elif profile_photo:
            return "profile_photo"
        else:
            return "cover_photo"


class UserProfileDocumentsUploadSerializer(serializers.ModelSerializer):
    emirates_id_image = serializers.FileField(
        required=False,
        allow_empty_file=True,
        validators=[partial(check_image_validity, allow_pdf=True)],
    )
    passport_image = serializers.FileField(
        required=False,
        allow_empty_file=True,
        validators=[partial(check_image_validity, allow_pdf=True)],
    )

    class Meta:
        model = User
        fields = ["emirates_id_image", "passport_image"]

    def validate(self, data):
        # Ensure only one file field is provided
        provided_files = [field for field in data]

        if len(provided_files) == 0:
            raise serializers.ValidationError(
                "No file provided. Please upload one file."
            )
        elif len(provided_files) > 1:
            raise serializers.ValidationError(
                "Only one file can be uploaded at a time."
            )

        return data

    def update(self, instance, validated_data):
        # Get the field that was provided
        file_field = list(validated_data.keys())[0]
        file = validated_data[file_field]

        uploaded_document_key = PRESIGNED_POST_STRUCTURES.get(
            file_field.upper(), {}
        ).get(KEY, "")
        uploaded_document_key = uploaded_document_key.format(
            user_id=instance.id,
            filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_{file_field}",
        )
        s3_client = S3Client()
        s3_client.upload_file(file, uploaded_document_key)

        file_key_field = f"{file_field}_{KEY}".lower()
        file_name_field = f"{file_field}_{NAME}"
        file_size_field = f"{file_field}_{SIZE}"
        # Update the model instance with the new file
        setattr(instance, file_key_field, uploaded_document_key)
        setattr(instance, file_name_field, file.name)
        setattr(instance, file_size_field, file.size)
        instance.save()

        return instance


class UserProfileDocumentsDeleteSerializer(serializers.Serializer):
    emirates_id_image = serializers.BooleanField(required=False)
    passport_image = serializers.BooleanField(required=False)

    class Meta:
        fields = ["emirates_id_image", "passport_image"]

    def validate(self, data):
        # Ensure only one file field is provided
        provided_fields = [field for field in data]

        if len(provided_fields) == 0:
            raise serializers.ValidationError("Document type not provided")
        elif len(provided_fields) > 1:
            raise serializers.ValidationError("Only one file can be deleted at a time.")

        return data


class UserDetailSerializer(serializers.ModelSerializer):
    emirates_id_image = serializers.SerializerMethodField(read_only=True)
    passport_image = serializers.SerializerMethodField(read_only=True)
    emirates_id_image_size = serializers.IntegerField(read_only=True)
    passport_image_size = serializers.IntegerField(read_only=True)
    primary_phone_number = serializers.CharField(required=True)
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    secondary_phone_number = serializers.CharField(required=False, allow_null=True)
    secondary_phone_code = serializers.SerializerMethodField(read_only=True)
    secondary_number = serializers.SerializerMethodField(read_only=True)
    emirates_id = serializers.CharField(
        required=False, allow_null=True, validators=[emirates_id_validator]
    )
    passport_number = serializers.CharField(
        required=False, allow_null=True, validators=[passport_validator]
    )

    class Meta:
        model = User
        fields = [
            "id",
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "secondary_phone_number",
            "secondary_phone_code",
            "secondary_number",
            "emirates_id",
            "emirates_id_image",
            "emirates_id_image_name",
            "emirates_id_image_size",
            "passport_number",
            "passport_image",
            "passport_image_name",
            "passport_image_size",
        ]
        read_only_fields = ["id"]
        extra_kwargs = {
            "secondary_phone_number": {"allow_null": True, "allow_blank": True},
            "emirates_id": {
                "required": True,
                "allow_null": False,
                "allow_blank": False,
            },
            "passport_number": {
                "required": True,
                "allow_null": False,
                "allow_blank": False,
            },
            "primary_phone_number": {
                "required": True,
                "allow_null": False,
                "allow_blank": False,
            },
        }

    def validate_primary_phone_number(self, phone_number):
        phone_number = PhoneNumber.from_string(phone_number)
        if not phone_number.is_valid():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"{phone_number} is invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                }
            )

        return phone_number

    def validate_secondary_phone_number(self, phone_number):
        if phone_number:
            phone_number = PhoneNumber.from_string(phone_number)
            if not phone_number.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: f"{phone_number} is invalid",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                    }
                )

        return phone_number

    def validate_name(self, name):
        if not contains_special_characters(name):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def get_emirates_id_image(self, instance):
        if instance.emirates_id_image_key:
            return get_s3_object(instance.emirates_id_image_key)
        return None

    def get_passport_image(self, instance):
        if instance.passport_image_key:
            return get_s3_object(instance.passport_image_key)
        return None

    def get_primary_phone_code(self, instance):
        print(instance.primary_phone_number.__dict__)
        return f"+{instance.primary_phone_number.country_code}"

    def get_primary_number(self, instance):
        return str(instance.primary_phone_number.national_number)

    def get_secondary_phone_code(self, instance):
        return (
            f"+{instance.secondary_phone_number.country_code}"
            if instance.secondary_phone_number
            else None
        )

    def get_secondary_number(self, instance):
        print()
        return (
            str(instance.secondary_phone_number.national_number)
            if instance.secondary_phone_number
            else None
        )


class FollowingSerializer(serializers.ModelSerializer):
    role = serializers.CharField(source="to_user_role.name")
    profile_photo = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    brn = serializers.SerializerMethodField()
    agency = serializers.SerializerMethodField()
    enable_unfollow = serializers.SerializerMethodField()
    license_verification_status = serializers.SerializerMethodField()
    working_type = serializers.SerializerMethodField()

    class Meta:
        model = Follow
        fields = [
            "id",
            "to_user",
            "name",
            "role",
            "profile_photo",
            "brn",
            "agency",
            "enable_unfollow",
            "license_verification_status",
            "working_type",
        ]

    def get_name(self, instance):
        if instance.to_user_role.name == AGENT:
            return instance.to_user.agentprofile.name
        elif instance.to_user_role.name == INVESTOR:
            return instance.to_user.investorprofile.name

        return None

    def get_profile_photo(self, instance):
        if instance.to_user_role.name == AGENT:
            if instance.to_user.agentprofile.profile_photo_key:
                return get_s3_object(instance.to_user.agentprofile.profile_photo_key)
        elif instance.to_user_role.name == INVESTOR:
            if instance.to_user.investorprofile.profile_photo_key:
                return get_s3_object(instance.to_user.investorprofile.profile_photo_key)

        return None

    def get_brn(self, instance):
        if instance.to_user_role.name == AGENT:
            return instance.to_user.agentprofile.brn

        return None

    def get_agency(self, instance):
        if instance.to_user_role.name == AGENT and instance.to_user.agentprofile.agency:
            return instance.to_user.agentprofile.agency.name

        return None

    def get_enable_unfollow(self, instance):
        if instance.from_user == self.context.get("user"):
            return True

        return False

    def get_license_verification_status(self, instance):
        if instance.to_user_role.name == AGENT:
            return instance.to_user.agentprofile.license_verification_status
        return None

    def get_working_type(self, instance):
        if instance.to_user_role.name == AGENT:
            return instance.to_user.agentprofile.working_type


class FollowerSerializer(serializers.ModelSerializer):
    role = serializers.CharField(source="from_user_role.name")
    profile_photo = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    brn = serializers.SerializerMethodField()
    agency = serializers.SerializerMethodField()
    enable_remove = serializers.SerializerMethodField()
    license_verification_status = serializers.SerializerMethodField()
    working_type = serializers.SerializerMethodField()

    class Meta:
        model = Follow
        fields = [
            "id",
            "from_user",
            "name",
            "role",
            "profile_photo",
            "brn",
            "agency",
            "enable_remove",
            "license_verification_status",
            "working_type",
        ]

    def get_name(self, instance):
        if instance.from_user_role.name == AGENT:
            return instance.from_user.agentprofile.name
        elif instance.from_user_role.name == INVESTOR:
            return instance.from_user.investorprofile.name

        return None

    def get_profile_photo(self, instance):
        if instance.from_user_role.name == AGENT:
            if instance.from_user.agentprofile.profile_photo_key:
                return get_s3_object(instance.from_user.agentprofile.profile_photo_key)
        elif instance.from_user_role.name == INVESTOR:
            if instance.from_user.investorprofile.profile_photo_key:
                return get_s3_object(
                    instance.from_user.investorprofile.profile_photo_key
                )

        return None

    def get_brn(self, instance):
        if instance.from_user_role.name == AGENT:
            return instance.from_user.agentprofile.brn

        return None

    def get_agency(self, instance):
        if (
            instance.from_user_role.name == AGENT
            and instance.from_user.agentprofile.agency
        ):
            return instance.from_user.agentprofile.agency.name

        return None

    def get_enable_remove(self, instance):
        if instance.to_user == self.context.get("user"):
            return True

        return False

    def get_license_verification_status(self, instance):
        if instance.from_user_role.name == AGENT:
            return instance.from_user.agentprofile.license_verification_status
        return None

    def get_working_type(self, instance):
        if instance.from_user_role.name == AGENT:
            return instance.from_user.agentprofile.working_type


class OtherSocialLinksSerializer(serializers.Serializer):
    name = serializers.CharField(required=True)
    link = serializers.CharField(required=True, validators=[url_validator])


class SocialLinksSerializer(serializers.Serializer):
    user_id = serializers.CharField(read_only=True)
    twitter_link = serializers.CharField(
        allow_null=True, allow_blank=True, validators=[url_validator]
    )
    instagram_link = serializers.CharField(
        allow_null=True, allow_blank=True, validators=[url_validator]
    )
    linkedin_link = serializers.CharField(
        allow_null=True, allow_blank=True, validators=[url_validator]
    )
    facebook_link = serializers.CharField(
        allow_null=True, allow_blank=True, validators=[url_validator]
    )
    other_links = serializers.ListField(
        child=OtherSocialLinksSerializer(), allow_null=True, allow_empty=True
    )

    def validate_other_links(self, other_links):
        seen_names = {"facebook", "twitter", "instagram", "linkedin"}
        for link_data in other_links:
            link_name = link_data.get(
                "name"
            ).lower()  # Extract the 'name' key from the dict
            if link_name in seen_names:
                raise serializers.ValidationError(
                    "Cannot allow duplicate link names"
                )  # Duplicate found
            seen_names.add(link_name)
        return other_links

    def update(self, instance, validated_data):
        # Update profile fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class AgentLicenseUpdateSerializer(serializers.Serializer):
    """
    Update agent license serializer
    """

    license_file = serializers.FileField(required=True)

    def validate_license_file(self, license_file):
        mime_type, _ = mimetypes.guess_type(license_file.name)

        if mime_type == "application/pdf":
            check_file_validity(license_file)
        elif mime_type in ["image/png", "image/jpeg", "image/jpg", "image/heic"]:
            is_valid_image_or_video(license_file)
        else:
            serializers.ValidationError(f"File type '{mime_type}' is not supported")

        return license_file


class AgentInvitationSerializer(serializers.Serializer):
    """
    Agent invitation serializer
    """

    phone_number = PhoneNumberField(required=True, allow_null=False, allow_blank=False)
    property_id = serializers.IntegerField(required=False, allow_null=True)

    def validate_phone_number(self, phone_number):
        phone_number, is_error, error_message = validate_phone_number(phone_number)
        if is_error:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: error_message,
                }
            )

        return phone_number


class SetPropertySharingPrivateFieldsSerializer(serializers.Serializer):
    """
    Serializer to set property sharing private fields
    """

    private_fields = serializers.ListField(
        child=serializers.CharField(), required=False, allow_null=True, default=[]
    )
    property_category = serializers.ChoiceField(
        choices=PropertyCategory.choices, required=True
    )
    public_fields = serializers.ListField(
        child=serializers.CharField(), required=False, allow_null=True, default=[]
    )
