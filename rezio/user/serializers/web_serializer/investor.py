import logging

from rest_framework import serializers
from rest_framework.serializers import ModelSerializer

from rezio.properties.serializers import PropertySectionSerializer
from rezio.properties.text_choices import (
    UserRequestActions,
    PropertyAvailabilityStatus,
    PropertyCompletionStateChoices,
)
from rezio.properties.utils import get_s3_object
from rezio.user.constants import INVESTOR
from rezio.user.helper import (
    get_investor_budget,
    get_investor_is_active,
    get_country_name_from_profile,
    get_onboarding_questions_with_answer,
)
from rezio.user.models import (
    InvestorProfile,
    OnboardingAnswer,
    OnboardingQuestion,
    AgentProfile,
)
from rezio.properties.models import (
    Property,
    AgentAssociatedProperty,
    PropertyFinancialDetails,
    PropertyUnitSections,
    PropertyVerifiedDataFields,
    PropertyCompletionState,
    PropertyFeatures,
)
from rezio.user.serializers import BasicAgentProfileInfoSerializer
from rezio.user.serializers.web_serializer.agent import WebOtherSocialLinksSerializer
from rezio.user.text_choices import OptionTypes
from rezio.utils.constants import (
    <PERSON><PERSON><PERSON>_ME<PERSON>AG<PERSON>,
    <PERSON><PERSON><PERSON>_PAYLOAD,
    <PERSON><PERSON><PERSON>_ERROR,
    K<PERSON>Y_ERROR_MESSAGE,
    <PERSON><PERSON>GO_LOGGER_NAME,
)
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class WebViewInvestorProfileSerializer(ModelSerializer):
    """
    Investor profile web view serializer
    """

    country = serializers.SerializerMethodField()
    is_active_investor = serializers.SerializerMethodField()
    budget = serializers.SerializerMethodField()
    role = serializers.CharField(default=INVESTOR)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)
    other_links = serializers.ListField(child=WebOtherSocialLinksSerializer())
    followers_count = serializers.SerializerMethodField(read_only=True)
    following_count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            "user_id",
            "name",
            "country",
            "is_active_investor",
            "budget",
            "role",
            "followers_count",
            "following_count",
            "twitter_link",
            "instagram_link",
            "facebook_link",
            "linkedin_link",
            "other_links",
            "profile_photo",
            "cover_photo",
        ]

    def get_country(self, instance):
        return get_country_name_from_profile(instance)

    def get_budget(self, instance):
        budget = get_investor_budget(self.context.get("user"), instance)
        return budget

    def get_is_active_investor(self, instance):
        return get_investor_is_active(self.context.get("user"), instance)

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return None

    def get_followers_count(self, instance):
        return instance.followers.all().count()

    def get_following_count(self, instance):
        return instance.following.all().count()


class InvestorPreferencesSerializer(serializers.ModelSerializer):
    options = serializers.SerializerMethodField()
    is_answered = serializers.SerializerMethodField()
    is_skipped = serializers.SerializerMethodField()

    class Meta:
        model = OnboardingQuestion
        fields = [
            "id",
            "question_text",
            "option_type",
            "is_mandatory",
            "is_answered",
            "preference_icon_url",
            "illustration_url",
            "is_skipped",
            "is_main_preference",
            "display_title",
            "options",
            "preference_category",
        ]

    def get_is_answered(self, instance):
        user = self.context.get("user", None)
        return OnboardingAnswer.objects.filter(
            onboarding_question=instance, user=user, is_skipped=False
        ).exists()

    def get_is_skipped(self, instance):
        user = self.context.get("user", None)
        onboarding_answer = OnboardingAnswer.objects.filter(
            onboarding_question=instance, user=user
        )
        if onboarding_answer.exists():
            return onboarding_answer.first().is_skipped
        else:
            return False

    def get_options(self, instance):
        user = self.context.get("user", None)
        options = get_onboarding_questions_with_answer(user, instance)
        return options


class WebInvestorPropertyPortfolioViewSerializer(ModelSerializer):
    """
    Investor property portfolio web view serializer
    """

    owner = serializers.SerializerMethodField()
    role = serializers.CharField(source="created_by_role.name")
    distressed_deal = serializers.SerializerMethodField()
    unit_images = serializers.SerializerMethodField()
    community = serializers.CharField(source="community.name")
    availability_status = serializers.IntegerField(
        source="propertyavailabilityandstatus.occupancy_status",
        read_only=True,
        allow_null=True,
    )
    asking_price = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    rent_data = serializers.SerializerMethodField()
    manual_added_details = serializers.SerializerMethodField()
    agents = serializers.SerializerMethodField()
    is_associated_agent = serializers.SerializerMethodField()
    is_owner = serializers.SerializerMethodField()
    is_co_owner = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    property_specification_type = serializers.SerializerMethodField()
    building_name = serializers.SerializerMethodField()
    furnished = serializers.SerializerMethodField()
    branded_building = serializers.SerializerMethodField()
    premium_view = serializers.SerializerMethodField()
    is_added_by_investor = serializers.SerializerMethodField()
    property_currency_code = serializers.CharField()
    preferred_currency_code = serializers.CharField()

    class Meta:
        model = Property
        fields = [
            "id",
            "owner",
            "community",
            "unit_number",
            "building_number",
            "property_type",
            "floor_number",
            "property_unit_type",
            "total_area",
            "carpet_area",
            "balcony_area",
            "number_of_bedrooms",
            "property_currency_code",
            "number_of_common_bathrooms",
            "number_of_attached_bathrooms",
            "number_of_powder_rooms",
            "preferred_currency_code",
            "availability_status",
            "owner_intent",
            "asking_price",
            "currency_code",
            "rent_data",
            "unit_images",
            "distressed_deal",
            "manual_added_details",
            "user_unit_preference",
            "building_name",
            "is_co_owner",
            "agents",
            "is_associated_agent",
            "is_owner",
            "address",
            "property_specification_type",
            "is_archived",
            "furnished",
            "branded_building",
            "premium_view",
            "agent_type",
            "owner_verified",
            "is_added_by_investor",
            "default_image",
            "role",
        ]

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)

        price_details_data = dict()
        price_details_data["property_currency_asking_price"] = instance.asking_price
        price_details_data["property_currency_valuation"] = instance.valuation
        price_details_data["preferred_currency_asking_price"] = None
        price_details_data["preferred_currency_valuation"] = None

        rent_details = dict()
        total_rent = instance.annual_rent
        original_price = instance.original_price
        rent_details["property_currency_total_rent"] = total_rent
        rent_details["preferred_currency_total_rent"] = None
        if (original_price and original_price > 0) and (total_rent and total_rent > 0):
            rent_increase_percentage = (total_rent / original_price) * 100
            rent_details["rent_increase_percentage"] = rent_increase_percentage
            rent_details["is_upward"] = True
        else:
            rent_details["rent_increase_percentage"] = None
            rent_details["is_upward"] = None
        data["price_details"] = price_details_data
        data["rent_details"] = rent_details

        data["tentative_commission"] = None
        commission_data = dict()
        commission_data["preferred_currency_tentative_commission"] = None
        commission_data["property_currency_tentative_commission"] = None
        data["commission_data"] = commission_data
        data["can_archive"] = None
        data["unit_number"] = None
        return data

    def get_owner(self, instance):
        return None

    def get_agents(self, instance):
        associated_agents = AgentAssociatedProperty.objects.filter(
            action_status=UserRequestActions.ACCEPTED,
            is_associated=True,
            property=instance,
            is_request_expired=False,
        ).values_list("agent_profile_id", flat=True)
        agents = AgentProfile.objects.filter(id__in=associated_agents)
        return BasicAgentProfileInfoSerializer(agents, many=True).data

    def get_asking_price(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            return financial_details.asking_price
        except PropertyFinancialDetails.DoesNotExist:
            return None

    def get_currency_code(self, instance):
        return None

    def get_unit_images(self, instance):
        unit_sections = PropertyUnitSections.objects.filter(property=instance).order_by(
            "id"
        )
        return PropertySectionSerializer(unit_sections, many=True).data

    def get_rent_data(self, instance):
        return None

    def get_distressed_deal(self, instance):
        try:
            financial_details = instance.propertyfinancialdetails
            if (
                financial_details.asking_price
                and financial_details.original_price
                and financial_details.asking_price < financial_details.original_price
            ):
                return True
            return False
        except PropertyFinancialDetails.DoesNotExist:
            return False

    def get_manual_added_details(self, instance):
        # Fetch the fields from PropertyVerifiedDataFields that have been manually edited
        verified_fields = PropertyVerifiedDataFields.objects.filter(property=instance)
        return [field.field_name for field in verified_fields]

    def get_is_associated_agent(self, instance):
        return None

    def get_is_owner(self, instance):
        return None

    def get_is_co_owner(self, instance):
        return None

    def get_address(self, obj):
        skip_loc = None
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    skip_loc = f"sub_loc_{loc_no}"
                    break

        address_parts = [
            *[
                getattr(obj.community, f"sub_loc_{loc_no}")
                for loc_no in range(5, 0, -1)
                if obj.community
                and f"sub_loc_{loc_no}" != skip_loc
                and getattr(obj.community, f"sub_loc_{loc_no}", None)
            ],
            obj.community.name if obj.community and skip_loc else None,
            obj.area.name if obj.area else None,
            obj.city.name if obj.city else None,
            obj.state.name if obj.state else None,
            obj.country.name if obj.country else None,
        ]

        return ", ".join(filter(None, address_parts))

    def get_building_name(self, obj):
        if obj.community:
            for loc_no in range(5, 0, -1):
                if getattr(obj.community, f"sub_loc_{loc_no}", None):
                    return getattr(obj.community, f"sub_loc_{loc_no}")
            else:
                return obj.community.name

        return None

    def get_property_specification_type(self, instance):
        property_specification_completion_state = (
            PropertyCompletionState.objects.filter(
                property=instance,
                state=PropertyCompletionStateChoices.PROPERTY_SPECIFICATIONS,
            )
        )
        if property_specification_completion_state.exists():
            property_specification_completion_state = (
                property_specification_completion_state.first()
            )
            return property_specification_completion_state.data_source
        return None

    def get_furnished(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.furnished
        return False

    def get_branded_building(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.branded_building
        return False

    def get_premium_view(self, instance):
        features = PropertyFeatures.objects.filter(property=instance)
        if features.exists():
            features = features.first()
            return features.premium_view
        return False

    def get_is_added_by_investor(self, instance):
        if instance.created_by_role.name == INVESTOR:
            return True
        return False
