from rest_framework import serializers
from rest_framework.serializers import ModelSerializer

from rezio.properties.models import Property
from rezio.properties.text_choices import PropertyPublishStatus
from rezio.properties.utils import (
    get_s3_object,
    get_primary_phone_code,
    get_primary_number,
)
from rezio.user.constants import AGENT
from rezio.user.models import AgentProfile
from rezio.user.utils import url_validator
from rezio.utils.text_choices import AgentLicenseVerificationStatus


class WebOtherSocialLinksSerializer(serializers.Serializer):
    """
    Other social links serializer
    """

    name = serializers.CharField(required=True)
    link = serializers.CharField(required=True, validators=[url_validator])


class WebViewAgentProfileSerializer(ModelSerializer):
    """
    Agent profile web view serializer
    """

    agency = serializers.CharField(source="agency.name", allow_null=True)
    role = serializers.CharField(default=AGENT)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)
    other_links = serializers.ListField(child=WebOtherSocialLinksSerializer())
    followers_count = serializers.SerializerMethodField(read_only=True)
    following_count = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(source="user.primary_phone_number")
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    total_property_count = serializers.SerializerMethodField(read_only=True)
    license_verification_status = serializers.CharField(read_only=True)
    license_end_date = serializers.SerializerMethodField(read_only=True)
    gender = serializers.CharField()
    license_start_date = serializers.SerializerMethodField(read_only=True)
    secondary_phone_number = serializers.CharField(source="user.secondary_phone_number")
    secondary_phone_code = serializers.SerializerMethodField(read_only=True)
    secondary_number = serializers.SerializerMethodField(read_only=True)
    working_type = serializers.CharField()
    is_chat_enabled = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "user_id",
            "name",
            "brn",
            "agency",
            "role",
            "bio",
            "followers_count",
            "following_count",
            "twitter_link",
            "instagram_link",
            "facebook_link",
            "linkedin_link",
            "other_links",
            "profile_photo",
            "cover_photo",
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "total_property_count",
            "license_verification_status",
            "license_end_date",
            "gender",
            "license_start_date",
            "secondary_phone_number",
            "secondary_phone_code",
            "secondary_number",
            "working_type",
            "is_chat_enabled",
            "subscription_status"
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return None

    def get_followers_count(self, instance):
        return instance.followers.all().count()

    def get_following_count(self, instance):
        return instance.following.all().count()

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)

    def get_total_property_count(self, instance):
        properties = Property.objects.filter(
            property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO
        ).values_list("id")
        return len(properties)

    def get_license_end_date(self, instance):
        if (
            instance.license_verification_status
            == AgentLicenseVerificationStatus.LICENSE_VERIFIED
        ):
            return instance.license_end_date
        return None

    def get_license_start_date(self, instance):
        if (
            instance.license_verification_status
            == AgentLicenseVerificationStatus.LICENSE_VERIFIED
        ):
            return instance.license_start_date
        return None

    def get_secondary_phone_code(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_phone_code(instance.user.secondary_phone_number)

    def get_secondary_number(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_number(instance.user.secondary_phone_number)

    def get_is_chat_enabled(self, instance):
        return True
