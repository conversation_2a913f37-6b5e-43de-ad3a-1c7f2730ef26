import logging
import mimetypes
from datetime import date

from django.conf import settings
from django.db.models import Q
from phonenumber_field.phonenumber import PhoneNumber
from rest_framework import serializers

from rezio.properties.helper import is_valid_image_or_video
from rezio.properties.models import AgentAssociatedProperty
from rezio.properties.text_choices import UserRequestActions

# from rezio.properties.serializers.property_details_serializer import PropertyViewSerializer
from rezio.properties.utils import (
    get_s3_object,
    get_primary_phone_code,
    get_primary_number,
)
from rezio.user.constants import DEFAULT_REGION, AGENT
from rezio.user.helper import check_file_validity
from rezio.user.models import <PERSON><PERSON>rofile, Agency, User, Agent
from rezio.user.serializers import UserDetailSerializer, OtherSocialLinksSerializer
from rezio.user.utils import (
    contains_special_characters,
    emirates_id_validator,
    passport_validator,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.constants import KEY_MESSAGE, <PERSON><PERSON>Y_PAYLOAD, <PERSON><PERSON><PERSON>_ERROR, <PERSON><PERSON>Y_ERROR_MESSAGE
from rezio.utils.custom_exceptions import InvalidSerializerDataException
from rezio.utils.text_choices import Pronouns, Gender, AgentLicenseVerificationStatus
from rezio.utils.validation_utils import validate_phone_number

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class AgentProfileSerializer(serializers.ModelSerializer):
    agency_name = serializers.CharField(
        source="agency.name",
        error_messages={
            "blank": "Agency cannot be empty",
        },
        required=False,
    )
    primary_phone_number = serializers.CharField(write_only=True)
    secondary_phone_number = serializers.CharField(
        write_only=True, required=False, allow_null=True
    )
    agreed_to_terms = serializers.BooleanField(write_only=True)
    onboarding_completed = serializers.BooleanField(read_only=True)
    email = serializers.EmailField(read_only=True)
    working_type = serializers.CharField(read_only=True)
    license_verification_status = serializers.CharField(read_only=True)
    agency_country_code = serializers.CharField(
        source="agency.country_code", required=False, allow_null=True, allow_blank=True
    )
    license_country_code = serializers.CharField(
        required=False, allow_null=True, allow_blank=True, default="AE"
    )
    license_country = serializers.CharField(
        required=False, allow_null=True, allow_blank=True, default="UAE"
    )
    profile_photo_url = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        model = AgentProfile

        fields = [
            "id",
            "brn",
            "name",
            "gender",
            "license_start_date",
            "license_end_date",
            "user_id",
            "agency_name",
            "primary_phone_number",
            "secondary_phone_number",
            "agreed_to_terms",
            "onboarding_completed",
            "email",
            "working_type",
            "license_verification_status",
            "agency_country_code",
            "license_country_code",
            "license_country",
            "profile_photo_url",
        ]

        extra_kwargs = {
            "name": {"required": True},
            "gender": {"required": False},
            "brn": {"required": True},
            "license_start_date": {"required": False},
            "license_end_date": {"required": False},
            "agency_name": {"required": False},
            "primary_phone_number": {"required": True},
            "secondary_phone_number": {"required": False},
            "agreed_to_terms": {"required": True},
            "agency_country_code": {"required": False},
        }

    def validate(self, data):
        primary_phone_number = data.get("primary_phone_number", None)
        secondary_phone_number = data.get("secondary_phone_number", None)
        request = self.context.get("request")

        if (
            primary_phone_number
            and secondary_phone_number
            and primary_phone_number == secondary_phone_number
        ):
            message = "Both number cannot be same"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            User.objects.filter(primary_phone_number=primary_phone_number)
            .exclude(id=request.user.pk)
            .exists()
        ):
            message = "User with this phone number already exists"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if data.get("license_start_date") and not data.get("license_end_date"):
            message = "License end date is required"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if data.get("license_end_date") and not data.get("license_start_date"):
            message = "License start date is required"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_start_date")
            and data.get("license_end_date")
            and data.get("license_start_date") >= data.get("license_end_date")
        ):
            message = "License start date cannot be equal or more than the end date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if (
            data.get("license_start_date")
            and data.get("license_start_date").date() > date.today()
        ):
            message = "License start date cannot be a future date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_end_date")
            and data.get("license_end_date").date() < date.today()
        ):
            message = "License end date cannot be a past date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        data.pop("primary_phone_number")
        data.pop("secondary_phone_number", None)
        data.pop("agreed_to_terms")
        data.pop("profile_photo_url")
        return data

    def validate_primary_phone_number(self, phone_number):
        phone_number = PhoneNumber.from_string(phone_number)
        if not phone_number.is_valid():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"{phone_number} is invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                }
            )

        return phone_number

    def validate_secondary_phone_number(self, phone_number):
        if phone_number:
            phone_number = PhoneNumber.from_string(phone_number)
            if not phone_number.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: f"{phone_number} is invalid",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                    }
                )

        return phone_number

    def validate_name(self, name):
        if not contains_special_characters(
            name, "^[a-zA-Zà-ÿÀ-Ÿä-öø-ÿÄ-ÖØ-ß ./’'‘-]+$"
        ):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def validate_agreed_to_terms(self, agreed_to_terms):
        if not agreed_to_terms:
            raise serializers.ValidationError(
                "You must agree to the terms and conditions to proceed."
            )
        return agreed_to_terms


class BRNDataSerializer(serializers.Serializer):
    brn = serializers.IntegerField(source="broker_number")
    name = serializers.CharField(source="broker_name_en")
    phone = serializers.SerializerMethodField()
    license_start_date = serializers.CharField()
    license_end_date = serializers.CharField()
    gender = serializers.SerializerMethodField()

    class Meta:
        fields = ["brn", "name", "phone", "license_start_date", "license_end_date"]

        extra_kwargs = {
            "brn": {"required": True},
            "name": {"required": True},
            "phone": {"required": True},
            "license_start_date": {"required": False},
            "license_end_date": {"required": False},
            "gender": {"required": True},
        }

    def get_gender(self, obj):
        return "Male" if obj.get("gender") == "0" else "Female"

    def get_phone(self, obj):
        from phonenumber_field.phonenumber import to_python

        phone_number = obj.get("phone")
        if len(phone_number) > 0:
            if not phone_number.startswith("+"):
                phone_number = f"+{phone_number}"
            phone_number = to_python(phone_number)
            phone_number, is_error, error_message = validate_phone_number(phone_number)
            if is_error:
                phone_number, is_error, error_message = validate_phone_number(
                    phone_number, DEFAULT_REGION
                )
                if is_error:
                    return ""
                return str(phone_number)
            return str(phone_number)


class AgencySerializer(serializers.ModelSerializer):
    class Meta:
        model = Agency
        fields = ["id", "name"]

        extra_kwargs = {
            "name": {"required": True},
        }


class AgencyNameSerializer(serializers.ModelSerializer):
    class Meta:
        model = Agency
        fields = ["name"]


class AgentDataSerializer(serializers.ModelSerializer):
    agency = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()

    class Meta:
        model = Agent
        fields = [
            "id",
            "name",
            "agency",
            "brn",
            "phone",
            "gender",
            "license_start_date",
            "license_end_date",
            "profile_photo_url",
        ]

    def get_agency(self, obj):
        # Retrieve the name of the agency
        return obj.agency.name if obj.agency else None

    def get_phone(self, obj):
        from phonenumber_field.phonenumber import to_python

        phone_number = str(obj.phone) if obj.phone else None
        if phone_number and len(phone_number) > 0:
            if not phone_number.startswith("+"):
                phone_number = f"+{phone_number}"
            phone_number = to_python(phone_number)
            phone_number, is_error, error_message = validate_phone_number(phone_number)
            if is_error:
                phone_number, is_error, error_message = validate_phone_number(
                    phone_number, DEFAULT_REGION
                )
                if is_error:
                    return ""
                return str(phone_number)
            return str(phone_number)
        else:
            return None


class AgentProfileInformationSerializer(serializers.ModelSerializer):
    user = UserDetailSerializer()
    brn = serializers.CharField(read_only=True)
    agency_name = serializers.SerializerMethodField(source="agency.name")
    pronouns = serializers.ChoiceField(choices=Pronouns.choices, allow_null=True)
    gender = serializers.ChoiceField(choices=Gender.choices, allow_null=True)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)
    license_verification_status = serializers.CharField(read_only=True)
    license = serializers.SerializerMethodField(read_only=True)
    working_type = serializers.CharField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "user",
            "name",
            "brn",
            "gender",
            "email",
            "pronouns",
            "profile_photo",
            "bio",
            "agency_name",
            "license_start_date",
            "license_end_date",
            "cover_photo",
            "profile_photo_size",
            "cover_photo_size",
            "license_verification_status",
            "license",
            "working_type",
            "license_file_size",
            "license_file_name",
            "license_country_code",
        ]
        extra_kwargs = {
            "pronouns": {"allow_null": True, "allow_blank": True},
            "name": {"required": True, "allow_null": False, "allow_blank": False},
        }

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)
        try:
            if data.get("pronouns", None):
                data["pronouns"] = int(data["pronouns"])
        except Exception as error:
            logger.error(f"Invalid data in pronoun - AgentProfileInformation - {error}")

        return data

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return settings.DEFAULT_COVER_PHOTO

    def get_agency_name(self, instance):
        if instance.agency:
            return instance.agency.name
        return None

    def get_license(self, instance):
        if instance.license_file_key:
            return get_s3_object(instance.license_file_key)


class AgentPersonalInformationSerializer(serializers.ModelSerializer):
    agency_name = serializers.CharField(required=False, allow_null=True)
    primary_phone_number = serializers.CharField(write_only=True)
    secondary_phone_number = serializers.CharField(
        write_only=True, required=False, allow_null=True
    )
    license_country_code = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    license_country = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    agency_country_code = serializers.CharField(
        required=False, allow_null=True, allow_blank=True
    )
    license_file = serializers.FileField(required=False, allow_null=True)
    is_license_deleted = serializers.BooleanField(
        write_only=True, required=True, allow_null=False
    )
    is_verification_required = serializers.BooleanField(
        write_only=True, required=True, allow_null=False
    )

    class Meta:
        model = AgentProfile

        fields = [
            "id",
            "brn",
            "name",
            "gender",
            "license_start_date",
            "license_end_date",
            "agency_name",
            "primary_phone_number",
            "secondary_phone_number",
            "working_type",
            "license_country_code",
            "license_country",
            "agency_country_code",
            "license_file",
            "is_license_deleted",
            "is_verification_required",
        ]

        extra_kwargs = {
            "name": {"required": True},
            "gender": {"required": False},
            "brn": {"required": False},
            "license_start_date": {"required": False},
            "license_end_date": {"required": False},
            "primary_phone_number": {"required": True},
            "secondary_phone_number": {"required": False},
            "working_type": {"required": False},
        }

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)
        data["license_file"] = instance.license_file_name

        return data

    def validate(self, data):
        primary_phone_number = data.get("primary_phone_number", None)
        secondary_phone_number = data.get("secondary_phone_number", None)
        request = self.context.get("request")

        if (
            primary_phone_number
            and secondary_phone_number
            and primary_phone_number == secondary_phone_number
        ):
            message = "Both number cannot be same"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            User.objects.filter(primary_phone_number=primary_phone_number)
            .exclude(id=request.user.pk)
            .exists()
        ):
            message = "User with this phone number already exists"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if data.get("license_start_date") and not data.get("license_end_date"):
            message = "License expiry date is required"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if data.get("license_end_date") and not data.get("license_start_date"):
            message = "License issue date is required"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_start_date")
            and data.get("license_end_date")
            and data.get("license_start_date") >= data.get("license_end_date")
        ):
            message = "License issue date cannot be equal or more than the expiry date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if (
            data.get("license_start_date")
            and data.get("license_start_date").date() > date.today()
        ):
            message = "License issue date cannot be a future date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_end_date")
            and data.get("license_end_date").date() < date.today()
        ):
            message = "License expiry date cannot be a past date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        return data

    def validate_primary_phone_number(self, phone_number):
        phone_number = PhoneNumber.from_string(phone_number)
        if not phone_number.is_valid():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"{phone_number} is invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                }
            )

        return phone_number

    def validate_secondary_phone_number(self, phone_number):
        if phone_number:
            phone_number = PhoneNumber.from_string(phone_number)
            if not phone_number.is_valid():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: f"{phone_number} is invalid",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                    }
                )

        return phone_number

    def validate_name(self, name):
        if not contains_special_characters(name):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def validate_license_file(self, license_file):
        if license_file:
            mime_type, _ = mimetypes.guess_type(license_file.name)

            if mime_type == "application/pdf":
                check_file_validity(license_file)
            elif mime_type in ["image/png", "image/jpeg", "image/jpg", "image/heic"]:
                is_valid_image_or_video(license_file)
            else:
                serializers.ValidationError(f"File type '{mime_type}' is not supported")

            return license_file


class SaveAgentProfileInformationSerializer(serializers.Serializer):
    emirates_id = serializers.CharField(
        required=False, allow_null=True, validators=[emirates_id_validator]
    )
    passport_number = serializers.CharField(
        required=False, allow_null=True, validators=[passport_validator]
    )
    bio = serializers.CharField(allow_null=True)
    pronouns = serializers.ChoiceField(choices=Pronouns.choices, allow_null=True)
    email = serializers.EmailField(required=False, allow_null=True)


class BasicAgentProfileInfoSerializer(serializers.ModelSerializer):
    primary_phone_number = serializers.CharField(source="user.primary_phone_number")
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    role = serializers.CharField(default=AGENT)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    license_verification_status = serializers.CharField(read_only=True)
    working_type = serializers.CharField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "user_id",
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "name",
            "brn",
            "role",
            "profile_photo",
            "twitter_link",
            "instagram_link",
            "linkedin_link",
            "facebook_link",
            "other_links",
            "license_verification_status",
            "working_type",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)


class ViewAgentProfileSerializer(serializers.ModelSerializer):
    agency = serializers.CharField(source="agency.name", allow_null=True)
    role = serializers.CharField(default=AGENT)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)
    other_links = serializers.ListField(child=OtherSocialLinksSerializer())
    editable = serializers.SerializerMethodField(read_only=True)
    is_following = serializers.SerializerMethodField(read_only=True)
    followers_count = serializers.SerializerMethodField(read_only=True)
    following_count = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(source="user.primary_phone_number")
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    license_verification_status = serializers.CharField(read_only=True)
    secondary_phone_number = serializers.CharField(source="user.secondary_phone_number")
    secondary_phone_code = serializers.SerializerMethodField(read_only=True)
    secondary_number = serializers.SerializerMethodField(read_only=True)
    license_end_date = serializers.SerializerMethodField(read_only=True)
    license_start_date = serializers.SerializerMethodField(read_only=True)
    gender = serializers.CharField(read_only=True)
    working_type = serializers.CharField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "user_id",
            "name",
            "brn",
            "agency",
            "role",
            "bio",
            "followers_count",
            "following_count",
            "twitter_link",
            "instagram_link",
            "facebook_link",
            "linkedin_link",
            "other_links",
            "editable",
            "profile_photo",
            "cover_photo",
            "is_following",
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "license_verification_status",
            "license_end_date",
            "secondary_phone_number",
            "secondary_phone_code",
            "secondary_number",
            "license_start_date",
            "gender",
            "working_type",
            "subscription_status",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return None

    def get_followers_count(self, instance):
        return instance.followers.all().count()

    def get_following_count(self, instance):
        return instance.following.all().count()

    def get_editable(self, instance):
        request = self.context.get("request")
        if instance.user == request.user and self.context.get("role") == AGENT:
            return True
        return False

    def get_is_following(self, instance):
        request = self.context.get("request")
        user = self.context.get("user")
        viewing_role = self.context.get("viewing_role")
        role = self.context.get("role")
        if (instance.user == request.user and role != viewing_role) or (
            instance.user != request.user
        ):
            return instance.followers.filter(
                from_user=request.user,
                from_user_role__name=role,
                to_user=user,
                to_user_role__name=viewing_role,
            ).exists()
        return False

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)

    def get_license_end_date(self, instance):
        if (
            instance.license_verification_status
            == AgentLicenseVerificationStatus.LICENSE_VERIFIED
        ):
            return instance.license_end_date
        return None

    def get_secondary_phone_code(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_phone_code(instance.user.secondary_phone_number)

    def get_secondary_number(self, instance):
        if instance.user.secondary_phone_number:
            return get_primary_number(instance.user.secondary_phone_number)

    def get_license_start_date(self, instance):
        if (
            instance.license_verification_status
            == AgentLicenseVerificationStatus.LICENSE_VERIFIED
        ):
            return instance.license_start_date
        return None


class AgentCommissionSerializer(serializers.ModelSerializer):
    commission_percentage = serializers.DecimalField(
        required=True, max_digits=5, decimal_places=2, min_value=0
    )

    class Meta:
        model = AgentProfile
        fields = ["user_id", "commission_percentage"]

    def validate_commission_percentage(self, commission_percentage):
        if commission_percentage > 100:
            raise serializers.ValidationError("Commission cannot be more than 100")
        if commission_percentage.is_zero():
            raise serializers.ValidationError("Commission cannot be 0")

        return commission_percentage


class ViewAgentListSerializer(serializers.Serializer):
    id = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField(read_only=True)
    email = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(read_only=True)
    primary_phone_code = serializers.SerializerMethodField()
    agency_name = serializers.CharField()
    rating = serializers.SerializerMethodField()
    is_previously_associated = serializers.BooleanField(default=False)
    license_verification_status = serializers.SerializerMethodField(read_only=True)
    invitation_status = serializers.CharField()
    brn = serializers.SerializerMethodField()
    gender = serializers.SerializerMethodField()
    license_country_code = serializers.SerializerMethodField()
    working_type = serializers.SerializerMethodField()

    def get_id(self, instance):
        return instance["agent_id"]

    def get_user_id(self, instance):
        return instance["agent_user_id"]

    def get_name(self, instance):
        return instance["agent_name"]

    def get_email(self, instance):
        return instance["agent_email"]

    def get_profile_photo(self, instance):
        if instance["agent_profile_photo_key"]:
            return get_s3_object(instance["agent_profile_photo_key"])

    def get_license_verification_status(self, instance):
        return instance["agent_license_verification_status"]

    def get_license_country_code(self, instance):
        return instance["agent_license_country_code"]

    def get_brn(self, instance):
        return instance["agent_brn"]

    def get_gender(self, instance):
        return instance["agent_gender"]

    def get_rating(self, instance):
        return 0

    def get_primary_phone_code(self, instance):
        return str(get_primary_phone_code(instance["primary_phone_number"]))

    def get_working_type(self, instance):
        return instance["agent_working_type"]


class PropertyAssociatedAgentsSerializer(serializers.ModelSerializer):
    profile_photo = serializers.SerializerMethodField(read_only=True)
    agent_contract = serializers.SerializerMethodField(read_only=True)
    agent_contract_file_name = serializers.SerializerMethodField(read_only=True)
    agent_contract_file_size = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(
        source="user.primary_phone_number", read_only=True
    )
    primary_phone_code = serializers.SerializerMethodField()
    agency = serializers.SerializerMethodField()
    rating = serializers.SerializerMethodField()
    license_verification_status = serializers.CharField(read_only=True)
    agent_action_status = serializers.SerializerMethodField(read_only=True)
    working_type = serializers.CharField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "id",
            "user_id",
            "name",
            "profile_photo",
            "email",
            "primary_phone_number",
            "primary_phone_code",
            "gender",
            "brn",
            "agency",
            "rating",
            "agent_contract",
            "agent_contract_file_name",
            "agent_contract_file_size",
            "license_verification_status",
            "agent_action_status",
            "working_type",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_primary_phone_code(self, instance):
        return f"+{instance.user.primary_phone_number.country_code}"

    def get_agency(self, instance):
        if instance.agency:
            return instance.agency.name
        return None

    def get_rating(self, instance):
        return 0

    def get_agent_contract(self, instance):
        property_id = self.context.get("property_id")
        associated_agent = AgentAssociatedProperty.objects.filter(
            (Q(action_status=UserRequestActions.PENDING) & Q(is_associated=False))
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True)),
            agent_profile=instance,
            property_id=property_id,
            is_request_expired=False,
        )
        if associated_agent.exists() and associated_agent.first().agent_contract_key:
            return get_s3_object(associated_agent.first().agent_contract_key)
        return None

    def get_agent_contract_file_name(self, instance):
        property_id = self.context.get("property_id")
        associated_agent = AgentAssociatedProperty.objects.filter(
            (Q(action_status=UserRequestActions.PENDING) & Q(is_associated=False))
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True)),
            agent_profile=instance,
            property_id=property_id,
            is_request_expired=False,
        )
        if associated_agent.exists() and associated_agent.first().agent_contract_key:
            return associated_agent.first().agent_contract_file_name
        return None

    def get_agent_contract_file_size(self, instance):
        property_id = self.context.get("property_id")
        associated_agent = AgentAssociatedProperty.objects.filter(
            (Q(action_status=UserRequestActions.PENDING) & Q(is_associated=False))
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True)),
            agent_profile=instance,
            property_id=property_id,
            is_request_expired=False,
        )
        if associated_agent.exists() and associated_agent.first().agent_contract_key:
            return associated_agent.first().agent_contract_file_size
        return None

    def get_agent_action_status(self, instance):
        property_id = self.context.get("property_id")
        associated_agent = AgentAssociatedProperty.objects.filter(
            (Q(action_status=UserRequestActions.PENDING) & Q(is_associated=False))
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True)),
            agent_profile=instance,
            property_id=property_id,
            is_request_expired=False,
        )
        if associated_agent.exists() and associated_agent.first().action_status:
            return associated_agent.first().action_status


class RegisterAgentProfileSerializer(serializers.ModelSerializer):
    """
    Agent profile serializer for new flow
    """

    primary_phone_number = serializers.CharField(
        required=True, allow_null=False, allow_blank=False, write_only=True
    )
    name = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    email = serializers.EmailField(required=False, allow_null=True, allow_blank=True)
    agency_name = serializers.CharField(
        source="agency.name", required=False, allow_null=True
    )
    onboarding_completed = serializers.BooleanField(read_only=True)
    agreed_to_terms = serializers.BooleanField(required=True, write_only=True)
    agency_country_code = serializers.CharField(
        source="agency.country_code", required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        model = AgentProfile

        fields = [
            "id",
            "brn",
            "name",
            "gender",
            "license_start_date",
            "license_end_date",
            "user_id",
            "agency_name",
            "primary_phone_number",
            "onboarding_completed",
            "email",
            "working_type",
            "agreed_to_terms",
            "agency_country_code",
        ]

        extra_kwargs = {
            "gender": {"required": True},
            "working_type": {"required": True},
            "brn": {"required": False},
        }

    def validate(self, data):
        primary_phone_number = data.get("primary_phone_number", None)
        request = self.context.get("request")

        if (
            User.objects.filter(primary_phone_number=primary_phone_number)
            .exclude(id=request.user.pk)
            .exists()
        ):
            message = "User with this phone number already exists"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        data.pop("primary_phone_number")
        data.pop("agreed_to_terms")
        return data

    def validate_primary_phone_number(self, phone_number):
        phone_number = PhoneNumber.from_string(phone_number)
        if not phone_number.is_valid():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"{phone_number} is invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{phone_number} is invalid"},
                }
            )

        return phone_number

    def validate_name(self, name):
        if not contains_special_characters(
            name, "^[a-zA-Zà-ÿÀ-Ÿä-öø-ÿÄ-ÖØ-ß ./’'‘-]+$"
        ):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def validate_agreed_to_terms(self, agreed_to_terms):
        if not agreed_to_terms:
            raise serializers.ValidationError(
                "You must agree to the terms and conditions to proceed."
            )
        return agreed_to_terms


class SaveAgentVerificationDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer to save agent verification details
    """

    license_country_code = serializers.CharField(
        required=True, allow_null=False, allow_blank=False
    )
    license_country = serializers.CharField(
        required=True, allow_null=False, allow_blank=False
    )
    brn = serializers.CharField(required=True, allow_null=False, allow_blank=False)
    license_start_date = serializers.DateTimeField(required=True, allow_null=False)
    license_end_date = serializers.DateTimeField(required=True, allow_null=False)
    license_file = serializers.FileField(required=False, allow_null=True)

    class Meta:
        model = AgentProfile

        fields = [
            "license_country_code",
            "license_country",
            "brn",
            "license_start_date",
            "license_end_date",
            "license_file",
        ]

    def validate(self, data):
        if (
            data.get("license_start_date")
            and data.get("license_end_date")
            and data.get("license_start_date") >= data.get("license_end_date")
        ):
            message = "License start date cannot be equal or more than the end date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_start_date")
            and data.get("license_start_date").date() > date.today()
        ):
            message = "License start date cannot be a future date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            data.get("license_end_date")
            and data.get("license_end_date").date() < date.today()
        ):
            message = "License end date cannot be a past date"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        return data

    def validate_license_file(self, license_file):
        mime_type, _ = mimetypes.guess_type(license_file.name)

        if mime_type == "application/pdf":
            check_file_validity(license_file)
        elif mime_type in ["image/png", "image/jpeg", "image/jpg", "image/heic"]:
            is_valid_image_or_video(license_file)
        else:
            serializers.ValidationError(f"File type '{mime_type}' is not supported")

        return license_file


class ViewCombinedAgentListSerializer(serializers.Serializer):
    """
    Serializer to get combined (onboarded + BRN database) agent list
    """

    id = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField(read_only=True)
    email = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(read_only=True)
    primary_phone_code = serializers.SerializerMethodField()
    agency_name = serializers.CharField()
    rating = serializers.SerializerMethodField()
    is_previously_associated = serializers.BooleanField(default=False)
    license_verification_status = serializers.SerializerMethodField(read_only=True)
    invitation_status = serializers.CharField()
    brn = serializers.SerializerMethodField()
    gender = serializers.SerializerMethodField()
    license_country_code = serializers.SerializerMethodField()
    working_type = serializers.SerializerMethodField()

    def get_id(self, instance):
        return instance["agent_id"]

    def get_user_id(self, instance):
        return instance["agent_user_id"]

    def get_name(self, instance):
        return instance["agent_name"]

    def get_email(self, instance):
        return instance["agent_email"]

    def get_profile_photo(self, instance):
        if instance["agent_profile_photo_key"]:
            return get_s3_object(instance["agent_profile_photo_key"])

    def get_license_verification_status(self, instance):
        return instance["agent_license_verification_status"]

    def get_license_country_code(self, instance):
        return instance["agent_license_country_code"]

    def get_brn(self, instance):
        return instance["agent_brn"]

    def get_gender(self, instance):
        return instance["agent_gender"]

    def get_rating(self, instance):
        return 0

    def get_primary_phone_code(self, instance):
        return str(get_primary_phone_code(instance["primary_phone_number"]))

    def get_working_type(self, instance):
        return instance["agent_working_type"]


class AgentDetailSerializer(serializers.ModelSerializer):
    """
    Custom serializer for agent details that includes primary phone number from User
    """

    # Get phone number from the related User model
    primary_phone_number = serializers.SerializerMethodField()
    email = serializers.EmailField(source="user.email", read_only=True)
    agency = serializers.CharField(source="agency.name", allow_null=True)
    profile_photo = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = AgentProfile
        fields = [
            "id",
            "name",
            "email",
            "primary_phone_number",
            "profile_photo_key",
            "brn",
            "agency",
            "profile_photo",
        ]

    def get_primary_phone_number(self, obj):
        """Get primary_phone_number from the related User model"""
        if obj.user and hasattr(obj.user, "primary_phone_number"):
            return str(obj.user.primary_phone_number)
        return None

    @staticmethod
    def get_profile_photo(instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None
