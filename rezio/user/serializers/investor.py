import logging
from phonenumbers import geocoder
from rest_framework import serializers
from django.conf import settings
from firebase_admin import auth

from rezio.user.constants import INVESTOR, SELLER
from rezio.user.helper import (
    get_investor_budget,
    get_country_name_from_profile,
    get_investor_is_active,
)
from rezio.user.models import (
    InvestorProfile,
    OnboardingAnswer,
    User,
    OnboardingQuestion,
)
from rezio.user.serializers import UserDetailSerializer, OtherSocialLinksSerializer
from rezio.user.text_choices import OptionTypes
from rezio.user.utils import contains_special_characters
from rezio.utils.text_choices import Pronouns, Gender
from rezio.properties.utils import (
    get_s3_object,
    get_primary_phone_code,
    get_primary_number,
)
from rezio.utils.constants import (
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    DJANGO_LOGGER_NAME,
)
from rezio.utils.custom_exceptions import InvalidSerializerDataException

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class InvestorProfileSerializer(serializers.ModelSerializer):
    agreed_to_terms = serializers.BooleanField(write_only=True)
    onboarding_completed = serializers.BooleanField(read_only=True)

    class Meta:
        model = InvestorProfile

        fields = [
            "id",
            "name",
            "gender",
            "user_id",
            "agreed_to_terms",
            "onboarding_completed",
            "agreed_to_terms",
        ]

        extra_kwargs = {
            "name": {"required": True},
            "gender": {"required": False},
            "agreed_to_terms": {"required": True},
        }

    def validate_name(self, name):
        if not contains_special_characters(name):
            raise serializers.ValidationError("Enter valid full name")
        return name

    def validate_agreed_to_terms(self, agreed_to_terms):
        if not agreed_to_terms:
            raise serializers.ValidationError(
                "You must agree to the terms and conditions to proceed."
            )
        return agreed_to_terms


class InvestorOnboardingAnswerSerializer(serializers.ModelSerializer):
    investor_type_id = serializers.IntegerField(required=False)

    class Meta:
        model = OnboardingAnswer
        fields = [
            "id",
            "user_id",
            "onboarding_question_id",
            "answer",
            "investor_type_id",
        ]

        extra_kwargs = {
            "onboarding_question_id": {"required": True},
            "answer": {"required": True},
        }


class InvestorProfileInformationSerializer(serializers.ModelSerializer):
    user = UserDetailSerializer()
    pronouns = serializers.ChoiceField(choices=Pronouns.choices, allow_null=True)
    gender = serializers.ChoiceField(choices=Gender.choices, allow_null=True)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            "user",
            "name",
            "gender",
            "email",
            "pronouns",
            "profile_photo",
            "cover_photo",
        ]
        extra_kwargs = {
            "pronouns": {"allow_null": True, "allow_blank": True},
            "name": {"required": True, "allow_null": False, "allow_blank": False},
        }

    def to_representation(self, instance):
        # Get the default representation
        data = super().to_representation(instance)
        try:
            if data.get("pronouns", None):
                data["pronouns"] = int(data["pronouns"])
        except Exception as error:
            logger.error(f"Invalid data in pronoun - AgentProfileInformation - {error}")

        return data

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return settings.DEFAULT_COVER_PHOTO

    def validate(self, data):
        primary_phone_number = data.get("user", {}).get("primary_phone_number", None)
        secondary_phone_number = data.get("user", {}).get(
            "secondary_phone_number", None
        )
        request = self.context.get("request")

        if (
            primary_phone_number
            and secondary_phone_number
            and primary_phone_number == secondary_phone_number
        ):
            message = "Both number cannot be same"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        if (
            User.objects.filter(primary_phone_number=primary_phone_number)
            .exclude(id=request.user.pk)
            .exists()
        ):
            message = "User with this phone number already exists"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        return data

    def update(self, instance, validated_data):
        user_data = validated_data.pop("user", {})
        user = instance.user

        primary_phone_number = user_data.pop("primary_phone_number")
        primary_phone_number = primary_phone_number.as_e164
        if primary_phone_number and user.primary_phone_number != primary_phone_number:
            user.primary_phone_number = primary_phone_number
            user.save()
            # update number on firebase
            auth.update_user(user.id, phone_number=primary_phone_number)

        # Update User fields
        for attr, value in user_data.items():
            setattr(user, attr, value)
        user.save()

        # Update InvestorProfile fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class BasicInvestorProfileInfoSerializer(serializers.ModelSerializer):
    primary_phone_number = serializers.CharField(source="user.primary_phone_number")
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    role = serializers.CharField(default=INVESTOR)
    profile_photo = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "name",
            "role",
            "profile_photo",
            "twitter_link",
            "instagram_link",
            "linkedin_link",
            "facebook_link",
            "other_links",
        ]

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)


class ViewInvestorProfileSerializer(serializers.ModelSerializer):
    country = serializers.SerializerMethodField()
    is_active_investor = serializers.SerializerMethodField()
    budget = serializers.SerializerMethodField()
    role = serializers.CharField(default=INVESTOR)
    profile_photo = serializers.SerializerMethodField(read_only=True)
    cover_photo = serializers.SerializerMethodField(read_only=True)
    other_links = serializers.ListField(child=OtherSocialLinksSerializer())
    editable = serializers.SerializerMethodField(read_only=True)
    is_following = serializers.SerializerMethodField(read_only=True)
    followers_count = serializers.SerializerMethodField(read_only=True)
    following_count = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(source="user.primary_phone_number")
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = InvestorProfile
        fields = [
            "user_id",
            "name",
            "country",
            "is_active_investor",
            "budget",
            "role",
            "followers_count",
            "following_count",
            "twitter_link",
            "instagram_link",
            "is_following",
            "facebook_link",
            "linkedin_link",
            "other_links",
            "editable",
            "profile_photo",
            "cover_photo",
            "primary_phone_number",
            "primary_phone_code",
            "primary_number",
            "profile_public_view",
        ]

    def get_country(self, instance):
        return get_country_name_from_profile(instance)

    def get_budget(self, instance):
        budget = get_investor_budget(self.context.get("user"), instance)
        return budget

    def get_is_active_investor(self, instance):
        return get_investor_is_active(self.context.get("user"), instance)

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_cover_photo(self, instance):
        if instance.cover_photo_key:
            return get_s3_object(instance.cover_photo_key)
        return None

    def get_followers_count(self, instance):
        return instance.followers.all().count()

    def get_following_count(self, instance):
        return instance.following.all().count()

    def get_editable(self, instance):
        request = self.context.get("request")
        if instance.user == request.user and self.context.get("role") == INVESTOR:
            return True
        return False

    def get_is_following(self, instance):
        request = self.context.get("request")
        user = self.context.get("user")
        viewing_role = self.context.get("viewing_role")
        role = self.context.get("role")
        if (instance.user == request.user and role != viewing_role) or (
            instance.user != request.user
        ):
            return instance.followers.filter(
                from_user=request.user,
                from_user_role__name=role,
                to_user=user,
                to_user_role__name=viewing_role,
            ).exists()
        return False

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)


class ViewInvestorListSerializer(serializers.ModelSerializer):
    is_active_investor = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField(read_only=True)
    primary_phone_number = serializers.CharField(
        source="user.primary_phone_number", read_only=True
    )
    primary_phone_code = serializers.SerializerMethodField(read_only=True)
    primary_number = serializers.SerializerMethodField(read_only=True)
    user_id = serializers.IntegerField(
        source="id", read_only=True
    )  # Renaming 'id' to 'user_id'

    class Meta:
        model = InvestorProfile
        fields = [
            "id",
            "user_id",
            "name",
            "is_active_investor",
            "investor_type",
            "profile_photo",
            "email",
            "primary_phone_number",
            "gender",
            "primary_phone_code",
            "primary_number",
        ]

    def get_is_active_investor(self, instance):
        active_investor_question = OnboardingQuestion.objects.filter(
            investor_type=instance.investor_type, option_type=OptionTypes.SLIDER
        ).first()
        answer = OnboardingAnswer.objects.filter(
            onboarding_question=active_investor_question, user=self.context.get("user")
        )
        if answer.exists():
            answer = answer.first()
            if answer.answer:
                answered_value = answer.answer.get("answer_options", {}).get(
                    "answered_value", None
                )
                if answered_value and answered_value == 5:
                    return True
        return False

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_primary_phone_code(self, instance):
        return get_primary_phone_code(instance.user.primary_phone_number)

    def get_primary_number(self, instance):
        return get_primary_number(instance.user.primary_phone_number)


class InvestorListSerializer(serializers.ModelSerializer):
    is_active_investor = serializers.SerializerMethodField()
    profile_photo = serializers.SerializerMethodField(read_only=True)
    email = serializers.EmailField(source="user.email", read_only=True)
    primary_phone_number = serializers.CharField(
        source="user.primary_phone_number", read_only=True
    )
    primary_phone_code = serializers.SerializerMethodField()

    class Meta:
        model = InvestorProfile
        fields = [
            "id",
            "user_id",
            "name",
            "is_active_investor",
            "investor_type",
            "profile_photo",
            "email",
            "primary_phone_number",
            "gender",
            "primary_phone_code",
        ]

    def get_is_active_investor(self, instance):
        active_investor_question = OnboardingQuestion.objects.filter(
            investor_type=instance.investor_type, option_type=OptionTypes.SLIDER
        ).first()
        answer = OnboardingAnswer.objects.filter(
            onboarding_question=active_investor_question, user=self.context.get("user")
        )
        if answer.exists():
            answer = answer.first()
            if answer.answer:
                answered_value = answer.answer.get("answer_options", {}).get(
                    "answered_value", None
                )
                if answered_value and answered_value == 5:
                    return True
        return False

    def get_profile_photo(self, instance):
        if instance.profile_photo_key:
            return get_s3_object(instance.profile_photo_key)
        return None

    def get_primary_phone_code(self, instance):
        return f"+{instance.user.primary_phone_number.country_code}"
