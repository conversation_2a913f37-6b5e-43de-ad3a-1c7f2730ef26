import logging

from django.conf import settings
from django.db.models import Q, Count, Value, <PERSON>r<PERSON><PERSON>, F
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView

from rezio.properties.models import Property, PropertyCoOwner
from rezio.properties.serializers import PropertyPortfolioViewSerializer
from rezio.properties.text_choices import (
    PropertyPublishStatus,
    OwnerIntentForProperty,
)
from rezio.user.authentication import JWTAuthentication, FirebaseAuthentication
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import (
    raise_invalid_data_exception,
    get_profile_object_by_role,
    add_unregistered_co_owner_as_registered,
    get_country_and_currency,
    get_investor_preferences,
    build_profile_permission_classes,
)
from rezio.user.models import (
    InvestorProfile,
    OnboardingQuestion,
    OnboardingAnswer,
    InvestorType,
    TermsAndConditions,
    UserAgreements,
    User,
    UserPrivacyPolicyAgreements,
)
from rezio.user.permissions import Is<PERSON><PERSON>enticatedInvestor, IsAuthenticatedAgent
from rezio.user.serializers import (
    InvestorProfileSerializer,
    OnboardingQuestionSerializer,
    OnboardingAnswerSerializer,
    InvestorProfileInformationSerializer,
    BasicInvestorProfileInfoSerializer,
    ViewInvestorProfileSerializer,
    ViewInvestorListSerializer,
)
from rezio.user.utils import (
    get_investor_role_object,
    get_default_investor_type_object,
    get_investor_profile_object,
    get_role_object,
    get_user_object,
    get_terms_and_condition_object,
    get_menu_settings_privacy_policy_object,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    ResourceNotFoundException,
    InternalServerException,
)
from rezio.utils.decorators import log_input_output, general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import PreferenceCategory

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class InvestorProfileViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    @log_input_output
    def get(self, request):
        """
        get investor profile details
        """
        try:
            investor_profile = InvestorProfile.objects.get(user=request.user)
        except InvestorProfile.DoesNotExist:
            logger.warning(f"Investor details not found")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Investor details not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Investor details not found"},
                }
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"InvestorProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

        serializer = InvestorProfileSerializer(instance=investor_profile)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Investor details fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )

    @log_input_output
    def post(self, request):
        """
        save investor profile details
        """
        try:
            if InvestorProfile.objects.filter(user=request.user).exists():
                logger.warning(f"Investor was already onboarded")
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={
                        KEY_MESSAGE: "Investor was already onboarded",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "Investor was already onboarded"
                        },
                    },
                )
            request.data["user_id"] = request.user.id
            serializer = InvestorProfileSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )

            # agree terms & conditions
            terms_and_conditions = get_terms_and_condition_object()
            if not UserAgreements.objects.filter(
                user=request.user, terms_and_conditions=terms_and_conditions
            ).exists():
                UserAgreements.objects.create(
                    user=request.user, terms_and_conditions=terms_and_conditions
                )

            # agree privacy policy
            privacy_policy = get_menu_settings_privacy_policy_object()
            if not UserPrivacyPolicyAgreements.objects.filter(
                user=request.user, privacy_policy=privacy_policy
            ).exists():
                UserPrivacyPolicyAgreements.objects.create(
                    user=request.user, privacy_policy=privacy_policy
                )

            serializer.validated_data.pop("agreed_to_terms", None)
            investor_profile = serializer.save(user=request.user)
            register_user = add_unregistered_co_owner_as_registered(investor_profile)
            currency_code = get_country_and_currency(
                str(request.user.primary_phone_number)
            )
            if not currency_code:
                currency_code = settings.DEFAULT_CURRENCY_CODE
            investor_profile.preferred_currency_code = currency_code
            investor_profile.save()
            if register_user:
                logger.info("Un-registered profile registered as co-owner")
            else:
                logger.info("Un-registered profile not found")
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Investor details are saved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except TermsAndConditions.DoesNotExist:
            message = "Terms and conditions URL not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in InvestorProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class InvestorOnboardingQuestionView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    @log_input_output
    def get(self, request):
        """
        get investor onboarding questions
        """
        investor_role = get_investor_role_object()
        investor_profile = get_investor_profile_object(request.user)
        investor_type = investor_profile.investor_type

        if not investor_type:
            # get default investor type to show default question list
            investor_type = get_default_investor_type_object()

        # get common question for investor
        common_investor_question = OnboardingQuestion.objects.filter(
            role=investor_role,
            investor_type__isnull=True,
            is_active=True,
            display_in_onboarding=True,
        )
        # get rest questions based on investor type

        investor_type_questions = OnboardingQuestion.objects.filter(
            investor_type=investor_type, is_active=True, display_in_onboarding=True
        )
        investor_questions = common_investor_question | investor_type_questions
        investor_questions = investor_questions.order_by("id")
        if investor_questions.exists():
            serializer = OnboardingQuestionSerializer(
                investor_questions, context={"user": request.user}, many=True
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Investor onboarding questions fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        else:
            message = "Investor onboarding questions not found"
            logger.warning(f"{message}")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: [],
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

    def check_investor_type(self, investor_type_id, user, investor_profile, response):
        try:
            investor_type = InvestorType.objects.get(id=investor_type_id)
        except InvestorType.DoesNotExist:
            message = "Investor type not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        # save investor type
        if investor_profile.investor_type != investor_type:
            investor_profile.investor_type = investor_type
            investor_profile.save()

            # delete all answered question as rest questions are listed based on investor type
            investor_role = get_investor_role_object()
            questions_ids = OnboardingQuestion.objects.filter(
                role=investor_role
            ).values_list("id", flat=True)
            OnboardingAnswer.objects.filter(
                user=user, onboarding_question_id__in=questions_ids
            ).delete()
            response["call_question_api"] = True

        return response, investor_profile

    @log_input_output
    def post(self, request):
        """
        save investor onboarding question answer
        """
        response = dict()
        investor_role = get_investor_role_object()
        user = request.user
        try:
            onboarding_question = OnboardingQuestion.objects.get(
                id=request.data.get("onboarding_question_id"), role=investor_role
            )
        except OnboardingQuestion.DoesNotExist:
            message = "Onboarding question not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if OnboardingAnswer.objects.filter(
            onboarding_question_id=request.data.get("onboarding_question_id"),
            user=user,
            is_skipped=False,
        ).exists():
            message = "This question has been answered already"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        onboarding_answer = OnboardingAnswer.objects.filter(
            onboarding_question_id=request.data.get("onboarding_question_id"),
            user=user,
            is_skipped=True,
        )
        if onboarding_answer.exists():
            onboarding_answer.delete()

        # validate this variable to check if question related to investor type was answered
        investor_type_id = request.data.pop("investor_type_id", None)
        investor_profile = get_investor_profile_object(user)
        if investor_type_id:
            response, investor_profile = self.check_investor_type(
                investor_type_id, user, investor_profile, response
            )

        if not investor_profile.investor_type:
            message = "You cannot proceed without answering the first question"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        serializer = OnboardingAnswerSerializer(
            data=request.data, context={"onboarding_question": onboarding_question}
        )
        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )
        serializer.save(user=user)

        # check if all questions are answered
        questions_with_answers = OnboardingQuestion.objects.filter(
            Q(investor_type=investor_profile.investor_type)
            | Q(investor_type__isnull=True),
            role=investor_role,
            is_active=True,
            display_in_onboarding=True,
        ).annotate(
            answer_count=Count(
                "onboardinganswer", filter=Q(onboardinganswer__user=user)
            )
        )
        questions_without_answer = questions_with_answers.filter(answer_count=0)
        if not questions_without_answer:
            investor_profile.onboarding_completed = True
            investor_profile.save()

        response.update(serializer.data)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Onboarding answer saved successfully",
                KEY_PAYLOAD: response,
                KEY_ERROR: {},
            },
        )

    @log_input_output
    def put(self, request):
        """
        update investor onboarding question answer
        """
        response = dict()
        investor_role = get_investor_role_object()
        user = request.user
        try:
            onboarding_question = OnboardingQuestion.objects.get(
                id=request.data.get("onboarding_question_id"), role=investor_role
            )
        except OnboardingQuestion.DoesNotExist:
            message = "Onboarding question details not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        try:
            answer = OnboardingAnswer.objects.get(
                onboarding_question=onboarding_question, user=user
            )
        except OnboardingAnswer.DoesNotExist:
            message = "Onboarding answer details not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        # validate this variable to check if question related to investor type was answered
        investor_type_id = request.data.pop("investor_type_id", None)
        investor_profile = get_investor_profile_object(user)
        if investor_type_id:
            response, investor_profile = self.check_investor_type(
                investor_type_id, user, investor_profile, response
            )

        if not investor_profile.investor_type:
            message = "You cannot proceed without answering the first question"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        serializer = OnboardingAnswerSerializer(
            answer,
            data=request.data,
            partial=True,
            context={"onboarding_question": onboarding_question},
        )
        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )
        serializer.save(user=request.user)

        # check if all questions are answered
        questions_with_answers = OnboardingQuestion.objects.filter(
            Q(investor_type=investor_profile.investor_type)
            | Q(investor_type__isnull=True),
            role=investor_role,
        ).annotate(
            answer_count=Count(
                "onboardinganswer", filter=Q(onboardinganswer__user=user)
            )
        )
        questions_without_answer = questions_with_answers.filter(answer_count=0)
        if not questions_without_answer.exists():
            investor_profile.onboarding_completed = True
            investor_profile.save()

        response.update(serializer.data)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Onboarding answer saved successfully",
                KEY_PAYLOAD: response,
                KEY_ERROR: {},
            },
        )


class SkipInvestorOnboardingQuestionView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    def post(self, request):
        try:
            investor_role = get_investor_role_object()
            investor_profile = get_investor_profile_object(request.user)
            investor_type = investor_profile.investor_type
            if not investor_type:
                investor_type = get_default_investor_type_object()
                investor_profile.investor_type = investor_type

            answered__questions = OnboardingAnswer.objects.filter(
                user=request.user, onboarding_question__role=investor_role
            ).values_list("onboarding_question_id", flat=True)

            skipped_questions = (
                OnboardingQuestion.objects.filter(
                    Q(investor_type=investor_type) | Q(investor_type__isnull=True),
                    role=investor_role,
                )
                .exclude(id__in=answered__questions)
                .values_list("id", flat=True)
            )

            skipped_answers = (
                OnboardingAnswer(
                    user=request.user,
                    onboarding_question_id=question_id,
                    is_skipped=True,
                )
                for question_id in skipped_questions
            )

            investor_profile.onboarding_completed = True
            investor_profile.save()
            OnboardingAnswer.objects.bulk_create(skipped_answers)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Skipped onboarding questions successfully",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"Error in SkipInvestorOnboardingQuestionView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class InvestorProfileInformationViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    def put(self, request):
        try:
            investor_profile = get_investor_profile_object(request.user)
            serializer = InvestorProfileInformationSerializer(
                investor_profile, data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Profile updated successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"InvestorProfileInformationViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def get(self, request):
        try:
            investor_profile = get_investor_profile_object(request.user)
            serializer = InvestorProfileInformationSerializer(investor_profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Investor profile details fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"InvestorProfileInformationViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class InvestorPreferencesViewSet(APIView):
    """
    An API to get investor preferences
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    def get(self, request):
        try:
            investor_profile = get_investor_profile_object(request.user)

            investor_preferences = get_investor_preferences(investor_profile)

            if investor_preferences.exists():
                investor_preferences_serializer = OnboardingQuestionSerializer(
                    investor_preferences, context={"user": request.user}, many=True
                )
                investor_profile_serializer = BasicInvestorProfileInfoSerializer(
                    investor_profile
                )
                payload = {
                    "user": investor_profile_serializer.data,
                    "preferred_currency_code": investor_profile.preferred_currency_code,
                    "profile_public_view": investor_profile.profile_public_view,
                    "preferences": investor_preferences_serializer.data,
                    "preferences_category": [
                        choice[0] for choice in PreferenceCategory.choices
                    ],
                }
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Investor preferences fetched successfully",
                        KEY_PAYLOAD: payload,
                        KEY_ERROR: {},
                    },
                )
            else:
                message = "Investor preferences not found"
                logger.warning(f"{message}")
                raise ResourceNotFoundException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: [],
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"InvestorPreferencesViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class ViewInvestorProfileViewSet(APIView):
    """
    An API to get investor profile
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, user_id=None):
        try:
            user_role_obj = get_investor_role_object()
            user = request.user
            if user_id:
                user_role = request.query_params.get("user_role", None)
                if not user_role:
                    raise_invalid_data_exception("Role not found")
                user_role_obj = get_role_object(user_role)
                if user_id == request.user.pk and user_role == INVESTOR:
                    raise_invalid_data_exception("Invalid data sent")
                if user_role_obj.name == INVESTOR:
                    raise_invalid_data_exception(
                        "User is not authorised to view this profile"
                    )
                user = User.objects.filter(id=user_id)
                if not user.exists():
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "User profile not found.",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "The profile you are looking for does not exist on Rezio anymore."
                            },
                        }
                    )
                user = user.first()
            investor_profile = InvestorProfile.objects.filter(user=user)
            if not investor_profile.exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "User profile not found.",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "The profile you are looking for does not exist on Rezio anymore."
                        },
                    }
                )
            investor_profile = investor_profile.first()
            serializer = ViewInvestorProfileSerializer(
                investor_profile,
                context={
                    "user": user,
                    "role": user_role_obj.name,
                    "request": request,
                    "viewing_role": INVESTOR,
                },
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Investor profile fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"ViewInvestorProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class InvestorPropertyListAPIView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get(self, request, user_id=None):
        try:
            self_user = False
            viewer_role = request.query_params.get("user_role")
            role_obj = get_role_object(viewer_role)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            if not user_id and viewer_role == INVESTOR:
                user = request.user
                self_user = True
            else:
                if user_id == request.user.pk and role_obj.name == INVESTOR:
                    raise_invalid_data_exception(
                        "An investor cannot view other investors profile"
                    )
                if viewer_role == AGENT and not user_id:
                    raise_invalid_data_exception("Invalid data sent")
                user = get_user_object(user_id)

            investor_profile = get_investor_profile_object(user)
            properties = Property.objects.filter(
                owner=investor_profile,
                owner_verified=True,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            if not self_user:
                properties = properties.exclude(
                    owner_intent=OwnerIntentForProperty.NOT_FOR_SALE
                )

            properties_with_co_owner = PropertyCoOwner.objects.filter(
                co_owner=investor_profile, is_associated=True
            ).values_list("property_id", flat=True)
            properties_with_cow_owner_queryset = Property.objects.filter(
                id__in=properties_with_co_owner,
                owner_verified=True,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            if not self_user:
                properties_with_cow_owner_queryset = (
                    properties_with_cow_owner_queryset.exclude(
                        owner_intent=OwnerIntentForProperty.NOT_FOR_SALE
                    )
                )

            all_properties = properties | properties_with_cow_owner_queryset

            all_properties = (
                all_properties.select_related("propertyfinancialdetails")
                .annotate(
                    preferred_currency_code=Value(
                        viewer_profile.preferred_currency_code, output_field=CharField()
                    ),
                    property_currency_code=F(
                        "propertyfinancialdetails__property_currency_code"
                    ),
                    asking_price=F("propertyfinancialdetails__asking_price"),
                    original_price=F("propertyfinancialdetails__original_price"),
                    valuation=F("propertyfinancialdetails__valuation"),
                    annual_rent=F("propertyfinancialdetails__annual_rent"),
                )
                .order_by("-created_ts")
            )

            paginator = StandardResultsSetPagination()
            paginated_queryset = paginator.paginate_queryset(all_properties, request)
            serializer = PropertyPortfolioViewSerializer(
                paginated_queryset,
                many=True,
                context={
                    "viewing": user,
                    "viewing_role": INVESTOR,
                    "self_user": self_user,
                    "viewer_role": viewer_role,
                    "viewer": request.user,
                },
            )
            return paginator.get_paginated_response(serializer.data)

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in GetInvestorsListView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"{message} - {error}",
                    },
                }
            )


class GetInvestorsListView(APIView):
    """
    An API to get investor list excluding the user requesting
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    # permission_classes = [IsAuthenticatedInvestor]

    def get(self, request):
        try:
            query = request.query_params.get("search_text", None)
            property_id = request.query_params.get("property_id", None)

            investors = InvestorProfile.objects.select_related("user")

            if property_id:
                investors = investors.exclude(user=request.user)
                co_owner_investors = (
                    PropertyCoOwner.objects.filter(property_id=property_id)
                    .exclude(co_owner__isnull=True)
                    .values_list("co_owner", flat=True)
                )

                logger.info(f"Co-owner Investors IDs: {list(co_owner_investors)}")

                # Exclude the co-owners from the investors list
                investors = investors.exclude(id__in=co_owner_investors)
                logger.info(
                    f"Remaining Investor IDs: {list(investors.values_list('id', flat=True))}"
                )

            if query:
                # Apply filters using Q objects
                investors = investors.filter(
                    Q(name__icontains=query)
                    | Q(email__icontains=query)
                    | Q(user__primary_phone_number__icontains=query)
                )

            else:
                investors = investors.order_by("name")

            paginator = StandardResultsSetPagination()
            result_page = paginator.paginate_queryset(investors, request)

            # Serialize the paginated data
            serializer = ViewInvestorListSerializer(result_page, many=True)

            return paginator.get_paginated_response(serializer.data)

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"GetInvestorsListView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class InvestorProfilePublicViewToggleViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    @general_exception_handler
    @log_input_output
    def post(self, request):
        """
        enable/disable investor profile public view
        """
        enable_profile_public_view = request.data.get(
            "enable_profile_public_view", None
        )
        if type(enable_profile_public_view) != bool:
            raise_invalid_data_exception("Invalid data sent")
        investor_profile = get_investor_profile_object(request.user)
        if enable_profile_public_view and investor_profile.profile_public_view:
            raise_invalid_data_exception("Profile is already public")
        if not enable_profile_public_view and not investor_profile.profile_public_view:
            raise_invalid_data_exception("Profile public view is already disabled")
        if enable_profile_public_view:
            investor_profile.profile_public_view = True
            sub_text = "enabled"
        else:
            investor_profile.profile_public_view = False
            sub_text = "disabled"
        investor_profile.save()
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Investor profile sharing {sub_text} successfully",
                KEY_PAYLOAD: {
                    "user_id": request.user.id,
                },
                KEY_ERROR: {},
            },
        )


class ViewInvestorPreferencesViewSet(APIView):
    """
    An API to get self/other investor preferences
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    @general_exception_handler
    def get(self, request, user_id=None):
        user_role = request.query_params.get("user_role")
        if user_id == request.user.id and user_role == INVESTOR:
            raise_invalid_data_exception("Invalid request")
        elif user_id and user_role == INVESTOR:
            raise_invalid_data_exception("Invalid request")
        elif not user_id and user_role != INVESTOR:
            raise_invalid_data_exception("Invalid request")
        if user_id:
            user = get_user_object(user_id)
            investor_profile = get_investor_profile_object(user)
        else:
            user = request.user
            investor_profile = get_investor_profile_object(user)

        investor_preferences = get_investor_preferences(investor_profile)

        if investor_preferences.exists():
            investor_preferences_serializer = OnboardingQuestionSerializer(
                investor_preferences, context={"user": user}, many=True
            )
            payload = {
                "preferences": investor_preferences_serializer.data,
                "preferences_category": [
                    choice[0] for choice in PreferenceCategory.choices
                ],
            }
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Investor preferences fetched successfully",
                    KEY_PAYLOAD: payload,
                    KEY_ERROR: {},
                },
            )
        else:
            message = "Investor preferences not found"
            logger.warning(f"{message}")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: [],
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
