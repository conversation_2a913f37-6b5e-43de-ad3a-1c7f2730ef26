import logging
import traceback
from datetime import date, datetime

from django.conf import settings
from django.db import transaction, IntegrityError
from django.db.models import (
    Q,
    Count,
    Case,
    When,
    Value,
    <PERSON>r<PERSON>ield,
    F,
    BigIntegerField,
    EmailField,
    BooleanField,
    DateTimeField,
    Prefetch,
)
from django.db.models.functions import Cast
from django.db.models.signals import post_save
# from firebase_admin import auth
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView

from rezio.properties.models import (
    AgentAssociatedProperty,
    Property,
    PropertyCoOwner,
)
from rezio.properties.serializers.property_details_serializer import (
    PropertyPortfolioViewSerializer,
    AIAgentPortfolioViewSerializer,
)
from rezio.properties.services.property_service import recalculate_unlocked_properties
from rezio.properties.services.property_service import (
    remove_floor_payment_plan_data_for_agent,
    validate_serializer,
)
from rezio.properties.text_choices import (
    OwnerIntentFor<PERSON>roperty,
    PropertyPublishStatus,
    UserRequestActions,
    PropertyAgentType,
    RequestType,
)
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    PRESIGNED_POST_STRUCTURES,
    AGENT_LICENSE_FILE,
    KEY,
    DD_MMM_YYYY,
    PROFILE_PHOTO,
)
from rezio.user.authentication import (
    JWTTokenAuthentication,
    JWTAuthentication,
    FirebaseAuthentication,
)
from rezio.user.constants import AGENT, INVESTOR
from rezio.user.helper import (
    raise_invalid_data_exception,
    get_profile_object_by_role,
    get_country_and_currency,
)
from rezio.user.models import (
    AgentProfile,
    Agency,
    OnboardingAnswer,
    OnboardingQuestion,
    Agent,
    TermsAndConditions,
    UserAgreements,
    User,
    UserPrivacyPolicyAgreements,
)
from rezio.user.permissions import IsAuthenticatedAgent, IsAuthenticatedInvestor
from rezio.user.serializers import (
    AgentProfileSerializer,
    AgencySerializer,
    OnboardingQuestionSerializer,
    OnboardingAnswerSerializer,
    AgentDataSerializer,
    AgentProfileInformationSerializer,
    SaveAgentProfileInformationSerializer,
    AgentPersonalInformationSerializer,
    BasicAgentProfileInfoSerializer,
    ViewAgentProfileSerializer,
    AgentCommissionSerializer,
    SaveAgentVerificationDetailsSerializer,
    ViewAgentListSerializer,
    RegisterAgentProfileSerializer,
)
from rezio.user.text_choices import (
    UserDataSource,
    AgentOnboardingStatus,
    AgentInvitationStatus,
    AgentSubscriptionPlanChoices,
)
from rezio.user.utils import (
    get_agent_role_object,
    get_agent_profile_object,
    capitalize_first_letters,
    get_user_object,
    get_role_object,
    get_investor_role_object,
    get_terms_and_condition_object,
    get_menu_settings_privacy_policy_object,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    ResourceNotFoundException,
    InternalServerException,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import (
    AgentLicenseVerificationStatus,
    AgentWorkingType,
    PreferenceCategory,
)

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class AgentProfileViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @general_exception_handler
    def get(self, request):
        """
        get agent profile details
        """
        try:
            agent_profile = AgentProfile.objects.get(user=request.user)
        except AgentProfile.DoesNotExist:
            logger.warning(f"Agent details not found")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Agent details not found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: "Agent details not found"},
                }
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"AgentProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

        serializer = AgentProfileSerializer(instance=agent_profile)
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent details fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )

    @transaction.atomic
    def post(self, request):
        """
        save agent profile details
        """
        try:
            if AgentProfile.objects.filter(user=request.user).exists():
                logger.warning(f"Agent was already onboarded")
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={
                        KEY_MESSAGE: "Agent was already onboarded",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Agent was already onboarded"},
                    },
                )

            serializer = AgentProfileSerializer(
                data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            user = request.user
            request.data["user_id"] = user.id
            agency_name = request.data.get("agency_name", None)
            agency_country_code = request.data.get("agency_country_code", None)

            # update user with primary and secondary phone number
            primary_phone_number = request.data.get("primary_phone_number", None)
            secondary_phone_number = request.data.get("secondary_phone_number", None)
            if (
                primary_phone_number
                and not user.primary_phone_number == primary_phone_number
            ):
                user.primary_phone_number = primary_phone_number
                user.save()
                # update number on firebase
                # auth.update_user(user.id, phone_number=primary_phone_number)
            if secondary_phone_number:
                user.secondary_phone_number = secondary_phone_number
                user.save()

            # save agency name
            agency = None
            if agency_name:
                agency_name = capitalize_first_letters(agency_name)
                agency = Agency.objects.filter(
                    name__iexact=agency_name, country_code=agency_country_code
                )
                if agency.exists():
                    agency = agency.first()
                else:
                    agency = Agency.objects.create(
                        name=agency_name, country_code=agency_country_code
                    )

            # agree terms & conditions
            terms_and_conditions = get_terms_and_condition_object()
            if not UserAgreements.objects.filter(
                user=request.user, terms_and_conditions=terms_and_conditions
            ).exists():
                UserAgreements.objects.create(
                    user=request.user, terms_and_conditions=terms_and_conditions
                )

            # agree privacy policy
            privacy_policy = get_menu_settings_privacy_policy_object()
            if not UserPrivacyPolicyAgreements.objects.filter(
                user=request.user, privacy_policy=privacy_policy
            ).exists():
                UserPrivacyPolicyAgreements.objects.create(
                    user=request.user, privacy_policy=privacy_policy
                )

            agent_profile = serializer.save(
                user=request.user,
                agency=agency,
                created_by=request.user,
                created_by_role=get_role_object(AGENT),
                data_source=UserDataSource.LICENSE_DATA_BASE,
                license_verification_status=AgentLicenseVerificationStatus.LICENSE_VERIFIED,
            )
            if request.data.get("profile_photo_url"):
                s3_client = S3Client()
                try:
                    # Generate a unique key for the profile photo
                    profile_photo_key = PRESIGNED_POST_STRUCTURES.get(
                        PROFILE_PHOTO, {}
                    ).get(KEY, "")
                    profile_photo_key = profile_photo_key.format(
                        user_id=request.user.id,
                        role_name=AGENT,
                        filename=f"{request.user.id}_{datetime.now().strftime(DD_MMM_YYYY)}_{AGENT}_{PROFILE_PHOTO}",
                    )
                    # Download and upload the photo to S3
                    upload_result = s3_client.download_and_upload_file(
                        url=request.data.get("profile_photo_url"),
                        s3_key=profile_photo_key,
                    )

                    # Update agent profile with the new photo details
                    agent_profile.profile_photo_key = upload_result["key"]
                    agent_profile.profile_photo_name = request.data.get(
                        "profile_photo_name"
                    )
                    agent_profile.profile_photo_size = upload_result["size"]
                except Exception as error:
                    logger.error(f"Failed to process profile photo: {str(error)}")
                    # Continue without the profile photo if there's an error
                    pass

            db_agent = Agent.objects.get(
                brn=request.data.get("brn"),
                license_country_code=request.data.get("license_country_code", "AE"),
            )
            db_agent.agent_onboarding_status = AgentOnboardingStatus.ONBOARDED
            db_agent.save()

            currency_code = get_country_and_currency(
                str(request.user.primary_phone_number)
            )
            if not currency_code:
                currency_code = settings.DEFAULT_CURRENCY_CODE
            agent_profile.preferred_currency_code = currency_code
            agent_profile.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent details are saved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except TermsAndConditions.DoesNotExist:
            message = "Terms and conditions URL not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in AgentProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class BRNDetailsView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def get(self, request):
        """
        fetch brn details from DLD
        """
        try:
            brn = request.query_params.get("brn")

            # TODO: Once FE is ready with the changes, need to remove default values
            license_country_code = request.query_params.get("license_cc", "AE")
            license_country_name = request.query_params.get("license_cn", "UAE")

            if not brn:
                message = "License number is required."
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            if not license_country_code:
                message = "Country code is required"
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            if not license_country_name:
                message = "Country name is required"
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            if (
                AgentProfile.objects.filter(
                    brn=brn,
                    license_country_code=license_country_code,
                )
                .exclude(user=request.user)
                .exists()
            ):
                message = "Agent with this license number already exists."
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            # Check if the BRN exists in the Agency model
            # todo : change this logic once DLD API's are live again
            try:
                agent_data = Agent.objects.get(
                    brn=brn, license_country_code=license_country_code
                )
                agent_serializer = AgentDataSerializer(agent_data)
                agent_serializer_data = agent_serializer.data
                agent_serializer_data["license_country"] = license_country_name

                license_end_date = agent_serializer.data.get("license_end_date")
                is_license_expired = False

                if license_end_date:
                    license_end_date = datetime.strptime(
                        license_end_date, "%Y-%m-%dT%H:%M:%SZ"
                    ).date()
                else:
                    message = f"License end date is not found for license number {brn}"
                    logger.error(message)
                    is_license_expired = True

                if license_end_date and license_end_date < date.today():
                    message = f"License number {brn} is expired"
                    logger.error(message)
                    is_license_expired = True

                agent_serializer_data["is_license_expired"] = is_license_expired
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "License details fetched successfully from database",
                        KEY_PAYLOAD: agent_serializer_data,
                        KEY_ERROR: {},
                    },
                )
            except Agent.DoesNotExist:
                message = f"License number {brn} is not found! Please enter a valid License Number."
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: message,
                        },
                    }
                )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = f"License number {brn} is not found! Please enter a valid License Number."
            logger.error(f"Error in BRNDetailsView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class AgencyView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def get(self, request):
        """
        get paginated list of agencies
        """
        try:
            paginator = StandardResultsSetPagination()
            search = request.query_params.get("name")
            if search:
                agencies = Agency.objects.filter(name__icontains=search)
            else:
                agencies = Agency.objects.all()
            paginated_queryset = paginator.paginate_queryset(agencies, request)

            serializer = AgencySerializer(paginated_queryset, many=True)
            return paginator.get_paginated_response(serializer.data)
        except Exception as error:
            message = "Unable to fetch agencies"
            logger.error(f"Error in AgencyView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class AgentOnboardingQuestionView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def get(self, request):
        """
        get agent onboarding question details
        """
        get_agent_profile_object(request.user)
        agent_role = get_agent_role_object()
        agent_questions = OnboardingQuestion.objects.filter(
            role=agent_role, is_active=True, display_in_onboarding=True
        ).order_by("id")
        if agent_questions.exists():
            serializer = OnboardingQuestionSerializer(
                agent_questions, context={"user": request.user}, many=True
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent onboarding questions fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        else:
            message = "Agent onboarding questions not found"
            logger.warning(f"{message}")
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: [],
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

    def post(self, request):
        """
        save agent onboarding question answer
        """
        agent_role = get_agent_role_object()
        agent_profile = get_agent_profile_object(request.user)
        try:
            onboarding_question = OnboardingQuestion.objects.get(
                id=request.data.get("onboarding_question_id"), role=agent_role
            )
        except OnboardingQuestion.DoesNotExist:
            message = "Onboarding question not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        if OnboardingAnswer.objects.filter(
            onboarding_question_id=request.data.get("onboarding_question_id"),
            user=request.user,
            is_skipped=False,
        ).exists():
            message = "This question has been answered already"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        serializer = OnboardingAnswerSerializer(
            data=request.data, context={"onboarding_question": onboarding_question}
        )
        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )

        onboarding_answer = OnboardingAnswer.objects.filter(
            onboarding_question_id=request.data.get("onboarding_question_id"),
            user=request.user,
            is_skipped=True,
        )

        if onboarding_answer.exists():
            onboarding_answer.delete()

        serializer.save(user=request.user)

        # check if all questions are answered
        questions_with_answers = OnboardingQuestion.objects.filter(
            role=agent_role, is_active=True, display_in_onboarding=True
        ).annotate(
            answer_count=Count(
                "onboardinganswer", filter=Q(onboardinganswer__user=request.user)
            )
        )
        questions_without_answer = questions_with_answers.filter(answer_count=0)
        if not questions_without_answer.exists():
            agent_profile.onboarding_completed = True
            agent_profile.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Onboarding answer saved successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )

    def put(self, request):
        """
        update agent onboarding question answer
        """
        agent_role = get_agent_role_object()
        agent_profile = get_agent_profile_object(request.user)
        try:
            onboarding_question = OnboardingQuestion.objects.get(
                id=request.data.get("onboarding_question_id"),
                role=get_agent_role_object(),
            )
        except OnboardingQuestion.DoesNotExist:
            message = "Onboarding question details not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        try:
            answer = OnboardingAnswer.objects.get(
                onboarding_question=onboarding_question, user=request.user
            )
        except OnboardingAnswer.DoesNotExist:
            message = "Onboarding answer details not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        serializer = OnboardingAnswerSerializer(
            answer,
            data=request.data,
            context={"onboarding_question": onboarding_question},
        )
        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )
        serializer.save(user=request.user)

        # check if all questions are answered
        questions_with_answers = OnboardingQuestion.objects.filter(
            role=agent_role
        ).annotate(
            answer_count=Count(
                "onboardinganswer", filter=Q(onboardinganswer__user=request.user)
            )
        )
        questions_without_answer = questions_with_answers.filter(answer_count=0)
        if not questions_without_answer.exists():
            agent_profile.onboarding_completed = True
            agent_profile.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Onboarding answer saved successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class SkipAgentOnboardingQuestionView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def post(self, request):
        try:
            agent_role = get_agent_role_object()
            agent_profile = get_agent_profile_object(request.user)
            answered__questions = OnboardingAnswer.objects.filter(
                user=request.user, onboarding_question__role=agent_role
            ).values_list("onboarding_question_id", flat=True)

            skipped_questions = (
                OnboardingQuestion.objects.filter(role=agent_role)
                .exclude(id__in=answered__questions)
                .values_list("id", flat=True)
            )

            skipped_answers = (
                OnboardingAnswer(
                    user=request.user,
                    onboarding_question_id=question_id,
                    is_skipped=True,
                )
                for question_id in skipped_questions
            )
            OnboardingAnswer.objects.bulk_create(skipped_answers)
            agent_profile.onboarding_completed = True
            agent_profile.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Skipped onboarding questions successfully",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(
                f"Error in SkipAgentOnboardingQuestionView - {message} - {error}"
            )
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class AgentProfileInformationViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def put(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)
            serializer = SaveAgentProfileInformationSerializer(
                agent_profile, data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            user = request.user
            validated_data = serializer.validated_data
            # update user fields
            user.emirates_id = validated_data.get("emirates_id")
            user.passport_number = validated_data.get("passport_number")
            user.save()
            # update agent profile fields
            agent_profile.pronouns = validated_data.get("pronouns")
            agent_profile.bio = validated_data.get("bio")
            agent_profile.email = validated_data.get("email")
            agent_profile.updated_by = request.user
            agent_profile.updated_by_role = get_role_object(AGENT)
            agent_profile.save()
            agen_profile_serializer = AgentProfileInformationSerializer(agent_profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Profile updated successfully",
                    KEY_PAYLOAD: agen_profile_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"InvestorProfileInformationViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def get(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)
            serializer = AgentProfileInformationSerializer(agent_profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent profile details fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"AgentProfileInformationViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class AgentPersonalInformationViewSet(APIView):
    """
    An API to update personal information of a user
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @transaction.atomic
    def put(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)

            if (
                agent_profile.license_verification_status
                == AgentLicenseVerificationStatus.LICENCE_VERIFICATION_PENDING
            ):
                message = "The status of profile is verification pending"
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            serializer = AgentPersonalInformationSerializer(
                agent_profile, data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            validated_data = serializer.validated_data

            # update user fields
            user = request.user
            primary_phone_number = validated_data.get("primary_phone_number")
            secondary_phone_number = validated_data.get("secondary_phone_number")

            if (
                primary_phone_number
                and not user.primary_phone_number == primary_phone_number
            ):
                primary_phone_number = primary_phone_number.as_e164
                user.primary_phone_number = primary_phone_number
                user.save()

                # update number on firebase
                # auth.update_user(user.id, phone_number=primary_phone_number)

            user.secondary_phone_number = secondary_phone_number
            user.save()

            # save agency name
            agency = None
            agency_name = validated_data.get("agency_name")
            agency_country_code = request.data.get("agency_country_code", None)

            if agency_name:
                agency_name = capitalize_first_letters(agency_name)
                agency = Agency.objects.filter(
                    name__iexact=agency_name, country_code=agency_country_code
                )
                if agency.exists():
                    agency = agency.first()
                else:
                    agency = Agency.objects.create(
                        name=agency_name, country_code=agency_country_code
                    )

            license_file = validated_data.get("license_file")
            is_license_deleted = validated_data.get("is_license_deleted")
            s3_client = S3Client()

            if not is_license_deleted and license_file:
                license_file_media_key = PRESIGNED_POST_STRUCTURES.get(
                    AGENT_LICENSE_FILE, {}
                ).get(KEY, "")

                license_file_media_key = license_file_media_key.format(
                    user_id=request.user.id,
                    filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_agent_license",
                )

                # Upload license to S3
                s3_client.upload_file(license_file, license_file_media_key)

                license_file_key = license_file_media_key
                license_file_size = license_file.size
                license_file_name = license_file.name
            elif is_license_deleted and not license_file:
                license_file_key = None
                license_file_size = None
                license_file_name = None
                if agent_profile.license_file_key:
                    s3_client.delete_file(agent_profile.license_file_key)
            else:
                license_file_key = agent_profile.license_file_key
                license_file_size = agent_profile.license_file_size
                license_file_name = agent_profile.license_file_name

            if validated_data.get("is_verification_required"):
                license_verification_status = (
                    AgentLicenseVerificationStatus.LICENCE_VERIFICATION_PENDING
                )
            else:
                license_verification_status = agent_profile.license_verification_status

            serializer.save(
                agency=agency,
                updated_by=request.user,
                updated_by_role=get_role_object(AGENT),
                license_verification_status=license_verification_status,
                license_file_key=license_file_key,
                license_file_size=license_file_size,
                license_file_name=license_file_name,
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Profile updated successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"AgentPersonalInformationViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class AgentPreferencesViewSet(APIView):
    """
    An API to get agent preferences
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def get(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)
            agent_role = get_agent_role_object()
            agent_preferences = OnboardingQuestion.objects.filter(
                role=agent_role, is_main_preference__isnull=False, is_active=True
            ).order_by("preference_order")

            if agent_preferences.exists():
                agent_preferences_serializer = OnboardingQuestionSerializer(
                    agent_preferences, context={"user": request.user}, many=True
                )
                agent_profile_serializer = BasicAgentProfileInfoSerializer(
                    agent_profile
                )

                social_links_added = agent_profile.has_social_links()

                payload = {
                    "user": agent_profile_serializer.data,
                    "commission_percentage": agent_profile.commission_percentage,
                    "preferred_currency_code": agent_profile.preferred_currency_code,
                    "preferences": agent_preferences_serializer.data,
                    "social_links_added": social_links_added,
                    "preferences_category": [
                        choice[0] for choice in PreferenceCategory.choices
                    ],
                }

                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Agent preferences fetched successfully",
                        KEY_PAYLOAD: payload,
                        KEY_ERROR: {},
                    },
                )
            else:
                message = "Agent preferences not found"
                logger.warning(f"{message}")
                raise ResourceNotFoundException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: [],
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"AgentPreferencesViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class ViewAgentProfileViewSet(APIView):
    """
    An API to get agent profile
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request, user_id=None):
        try:
            user_role_obj = get_agent_role_object()
            user = request.user
            if user_id:
                user_role = request.query_params.get("user_role", None)
                if not user_role:
                    raise_invalid_data_exception("Role not found")
                user_role_obj = get_role_object(user_role)
                if user_id == request.user.pk and user_role == AGENT:
                    raise_invalid_data_exception("Invalid data sent")
                user = User.objects.filter(id=user_id)
                if not user.exists():
                    raise InvalidSerializerDataException(
                        {
                            KEY_MESSAGE: "User profile not found.",
                            KEY_PAYLOAD: {},
                            KEY_ERROR: {
                                KEY_ERROR_MESSAGE: "The profile you are looking for does not exist on Rezio anymore."
                            },
                        }
                    )
                user = user.first()
            agent_profile = AgentProfile.objects.filter(user=user)
            if not agent_profile.exists():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "User profile not found.",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: "The profile you are looking for does not exist on Rezio anymore."
                        },
                    }
                )
            agent_profile = agent_profile.first()
            serializer = ViewAgentProfileSerializer(
                agent_profile,
                context={
                    "user": user,
                    "role": user_role_obj.name,
                    "request": request,
                    "viewing_role": AGENT,
                },
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent profile fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"ViewAgentProfileViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class AgentPropertyListAPIView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def window_f(self, field):
        """Helper function to handle F() expressions in annotations"""
        return F(field)

    def get(self, request, user_id=None):
        try:
            self_user = False
            unlocked_ids = None  # unlocked properties
            viewer_role = request.query_params.get("user_role")
            role_obj = get_role_object(viewer_role)
            viewer_profile = get_profile_object_by_role(request.user, role_obj)
            if not user_id and viewer_role == AGENT:
                user = request.user
                self_user = True
                agent_profile = get_agent_profile_object(user.pk)
            else:
                if not user_id:
                    raise_invalid_data_exception("User not found")
                if user_id == request.user.pk and viewer_role == AGENT:
                    raise_invalid_data_exception("Invalid data sent")
                user = get_user_object(user_id)
                agent_profile = get_agent_profile_object(user_id)

            # Check subscription status
            is_basic_subscription = (
                agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC
            )

            agent_associated_properties = (
                AgentAssociatedProperty.objects.filter(
                    action_status=UserRequestActions.ACCEPTED,
                    is_associated=True,
                    agent_profile=agent_profile,
                    is_request_expired=False,
                )
                .order_by("-created_ts")
                .values_list("property_id", flat=True)
            )

            properties = (
                Property.objects.filter(
                    id__in=agent_associated_properties,
                    property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                    is_archived=False,
                )
                .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
                .order_by("-created_ts")
            )

            if is_basic_subscription:
                unlocked_ids = agent_profile.unlocked_properties.values_list(
                    "id", flat=True
                )
                if not self_user:
                    properties = properties.filter(id__in=unlocked_ids)

            properties = properties.select_related("propertyfinancialdetails").annotate(
                preferred_currency_code=Value(
                    viewer_profile.preferred_currency_code, output_field=CharField()
                ),
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )

            paginator = StandardResultsSetPagination()
            paginated_queryset = paginator.paginate_queryset(properties, request)
            serializer = PropertyPortfolioViewSerializer(
                paginated_queryset,
                many=True,
                context={
                    "viewing": user,
                    "viewing_role": AGENT,
                    "self_user": self_user,
                    "agent_profile": agent_profile,
                    "viewer_role": viewer_role,
                    "viewer": request.user,
                    "is_basic_subscription": is_basic_subscription,
                    "page": request.query_params.get("page", 1),
                    "paginated_queryset_list": list(paginated_queryset),
                    "unlocked_ids": unlocked_ids,
                },
            )
            return paginator.get_paginated_response(serializer.data)

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"Error in AgentPropertyListAPIView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class AgentCommissionViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def get(self, request):
        """
        get agent commission
        """
        try:
            agent_profile = get_agent_profile_object(request.user)
            serializer = AgentCommissionSerializer(agent_profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent commission fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unable to fetch agencies"
            logger.error(f"Error in AgencyView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )

    def post(self, request):
        """
        get agent commission
        """
        try:
            serializer = AgentCommissionSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            agent_profile = get_agent_profile_object(request.user)
            agent_profile.commission_percentage = serializer.validated_data.get(
                "commission_percentage"
            )
            agent_profile.updated_by = request.user
            agent_profile.updated_by_role = get_role_object(AGENT)
            agent_profile.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent commission saved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unable to fetch agencies"
            logger.error(f"Error in AgentCommissionViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class GetAgentsListView(APIView):
    """
    An API to get agent list excluding the user requesting
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedInvestor]

    def get(self, request):
        try:
            query = request.query_params.get("search_text", None)
            is_exclusive = (
                True if request.query_params.get("is_exclusive") == "true" else False
            )

            investor_role = get_investor_role_object()
            previously_associated_agents = (
                AgentAssociatedProperty.objects.filter(
                    created_by=request.user, created_by_role=investor_role
                )
                .values("agent_profile")
                .annotate(association_count=Count("agent_profile"))
                .order_by("-association_count")[:3]  # Top 3 most associated
                .values_list("agent_profile", flat=True)
            )

            agent_profile = AgentProfile.objects.select_related("user", "agency")

            all_agents = agent_profile.annotate(
                agent_id=Cast("id", output_field=BigIntegerField()),
                agent_user_id=Cast("user_id", output_field=CharField()),
                agent_name=Cast("name", output_field=CharField()),
                agent_profile_photo_key=Cast(
                    "profile_photo_key", output_field=CharField()
                ),
                agent_email=Cast("email", output_field=EmailField()),
                primary_phone_number=F("user__primary_phone_number"),
                agent_gender=Cast("gender", output_field=CharField()),
                agent_brn=Cast("brn", output_field=CharField()),
                agency_name=F("agency__name"),
                is_previously_associated=Case(
                    When(id__in=previously_associated_agents, then=Value(True)),
                    default=Value(False),
                    output_field=BooleanField(),
                ),
                agent_license_verification_status=Cast(
                    "license_verification_status", output_field=CharField()
                ),
                agent_license_country_code=Cast(
                    "license_country_code", output_field=CharField()
                ),
                invitation_status=Value(
                    AgentInvitationStatus.ALREADY_PRESENT, output_field=CharField()
                ),
                agent_working_type=Cast("working_type", output_field=CharField()),
                agent_created_ts=Cast("created_ts", output_field=DateTimeField()),
            ).values(
                "agent_id",
                "agent_user_id",
                "agent_name",
                "agent_profile_photo_key",
                "agent_email",
                "primary_phone_number",
                "agent_gender",
                "agent_brn",
                "agency_name",
                "is_previously_associated",
                "agent_license_verification_status",
                "agent_license_country_code",
                "invitation_status",
                "agent_working_type",
                "agent_created_ts",
            )

            duplicate_agents = (
                agent_profile.filter(id__in=previously_associated_agents)
                .annotate(
                    agent_id=Cast("id", output_field=BigIntegerField()),
                    agent_user_id=Cast("user_id", output_field=CharField()),
                    agent_name=Cast("name", output_field=CharField()),
                    agent_profile_photo_key=Cast(
                        "profile_photo_key", output_field=CharField()
                    ),
                    agent_email=Cast("email", output_field=EmailField()),
                    primary_phone_number=F("user__primary_phone_number"),
                    agent_gender=Cast("gender", output_field=CharField()),
                    agent_brn=Cast("brn", output_field=CharField()),
                    agency_name=F("agency__name"),
                    is_previously_associated=Value(False, output_field=BooleanField()),
                    agent_license_verification_status=Cast(
                        "license_verification_status", output_field=CharField()
                    ),
                    agent_license_country_code=Cast(
                        "license_country_code", output_field=CharField()
                    ),
                    invitation_status=Value(
                        AgentInvitationStatus.ALREADY_PRESENT, output_field=CharField()
                    ),
                    agent_working_type=Cast("working_type", output_field=CharField()),
                    agent_created_ts=Cast("created_ts", output_field=DateTimeField()),
                )
                .values(
                    "agent_id",
                    "agent_user_id",
                    "agent_name",
                    "agent_profile_photo_key",
                    "agent_email",
                    "primary_phone_number",
                    "agent_gender",
                    "agent_brn",
                    "agency_name",
                    "is_previously_associated",
                    "agent_license_verification_status",
                    "agent_license_country_code",
                    "invitation_status",
                    "agent_working_type",
                    "agent_created_ts",
                )
            )

            if not is_exclusive:
                db_agents = Agent.objects.filter(
                    agent_onboarding_status=AgentOnboardingStatus.NOT_ONBOARDED
                ).select_related("agency")

                db_agents = db_agents.annotate(
                    agent_id=Cast("id", output_field=BigIntegerField()),
                    agent_user_id=Value(None, output_field=CharField()),
                    agent_name=Cast("name", output_field=CharField()),
                    agent_profile_photo_key=Value(None, output_field=CharField()),
                    agent_email=Value(None, output_field=EmailField()),
                    primary_phone_number=F("phone"),
                    agent_gender=Cast("gender", output_field=CharField()),
                    agent_brn=Cast("brn", output_field=CharField()),
                    agency_name=F("agency__name"),
                    is_previously_associated=Value(False, output_field=BooleanField()),
                    agent_license_verification_status=Value(
                        AgentLicenseVerificationStatus.LICENSE_VERIFIED,
                        output_field=CharField(),
                    ),
                    agent_license_country_code=Cast(
                        "license_country_code", output_field=CharField()
                    ),
                    invitation_status=Case(
                        When(
                            Q(agentinvitations__created_by=request.user)
                            & Q(agentinvitations__created_by_role=investor_role),
                            then=Value(AgentInvitationStatus.INVITED),
                        ),
                        When(
                            (
                                Q(agentinvitations__created_by=request.user)
                                & ~Q(agentinvitations__created_by_role=investor_role)
                            )
                            | Q(agentinvitations__created_by__isnull=True),
                            then=Value(AgentInvitationStatus.INVITE_TO_REZIO),
                        ),
                        output_field=CharField(),
                    ),
                    agent_working_type=Case(
                        When(
                            agency_id__isnull=True,
                            then=Value(AgentWorkingType.AS_A_FREELANCER),
                        ),
                        default=Value(AgentWorkingType.WITH_AN_AGENCY),
                        output_field=CharField(),
                    ),
                    agent_created_ts=Cast("created_ts", output_field=DateTimeField()),
                ).values(
                    "agent_id",
                    "agent_user_id",
                    "agent_name",
                    "agent_profile_photo_key",
                    "agent_email",
                    "primary_phone_number",
                    "agent_gender",
                    "agent_brn",
                    "agency_name",
                    "is_previously_associated",
                    "agent_license_verification_status",
                    "agent_license_country_code",
                    "invitation_status",
                    "agent_working_type",
                    "agent_created_ts",
                )

            if query:
                # Apply filters using Q objects
                all_agents = all_agents.filter(
                    Q(name__icontains=query)
                    | Q(brn__icontains=query)
                    | Q(agency__name__icontains=query)
                )
                duplicate_agents = duplicate_agents.filter(
                    Q(name__icontains=query)
                    | Q(brn__icontains=query)
                    | Q(agency__name__icontains=query)
                )
                if not is_exclusive:
                    db_agents = db_agents.filter(
                        Q(name__icontains=query)
                        | Q(brn__icontains=query)
                        | Q(agency__name__icontains=query)
                    )
                    agents = all_agents.union(duplicate_agents, db_agents)
                else:
                    agents = all_agents.union(duplicate_agents)
                agents = agents.order_by(
                    "-is_previously_associated", "-agent_created_ts"
                )

            else:
                if not is_exclusive:
                    agents = all_agents.union(duplicate_agents, db_agents)
                else:
                    agents = all_agents.union(duplicate_agents)
                agents = agents.order_by("-is_previously_associated", "agent_name")

            paginator = StandardResultsSetPagination()
            result_page = paginator.paginate_queryset(agents, request)
            agent_list_serializer = ViewAgentListSerializer(result_page, many=True)

            return paginator.get_paginated_response(agent_list_serializer.data)

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"GetAgentsListView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class RegisterAgentProfileViewSet(APIView):
    """
    Agent profile view for new flow
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @transaction.atomic
    def post(self, request):
        """
        Save agent profile details for new flow
        """
        try:
            if AgentProfile.objects.filter(user=request.user).exists():
                logger.warning(f"Agent was already onboarded")
                return Response(
                    status=status.HTTP_400_BAD_REQUEST,
                    data={
                        KEY_MESSAGE: "Agent was already onboarded",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Agent was already onboarded"},
                    },
                )

            serializer = RegisterAgentProfileSerializer(
                data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            user = request.user
            request.data["user_id"] = user.id
            agency_name = request.data.get("agency_name", None)
            agency_country_code = request.data.get("agency_country_code", None)

            # update user with primary and secondary phone number
            primary_phone_number = request.data.get("primary_phone_number", None)
            if (
                primary_phone_number
                and not user.primary_phone_number == primary_phone_number
            ):
                user.primary_phone_number = primary_phone_number
                user.save()
                # update number on firebase
                # auth.update_user(user.id, phone_number=primary_phone_number)

            # save agency name
            agency = None
            if agency_name:
                agency_name = capitalize_first_letters(agency_name)
                agency = Agency.objects.filter(
                    name__iexact=agency_name, country_code=agency_country_code
                )
                if agency.exists():
                    agency = agency.first()
                else:
                    agency = Agency.objects.create(
                        name=agency_name, country_code=agency_country_code
                    )

            # agree terms & conditions
            terms_and_conditions = get_terms_and_condition_object()
            if not UserAgreements.objects.filter(
                user=request.user, terms_and_conditions=terms_and_conditions
            ).exists():
                UserAgreements.objects.create(
                    user=request.user, terms_and_conditions=terms_and_conditions
                )

            # agree privacy policy
            privacy_policy = get_menu_settings_privacy_policy_object()
            if not UserPrivacyPolicyAgreements.objects.filter(
                user=request.user, privacy_policy=privacy_policy
            ).exists():
                UserPrivacyPolicyAgreements.objects.create(
                    user=request.user, privacy_policy=privacy_policy
                )

            agent_profile = serializer.save(
                user=request.user,
                agency=agency,
                created_by=request.user,
                created_by_role=get_role_object(AGENT),
                data_source=UserDataSource.MANUALLY_ADDED,
                license_verification_status=AgentLicenseVerificationStatus.ADD_LICENCE,
            )

            currency_code = get_country_and_currency(
                str(request.user.primary_phone_number)
            )
            if not currency_code:
                currency_code = settings.DEFAULT_CURRENCY_CODE
            agent_profile.preferred_currency_code = currency_code
            agent_profile.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent details are saved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except TermsAndConditions.DoesNotExist:
            message = "Terms and conditions URL not found"
            logger.warning(message)
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except IntegrityError as error:
            message = "Database error occurred"
            logger.error(f"Error in AgentProfileNewViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"Error in AgentProfileNewViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: error},
                }
            )


class SaveAgentVerificationDetails(APIView):
    """
    Save agent verification details view
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def put(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)

            # If verification status is pending or verified then raise the error
            if agent_profile.license_verification_status not in [
                AgentLicenseVerificationStatus.ADD_LICENCE,
                AgentLicenseVerificationStatus.LICENCE_VERIFICATION_FAILED,
            ]:
                message = "The status of profile is verified or verification pending"
                logger.error(message)
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: message,
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                    }
                )

            serializer = SaveAgentVerificationDetailsSerializer(data=request.data)

            validated_data = validate_serializer(serializer)

            license_file = request.FILES.get("license_file")

            license_file_media_key = PRESIGNED_POST_STRUCTURES.get(
                AGENT_LICENSE_FILE, {}
            ).get(KEY, "")

            license_file_media_key = license_file_media_key.format(
                user_id=request.user.id,
                filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_agent_license",
            )

            with transaction.atomic():
                s3_client = S3Client()

                # Upload license to S3 bucket
                s3_client.upload_file(license_file, license_file_media_key)

                # Update the details in the agent profile
                agent_profile.license_country_code = validated_data.get(
                    "license_country_code"
                )
                agent_profile.license_country = validated_data.get("license_country")
                agent_profile.license_start_date = validated_data.get(
                    "license_start_date"
                )
                agent_profile.license_end_date = validated_data.get("license_end_date")
                agent_profile.brn = validated_data.get("brn")
                agent_profile.license_verification_status = (
                    AgentLicenseVerificationStatus.LICENCE_VERIFICATION_PENDING
                )
                agent_profile.license_file_key = license_file_media_key
                agent_profile.license_file_name = license_file.name
                agent_profile.license_file_size = license_file.size
                agent_profile.updated_by = request.user
                agent_profile.updated_by_role = get_role_object(AGENT)
                agent_profile.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Agent verification details are saved in the database",
                    KEY_PAYLOAD: {"user_id": request.user.id},
                    KEY_ERROR: {},
                },
            )

        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"SaveAgentVerificationDetails - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class AgentSubscription(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def post(self, request):
        try:
            subscription_status = request.data.get("subscription_status")
            valid_statuses = list(
                map(int, dict(AgentSubscriptionPlanChoices.choices).keys())
            )
            if subscription_status not in valid_statuses:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid subscription status.",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid subscription status."},
                    }
                )

            agent_profile = AgentProfile.objects.filter(user=request.user).first()
            agent_profile.subscription_status = subscription_status
            agent_profile.updated_by = request.user
            agent_profile.save()

            # Recalculate property unlocking based on new subscription
            recalculate_unlocked_properties(agent_profile)

            return Response(
                {
                    KEY_MESSAGE: "Subscription updated successfully",
                    KEY_PAYLOAD: {"subscription_status": subscription_status},
                    KEY_ERROR: {},
                },
                status=status.HTTP_200_OK,
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred in AgentSubscription POST"
            logger.error(f"AgentSubscription - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class SelfRemoveFromProperty(APIView):
    """
    Self remove from property API view
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    @general_exception_handler
    def delete(self, request, property_id):
        role = get_agent_role_object()

        # Get all the associated agent data with the given property
        association_details = AgentAssociatedProperty.objects.filter(
            property__id=property_id, is_associated=True
        )

        # Prefetch all the co-owners associated with the given property
        coowner_prefetch = Prefetch(
            "property__propertycoowner_set",
            queryset=PropertyCoOwner.objects.filter(
                property__id=property_id
            ).select_related("co_owner"),
        )

        # Get associated agent details
        associated_agent_data = (
            association_details.filter(agent_profile__user=request.user)
            .select_related("property__owner")
            .prefetch_related(coowner_prefetch)
        ).first()

        # If given agent is not associated with the property then raise an error
        if not associated_agent_data:
            message = "The agent is not associated with this property"
            logger.error(message)
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        # If given agent has created the property then raise an error
        if (
            associated_agent_data.property.created_by == request.user
            and associated_agent_data.property.created_by_role == role
        ):
            message = "Can't self remove from property created by yourself"
            logger.error(message)
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )

        # Update the associated agent entry

        associated_agent_data.is_associated = False
        associated_agent_data.is_request_expired = True
        associated_agent_data.updated_by = request.user
        associated_agent_data.updated_by_role = role
        associated_agent_data.save()

        # Send notification to related users
        post_save.send(
            sender=AgentAssociatedProperty,
            instance=associated_agent_data,
            manual_trigger=True,
            is_self_remove=True,
        )

        # If the given agent is the last one who is associated with the property
        # then make the agent type of property as OPEN TO ALL
        all_associated_agents = AgentAssociatedProperty.objects.filter(
            (
                Q(action_status=UserRequestActions.PENDING)
                & Q(is_associated=False)
                & Q(request_type=RequestType.AGENT_INVITE)
            )
            | (Q(action_status=UserRequestActions.ACCEPTED) & Q(is_associated=True))
            | (
                Q(action_status=UserRequestActions.ACCEPTED)
                & Q(request_type=RequestType.INVESTOR_REQUEST)
            ),
            property__id=property_id,
            is_request_expired=False,
        )

        if not all_associated_agents:
            property_obj = associated_agent_data.property
            property_obj.agent_type = PropertyAgentType.OPEN_TO_ALL
            property_obj.updated_by = request.user
            property_obj.updated_by_role = role
            property_obj.save()

        remove_floor_payment_plan_data_for_agent(
            property_id, [request.user.agentprofile.id]
        )
        recalculate_unlocked_properties(request.user.agentprofile)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent is successfully self removed from the property",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class AIAgentPropertyListAPIView(APIView):
    """
    API view for agent portfolio API for AI
    """

    authentication_classes = [JWTTokenAuthentication]

    def window_f(self, field):
        """
        Helper function to handle F() expressions in annotations
        """
        return F(field)

    @general_exception_handler
    def get(self, request, user_id):
        """
        Method to retrieve agent portfolio of given agent id

        :param request: Request object
        :param user_id: ID of the agent
        """
        agent_profile = get_agent_profile_object(user_id)

        agent_associated_properties = (
            AgentAssociatedProperty.objects.filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                agent_profile=agent_profile,
                is_request_expired=False,
            )
            .order_by("-created_ts")
            .values_list("property_id", flat=True)
        )

        properties = (
            Property.objects.filter(
                id__in=agent_associated_properties,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False,
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .order_by("-created_ts")
        )

        properties = (
            properties.select_related("propertyfinancialdetails").annotate(
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )
        ).order_by("-created_ts")

        serializer = AIAgentPortfolioViewSerializer(
            properties, context={"agent_profile": agent_profile}, many=True
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent portfolio detailed fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )
