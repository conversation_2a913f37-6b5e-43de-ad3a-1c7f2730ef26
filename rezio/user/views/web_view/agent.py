from django.db.models import F, Value, <PERSON>r<PERSON>ield
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status

from rezio.rezio.constants import AgentProfileThrottle, AgentPropertyListThrottle
from rezio.properties.models import AgentAssociatedProperty, Property
from rezio.properties.serializers.web_serializer.property_details_serializer import WebPropertyPortfolioViewSerializer
from rezio.properties.text_choices import PropertyPublishStatus, OwnerIntentForProperty, UserRequestActions
from rezio.user.helper import add_agent_profile_view
from rezio.user.serializers.web_serializer.agent import WebViewAgentProfileSerializer
from rezio.user.utils import get_user_object, get_agent_profile_object
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination

# Import AgentSubscriptionPlanChoices to check subscription status
from rezio.user.text_choices import AgentSubscriptionPlanChoices

class WebViewAgentProfileViewSet(APIView):
    """
    View to get agent profile in web
    """
    throttle_classes = [AgentProfileThrottle]

    @general_exception_handler
    def get(self, request, user_id):
        user = get_user_object(user_id)
        agent_profile = get_agent_profile_object(user)
        serializer = WebViewAgentProfileSerializer(agent_profile)

        # Add analytics data for agent profile view
        add_agent_profile_view(request, agent_profile)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Agent profile fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {}
            }
        )


class WebAgentPropertyListAPIView(APIView):
    """
    View to get agent properties in web
    """
    throttle_classes = [AgentPropertyListThrottle]

    @general_exception_handler
    def get(self, request, user_id, *args, **kwargs):
        user = get_user_object(user_id)
        agent_profile = get_agent_profile_object(user)

        # Check subscription status for property filtering
        is_basic_subscription = agent_profile.subscription_status == AgentSubscriptionPlanChoices.BASIC

        agent_associated_properties = (
            AgentAssociatedProperty.objects
            .filter(
                action_status=UserRequestActions.ACCEPTED,
                is_associated=True,
                is_request_expired=False,
                agent_profile=agent_profile
            )
            .order_by('-created_ts')
            .values_list('property_id', flat=True)
        )

        properties = (
            Property.objects
            .filter(
                id__in=agent_associated_properties,
                property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                is_archived=False
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .select_related('propertyfinancialdetails')
            .annotate(
                preferred_currency_code=Value(None, output_field=CharField()),
                property_currency_code=F("propertyfinancialdetails__property_currency_code"),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )
            .order_by("-created_ts")
        )

        # For agents on a basic subscription, filter properties based on unlocked properties.
        if is_basic_subscription:
            unlocked_ids = agent_profile.unlocked_properties.values_list('id', flat=True)
            properties = properties.filter(id__in=unlocked_ids)

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(properties, request)

        serializer = WebPropertyPortfolioViewSerializer(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)