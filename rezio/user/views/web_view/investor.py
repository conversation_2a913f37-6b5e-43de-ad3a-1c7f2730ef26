from django.db.models import F, Value, CharField, Q
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status

from rezio.rezio.constants import (
    AgentProfileThrottle,
    AgentPropertyListThrottle,
    InvestorProfileThrottle,
    InvestorPropertyListThrottle,
    InvestorPreferencesThrottle,
)
from rezio.properties.models import AgentAssociatedProperty, Property, PropertyCoOwner
from rezio.properties.serializers.web_serializer.property_details_serializer import (
    WebPropertyPortfolioViewSerializer,
)
from rezio.properties.text_choices import (
    PropertyPublishStatus,
    OwnerIntentForProperty,
    PropertyType,
    UserRequestActions,
)
from rezio.user.helper import (
    add_agent_profile_view,
    add_investor_profile_analytics,
    get_investor_preferences,
)
from rezio.user.models import OnboardingQuestion
from rezio.user.serializers import OnboardingQuestionSerializer
from rezio.user.serializers.web_serializer.agent import WebViewAgentProfileSerializer
from rezio.user.serializers.web_serializer.investor import (
    WebViewInvestorProfileSerializer,
    WebInvestorPropertyPortfolioViewSerializer,
    InvestorPreferencesSerializer,
)
from rezio.user.utils import (
    get_user_object,
    get_agent_profile_object,
    get_investor_profile_object,
    get_default_investor_type_object,
    get_investor_role_object,
)
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import ResourceNotFoundException
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import PreferenceCategory


class WebViewInvestorProfileViewSet(APIView):
    """
    View to get investor profile in web
    """

    throttle_classes = [InvestorProfileThrottle]

    @general_exception_handler
    def get(self, request, user_id):
        user = get_user_object(user_id)
        investor_profile = get_investor_profile_object(user)
        if not investor_profile.profile_public_view:
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    },
                }
            )

        serializer = WebViewInvestorProfileSerializer(
            investor_profile, context={"user": user}
        )

        # Add analytics data for agent profile view
        add_investor_profile_analytics(request, investor_profile)

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Investor profile fetched successfully",
                KEY_PAYLOAD: serializer.data,
                KEY_ERROR: {},
            },
        )


class WebInvestorPortfolioViewSet(APIView):
    """
    Web API to list investor portfolio
    """

    throttle_classes = [InvestorPropertyListThrottle]

    @general_exception_handler
    def get(self, request, user_id, *args, **kwargs):
        user = get_user_object(user_id)
        investor_profile = get_investor_profile_object(user)
        if not investor_profile.profile_public_view:
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    },
                }
            )

        properties = (
            Property.objects.filter(
                Q(
                    # Direct owner conditions
                    owner=investor_profile,
                    owner_verified=True,
                    property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                    is_archived=False,
                )
                | Q(
                    # Co-owner conditions
                    id__in=PropertyCoOwner.objects.filter(
                        co_owner=investor_profile, is_associated=True
                    ).values_list("property_id", flat=True),
                    owner_verified=True,
                    property_publish_status=PropertyPublishStatus.ADDED_TO_PORTFOLIO,
                    is_archived=False,
                )
            )
            .exclude(owner_intent=OwnerIntentForProperty.NOT_FOR_SALE)
            .select_related("propertyfinancialdetails")
            .annotate(
                preferred_currency_code=Value(None, output_field=CharField()),
                property_currency_code=F(
                    "propertyfinancialdetails__property_currency_code"
                ),
                asking_price=F("propertyfinancialdetails__asking_price"),
                original_price=F("propertyfinancialdetails__original_price"),
                valuation=F("propertyfinancialdetails__valuation"),
                annual_rent=F("propertyfinancialdetails__annual_rent"),
            )
            .order_by("-created_ts")
        )

        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(properties, request)

        serializer = WebInvestorPropertyPortfolioViewSerializer(
            paginated_queryset, many=True
        )
        return paginator.get_paginated_response(serializer.data)


class WebViewInvestorPreferencesViewSet(APIView):
    """
    View to get investor preferences in web
    """

    throttle_classes = [InvestorPreferencesThrottle]

    @general_exception_handler
    def get(self, request, user_id):
        user = get_user_object(user_id)
        investor_profile = get_investor_profile_object(user)
        if not investor_profile.profile_public_view:
            raise ResourceNotFoundException(
                {
                    KEY_MESSAGE: "Portfolio Access Denied",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: "Your access to this portfolio has been restricted or denied."
                    },
                }
            )

        investor_preferences = get_investor_preferences(investor_profile)

        investor_preferences_serializer = InvestorPreferencesSerializer(
            investor_preferences, context={"user": user}, many=True
        )
        data = {
            "preferences": investor_preferences_serializer.data,
            "preferences_category": [
                choice[0] for choice in PreferenceCategory.choices
            ],
        }
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Investor preferences fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )
