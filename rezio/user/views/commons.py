import logging
import traceback
from datetime import datetime

from django.conf import settings
from django.contrib.postgres.search import TrigramSimilarity
from django.db import transaction
from django.db.models import (
    Q,
    Case,
    When,
    Value,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>ield,
    BigIntegerField,
    <PERSON>ail<PERSON>ield,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Count,
)
from django.db.models.functions import Cast, Replace
from firebase_admin import auth
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.exceptions import InvalidToken
from rest_framework_simplejwt.token_blacklist.models import (
    OutstandingToken,
    BlacklistedToken,
)
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView as SimpleJWTTokenRefreshView

from rezio.infobip_sms_gateway import InfobipClient
from rezio.notifications.models import Notification
from rezio.properties.models import PropertyCoOwner, Property
from rezio.properties.services.property_service import (
    validate_serializer,
    fetch_role_obj_and_name,
)
from rezio.properties.utils import user_has_dubai_property, user_has_india_property
from rezio.rezio.aws import S3Client
from rezio.rezio.constants import (
    KEY,
    NAME,
    SIZE,
    PRESIGNED_POST_STRUCTURES,
    AGENT_LICENSE_FILE,
    DD_MMM_YYYY,
)
from rezio.rezio.custom_error_codes import (
    LOGOUT_FAILED,
    TOKEN_GENERATION_FAILED,
    REFRESH_TOKEN_EXPIRED,
)
from rezio.twilio_integration.models import TwilioVerification
from rezio.twilio_integration.services.twilio_service import TwilioService
from rezio.twilio_integration.text_choices import VerificationType
from rezio.user.authentication import FirebaseAuthentication, JWTAuthentication
from rezio.user.constants import INVESTOR, AGENT
from rezio.user.helper import (
    get_profile_object_by_role,
    raise_invalid_data_exception,
    build_profile_permission_classes,
)
from rezio.user.models import (
    Role,
    InvestorProfile,
    AgentProfile,
    Follow,
    Agent,
    AgentInvitations,
    UserPropertySharingControlAttributes,
)
from rezio.user.permissions import IsAuthenticatedAgent, IsAuthenticatedInvestor
from rezio.user.serializers import (
    UserLoginSerializer,
    UserSerializer,
    UserProfileImageUploadSerializer,
    UserProfileDocumentsUploadSerializer,
    UserProfileDocumentsDeleteSerializer,
    FollowerSerializer,
    FollowingSerializer,
    SocialLinksSerializer,
    InvestorListSerializer,
    UserProfileImageDeleteSerializer,
    AgentLicenseUpdateSerializer,
    ViewCombinedAgentListSerializer,
    AgentInvitationSerializer,
    CombinedTermsPolicySerializer,
    SetPropertySharingPrivateFieldsSerializer,
)
from rezio.user.services.auth_service import AuthService
from rezio.user.text_choices import AgentOnboardingStatus, AgentInvitationStatus
from rezio.user.utils import (
    get_role_object,
    get_agent_profile_object,
    get_investor_profile_object,
    get_user_object,
    get_follow_object,
    get_db_agent_object,
    get_terms_and_condition_object,
    get_menu_settings_privacy_policy_object,
    get_or_create_db_object,
    get_or_create_user_with_role,
)
from rezio.utils.constants import DJANGO_LOGGER_NAME, KEY_ERROR_CODE
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InvalidSerializerDataException,
    InternalServerException,
    ResourceNotFoundException,
    TwilioException,
)
from rezio.utils.decorators import log_input_output, general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination
from rezio.utils.text_choices import AgentLicenseVerificationStatus, AgentWorkingType
from rezio.utils.validation_utils import validate_currency_code, validation_utility

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class UserLoginView(APIView):
    """
    Login/Signup view for authentication
    """

    authentication_classes = [FirebaseAuthentication]

    @log_input_output
    def post(self, request):
        """
        Login/Registration of a user with basic details
        """
        serializer = UserLoginSerializer(data=request.data)
        if not serializer.is_valid():
            logger.error(f"Invalid request data: {str(serializer.errors)}")
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={
                    KEY_MESSAGE: "Invalid request data",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                },
            )

        user = request.user

        if (
            user
            and user.primary_phone_number == serializer.validated_data["phone_number"]
        ):
            # assign role to the user
            if not user.roles.filter(name=request.data.get("role_name")).exists():
                role = Role.objects.get(name=request.data.get("role_name"))
                request.user.roles.add(role)

            user_serializer = UserSerializer(user)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Login successful",
                    KEY_PAYLOAD: user_serializer.data,
                    KEY_ERROR: {},
                },
            )
        else:
            message = "Requested phone number does not match the phone number of authenticated user"
            logger.error(f"UserLoginView - {message}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )


class TokenRefreshView(SimpleJWTTokenRefreshView):
    """
    View for refreshing access tokens using a valid refresh token
    Uses the built-in SimpleJWT TokenRefreshView
    """

    @log_input_output
    def post(self, request, *args, **kwargs):
        # Check if the client is using refresh_token instead of refresh
        if "refresh_token" in request.data and "refresh" not in request.data:
            # Create a new request.data with the correct key
            request._full_data = {"refresh": request.data["refresh_token"]}

        try:
            # Use the parent class's post method
            response = super().post(request, *args, **kwargs)

            # Customize the response format to match our API standards
            if response.status_code == status.HTTP_200_OK:
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Token refreshed successfully",
                        KEY_PAYLOAD: {
                            "access_token": response.data.get("access"),
                        },
                        KEY_ERROR: {},
                    },
                )

            # If there was an error, return the original response
            return response
        except InvalidToken as e:
            error_message = str(e)
            logger.warning(f"Token refresh error: {error_message}")

            # Check if it's an expiration error
            if "expired" in error_message.lower() or "exp" in error_message.lower():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Refresh token has expired",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: REFRESH_TOKEN_EXPIRED.get("message"),
                            KEY_ERROR_CODE: REFRESH_TOKEN_EXPIRED.get("code"),
                        },
                    }
                )
            if "blacklist" in error_message.lower():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Refresh token has expired",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: REFRESH_TOKEN_EXPIRED.get("message"),
                            KEY_ERROR_CODE: REFRESH_TOKEN_EXPIRED.get("code"),
                        },
                    }
                )

        except Exception as error:
            logger.error(f"TokenRefreshView - {error}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while refreshing token",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: TOKEN_GENERATION_FAILED.get("message"),
                        KEY_ERROR_CODE: TOKEN_GENERATION_FAILED.get("code"),
                    },
                }
            )


class LogoutView(APIView):
    """
    View for logging out a user by blacklisting their refresh token
    Uses the built-in SimpleJWT TokenBlacklistView
    """

    @log_input_output
    def post(self, request, *args, **kwargs):
        try:
            success = AuthService.blacklist_specific_tokens(request)

            # Customize the response format to match our API standards
            if success:
                return Response(
                    status=status.HTTP_200_OK,
                    data={
                        KEY_MESSAGE: "Logged out successfully",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {},
                    },
                )

            # If there was an error, return the original response
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Error while logging out",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: LOGOUT_FAILED.get("message"),
                            KEY_ERROR_CODE: LOGOUT_FAILED.get("code"),
                        },
                    }
                )

        except Exception as error:
            logger.error(f"LogoutView - {error}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while logging out",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: LOGOUT_FAILED.get("message"),
                        KEY_ERROR_CODE: LOGOUT_FAILED.get("code"),
                    },
                }
            )


class UserDeleteView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @log_input_output
    def delete(self, request):
        try:
            # Get user information
            user = request.user
            user_id = user.pk

            # Blacklist all outstanding refresh tokens for this user
            outstanding_tokens = OutstandingToken.objects.filter(user=user)
            for token in outstanding_tokens:
                BlacklistedToken.objects.get_or_create(token=token)

            # Get all the roles user is associated with
            user_roles = list(user.roles.values_list("name", flat=True))

            # Delete the profile for roles the user is associated with
            if INVESTOR in user_roles:
                InvestorProfile.objects.filter(user=user).delete()

            # Delete the profile for roles the user is associated with
            if AGENT in user_roles:
                AgentProfile.objects.filter(user=user).delete()

            # If using Firebase, attempt to delete the user there too
            try:
                auth.delete_user(user_id)
            except Exception as firebase_error:
                # Log but continue with deletion
                logger.warning(f"Could not delete user from Firebase: {firebase_error}")

            # Delete user from database
            user.delete()

        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"UserDeleteView - {message} - {error}")
            raise InternalServerException(
                {KEY_MESSAGE: message, KEY_PAYLOAD: {}, KEY_ERROR: {}}
            )
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "User has been deleted successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class TermsAndConditionsView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get(self, request):
        try:
            terms_and_conditions = get_terms_and_condition_object()
            privacy_policy = get_menu_settings_privacy_policy_object()

            serializer = CombinedTermsPolicySerializer(
                instance={},
                context={
                    "terms_and_conditions": terms_and_conditions,
                    "privacy_policy": privacy_policy,
                },
            )
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Terms & conditions and privacy policy URLs retrieved successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"TermsAndConditionsView - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class UserProfileImageUpdate(APIView):
    """
    An API to update the user profile photo
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["POST", "PUT", "PATCH"]:
            role_name = self.request.data.get("role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def post(self, request):
        try:
            role = request.data.get("role")
            role_obj = get_role_object(role)
            profile_object = get_profile_object_by_role(request.user, role_obj)
            serializer = UserProfileImageUploadSerializer(
                profile_object, data=request.data
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()
            user_serializer = UserSerializer(request.user)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Profile image uploaded successfully",
                    KEY_PAYLOAD: user_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"UserProfileImageUpdate - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    @general_exception_handler
    def delete(self, request):
        role = request.data.get("role")
        role_obj = get_role_object(role)
        profile_object = get_profile_object_by_role(request.user, role_obj)

        serializer = UserProfileImageDeleteSerializer(data=request.data)

        if not serializer.is_valid():
            logger.error("Invalid data sent :" + str(serializer.errors))
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: serializer.errors,
                }
            )

        file_field = serializer.validated_data
        file_field_key = f"{file_field}_{KEY}".lower()
        file_name_field = f"{file_field}_{NAME}".lower()
        file_size_field = f"{file_field}_{SIZE}".lower()
        file_key = getattr(profile_object, file_field_key)

        if not file_key:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid data sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{file_field} does not exist"},
                }
            )

        # delete file from s3
        s3_client = S3Client()
        s3_client.delete_file(file_key)

        setattr(profile_object, file_field_key, None)
        setattr(profile_object, file_name_field, None)
        setattr(profile_object, file_size_field, None)
        profile_object.save()

        return Response(status=status.HTTP_204_NO_CONTENT, data={})


class UserProfileDocumentsViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def post(self, request):
        try:
            serializer = UserProfileDocumentsUploadSerializer(
                request.user, data=request.data
            )
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()
            user_serializer = UserSerializer(request.user)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Document uploaded successfully",
                    KEY_PAYLOAD: user_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"UserProfilePhotoUpdate - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def delete(self, request):
        try:
            serializer = UserProfileDocumentsDeleteSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            # Get the field that was provided
            file_field = list(serializer.validated_data.keys())[0]
            file_field_key = f"{file_field}_{KEY}".lower()
            file_name_field = f"{file_field}_{NAME}".lower()
            file_size_field = f"{file_field}_{SIZE}".lower()
            file_key = getattr(request.user, file_field_key)

            if not file_key:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Document does not exist"},
                    }
                )
            # delete file from s3
            s3_client = S3Client()
            s3_client.delete_file(file_key)

            setattr(request.user, file_field_key, None)
            setattr(request.user, file_name_field, None)
            setattr(request.user, file_size_field, None)
            request.user.save()
            user_serializer = UserSerializer(request.user)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Document deleted successfully",
                    KEY_PAYLOAD: user_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"UserProfilePhotoUpdate - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class FollowUnfollowViewSet(APIView):
    """
    An API to follow and unfollow user profiles
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["POST", "PUT", "PATCH"]:
            role_name = self.request.data.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def post(self, request, user_id, role_name):
        try:
            if request.user.pk == user_id and role_name == request.data.get(
                "user_role", None
            ):
                raise_invalid_data_exception("A user cannot follow it's own profile")

            followed_user = get_user_object(user_id)
            role_obj = get_role_object(role_name)

            current_user_role = get_role_object(request.data.get("user_role", None))

            follow_queryset = Follow.objects.filter(
                from_user=request.user,
                to_user=followed_user,
                from_user_role=current_user_role,
                to_user_role=role_obj,
            )
            if follow_queryset.exists():
                latest_follow = follow_queryset.latest("followed_at")
                if not latest_follow.unfollowed_at:
                    raise_invalid_data_exception("You are already following the user")

            if request.data.get("user_role") == role_name == INVESTOR:
                raise_invalid_data_exception(
                    "An investor cannot follow another investor"
                )

            followed_profile = get_profile_object_by_role(followed_user, role_obj)

            following_profile = get_profile_object_by_role(
                request.user, current_user_role
            )

            follow = Follow.objects.create(
                from_user=request.user,
                from_user_role=current_user_role,
                to_user=followed_user,
                to_user_role=role_obj,
            )
            following_profile.following.add(follow)
            followed_profile.followers.add(follow)

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Followed the user successfully",
                    KEY_PAYLOAD: {"id": follow.id},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"FollowUnfollowViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def delete(self, request, user_id, role_name):
        try:
            if request.user.pk == user_id and role_name == request.data.get(
                "user_role", None
            ):
                raise_invalid_data_exception("A user cannot follow it's own profile")

            followed_user = get_user_object(user_id)
            role_obj = get_role_object(role_name)

            current_user_role = get_role_object(request.data.get("user_role", None))

            if request.data.get("user_role") == role_name == INVESTOR:
                raise_invalid_data_exception(
                    "An investor cannot follow another investor"
                )

            follow = get_follow_object(
                request.user, current_user_role, followed_user, role_obj
            )

            followed_profile = get_profile_object_by_role(followed_user, role_obj)

            following_profile = get_profile_object_by_role(
                request.user, current_user_role
            )

            followed_profile.followers.remove(follow)
            following_profile.following.remove(follow)
            follow.unfollowed_at = datetime.now()
            follow.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Unfollowed the user successfully",
                    KEY_PAYLOAD: {"id": follow.id},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"FollowUnfollowViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class RemoveFollowerViewSet(APIView):
    """
    An API to remove a follower from a user profiles
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["POST", "PUT", "PATCH"]:
            role_name = self.request.data.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def delete(self, request, user_id, role_name):
        try:
            if request.user.pk == user_id and role_name == request.data.get(
                "user_role", None
            ):
                raise_invalid_data_exception(
                    "A user cannot remove follow for it's own profile"
                )

            follower = get_user_object(user_id)
            role_obj = get_role_object(role_name)

            current_user_role = get_role_object(request.data.get("user_role", None))

            if request.data.get("user_role") == role_name == INVESTOR:
                raise_invalid_data_exception(
                    "An investor cannot follow another investor"
                )

            follow = get_follow_object(
                follower, role_obj, request.user, current_user_role
            )

            follower_profile = get_profile_object_by_role(follower, role_obj)

            viewer_profile = get_profile_object_by_role(request.user, current_user_role)

            follower_profile.following.remove(follow)
            viewer_profile.followers.remove(follow)
            follow.unfollowed_at = datetime.now()
            follow.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Unfollowed the user successfully",
                    KEY_PAYLOAD: {"id": follow.id},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"FollowUnfollowViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class FollowingListViewSet(APIView):
    """
    An API to list profiles a user is following
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get(self, request, user_id=None, role_name=None):
        try:
            user_role = request.query_params.get("user_role", None)
            query = request.query_params.get("search_text", None)
            if not user_role:
                raise_invalid_data_exception("Role not found")

            user_role_obj = get_role_object(user_role)

            if user_id is None and role_name is None:
                profile = get_profile_object_by_role(request.user, user_role_obj)
                following = profile.following
                if query:
                    following = following.filter(
                        Q(
                            to_user_role__name=AGENT,
                            to_user__agentprofile__name__icontains=query,
                        )
                        | Q(
                            to_user_role__name=INVESTOR,
                            to_user__investorprofile__name__icontains=query,
                        )
                    )

            elif user_id and role_name:
                if user_id == request.user.pk and role_name == user_role == INVESTOR:
                    raise_invalid_data_exception("Invalid data sent")

                role_obj = get_role_object(role_name)
                user = get_user_object(user_id)

                if role_name == user_role == INVESTOR:
                    raise_invalid_data_exception(
                        "User is not authorised to view this profile"
                    )

                profile = get_profile_object_by_role(user, role_obj)
                following = profile.following.filter(to_user_role__name=AGENT)

                if query:
                    following = following.filter(
                        to_user_role__name=AGENT,
                        to_user__agentprofile__name__icontains=query,
                    )

            else:
                raise_invalid_data_exception("Invalid data sent")

            paginator = StandardResultsSetPagination()
            paginated_queryset = paginator.paginate_queryset(following.all(), request)
            serializer = FollowingSerializer(
                paginated_queryset, many=True, context={"user": request.user}
            )
            return paginator.get_paginated_response(serializer.data)
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"FollowingListViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class FollowerListViewSet(APIView):
    """
    An API to list followers of a user profile
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get(self, request, user_id=None, role_name=None):
        try:
            user_role = request.query_params.get("user_role", None)
            query = request.query_params.get("search_text", None)
            if not user_role:
                raise_invalid_data_exception("Role not found")

            user_role_obj = get_role_object(user_role)
            if user_id is None and role_name is None:
                profile = get_profile_object_by_role(request.user, user_role_obj)
                followers = profile.followers

                if query:
                    followers = followers.filter(
                        Q(
                            from_user_role__name=AGENT,
                            from_user__agentprofile__name__icontains=query,
                        )
                        | Q(
                            from_user_role__name=INVESTOR,
                            from_user__investorprofile__name__icontains=query,
                        )
                    )

            elif user_id and role_name:
                if user_id == request.user.pk and role_name == user_role == INVESTOR:
                    raise_invalid_data_exception("Invalid data sent")

                role_obj = get_role_object(role_name)
                user = get_user_object(user_id)

                if role_name == user_role == INVESTOR:
                    raise_invalid_data_exception(
                        "User is not authorised to view this profile"
                    )

                profile = get_profile_object_by_role(user, role_obj)

                followers = profile.followers.filter(from_user_role__name=AGENT)

                if query:
                    followers = followers.filter(
                        from_user_role__name=AGENT,
                        from_user__agentprofile__name__icontains=query,
                    )

            else:
                raise_invalid_data_exception("Invalid data sent")

            paginator = StandardResultsSetPagination()
            paginated_queryset = paginator.paginate_queryset(followers.all(), request)
            serializer = FollowerSerializer(
                paginated_queryset, many=True, context={"user": request.user}
            )
            return paginator.get_paginated_response(serializer.data)
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )

        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"FollowerListViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class SocialLinksViewSet(APIView):
    """
    An API to get/update social links of user profile
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET", "POST"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get(self, request):
        try:
            user_role = request.query_params.get("user_role", None)
            if not user_role:
                raise_invalid_data_exception("Role not found")
            user_role_obj = get_role_object(user_role)
            profile = get_profile_object_by_role(request.user, user_role_obj)
            serializer = SocialLinksSerializer(profile)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Social links fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"SocialLinksViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def post(self, request):
        try:
            user_role = request.query_params.get("user_role", None)
            if not user_role:
                raise_invalid_data_exception("Role not found")
            user_role_obj = get_role_object(user_role)
            profile = get_profile_object_by_role(request.user, user_role_obj)
            serializer = SocialLinksSerializer(profile, data=request.data)
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            serializer.save()
            profile.save()
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Social links fetched successfully",
                    KEY_PAYLOAD: serializer.data,
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"SocialLinksViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class UniversalSearchViewSet(APIView):
    """
    An API for global search
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET", "POST"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    def get(self, request):
        try:
            query = request.query_params.get("search_text", None)
            user_role = request.query_params.get("user_role", None)
            search_type = request.query_params.get("search_type", None)

            # Initialize queries for agents, investors, and properties
            if search_type == AGENT:
                agents = AgentProfile.objects.select_related("user", "agency")
                db_agents = Agent.objects.filter(
                    agent_onboarding_status=AgentOnboardingStatus.NOT_ONBOARDED
                ).select_related("agency")

                if user_role == AGENT:
                    agents = agents.exclude(user=request.user)
                if query:
                    query_no_space = query.replace(" ", "")
                    query_words = query.split()

                    # Create Q objects for each word in the query
                    name_q = Q()
                    agency_q = Q()
                    brn_q = Q()

                    # Create Q objects for AND conditions (all words must match)
                    name_q_all = Q()
                    agency_q_all = Q()
                    brn_q_all = Q()

                    for word in query_words:
                        name_q |= Q(name__icontains=word)  # OR condition
                        agency_q |= Q(agency__name__icontains=word)
                        brn_q |= Q(brn__icontains=word)

                        name_q_all &= Q(name__icontains=word)  # AND condition
                        agency_q_all &= Q(agency__name__icontains=word)
                        brn_q_all &= Q(brn__icontains=word)

                    # Enhanced search for AgentProfile
                    agents = (
                        agents.annotate(
                            name_no_space=Replace("name", Value(" "), Value("")),
                            agency_name_no_space=Replace(
                                "agency__name", Value(" "), Value("")
                            ),
                            brn_no_space=Replace("brn", Value(" "), Value("")),
                            name_similarity=TrigramSimilarity("name", query),
                            agency_similarity=TrigramSimilarity("agency__name", query),
                            brn_similarity=TrigramSimilarity("brn", query),
                            matching_words_count=Count(
                                Case(
                                    *[
                                        When(name__icontains=word, then=1)
                                        for word in query_words
                                    ],
                                    default=0,
                                    output_field=IntegerField(),
                                )
                            ),
                            has_all_words=Case(
                                When(name_q_all, then=Value(True)),
                                default=Value(False),
                                output_field=BooleanField(),
                            ),
                        )
                        .filter(
                            name_q
                            | brn_q  # Individual word matches
                            | agency_q
                            | Q(name_similarity__gt=0.3)
                            | Q(agency_similarity__gt=0.3)
                            | Q(brn_similarity__gt=0.3)
                        )
                        .annotate(
                            # Create a more granular search ordering
                            search_ordering=Case(
                                When(
                                    name__iexact=query, then=Value(0)
                                ),  # Exact full match
                                When(
                                    name_no_space__iexact=query_no_space, then=Value(1)
                                ),  # No-space exact match
                                When(
                                    name__icontains=query, then=Value(2)
                                ),  # Contains exact phrase
                                When(
                                    has_all_words=True, then=Value(3)
                                ),  # Contains all words in any order
                                default=Value(4),
                                output_field=IntegerField(),
                            ),
                        )
                    )

                    # Similar changes for db_agents
                    db_agents = (
                        db_agents.annotate(
                            name_no_space=Replace("name", Value(" "), Value("")),
                            agency_name_no_space=Replace(
                                "agency__name", Value(" "), Value("")
                            ),
                            brn_no_space=Replace("brn", Value(" "), Value("")),
                            name_similarity=TrigramSimilarity("name", query),
                            agency_similarity=TrigramSimilarity("agency__name", query),
                            brn_similarity=TrigramSimilarity("brn", query),
                            matching_words_count=Count(
                                Case(
                                    *[
                                        When(name__icontains=word, then=1)
                                        for word in query_words
                                    ],
                                    default=0,
                                    output_field=IntegerField(),
                                )
                            ),
                            has_all_words=Case(
                                When(name_q_all, then=Value(True)),
                                default=Value(False),
                                output_field=BooleanField(),
                            ),
                        )
                        .filter(
                            name_q
                            | brn_q  # Individual word matches
                            | agency_q
                            | Q(name_similarity__gt=0.3)
                            | Q(agency_similarity__gt=0.3)
                            | Q(brn_similarity__gt=0.3)
                        )
                        .annotate(
                            search_ordering=Case(
                                When(
                                    name__iexact=query, then=Value(0)
                                ),  # Exact full match
                                When(
                                    name_no_space__iexact=query_no_space, then=Value(1)
                                ),  # No-space exact match
                                When(
                                    name__icontains=query, then=Value(2)
                                ),  # Contains exact phrase
                                When(
                                    has_all_words=True, then=Value(3)
                                ),  # Contains all words in any order
                                default=Value(4),
                                output_field=IntegerField(),
                            ),
                        )
                    )

                # Rest of the agent annotations...
                agents = agents.annotate(
                    agent_id=Cast("id", output_field=BigIntegerField()),
                    agent_user_id=Cast("user_id", output_field=CharField()),
                    agent_name=Cast("name", output_field=CharField()),
                    agent_profile_photo_key=Cast(
                        "profile_photo_key", output_field=CharField()
                    ),
                    agent_email=Cast("email", output_field=EmailField()),
                    primary_phone_number=F("user__primary_phone_number"),
                    agent_gender=Cast("gender", output_field=CharField()),
                    agent_brn=Cast("brn", output_field=CharField()),
                    agency_name=F("agency__name"),
                    is_previously_associated=Value(False, output_field=BooleanField()),
                    agent_license_verification_status=Cast(
                        "license_verification_status", output_field=CharField()
                    ),
                    agent_license_country_code=Cast(
                        "license_country_code", output_field=CharField()
                    ),
                    invitation_status=Value(
                        AgentInvitationStatus.ALREADY_PRESENT,
                        output_field=CharField(),
                    ),
                    agent_working_type=Cast("working_type", output_field=CharField()),
                ).values(
                    "agent_id",
                    "agent_user_id",
                    "agent_name",
                    "agent_profile_photo_key",
                    "agent_email",
                    "primary_phone_number",
                    "agent_gender",
                    "agent_brn",
                    "agency_name",
                    "is_previously_associated",
                    "agent_license_verification_status",
                    "agent_license_country_code",
                    "invitation_status",
                    "agent_working_type",
                    "matching_words_count",
                    "search_ordering",
                )

                db_agents = db_agents.annotate(
                    agent_id=Cast("id", output_field=BigIntegerField()),
                    agent_user_id=Value(None, output_field=CharField()),
                    agent_name=Cast("name", output_field=CharField()),
                    agent_profile_photo_key=Value(None, output_field=CharField()),
                    agent_email=Value(None, output_field=EmailField()),
                    primary_phone_number=F("phone"),
                    agent_gender=Cast("gender", output_field=CharField()),
                    agent_brn=Cast("brn", output_field=CharField()),
                    agency_name=F("agency__name"),
                    is_previously_associated=Value(False, output_field=BooleanField()),
                    agent_license_verification_status=Value(
                        AgentLicenseVerificationStatus.LICENSE_VERIFIED,
                        output_field=CharField(),
                    ),
                    agent_license_country_code=Cast(
                        "license_country_code", output_field=CharField()
                    ),
                    invitation_status=Case(
                        When(
                            Q(agentinvitations__created_by=request.user)
                            & Q(
                                agentinvitations__created_by_role=get_role_object(
                                    user_role
                                )
                            ),
                            then=Value(AgentInvitationStatus.INVITED),
                        ),
                        When(
                            (
                                Q(agentinvitations__created_by=request.user)
                                & ~Q(
                                    agentinvitations__created_by_role=get_role_object(
                                        user_role
                                    )
                                )
                            )
                            | Q(agentinvitations__created_by__isnull=True),
                            then=Value(AgentInvitationStatus.INVITE_TO_REZIO),
                        ),
                        output_field=CharField(),
                    ),
                    agent_working_type=Case(
                        When(
                            agency_id__isnull=True,
                            then=Value(AgentWorkingType.AS_A_FREELANCER),
                        ),
                        default=Value(AgentWorkingType.WITH_AN_AGENCY),
                        output_field=CharField(),
                    ),
                ).values(
                    "agent_id",
                    "agent_user_id",
                    "agent_name",
                    "agent_profile_photo_key",
                    "agent_email",
                    "primary_phone_number",
                    "agent_gender",
                    "agent_brn",
                    "agency_name",
                    "is_previously_associated",
                    "agent_license_verification_status",
                    "agent_license_country_code",
                    "invitation_status",
                    "agent_working_type",
                    "matching_words_count",
                    "search_ordering",
                )

                combined_agents = agents.union(db_agents)
                combined_agents = combined_agents.order_by(
                    "search_ordering",  # Primary sort by our detailed ordering
                    "-matching_words_count",  # Then by how many words match
                    "agent_name",  # Finally alphabetically
                )

                paginator = StandardResultsSetPagination()
                agents_paginated = paginator.paginate_queryset(combined_agents, request)

                agents_serializer = ViewCombinedAgentListSerializer(
                    agents_paginated, many=True
                )
                return paginator.get_paginated_response(agents_serializer.data)
            elif search_type == INVESTOR and user_role == AGENT:
                investors = InvestorProfile.objects.select_related("user")
                if query:
                    query_no_space = query.replace(" ", "")
                    query_words = query.split()

                    # Create Q objects for each word in the query
                    name_q = Q()
                    email_q = Q()
                    phone_q = Q()

                    # Create Q objects for AND conditions
                    name_q_all = Q()
                    email_q_all = Q()
                    phone_q_all = Q()

                    for word in query_words:
                        name_q |= Q(name__icontains=word)
                        email_q |= Q(email__icontains=word)
                        phone_q |= Q(user__primary_phone_number__icontains=word)

                        name_q_all &= Q(name__icontains=word)
                        email_q_all &= Q(email__icontains=word)
                        phone_q_all &= Q(user__primary_phone_number__icontains=word)

                    investors = (
                        investors.annotate(
                            name_no_space=Replace("name", Value(" "), Value("")),
                            email_no_space=Replace("email", Value(" "), Value("")),
                            phone_no_space=Replace(
                                "user__primary_phone_number", Value(" "), Value("")
                            ),
                            name_similarity=TrigramSimilarity("name", query),
                            email_similarity=TrigramSimilarity("email", query),
                            phone_similarity=TrigramSimilarity(
                                "user__primary_phone_number", query
                            ),
                            # Count how many words from the search query appear in the name
                            matching_words_count=Count(
                                Case(
                                    *[
                                        When(name__icontains=word, then=1)
                                        for word in query_words
                                    ],
                                    default=0,
                                    output_field=IntegerField(),
                                )
                            ),
                            has_all_words=Case(
                                When(name_q_all, then=Value(True)),
                                default=Value(False),
                                output_field=BooleanField(),
                            ),
                        )
                        .filter(
                            name_q
                            | email_q
                            | phone_q
                            | Q(name_similarity__gt=0.3)
                            | Q(email_similarity__gt=0.3)
                            | Q(phone_similarity__gt=0.3)
                        )
                        .annotate(
                            search_ordering=Case(
                                When(
                                    name__iexact=query, then=Value(0)
                                ),  # Exact full match
                                When(
                                    name_no_space__iexact=query_no_space, then=Value(1)
                                ),  # No-space exact match
                                When(
                                    name__icontains=query, then=Value(2)
                                ),  # Contains exact phrase
                                When(
                                    has_all_words=True, then=Value(3)
                                ),  # Contains all words in any order
                                default=Value(4),
                                output_field=IntegerField(),
                            ),
                        )
                        .order_by(
                            "search_ordering",
                            "-matching_words_count",
                            "-name_similarity",
                            "name",
                        )
                    )

                investors = investors.order_by("name")

                # Paginate each queryset
                paginator = StandardResultsSetPagination()

                investors_paginated = paginator.paginate_queryset(investors, request)
                investors_serializer = InvestorListSerializer(
                    investors_paginated, many=True
                )
                return paginator.get_paginated_response(investors_serializer.data)
            else:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Invalid data sent"},
                    }
                )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            traceback.print_exc()
            message = "Unknown error occurred"
            logger.error(f"GlobalSearchViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class ProfilePreferredCurrencyViewSet(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        return build_profile_permission_classes(self.request)

    @general_exception_handler
    def get(self, request):
        """
        get preferred currency
        """
        user = request.user
        user_role = request.query_params.get("user_role")
        role_obj = get_role_object(user_role)
        profile = get_profile_object_by_role(user, role_obj)
        data = {
            "user_id": profile.user_id,
            "id": profile.id,
            "preferred_currency_code": profile.preferred_currency_code,
        }
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Preferred fetched successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )

    @general_exception_handler
    def post(self, request):
        """
        save preferred currency
        """
        user = request.user
        user_role = request.query_params.get("user_role")
        role_obj = get_role_object(user_role)
        profile = get_profile_object_by_role(user, role_obj)
        preferred_currency_code = request.data.get("preferred_currency_code", None)

        if not request.data.get("preferred_currency_code"):
            raise_invalid_data_exception("Preferred currency code not found")

        if not validate_currency_code(preferred_currency_code):
            raise_invalid_data_exception(
                f"'{preferred_currency_code}' is not a valid currency code."
            )

        profile.preferred_currency_code = preferred_currency_code
        profile.save()
        data = {
            "user_id": profile.user_id,
            "id": profile.id,
            "preferred_currency_code": profile.preferred_currency_code,
        }
        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Preferred currency saved successfully",
                KEY_PAYLOAD: data,
                KEY_ERROR: {},
            },
        )


class AgentLicenseUpdate(APIView):
    """
    An API view to update license of agent
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    permission_classes = [IsAuthenticatedAgent]

    def post(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)

            serializer = AgentLicenseUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error("Invalid data sent :" + str(serializer.errors))
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: serializer.errors,
                    }
                )
            validated_data = serializer.validated_data

            license_file = validated_data.get("license_file")

            license_file_media_key = PRESIGNED_POST_STRUCTURES.get(
                AGENT_LICENSE_FILE, {}
            ).get(KEY, "")

            license_file_media_key = license_file_media_key.format(
                user_id=request.user.id,
                filename=f"{datetime.now().strftime(DD_MMM_YYYY)}_agent_license",
            )

            # Upload license to S3
            s3_client = S3Client()
            s3_client.upload_file(license_file, license_file_media_key)

            agent_profile.license_file_key = license_file_media_key
            agent_profile.license_file_name = license_file.name
            agent_profile.license_file_size = license_file.size
            agent_profile.save()

            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "License uploaded successfully",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
            )
        except ResourceNotFoundException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"AgentLicenseUpdateViewSet - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )

    def delete(self, request):
        try:
            agent_profile = get_agent_profile_object(request.user)

            file_key = getattr(agent_profile, "license_file_key")

            if not file_key:
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Invalid data sent",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {KEY_ERROR_MESSAGE: "Document does not exist"},
                    }
                )
            # Delete file from S3
            s3_client = S3Client()
            s3_client.delete_file(file_key)

            setattr(agent_profile, "license_file_key", None)
            setattr(agent_profile, "license_file_name", None)
            setattr(agent_profile, "license_file_size", None)
            agent_profile.save()
            user_serializer = UserSerializer(request.user)
            return Response(
                status=status.HTTP_200_OK,
                data={
                    KEY_MESSAGE: "Document deleted successfully",
                    KEY_PAYLOAD: user_serializer.data,
                    KEY_ERROR: {},
                },
            )
        except InvalidSerializerDataException as invalid_data:
            return Response(
                status=invalid_data.status_code, data=invalid_data.to_dict()
            )
        except Exception as error:
            message = "Unknown error occurred"
            logger.error(f"UserProfilePhotoUpdate - {message} - {error}")
            raise InternalServerException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: f"{message} - {error}"},
                }
            )


class AgentInvitationViewSet(APIView):
    """
    An API view to send an invitation to agent
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    def get_permissions(self):
        # Define your default permissions
        permission_classes = []

        # Access the request data (for POST, PUT, PATCH, etc.)
        if self.request.method in ["GET", "POST"]:
            role_name = self.request.query_params.get("user_role", None)
            role = get_role_object(role_name)
            if role.name == AGENT:
                permission_classes = [IsAuthenticatedAgent]
            elif role.name == INVESTOR:
                permission_classes = [IsAuthenticatedInvestor]
            else:
                raise PermissionDenied(detail="Role not found")
        # Return the appropriate permission classes
        return [permission() for permission in permission_classes]

    @general_exception_handler
    def post(self, request):
        user_role = request.query_params.get("user_role", None)
        if not user_role:
            raise_invalid_data_exception("Role not found")

        user_role_object = get_role_object(user_role)

        serialize = AgentInvitationSerializer(data=request.data)

        validated_data = validate_serializer(serialize)
        phone_number = validated_data.get("phone_number")

        db_agent_obj = get_db_agent_object(phone_number)

        invitation_details = AgentInvitations.objects.filter(
            agent__phone=phone_number,
            created_by_id=request.user,
            created_by_role_id=user_role_object,
        )

        if invitation_details.exists():
            error_message = "Invitation is already sent"
            logger.error(error_message)
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invitation is already sent",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: error_message,
                }
            )

        if user_role == AGENT:
            user_object = get_agent_profile_object(request.user)
        elif user_role == INVESTOR:
            user_object = get_investor_profile_object(request.user)

        property_id = validated_data.get("property_id")
        sms_gateway_client = InfobipClient(phone_number, user_object.name, user_role)
        sent_message = sms_gateway_client.send_invitation_to_agent(property_id)

        AgentInvitations.objects.create(
            agent=db_agent_obj,
            created_by=request.user,
            created_by_role_id=user_role_object.id,
            message_id=sent_message["messages"][0]["messageId"],
        )

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: "Successfully invited the agent to the app",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class RemoveUserProfileView(APIView):
    """
    Remove user profile API view
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    @log_input_output
    @transaction.atomic
    def delete(self, request):
        user_role = request.query_params.get("user_role", None)
        user_object = get_user_object(request.user.id)
        role = get_role_object(user_role)

        if role.name == AGENT:
            agent_profile = get_agent_profile_object(user_object)

            # If the property is created by agent and it is not verified by owner yet then delete it
            Property.objects.filter(
                created_by=user_object.id,
                created_by_role__name=role,
                owner_verified=False,
            ).delete()

            # Remove agent profile
            agent_profile.delete()

        elif role.name == INVESTOR:
            investor_profile = get_investor_profile_object(user_object)

            # If the property is created by investor then delete it
            Property.objects.filter(
                owner_verified=True,
                owner=investor_profile,
            ).delete()

            property_co_owner_object = PropertyCoOwner.objects.filter(
                co_owner=investor_profile
            )

            # Update the ownership percentage of lead owner once the current co-owner is deleted
            for property_details in property_co_owner_object.all():
                property_object = property_details.property
                new_ownership_percentage = (
                    property_details.ownership_percentage
                    + property_object.lead_owner_percentage
                )
                property_object.lead_owner_percentage = new_ownership_percentage
                property_object.save()

            property_co_owner_object.delete()

            # Remove investor profile
            investor_profile.delete()

        user_id = user_object.id

        # Remove user property sharing control attributes
        UserPropertySharingControlAttributes.objects.filter(
            created_by=user_id, created_by_role__name=role
        ).delete()

        # Remove follow object for related user
        Follow.objects.filter(
            (Q(from_user=request.user) & Q(from_user_role=role))
            | (Q(to_user=request.user) & Q(to_user_role=role))
        ).delete()

        # Remove notification object for related user
        Notification.objects.filter(
            (Q(user=request.user) & Q(role=role))
            | (Q(related_user=request.user) & Q(related_user_role=role))
        ).delete()

        AuthService.blacklist_specific_tokens(request)

        # Remove associated role
        user_object.roles.remove(role)
        if not list(request.user.roles.values_list("name", flat=True)):
            # If no role is associated then remove user
            user_object.delete()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"User profile {user_object} with {user_role} has been deleted successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class SetPropertySharingPrivateFieldsAPIView(APIView):
    """
    An API view to set property sharing private fields
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def post(self, request):
        user_role, role_obj = fetch_role_obj_and_name(request)
        serializer = SetPropertySharingPrivateFieldsSerializer(data=request.data)

        validated_data = validation_utility.validate_serializer(serializer)

        logger.info(
            f"Validated data in POST SetPropertySharingPrivateFieldsAPIView is: {validated_data}"
        )

        property_sharing_private_fields_instance, created = get_or_create_db_object(
            db_model=UserPropertySharingControlAttributes,
            created_by=request.user,
            created_by_role=role_obj,
            property_category=validated_data.get("property_category"),
        )

        if (
            "valuation" not in validated_data["public_fields"]
            and "valuation" not in validated_data["private_fields"]
        ):
            validated_data["private_fields"].append("valuation")

        property_sharing_private_fields_instance.private_fields = validated_data.get(
            "private_fields", []
        )
        property_sharing_private_fields_instance.save()

        return Response(
            status=status.HTTP_200_OK,
            data={
                KEY_MESSAGE: f"Property sharing private fields are updated successfully",
                KEY_PAYLOAD: {},
                KEY_ERROR: {},
            },
        )


class BaseOTPView(APIView):
    """
    Base class for OTP-related views with common functionality.

    This class provides shared functionality for OTP operations including:
    - Phone number validation
    - Verification status checking
    - Rate limiting
    - OTP sending and verification

    The class uses TwilioService for OTP operations and maintains verification
    records in the database to track OTP status and prevent abuse.
    """

    twilio_service = TwilioService()  # Initialize once at class level

    def validate_phone_number(self, phone_number):
        """
        Validate phone number format and existence.

        Args:
            phone_number (str): The phone number to validate

        Returns:
            str: The validated phone number

        Raises:
            InvalidSerializerDataException: If phone number is missing or invalid
        """
        if not phone_number:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Phone number is required",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {"error_message": "Phone number is required"},
                }
            )

        if not self.twilio_service.validate_phone_number(phone_number):
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Invalid phone number",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {"error_message": "Invalid phone number"},
                }
            )

        return phone_number

    @staticmethod
    def validate_role_name(role_name):
        """
        Validate role name

        Args:
            role_name (str): The role name to validate

        Returns:
            str: The validated role name

        Raises:
            InvalidSerializerDataException: If role name is missing or invalid
        """
        if not Role.objects.filter(name=role_name).exists():
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: f"Given user role {role_name} is invalid",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: f"Given user role {role_name} is invalid"
                    },
                }
            )

        return role_name

    def get_and_validate_verification(self, phone_number, is_resend=False):
        """
        Get active verification and handle rate limits.

        Args:
            phone_number (str): The phone number to check
            is_resend (bool): Whether this is a resend request (default: False)

        Returns:
            TwilioVerification: The active verification object or None
            Response: Error response if rate limit exceeded
        """
        verification = (
            TwilioVerification.objects.filter(
                phone_number=phone_number,
                verification_type=VerificationType.LOGIN,
                is_active=True,
            )
            .order_by("-created_at")
            .first()
        )

        if verification:
            if verification.has_exceeded_rate_limit():
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Too many attempts",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            "error_message": "Please try again after some time"
                        },
                    }
                )
            elif not is_resend:
                # For send requests, if rate limit not exceeded, mark as inactive
                verification.is_active = False
                verification.save()
                return None
        return verification

    def send_otp(self, phone_number):
        """
        Send OTP using Twilio service.

        Args:
            phone_number (str): The phone number to send OTP to

        Returns:
            str: The verification SID from Twilio

        Raises:
            InternalServerException: If OTP sending fails
        """
        result = self.twilio_service.send_verification(phone_number)

        if result.get("error"):
            raise TwilioException(
                {
                    KEY_MESSAGE: "Failed to send OTP",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: result["error"],
                }
            )

        return result["data"].get("sid", "")


class SendLoginOTPView(BaseOTPView):
    """
    View for sending initial OTP for user login/registration.

    This view handles the first step of the authentication process:
    1. Validates the phone number
    2. Checks for existing active verifications
    3. Sends a new OTP via Twilio
    4. Creates a verification record

    The view implements rate limiting and prevents multiple active
    verifications for the same phone number.
    """

    @general_exception_handler
    def post(self, request):
        """
        Initiate login process by sending OTP.

        Args:
            request: HTTP request containing:
                - phone_number: User's phone number to send OTP to

        Returns:
            Response containing:
                - message: Success message
                - payload: Phone number the OTP was sent to
                - error: Empty dict if successful

        Raises:
            InvalidSerializerDataException: If phone number is invalid or active OTP exists
            InternalServerException: If OTP sending fails
        """
        phone_number = self.validate_phone_number(request.data.get("phone_number"))

        # Check for existing verification and handle rate limits
        existing_verification = self.get_and_validate_verification(
            phone_number, is_resend=False
        )

        if existing_verification:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Active OTP exists",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        "error_message": "An active OTP already exists. Please use resend if needed."
                    },
                }
            )

        if (
            settings.ENVIRONMENT in ["development", "staging"]
            or phone_number == "+************"
        ):
            verification_sid = "testid"
        else:
            verification_sid = self.send_otp(phone_number)

        # Create new verification object
        TwilioVerification.objects.create(
            phone_number=phone_number,
            verification_sid=verification_sid,
            verification_type=VerificationType.LOGIN,
            attempts=1,  # Initialize attempt count
        )

        return Response(
            {
                KEY_MESSAGE: "OTP sent successfully",
                KEY_PAYLOAD: {"phone_number": str(phone_number)},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class ResendLoginOTPView(BaseOTPView):
    """
    View for resending OTP for user login.

    This view handles OTP resend requests:
    1. Validates the phone number
    2. Verifies an active verification exists
    3. Checks rate limits
    4. Sends a new OTP
    5. Updates the verification record

    The view ensures that resend requests are properly tracked
    and rate limited to prevent abuse.
    """

    @general_exception_handler
    def post(self, request):
        """
        Resend OTP for login.

        Args:
            request: HTTP request containing:
                - phone_number: User's phone number to resend OTP to

        Returns:
            Response containing:
                - message: Success message
                - payload: Phone number the OTP was resent to
                - error: Empty dict if successful

        Raises:
            InvalidSerializerDataException: If phone number is invalid or no active OTP exists
            InternalServerException: If OTP sending fails
        """
        phone_number = self.validate_phone_number(request.data.get("phone_number"))

        # Get existing verification and handle rate limits
        existing_verification = self.get_and_validate_verification(
            phone_number, is_resend=True
        )

        if not existing_verification:
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "No active OTP found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        "error_message": "No active OTP found. Please request a new OTP."
                    },
                }
            )

        # todo change it to proper flow
        if (
            settings.ENVIRONMENT in ["development", "staging"]
            or phone_number == "+************"
        ):
            verification_sid = "testid"
        else:
            verification_sid = self.send_otp(phone_number)

        # Create new verification object with incremented attempt count
        TwilioVerification.objects.create(
            phone_number=phone_number,
            verification_sid=verification_sid,
            verification_type=VerificationType.LOGIN,
            attempts=existing_verification.attempts + 1,  # Increment attempt count
        )

        return Response(
            {
                KEY_MESSAGE: "OTP resent successfully",
                KEY_PAYLOAD: {"phone_number": str(phone_number)},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class VerifyLoginOTPView(BaseOTPView):
    """
    View for verifying OTP and completing user login/registration.

    This view handles the final step of the authentication process:
    1. Validates the provided OTP
    2. Verifies the phone number
    3. Creates or retrieves the user account
    4. Returns the authentication response

    The user creation process:
    - For existing users: Retrieves the user by phone number
    - For new users: Creates a new user with a system-generated UUID
    - For Firebase users: The Firebase UID is set during Firebase authentication

    Authentication Flow:
    1. User provides phone number and OTP
    2. System validates OTP against stored verification
    3. If valid, system either:
       - Retrieves existing user by phone number
       - Creates new user with system-generated UUID
    4. Returns user details and authentication status
    """

    @general_exception_handler
    def post(self, request):
        """
        Handle OTP verification and user authentication.

        Args:
            request: HTTP request containing:
                - phone_number: User's phone number
                - otp: One-time password to verify

        Returns:
            Response containing:
                - user_id: User's unique identifier (Firebase UID or system UUID)
                - phone_number: Verified phone number
                - is_new_user: Boolean indicating if this is a new user registration

        Raises:
            InvalidSerializerDataException: If required fields are missing
            ResourceNotFoundException: If no active verification exists
            InternalServerException: If OTP verification fails
        """
        phone_number = request.data.get("phone_number")
        otp = request.data.get("otp")
        role_name = self.validate_role_name(request.data.get("role_name"))

        if phone_number == "+************" and otp not in [123456, "123456"]:
            return Response(
                {
                    KEY_MESSAGE: "OTP verification failed",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {},
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate required fields
        if not all([phone_number, otp]):
            return Response(
                {
                    KEY_MESSAGE: "Missing required fields",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {"error_message": "Phone number and OTP are required"},
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate phone number format
        phone_number = self.validate_phone_number(phone_number)

        # Get the most recent active verification
        verification = (
            TwilioVerification.objects.filter(
                phone_number=phone_number,
                verification_type=VerificationType.LOGIN,
                is_active=True,
            )
            .order_by("-created_at")
            .first()
        )

        if not verification:
            return Response(
                {
                    KEY_MESSAGE: "No active OTP found",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {"error_message": "Please request a new OTP"},
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if (
            settings.ENVIRONMENT in ["development", "staging"]
            or phone_number == "+************"
        ):
            verification_result = {"status": "approved"}
        else:
            # Verify OTP with Twilio service
            verification_result = self.twilio_service.verify_otp(phone_number, otp)

        if verification_result.get("error"):
            return Response(
                {
                    KEY_MESSAGE: "OTP verification failed",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: verification_result["error"],
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Mark verification as complete
        verification.is_verified = True
        verification.is_active = False
        verification.save()

        [user, created, new_user_with_role_created] = get_or_create_user_with_role(
            phone_number=phone_number, role_name=role_name
        )
        # Get or create user
        # user, created = User.objects.get_or_create(
        #     primary_phone_number=phone_number,
        #     defaults={
        #         "is_active": True,
        #         "id": str(
        #             uuid.uuid4()
        #         ),  # Generate a new UUID string for non-Firebase users
        #     },
        # )

        # new_user_with_role_created = False
        # if not user.roles.filter(name=role_name).exists():
        #     new_user_with_role_created = True
        #     role = Role.objects.get(name=role_name)
        #     user.roles.add(role)
        #     logger.info(f"Role {role_name} is assigned to {user}")

        # user.save()

        # Generate JWT tokens using SimpleJWT
        generated_token = RefreshToken.for_user(user)

        generated_token["phone_number"] = str(user.primary_phone_number)

        return Response(
            {
                KEY_MESSAGE: "Login successful",
                KEY_PAYLOAD: {
                    "user_id": str(user.id),  # Firebase UID or system UUID
                    "phone_number": str(phone_number),
                    "is_new_user": new_user_with_role_created,
                    "access_token": str(generated_token.access_token),
                    "refresh_token": str(generated_token),
                },
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class SubscriptionAssetsView(APIView):
    """
    API endpoint to get static subscription-related asset URLs
    """

    authentication_classes = [FirebaseAuthentication, JWTAuthentication]

    @general_exception_handler
    def get(self, request):
        """Get static subscription asset URLs"""

        assets = {
            "do_more_with_premium": settings.DO_MORE_WITH_PREMIUM,
            "unlocked_benefits": settings.UNLOCKED_BENIFITS,
        }

        return Response(
            {
                KEY_MESSAGE: "Login successful",
                KEY_PAYLOAD: {"assets": assets},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )


class ScreenVisibility(APIView):
    """
    API endpoint to get static subscription-related asset URLs
    """

    authentication_classes = [JWTAuthentication]

    @general_exception_handler
    def get(self, request):
        """Get static subscription asset URLs"""

        user_role, role_obj = fetch_role_obj_and_name(request)

        # Check if the user has a Dubai property
        has_dubai_property = user_has_dubai_property(request.user, role_obj)

        # Check if the user has a Dubai property
        has_india_property = user_has_india_property(request.user, role_obj)

        feature_visibility = {
            "news_is_visible": has_dubai_property,
            "trends_and_transactions_is_visible": has_dubai_property,
            "portfolio_is_visible": True,
            "add_property_is_visible": True,
            "explore_is_visible": has_india_property,
        }

        return Response(
            {
                KEY_MESSAGE: "Feature visibility fetched for user",
                KEY_PAYLOAD: {"feature_visibility": feature_visibility},
                KEY_ERROR: {},
            },
            status=status.HTTP_200_OK,
        )
