from rest_framework.permissions import BasePermission

from rezio.user.constants import INVESTOR
from rezio.user.models import User

from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import (
    InternalServerException,
    InvalidSerializerDataException,
)


class IsAuthenticatedInvestor(BasePermission):
    message = "Given user has to be authenticated investor"

    def has_permission(self, request, view):
        """
        If the user is authenticated and the user is an investor

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            if not request.user.is_authenticated:
                return False

            user = User.objects.prefetch_related("roles").get(id=request.user.id)

            return user.roles.filter(name=INVESTOR).exists()
        except (AttributeError, KeyError) as error:
            message = "Given user has to be authenticated investor"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
