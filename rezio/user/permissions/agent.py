from rest_framework.permissions import BasePermission

from rezio.user.constants import AGENT
from rezio.user.models import User
from rezio.utils.constants import KEY_MESSAGE, KEY_PAYLOAD, KEY_ERROR, KEY_ERROR_MESSAGE
from rezio.utils.custom_exceptions import InvalidSerializerDataException


class IsAuthenticatedAgent(BasePermission):
    message = "Given user has to be authenticated agent"

    def has_permission(self, request, view):
        """
        If the user is authenticated and the user is an agent

        Parameters:
            request: The request object
            view: The view being accessed
        Returns:
            A boolean value.
        """
        try:
            if not request.user.is_authenticated:
                return False

            user = User.objects.prefetch_related("roles").get(id=request.user.id)

            return user.roles.filter(name=AGENT).exists()
        except (AttributeError, KeyError):
            message = "Given user has to be authenticated agent"
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: message,
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {KEY_ERROR_MESSAGE: message},
                }
            )
