import logging
import os

from celery.signals import (
    after_setup_logger,
    after_setup_task_logger,
    task_failure,
    task_success,
    worker_ready,
    task_sent,
    task_prerun,
    task_revoked,
    task_retry,
)
from django.conf import settings

from celery import Celery
from rezio.utils.constants import DJANGO_LOGGER_NAME

# Set the default Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "rezio.rezio.settings")

# Initialize Django first
import django

django.setup()

logger = logging.getLogger(DJANGO_LOGGER_NAME)

# Initialize Celery app
app = Celery("rezio")

CELERY_BROKER_URL = settings.CELERY_BROKER_URL
app.conf.broker_url = CELERY_BROKER_URL
# Configure Celery using Django settings
app.config_from_object("django.conf:settings", namespace="CELERY")

# Default configuration
app.conf.update(
    # Task settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone=settings.TIME_ZONE,
    enable_utc=True,
    # Task execution settings
    task_always_eager=settings.DEBUG,
    task_eager_propagates=True,
    task_time_limit=900,  # 15 minutes
    task_soft_time_limit=600,  # 10 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend="django-db",
    # Retry settings
    task_publish_retry=True,
    task_publish_retry_policy={
        "max_retries": 3,
        "interval_start": 0,
        "interval_step": 0.2,
        "interval_max": 0.2,
    },
    # Signal settings
    task_send_sent_event=True,
    task_track_started=True,
    worker_send_task_events=True,
)

# Load tasks from all registered Django apps
app.autodiscover_tasks()

# Move task imports here, after Django is set up
from rezio.news.tasks import fetch_news_task
from rezio.user.tasks import check_agent_license_expiry, update_brn_details_from_dld
from rezio.properties.tasks.similar_transaction_notifications import (
    fetch_similar_transactions_and_notify,
)


# Celery signals for monitoring and logging
@after_setup_logger.connect
def setup_logger(logger, *args, **kwargs):
    """Configure Celery logger after setup."""
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Add file handler
    fh = logging.FileHandler("celery.log")
    fh.setFormatter(formatter)
    logger.addHandler(fh)


@after_setup_task_logger.connect
def setup_task_logger(logger, *args, **kwargs):
    """Configure task-specific logger."""
    formatter = logging.Formatter(
        "%(asctime)s - %(task_name)s - %(task_id)s - %(message)s"
    )

    # Add file handler
    fh = logging.FileHandler("celery_tasks.log")
    fh.setFormatter(formatter)
    logger.addHandler(fh)


@task_failure.connect
def handle_task_failure(task_id, exception, args, kwargs, traceback, einfo, **more):
    """Handle and log task failures."""
    logger.error(
        f"Task {task_id} failed: {exception}",
        exc_info=(type(exception), exception, traceback),
        extra={
            "task_id": task_id,
            "args": args,
            "kwargs": kwargs,
        },
    )


@task_success.connect
def handle_task_success(sender=None, **kwargs):
    """Log successful task completion."""
    logger.info(f"Task {sender.request.id} completed successfully")


@worker_ready.connect
def handle_worker_ready(**kwargs):
    """Log worker startup."""
    logger.info("Celery worker is ready to receive tasks")


@app.task(bind=True)
def debug_task(self):
    """Debug task to verify Celery configuration."""
    logger.info(f"Request: {self.request!r}")
    return {
        "task_id": self.request.id,
        "args": self.request.args,
        "kwargs": self.request.kwargs,
    }


# Health check task
@app.task(bind=True)
def health_check(self):
    """
    Health check task to verify Celery worker status.

    Returns:
        dict: Health status information
    """
    from django.utils import timezone

    return {
        "status": "healthy",
        "timestamp": timezone.now().isoformat(),
        "worker_id": self.request.hostname,
        "task_id": self.request.id,
    }


# Error reporting task
# @app.task(bind=True)
# def report_error(self, task_id, error_msg, traceback_str):
#     """
#     Report task errors to monitoring system.
#
#     Args:
#         task_id (str): Failed task ID
#         error_msg (str): Error message
#         traceback_str (str): Traceback string
#     """
#     logger.error(
#         f"Task Error Report - Task ID: {task_id}",
#         extra={
#             'error_msg': error_msg,
#             'traceback': traceback_str,
#         }
#     )
#
#     # Implement error reporting to your monitoring system
#     # Example: Send to Sentry, Slack, etc.
#     if hasattr(settings, 'SENTRY_DSN'):
#         from sentry_sdk import capture_message
#         capture_message(f"Celery Task Error: {error_msg}")


# Optional: Register periodic tasks
@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """
    Set up periodic tasks that should run at specified intervals.
    """

    sender.add_periodic_task(
        settings.TASK_SCHEDULE_TIME.get("check_agent_license_expiry"),
        check_agent_license_expiry.s(),
        name="Check agent license expiry",
    )

    sender.add_periodic_task(
        settings.TASK_SCHEDULE_TIME.get("fetch_news_task"),
        fetch_news_task.s(),
        name="Fetch news task",
    )
    sender.add_periodic_task(
        settings.TASK_SCHEDULE_TIME.get("fetch_similar_transactions_and_notify"),
        fetch_similar_transactions_and_notify.s(),
        name="Similar Transaction Notifications",
    )
    sender.add_periodic_task(
        settings.TASK_SCHEDULE_TIME.get("update_brn_details_from_dld"),
        update_brn_details_from_dld.s(),
        name="Update BRN details from DLD",
    )
    # Notification cleanup
    # sender.add_periodic_task(
    #     settings.CELERY_BEAT_SCHEDULE.get(
    #         'clean-old-notifications',
    #         {'schedule': 86400}  # daily
    #     ),
    #     'notifications.tasks.clean_old_notifications'
    # )

    # Health check
    sender.add_periodic_task(
        300,
        health_check.s(),
        name="celery-health-check",  # every 5 minutes
    )

    # Device token cleanup
    # sender.add_periodic_task(
    #     settings.CELERY_BEAT_SCHEDULE.get(
    #         'clean-inactive-devices',
    #         {'schedule': 604800}  # weekly
    #     ),
    #     'notifications.tasks.clean_inactive_devices'
    # )


# Task routing configuration
app.conf.task_routes = {
    "rezio.notifications.*": {"queue": "notifications"},
    # "rezio.properties.*": {"queue": "properties"},
    "rezio.analytics.*": {"queue": "analytics"},
    "default": {"queue": "default"},
    "rezio.assistant": {"queue": "ai_tasks"},
}

# Rate limiting configuration
app.conf.task_default_rate_limit = "10/m"  # Default rate limit
app.conf.task_annotations = {
    "rezio.notifications.tasks.send_push_notification": {"rate_limit": "100/m"},
    "rezio.notifications.tasks.send_email_notification": {"rate_limit": "50/m"},
}
