from django.db import models
from django.utils import timezone
from rezio.properties.models import Country

# Create your models here.


class NewsSource(models.Model):
    name = models.CharField(max_length=100)
    url = models.URLField()
    country = models.ForeignKey(
        Country, on_delete=models.CASCADE, related_name="news_sources"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "news_source"
        ordering = ["name"]
        unique_together = ["url", "country"]

    def __str__(self):
        return f"{self.name} - {self.country.name}"


class News(models.Model):
    title = models.CharField(max_length=500)
    source = models.ForeignKey(
        NewsSource, on_delete=models.CASCADE, related_name="news_items"
    )
    url = models.URLField(unique=True)
    thumbnail_url = models.URLField(null=True, blank=True)
    subtext = models.TextField(null=True, blank=True)
    published_date = models.DateTimeField()
    reading_time = models.IntegerField(default=5)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "news"
        ordering = ["-published_date"]
        verbose_name_plural = "News"
        indexes = [
            models.Index(fields=["-published_date"]),
            models.Index(fields=["source"]),
        ]

    def __str__(self):
        return self.title

    @property
    def country(self):
        return self.source.country
