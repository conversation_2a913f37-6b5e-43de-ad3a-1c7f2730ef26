from django.core.management.base import BaseCommand
from django.conf import settings
from rezio.news.tasks.news_tasks import fetch_news_task


class Command(BaseCommand):
    help = "Manually trigger news fetch from all sources"

    def handle(self, *args, **options):
        self.stdout.write(
            f"News fetch is scheduled to run daily at "
            f"{settings.NEWS_FETCH_HOUR:02d}:{settings.NEWS_FETCH_MINUTE:02d}"
        )
        self.stdout.write("Starting manual news fetch...")
        try:
            result = fetch_news_task.delay()
            self.stdout.write(
                self.style.SUCCESS(
                    f"News fetch task queued successfully. Task ID: {result.id}"
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Failed to queue news fetch task: {str(e)}")
            )
