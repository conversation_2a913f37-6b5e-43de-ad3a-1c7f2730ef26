import logging

from django.conf import settings
from django.core.cache import cache
from rest_framework.views import APIView

from rezio.news.constants import NEWS_CACHE_KEY
from rezio.news.models import News
from rezio.news.serializers.news_serializers import (
    NewsListRequestSerializer,
    NewsResponseSerializer,
)
from rezio.user.authentication import J<PERSON><PERSON>uthentication, FirebaseAuthentication
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
)
from rezio.utils.decorators import general_exception_handler
from rezio.utils.paginators import StandardResultsSetPagination

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class NewsListView(APIView):
    authentication_classes = [FirebaseAuthentication, JWTAuthentication]
    pagination_class = StandardResultsSetPagination

    @general_exception_handler
    def get(self, request):
        request_serializer = NewsListRequestSerializer(data=request.query_params)
        request_serializer.is_valid(raise_exception=True)

        # removed checking for dubai property --->

        # user_role, role_obj = fetch_role_obj_and_name(request)
        # Check if the user has a Dubai property
        # has_dubai_property = user_has_dubai_property(request.user, role_obj)
        #
        # if not has_dubai_property:  # Only show news for UAE
        #     return Response(
        #         status=status.HTTP_200_OK,
        #         data={
        #             KEY_MESSAGE: "News coming soon for your country",
        #             KEY_PAYLOAD: {},
        #             KEY_ERROR: {},
        #         },
        #     )

        # Get news data
        page = request_serializer.validated_data.get("page")
        page_size = request_serializer.validated_data.get("page_size")

        queryset = cache.get(NEWS_CACHE_KEY)
        if not queryset:
            queryset = (
                News.objects.select_related("source", "source__country")
                .filter(source__country__short_name="AE", source__is_active=True)
                .order_by("-published_date")
            )

            logger.info(f"Found {queryset.count()} news items")

            cache.set(NEWS_CACHE_KEY, queryset, settings.NEWS_CACHE_TIMEOUT)

        # Use the standard pagination
        paginator = StandardResultsSetPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = NewsResponseSerializer(paginated_queryset, many=True)

        return paginator.get_paginated_response(serializer.data)
