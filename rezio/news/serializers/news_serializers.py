from rest_framework import serializers
from rezio.news.models import News, NewsSource


class NewsListRequestSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1, min_value=1)
    page_size = serializers.IntegerField(default=20, min_value=1)


class NewsSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = NewsSource
        fields = ["name", "url"]


class NewsResponseSerializer(serializers.ModelSerializer):
    source = NewsSourceSerializer(read_only=True)

    class Meta:
        model = News
        fields = [
            "id",
            "title",
            "subtext",
            "url",
            "published_date",
            "source",
            "thumbnail_url",
            "reading_time",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["url"]  # Make url read-only for responses


class NewsListResponseSerializer(serializers.Serializer):
    count = serializers.IntegerField()
    next = serializers.IntegerField(allow_null=True)
    previous = serializers.IntegerField(allow_null=True)
    results = NewsResponseSerializer(
        many=True, read_only=True
    )  # Make results read-only
