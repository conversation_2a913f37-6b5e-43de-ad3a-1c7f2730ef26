from django.db import migrations


def add_dubai_news_sources(apps, schema_editor):
    Country = apps.get_model("properties", "Country")
    NewsSource = apps.get_model("news", "NewsSource")

    try:
        dubai = Country.objects.get(short_name="AE")

        sources = [
            {"name": "Gulf News", "url": "https://gulfnews.com/rss", "country": dubai},
            {
                "name": "Dubai Chronicle",
                "url": "https://www.dubaichronicle.com/news-feeds/",
                "country": dubai,
            },
            {
                "name": "Properties Market",
                "url": "https://properties.market/ae/blog/feed/",
                "country": dubai,
            },
            {
                "name": "Next Level Real Estate",
                "url": "https://www.nextlevelrealestate.ae/feed/",
                "country": dubai,
            },
        ]

        for source_data in sources:
            NewsSource.objects.get_or_create(
                url=source_data["url"],
                country=source_data["country"],
                defaults={"name": source_data["name"], "is_active": True},
            )

    except Country.DoesNotExist:
        print("UAE/Dubai country not found in database")


def remove_dubai_news_sources(apps, schema_editor):
    NewsSource = apps.get_model("news", "NewsSource")
    NewsSource.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("news", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(add_dubai_news_sources, remove_dubai_news_sources),
    ]
