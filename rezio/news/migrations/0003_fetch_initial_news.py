from django.db import migrations
from django.db.models import Q


def fetch_initial_news(apps, schema_editor):
    """
    Fetch initial news during first deployment
    """
    NewsSource = apps.get_model("news", "NewsSource")

    # Check if we have any active sources
    if not NewsSource.objects.filter(is_active=True).exists():
        return

    # Import and run the task
    try:
        from rezio.news.tasks import fetch_news_task

        fetch_news_task()
    except Exception as e:
        print(f"Warning: Failed to fetch initial news: {str(e)}")


def reverse_news_fetch(apps, schema_editor):
    """
    Reverse migration - no action needed
    """
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("news", "0002_add_dubai_news_sources"),
    ]

    operations = [
        migrations.RunPython(fetch_initial_news, reverse_news_fetch),
    ]
