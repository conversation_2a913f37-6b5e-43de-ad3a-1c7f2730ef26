import random

from django.db import migrations

from rezio.news.constants import DEFAULT_NEWS_THUMBNAILS


def update_news_thumbnails(apps, schema_editor):
    news_model = apps.get_model("news", "News")
    for news in news_model.objects.all():
        news.thumbnail_url = random.choice(DEFAULT_NEWS_THUMBNAILS)
        news.save()


def reverse_migrate(apps, schema_editor):
    # Can't restore original thumbnails, so doing nothing
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("news", "0004_updated_news_feed"),  # Replace with your last migration
    ]

    operations = [
        migrations.RunPython(update_news_thumbnails, reverse_migrate),
    ]
