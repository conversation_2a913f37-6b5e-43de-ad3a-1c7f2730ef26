# Generated by Django 4.1.7 on 2025-02-13 08:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("properties", "0074_alter_propertyavailabilityandstatus_occupancy_status"),
    ]

    operations = [
        migrations.CreateModel(
            name="NewsSource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("url", models.URLField()),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "country",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="news_sources",
                        to="properties.country",
                    ),
                ),
            ],
            options={
                "db_table": "news_source",
                "ordering": ["name"],
                "unique_together": {("url", "country")},
            },
        ),
        migrations.CreateModel(
            name="News",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=500)),
                ("url", models.URLField(unique=True)),
                ("thumbnail_url", models.URLField(blank=True, null=True)),
                ("subtext", models.TextField(blank=True, null=True)),
                ("published_date", models.DateTimeField()),
                ("reading_time", models.IntegerField(default=5)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="news_items",
                        to="news.newssource",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "News",
                "db_table": "news",
                "ordering": ["-published_date"],
            },
        ),
        migrations.AddIndex(
            model_name="news",
            index=models.Index(
                fields=["-published_date"], name="news_publish_032fef_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="news",
            index=models.Index(fields=["source"], name="news_source__e41e38_idx"),
        ),
    ]
