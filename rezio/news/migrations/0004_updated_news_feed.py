# Generated by Django 4.1.7 on 2025-02-19 05:55

from django.db import migrations


def update_new_dubai_news_sources(apps, schema_editor):
    country = apps.get_model("properties", "Country")
    news_source = apps.get_model("news", "NewsSource")

    try:
        dubai = country.objects.get(short_name="AE")

        news_source.objects.filter(
            url="https://www.dubaichronicle.com/news-feeds/",
            country=dubai,
        ).update(
            url="https://www.dubaichronicle.com/business/real-estate-business/feed/"
        )

    except country.DoesNotExist:
        print("UAE/Dubai country not found in database")


def update_old_dubai_news_sources(apps, schema_editor):
    country = apps.get_model("properties", "Country")
    news_source = apps.get_model("news", "NewsSource")
    try:
        dubai = country.objects.get(short_name="AE")
        news_source.objects.filter(
            url="https://www.dubaichronicle.com/business/real-estate-business/feed/",
            country=dubai,
        ).update(url="https://www.dubaichronicle.com/news-feeds/")
    except country.DoesNotExist:
        print("UAE/Dubai country not found in database")


class Migration(migrations.Migration):

    dependencies = [
        ("news", "0003_fetch_initial_news"),
    ]

    operations = [
        migrations.RunPython(
            update_new_dubai_news_sources, update_old_dubai_news_sources
        ),
    ]
