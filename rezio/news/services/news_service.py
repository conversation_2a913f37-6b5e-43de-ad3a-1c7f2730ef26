import logging
import random
from datetime import timed<PERSON><PERSON>, datetime

import feedparser
from django.apps import apps
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from rezio.news.constants import DEFAULT_NEWS_THUMBNAILS
from rezio.news.constants import NEWS_CACHE_KEY
from rezio.utils.constants import DJANGO_LOGGER_NAME

# ... other imports ...

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class NewsFetcherService:
    @staticmethod
    def fetch_and_store_news():
        logger.info("Starting news fetch process")
        try:
            cutoff_date = timezone.now() - timedelta(days=settings.NEWS_MAX_AGE_DAYS)
            news_model = apps.get_model("news", "News")
            news_model.objects.filter(published_date__lt=cutoff_date).delete()

            # Get sources by country
            news_source_model = apps.get_model("news", "NewsSource")
            sources = news_source_model.objects.filter(
                is_active=True,
                country__short_name="AE",  # Dubai/UAE sources
            ).select_related("country")

            if not sources.exists():
                logger.warning("No active news sources found for UAE")
                return 0

            new_articles_count = 0

            for source in sources:
                try:
                    feed = feedparser.parse(source.url)

                    if not feed.entries:
                        logger.info(
                            "❌ No articles found for! Check the feed URL or structure."
                        )
                        continue

                    for entry in feed.entries:
                        news_data = NewsFetcherService._process_entry(entry, source)
                        if news_data:
                            _, created = news_model.objects.update_or_create(
                                url=entry.link, defaults=news_data
                            )
                            if created:
                                new_articles_count += 1
                except Exception as e:
                    logger.error(f"Error processing source {source.name}: {str(e)}")
                    continue

            cache.delete(NEWS_CACHE_KEY)
            logger.info(f"Added {new_articles_count} new articles")
            return new_articles_count

        except Exception as e:
            logger.error(f"News fetch failed: {str(e)}")
            raise e

    @staticmethod
    def _process_entry(entry, source):
        """
        Process a single news entry with better image extraction.
        """
        try:
            # Extract data directly from feed entry
            description = entry.get("description", "")
            if not description and hasattr(entry, "summary"):
                description = entry.summary

            # Calculate reading time from description
            reading_time = (
                NewsFetcherService._calculate_reading_time(description)
                if description
                else 1
            )

            # Parse the published date
            published_date = None
            if hasattr(entry, "published_parsed"):
                published_date = timezone.make_aware(
                    datetime.fromtimestamp(
                        datetime(*entry.published_parsed[:6]).timestamp()
                    )
                )

            # Better image extraction logic
            thumbnail_url = random.choice(DEFAULT_NEWS_THUMBNAILS)

            processed_data = {
                "title": entry.title,
                "url": entry.link,
                "source": source,
                "published_date": published_date,
                "thumbnail_url": thumbnail_url,
                "subtext": description[:150] if description else None,
                "reading_time": reading_time,
            }

            return processed_data

        except Exception as error:
            logger.error(f"Error processing entry {entry.link} - {error}")
            return None

    @staticmethod
    def _calculate_reading_time(text):
        """
        Calculate estimated reading time in minutes.
        Average reading speed: 200 words per minute
        """
        words = len(text.split())
        minutes = max(1, round(words / 200))
        return minutes
