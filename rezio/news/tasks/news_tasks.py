import logging

from celery import shared_task
from django.conf import settings

from rezio.news.services.news_service import NewsFetcherService
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


@shared_task(
    name="fetch_news",
    queue="default",
    autoretry_for=(Exception,),
    max_retries=3,
    retry_backoff=True,
)
def fetch_news_task():
    """
    Task to fetch news from all configured sources.
    Scheduled to run daily at {NEWS_FETCH_HOUR}:{NEWS_FETCH_MINUTE}
    """
    logger.info(
        f"Starting scheduled news fetch (scheduled for "
        f"{settings.NEWS_FETCH_HOUR:02d}:{settings.NEWS_FETCH_MINUTE:02d})"
    )
    try:
        new_articles = NewsFetcherService.fetch_and_store_news()
        logger.info(f"Successfully fetched {new_articles} new articles")
        return new_articles
    except Exception as e:
        logger.error(f"Error in news fetch task: {str(e)}")
        raise
