import json
import requests
import logging

from django.conf import settings
from rezio.user.constants import AGENT, INVESTOR
from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class InfobipClient:
    def __init__(self, to_sms: str, sender_name: str, sender_role_name: str):
        self.sender = settings.INFOBIP_SENDER_NAME
        self.base_url = settings.INFOBIP_BASE_URL
        self._api_key = settings.INFOBIP_API_KEY
        self.sms_endpoint = settings.INFOBIP_SEND_SMS_ENDPOINT
        self.url = self.base_url + self.sms_endpoint
        self.headers = {
            "Authorization": f"App {self._api_key}",
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
        self.to_sms = to_sms
        self.sender_name = sender_name
        self.sender_role_name = sender_role_name

    def construct_invitation_message(self, property_id=None):
        message = None
        if self.sender_role_name == AGENT:
            if property_id:
                message = f"{self.sender_name} is inviting manage property on Rezio."
            else:
                message = f"{self.sender_name} is inviting you to create and manage your network and properties on Rezio."
        elif self.sender_role_name == INVESTOR:
            if property_id:
                message = f"{self.sender_name} is inviting manage property on Rezio."
            else:
                message = f"{self.sender_name} is inviting you to sell his property on Rezio. Join now."

        return message

    def send_sms(self, message):
        payload = json.dumps(
            {
                "messages": [
                    {
                        "sender": self.sender,
                        "destinations": [{"to": self.to_sms}],
                        "content": {"text": message},
                    }
                ]
            }
        )

        try:
            response = requests.post(self.url, headers=self.headers, data=payload)

            if response.status_code == 200:
                logger.info(f"Invitation message sent successfully to: {self.to_sms}")
                data = response.json()
                return data
            else:
                logger.warning(f"Failed to send invitation message to: {self.to_sms}")
                raise ValueError("Failed to send invitation message")
        except requests.exceptions.RequestException as error:
            logger.error(f"Invite request failed: {error}")
            raise
        except Exception as error:
            logger.error(f"Error in send_sms: {error}")
            raise

    def send_invitation_to_agent(self, property_id=None):
        invitation_message = self.construct_invitation_message(property_id)
        response = self.send_sms(invitation_message)
        return response
