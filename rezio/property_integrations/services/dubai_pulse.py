import logging
import requests
from django.conf import settings

from rezio.utils.constants import DJ<PERSON>GO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)

class DubaiPulseAPI:
    @staticmethod
    def get_access_token():
        """
            Obtain an access token from the Dubai Pulse API.
            :return: Access token as a string.
        """
        url = f"{settings.DUBAI_PULSE_API_HOST}{settings.DUBAI_PULSE_AUTH_URL}"

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        data = {
            'client_id': settings.DUBAI_PULSE_CLIENT_ID,
            'client_secret': settings.DUBAI_PULSE_CLIENT_SECRET,
        }
        try:
            response = requests.post(url=url, headers=headers, data=data)
            response.raise_for_status()
            access_token = response.json().get('access_token')
            if not access_token:
                logger.error('Failed to retrieve access token.')
                raise ValueError('Access token is missing in the response.')
            logger.info('Access token retrieved successfully.')
            return access_token
        except requests.exceptions.RequestException as e:
            logger.error(f'HTTP request failed: {e}')
            raise
        except Exception as error:
            logger.error(f'Error in get_access_token for dubai pulse: {error}')
            raise

    @staticmethod
    def get_broker_data(broker_number):
        """
            Retrieve broker data from the Dubai Pulse API.
            :param broker_number: Broker number as a string.
            :return: Broker data as a dictionary.
        """
        access_token = DubaiPulseAPI.get_access_token()
        url = f"{settings.DUBAI_PULSE_API_HOST}{settings.DUBAI_PULSE_DATA_ENDPOINT}?filter={settings.DUBAI_PULSE_DATA_FILTER.format(broker_number=broker_number)}"

        headers = {
            'Accept': 'application/json',
            'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
            'Authorization': f'Bearer {access_token}',
        }
        try:
            response = requests.get(url=url, headers=headers)
            response.raise_for_status()
            logger.info('Broker data retrieved successfully.')
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f'HTTP request failed: {e}')
            raise
