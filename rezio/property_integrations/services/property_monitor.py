import logging

import requests
from django.conf import settings
from django.core.cache import cache

from rezio.rezio.custom_error_codes import (
    DATA_NOT_FOUND_FROM_PM,
    REQUEST_EXCEPTION_FROM_PM,
    UNKNOWN_ERROR_FROM_PM,
)
from rezio.utils.constants import (
    DJANGO_LOGGER_NAME,
    KEY_MESSAGE,
    KEY_PAYLOAD,
    KEY_ERROR,
    KEY_ERROR_MESSAGE,
    KEY_ERROR_CODE,
)
from rezio.utils.custom_exceptions import InvalidSerializerDataException
from rezio.utils.decorators import log_input_output

logger = logging.getLogger(DJANGO_LOGGER_NAME)


class PropertyMonitorAPI:
    def __init__(self):
        self.api_token = settings.PROPERTY_MONITOR_API_TOKEN
        self.api_key = settings.PROPERTY_MONITOR_API_KEY
        self.host = settings.PROPERTY_MONITOR_HOST
        self.headers = {
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
            "Content-Type": "application/x-www-form-urlencoded",
            "Api-Token": self.api_token,
            "x-api-key": self.api_key,
        }

    def get_location(self, emirate, keyword):
        """
        Get location details from Property monitor APIs.
        :return: list of locations.
        """
        url = (
            f"{self.host}{settings.PROPERTY_MONITOR_LOCATION_ENDPOINT}?"
            f"{settings.PROPERTY_MONITOR_SEARCH_EMIRATE_KEY}{emirate}&"
            f"{settings.PROPERTY_MONITOR_SEARCH_KEY}{keyword}"
        )

        print(f"get_location URL {url}")

        try:
            response = requests.get(url=url, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            if not data or data.get("status") != 1:
                logger.error("Failed to retrieve location details.")
                raise ValueError("Location not found")
            logger.info("Locations retrieved successfully.")
            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as error:
            logger.error(f"Error in get_location for property monitor: {error}")
            raise

    def get_units_for_location(self, location_id, unit_search, page):
        """
        Get units for a location from Property monitor APIs.
        :return: list of unit info.
        """
        url = (
            f"{self.host}{settings.PROPERTY_MONITOR_ADDRESS_ENDPOINT}?"
            f"{settings.PROPERTY_MONITOR_SEARCH_LOCATION_KEY}{location_id}&"
            f"{settings.PROPERTY_MONITOR_SEARCH_UNIT_KEY}{unit_search}&"
            f"{settings.PROPERTY_MONITOR_PAGE_KEY}{page}"
        )

        try:
            response = requests.get(url=url, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            if not data or data.get("status") != 1:
                logger.error("Failed to retrieve units.")
                raise ValueError("Unit information data not found")
            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as error:
            logger.error(
                f"Error in get_units_for_location for property monitor: {error}"
            )
            raise

    @log_input_output
    def get_unit_details(self, unit_no):
        """
        Get units for a location from Property monitor APIs.
        :return: list of unit info.
        """
        url = (
            f"{self.host}{settings.PROPERTY_MONITOR_UNIT_DETAILS_ENDPOINT}?"
            f"{settings.PROPERTY_MONITOR_GET_UNIT_DETAILS_KEY}{unit_no}"
        )

        try:
            response = requests.get(url=url, headers=self.headers)
            response.raise_for_status()
            data = response.json().get("data", None)
            if not data:
                logger.error(f"Failed to retrieve unit details for {unit_no} from PM.")
                raise InvalidSerializerDataException(
                    {
                        KEY_MESSAGE: "Error while fetching unit details from PM",
                        KEY_PAYLOAD: {},
                        KEY_ERROR: {
                            KEY_ERROR_MESSAGE: DATA_NOT_FOUND_FROM_PM.get("message"),
                            KEY_ERROR_CODE: DATA_NOT_FOUND_FROM_PM.get("code"),
                        },
                    }
                )
            return data
        except InvalidSerializerDataException:
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed while fetching data from PM: {e}")
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while fetching unit details from PM",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: REQUEST_EXCEPTION_FROM_PM.get("message"),
                        KEY_ERROR_CODE: REQUEST_EXCEPTION_FROM_PM.get("code"),
                    },
                }
            )
        except Exception as error:
            logger.error(
                f"Error in get_units_for_location for property monitor: {error}"
            )
            raise InvalidSerializerDataException(
                {
                    KEY_MESSAGE: "Error while fetching unit details from PM",
                    KEY_PAYLOAD: {},
                    KEY_ERROR: {
                        KEY_ERROR_MESSAGE: UNKNOWN_ERROR_FROM_PM.get("message"),
                        KEY_ERROR_CODE: UNKNOWN_ERROR_FROM_PM.get("code"),
                    },
                }
            )

    @log_input_output
    def get_sale_price_volume_trend(
        self,
        master_development,
        location,
        property_type,
        category=None,
        last_x_months=None,
    ):
        """
        Get month wise price trend for a location.
        :return: list of price volume data.
        """
        if not category:
            category = "sale"
        url = (
            f"{self.host}{settings.PROPERTY_MONITOR_VOLUME_TREND_ENDPOINT}?"
            f"{settings.PROPERTY_MONITOR_MASTER_DEVELOPMENT_KEY}{master_development}&"
            f"{settings.PROPERTY_MONITOR_SEARCH_LOCATION_NAME_KEY}{location}&"
            f"{settings.PROPERTY_MONITOR_PROPERTY_TYPE_KEY}{property_type}&"
            f"{settings.PROPERTY_MONITOR_CATEGORY_KEY}{category}&"
            f"{settings.PROPERTY_MONITOR_LAST_X_MONTHS_KEY}{last_x_months}"
        )

        try:
            response = requests.get(url=url, headers=self.headers)
            response.raise_for_status()
            data = response.json().get("data", None)
            if not data:
                logger.error("Failed to volume trend data.")
                raise ValueError("Failed to volume trend data")
            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as error:
            logger.error(
                f"Error in get_sale_price_volume_trend for property monitor: {error}"
            )
            raise

    @log_input_output
    def get_property_listings(self, params, fetch_similar_transactions=False):
        """
        Fetch property listings based on specified search parameters.

        Args:
            params: An object containing the following fields:
                emirate (str): Name of the emirate
                master_development (str): Name of the master development
                sub_loc_1 (str): Sub-location level 1
                sub_loc_2 (str): Sub-location level 2
                sub_loc_3 (str): Sub-location level 3
                sub_loc_4 (str): Sub-location level 4
                category (str): Category of listing (e.g., 'rent', 'sale')
                property_types (list/str): Property types to search for
                number_of_bedrooms (int): Number of bedrooms
                min_area (float): Minimum area in sq ft
                max_area (float): Maximum area in sq ft
                duration (int): Number of months for date range calculation
                min_price (float): Minimum price
                max_price (float): Maximum price
                contract_type (str): Type of contract
                page (int): Page number for pagination
            fetch_similar_transactions (bool): Flag to fetch similar transactions

        Returns:
            dict: Property listings matching the search criteria

        Raises:
            ValueError: If property listings data is not found
            requests.exceptions.RequestException: If HTTP request fails
        """
        logger.info(
            f"Starting property listings fetch with params: {params}, fetch_similar_transactions: {fetch_similar_transactions}"
        )

        property_types_str = (
            ",".join(params.property_types)
            if isinstance(params.property_types, list)
            else params.property_types
        )
        logger.debug(f"Property types string: {property_types_str}")

        cache_key = f"property_monitor_transactions_{hash(str(params))}"
        logger.debug(f"Cache key generated: {cache_key}")

        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info("Returning cached property listings data")
            return cached_data

        params = {
            "emirate": params.emirate,
            "masterDevelopment": params.master_development,
            "subLoc1": params.sub_loc_1,
            "subLoc2": params.sub_loc_2,
            "subLoc3": params.sub_loc_3,
            "subLoc4": params.sub_loc_4,
            "category": params.category,
            "propertyType": property_types_str,
            "minBeds": params.number_of_bedrooms,
            "maxBeds": params.number_of_bedrooms,
            "minArea": params.min_area,
            "maxArea": params.max_area,
            "startDate": params.start_date,
            "endDate": params.end_date,
            "minPrice": params.min_price,
            "maxPrice": params.max_price,
            "transactionSequence": params.contract_type,
            "page": params.page,
        }

        url = f"{self.host}pmiq"
        logger.debug(f"Making API request to URL: {url}")

        try:
            response = requests.get(url=url, headers=self.headers, params=params)
            logger.debug(f"API response status code: {response.status_code}")
            response.raise_for_status()
            data = response.json()
            if not data:
                logger.error("Failed to retrieve property listings.")
                raise ValueError("Property listings data not found")
            logger.info("Successfully retrieved property listings data")
            cache.set(cache_key, data, timeout=60 * 300)  # Cache for 5 hour
            logger.debug("Property listings data cached successfully")
            return data
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP request failed: {e}")
            raise
        except Exception as error:
            logger.error(
                f"Error in get_property_listings for property monitor: {error}"
            )
            raise
