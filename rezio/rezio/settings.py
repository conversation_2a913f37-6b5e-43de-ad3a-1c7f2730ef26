"""
Django settings for rezio project.

Generated by 'django-admin startproject' using Django 4.2.13.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from datetime import timedelta
from pathlib import Path

import environ
import firebase_admin
import sentry_sdk
from celery.schedules import crontab
from firebase_admin import credentials

from rezio.utils.constants import DJANGO_LOGGER_NAME

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=True)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(BASE_DIR / ".env"))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env("DJANGO_SECRET_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = env.bool("DEBUG", default=False)

ALLOWED_HOSTS = env("DJANGO_ALLOWED_HOSTS").split(",")


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # django apps
    "rezio.user",
    "rezio.firebase",
    "rezio.properties",
    "rezio.analytics",
    "rezio.menu_settings",
    # "rezio.notifications",
    "rezio.news",
    "rezio.notifications.apps.NotificationsConfig",
    "rezio.assistant",
    "rezio.twilio_integration",
    "rezio.ai",
    # third-party packages
    "rest_framework",
    "rest_framework_simplejwt",
    "rest_framework_simplejwt.token_blacklist",
    "corsheaders",
    "django_filters",
    "phonenumber_field",
    "channels",
    "channels_redis",
    "django_celery_beat",
    "rezio.celery_tasks",
    # "fcm_django"
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "rezio.middlewares.logging_middleware.APILoggingMiddleware",
]

ROOT_URLCONF = "rezio.rezio.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "rezio.rezio.wsgi.application"
#
sentry_sdk.init(
    dsn=env("SENTRY_DSN"),
    # Add data like request headers and IP for users,
    # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
    send_default_pii=True,
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for tracing.
    traces_sample_rate=1.0,
    _experiments={
        # Set continuous_profiling_auto_start to True
        # to automatically start the profiler on when
        # possible.
        "continuous_profiling_auto_start": True,
    },
    environment=env("ENVIRONMENT", default="development"),
)


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

# DATABASES = {"default": env.db("DATABASE_URL")}
# DATABASES["default"]["ATOMIC_REQUESTS"] = True

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": os.environ.get("POSTGRES_DB"),
        "USER": os.environ.get("POSTGRES_USER"),
        "PASSWORD": os.environ.get("POSTGRES_PASSWORD"),
        "HOST": os.environ.get("POSTGRES_HOST"),
        "PORT": os.environ.get("POSTGRES_PORT"),
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")


# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "user.User"

RATE_LIMIT = env("RATE_LIMIT")
RATE_LIMIT_TIME_FRAME = env("RATE_LIMIT_TIME_FRAME")

USE_X_FORWARDED_HOST = True
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

REST_FRAMEWORK = {
    "USE_X_FORWARDED_FOR": True,
    "EXCEPTION_HANDLER": "rezio.utils.exception_handler.custom_exception_handler",
    "DEFAULT_THROTTLE_CLASSES": [
        "rezio.rezio.constants.AgentProfileThrottle",
        "rezio.rezio.constants.AgentPropertyListThrottle",
        "rezio.rezio.constants.PropertyDetailsThrottle",
        "rezio.rezio.constants.PropertyFinancialsThrottle",
        "rezio.rezio.constants.PropertyBasicDetailsThrottle",
        "rezio.rezio.constants.UserPortfolioThrottle",
        "rezio.rezio.constants.InvestorProfileThrottle",
        "rezio.rezio.constants.InvestorPropertyListThrottle",
        "rezio.rezio.constants.InvestorPreferencesThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "agent_profile_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "agent_property_list_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "property_details_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "property_financials_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "investor_profile_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "investor_property_list_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "investor_preferences_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "property_basic_details_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
        "user_portfolio_rate": f"{RATE_LIMIT}/{RATE_LIMIT_TIME_FRAME}",
    },
}

# user roles
ROLES = env("ROLES").split(",")

# investor types
INVESTOR_TYPES = env("INVESTOR_TYPES").split(",")


# * Check for logs dir path and create dir if it doesn't exist
LOGS_DIR_PATH = os.path.join(BASE_DIR, "logs")
Path(LOGS_DIR_PATH).mkdir(parents=True, exist_ok=True)

DJANGO_LOG_PATH = os.path.join(LOGS_DIR_PATH, "django.log")


LOGGING = {
    "version": 1,
    "disable_existing_loggers": True,
    "formatters": {
        "message": {
            "format": "{message}",
            "style": "{",
        },
        "verbose": {
            "format": "{request_uuid} {user_id} {levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
    },
    "filters": {
        "request_uuid": {
            "()": "rezio.middlewares.logging_middleware.RequestUUIDFilter",
        },
        "user_id": {
            "()": "rezio.middlewares.logging_middleware.RequestUserIDFilter",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "verbose",
            "filters": ["request_uuid", "user_id"],
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": DJANGO_LOG_PATH,
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 5,
            "formatter": "verbose",
            "filters": ["request_uuid", "user_id"],
        },
    },
    "loggers": {
        DJANGO_LOGGER_NAME: {
            "handlers": ["console", "file"],
            "level": "INFO",
        }
    },
}

# CRED = credentials.Certificate(
#     {
#         "type": "service_account",
#         "project_id": env("FIREBASE_PROJECT_ID"),
#         "private_key_id": env("FIREBASE_PRIVATE_KEY_ID"),
#         "private_key": env("FIREBASE_PRIVATE_KEY").replace("\\n", "\n"),
#         "client_email": env("FIREBASE_CLIENT_EMAIL"),
#         "client_id": env("FIREBASE_CLIENT_ID"),
#         "auth_uri": env("FIREBASE_AUTH_URI"),
#         "token_uri": env("FIREBASE_TOKEN_URI"),
#         "auth_provider_x509_cert_url": env("FIREBASE_AUTH_PROVIDER_X509_CERT_URL"),
#         "client_x509_cert_url": env("FIREBASE_CLIENT_X509_CERT_URL"),
#         "universe_domain": env("FIREBASE_UNIVERSE_DOMAIN"),
#     }
# )
CRED = credentials.Certificate(env("FIREBASE_ADMIN_CREDENTIALS_PATH"))
try:
    firebase_admin.get_app()
except ValueError as e:
    firebase_admin.initialize_app(CRED)


# property integration app cred
DUBAI_PULSE_CLIENT_ID = env("DUBAI_PULSE_CLIENT_ID")
DUBAI_PULSE_CLIENT_SECRET = env("DUBAI_PULSE_CLIENT_SECRET")

DUBAI_PULSE_API_HOST = env(
    "DUBAI_PULSE_API_HOST", default="https://api.dubaipulse.gov.ae"
)
DUBAI_PULSE_AUTH_URL = env(
    "DUBAI_PULSE_AUTH_URL",
    default="/oauth/client_credential/accesstoken?grant_type=client_credentials",
)
DUBAI_PULSE_DATA_ENDPOINT = env(
    "DUBAI_PULSE_DATA_ENDPOINT", default="/open/dld/dld_brokers-open-api"
)
DUBAI_PULSE_DATA_FILTER = env(
    "DUBAI_PULSE_DATA_FILTER", default="broker_number%3D{broker_number}"
)

PROPERTY_MONITOR_API_TOKEN = env("PROPERTY_MONITOR_API_TOKEN")
PROPERTY_MONITOR_API_KEY = env("PROPERTY_MONITOR_API_KEY")
PROPERTY_MONITOR_HOST = env(
    "PROPERTY_MONITOR_HOST", default="https://demoapi.propertymonitor.com/pm/v1/"
)
PROPERTY_MONITOR_LOCATION_ENDPOINT = env(
    "PROPERTY_MONITOR_LOCATION_ENDPOINT", default="locations"
)
PROPERTY_MONITOR_ADDRESS_ENDPOINT = env(
    "PROPERTY_MONITOR_ADDRESS_ENDPOINT", default="address-lookup"
)
PROPERTY_MONITOR_SEARCH_KEY = env("PROPERTY_MONITOR_SEARCH_KEY", default="keyword=")
PROPERTY_MONITOR_SEARCH_LOCATION_KEY = env(
    "PROPERTY_MONITOR_SEARCH_LOCATION_KEY", default="locationId="
)
PROPERTY_MONITOR_SEARCH_EMIRATE_KEY = env(
    "PROPERTY_MONITOR_SEARCH_EMIRATE_KEY", default="emirate="
)
PROPERTY_MONITOR_SEARCH_UNIT_KEY = env(
    "PROPERTY_MONITOR_SEARCH_UNIT_KEY", default="unitNumber="
)
PROPERTY_MONITOR_PAGE_KEY = env("PROPERTY_MONITOR_PAGE_KEY", default="page=")
PROPERTY_MONITOR_UNIT_DETAILS_ENDPOINT = env(
    "PROPERTY_MONITOR_UNIT_DETAILS_ENDPOINT", default="unit-history"
)
PROPERTY_MONITOR_GET_UNIT_DETAILS_KEY = env(
    "PROPERTY_MONITOR_GET_UNIT_DETAILS_KEY", default="addressId="
)
PROPERTY_MONITOR_VOLUME_TREND_ENDPOINT = env(
    "PROPERTY_MONITOR_VOLUME_TREND_ENDPOINT", default="price-volume-trend"
)
PROPERTY_MONITOR_MASTER_DEVELOPMENT_KEY = env(
    "PROPERTY_MONITOR_MASTER_DEVELOPMENT_KEY", default="masterDevelopment="
)
PROPERTY_MONITOR_PROPERTY_TYPE_KEY = env(
    "PROPERTY_MONITOR_PROPERTY_TYPE_KEY", default="propertyType="
)
PROPERTY_MONITOR_CATEGORY_KEY = env(
    "PROPERTY_MONITOR_CATEGORY_KEY", default="category="
)
PROPERTY_MONITOR_SEARCH_LOCATION_NAME_KEY = env(
    "PROPERTY_MONITOR_SEARCH_LOCATION_NAME_KEY", default="location="
)


# AWS Configuration
USE_AWS = env.bool("USE_AWS", default=False)

# Base AWS Settings with defaults
AWS_ACCESS_KEY_ID = env("AWS_ACCESS_KEY_ID", default="dummy")
AWS_SECRET_ACCESS_KEY = env("AWS_SECRET_ACCESS_KEY", default="dummy")
AWS_STORAGE_BUCKET_NAME = env("AWS_STORAGE_BUCKET_NAME", default="dummy")
AWS_S3_REGION_NAME = env("AWS_S3_REGION_NAME", default="dummy")
AWS_CLOUDFRONT_DOMAIN = env("AWS_CLOUDFRONT_DOMAIN", default="dummy")
AWS_CLOUDFRONT_KEY_ID = env("AWS_CLOUDFRONT_KEY_ID", default="dummy")
AWS_CLOUDFRONT_KEY = env("AWS_CLOUDFRONT_KEY", default="dummy")

if USE_AWS:
    # AWS-specific settings when AWS is enabled
    AWS_S3_CUSTOM_DOMAIN = f"{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com"
    # Add any other AWS-specific settings here
else:
    # Local storage setup
    DEFAULT_FILE_STORAGE = "django.core.files.storage.FileSystemStorage"
    MEDIA_URL = "/media/"
    MEDIA_ROOT = os.path.join(BASE_DIR, "media")

PRICE_MAX_LIMIT = env("PRICE_MAX_LIMIT", default=999999999999999)

FILE_SIZE_LIMIT_IN_MB = env("FILE_SIZE_LIMIT_IN_MB", default=5)
MAXIMUM_EXPIRY_TIME = env("MAXIMUM_EXPIRY_TIME", default=60 * 60)
IMAGE_SIZE_LIMIT_IN_MB = env("IMAGE_SIZE_LIMIT_IN_MB", default=10)
VIDEO_SIZE_LIMIT_IN_MB = env("VIDEO_SIZE_LIMIT_IN_MB", default=200)

DEFAULT_COVER_PHOTO = env("DEFAULT_COVER_PHOTO", default=None)


PAGE_SIZE = env("PAGE_SIZE", default=20)
PAGE_SIZE_QUERY_PARAM = env("PAGE_SIZE_QUERY_PARAM", default="page_size")
MAX_PAGE_SIZE = env("MAX_PAGE_SIZE", default=20)

DLD_FEES_PERCENTAGE = env("DLD_FEES_PERCENTAGE", default=4)
AGENT_COMMISSION_PERCENTAGE = env("AGENT_COMMISSION_PERCENTAGE", default=2)
BANK_PROCESSING_FEE_PERCENTAGE = env("BANK_PROCESSING_FEE_PERCENTAGE", default=0.25)

CACHE_TIMEOUT = int(MAXIMUM_EXPIRY_TIME) - 60

CORS_ALLOW_ALL_ORIGINS = env.bool("CORS_ALLOW_ALL_ORIGINS", default=False)
# CORS_ALLOWED_ORIGINS = env("CORS_ALLOWED_ORIGINS").split(',')

ALLOWED_SELECTIVE_AGENT_COUNT = env.int("ALLOWED_SELECTIVE_AGENT_COUNT", default=3)

ALLOWED_IMAGE_MIME_TYPES = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/heic",
]

ALLOWED_VIDEO_MIME_TYPES = ["image/heif", "video/mp4", "video/quicktime", "video/hevc"]

ALLOWED_DOCUMENT_MIME_TYPES = ["application/pdf"]

ALLOWED_MIME_TYPES = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/heic",
    "image/heif",
    "video/mp4",
    "video/quicktime",
    "video/hevc",
]

DEFAULT_CURRENCY_CODE = env("DEFAULT_CURRENCY_CODE", default="AED")
DEFAULT_PROPERTY_CURRENCY_CODE = env("DEFAULT_PROPERTY_CURRENCY_CODE", default="USD")


EXCHANGE_RATE_API_URL = env("EXCHANGE_RATE_API_URL")
EXCHANGE_RATE_API_KEY = env("EXCHANGE_RATE_API_KEY")
EXCHANGE_RATE_API_CONVERSION_ENDPOINT = env("EXCHANGE_RATE_API_CONVERSION_ENDPOINT")

# Celery Configuration Settings

# Broker Settings

CELERY_BROKER_URL = env("REDIS_URL")  #'redis://redis:6379/0'
CELERY_RESULT_BACKEND = env("REDIS_URL")  #'redis://redis:6379/0'

# Time and Execution Settings
CELERY_TIMEZONE = "UTC"
CELERY_ENABLE_UTC = True
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes
CELERY_TASK_SOFT_TIME_LIMIT = 25 * 60  # 25 minutes
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000  # Restart worker after 1000 tasks

# Task Settings
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_COMPRESSION = "gzip"
CELERY_RESULT_COMPRESSION = "gzip"

# Result Settings
CELERY_TASK_IGNORE_RESULT = False
CELERY_RESULT_EXPIRES = 60 * 60 * 24  # 24 hours
CELERY_TASK_STORE_ERRORS_EVEN_IF_IGNORED = True

# Performance Settings
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True

# Retry Settings
CELERY_TASK_RETRY_DELAY_START = 0
CELERY_TASK_MAX_RETRIES = 3
CELERY_TASK_DEFAULT_RATE_LIMIT = "10/s"

# Queue Settings
CELERY_TASK_DEFAULT_QUEUE = "default"
CELERY_TASK_QUEUES = {
    "default": {
        "exchange": "default",
        "routing_key": "default",
    },
    "notifications": {
        "exchange": "notifications",
        "routing_key": "notifications",
    },
    "high_priority": {
        "exchange": "high_priority",
        "routing_key": "high_priority",
    },
}

# Route tasks to specific queues
# CELERY_TASK_ROUTES = {
#     # Notification tasks
#     'notifications.tasks.send_push_notification': {'queue': 'notifications'},
#     # 'notifications.tasks.send_scheduled_notifications': {'queue': 'notifications'},
#     'notifications.tasks.send_email_notification': {'queue': 'notifications'},
#
#     # Maintenance tasks
#     'notifications.tasks.clean_old_notifications': {'queue': 'default'},
#     'notifications.tasks.clean_inactive_devices': {'queue': 'default'},
#
#     # High priority tasks
#     'notifications.tasks.create_profile_completion_reminder': {'queue': 'high_priority'},
# }

# Scheduled Tasks

RATELIMIT_ENABLED = True

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env("REDIS_URL"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
        "KEY_PREFIX": "rezio",
    }
}

# Redis Settings
CELERY_REDIS_MAX_CONNECTIONS = 20
CELERY_BROKER_TRANSPORT_OPTIONS = {
    "visibility_timeout": 3600,  # 1 hour
    "max_connections": 20,
    "socket_timeout": 30,
    "socket_connect_timeout": 30,
}

# Monitoring and Logging
CELERY_SEND_TASK_SENT_EVENT = True
CELERY_TASK_SEND_SENT_EVENT = True
CELERY_WORKER_SEND_TASK_EVENTS = True
CELERY_TASK_TRACK_STARTED = True

# Error Handling
CELERY_TASK_EAGER_PROPAGATES = True  # Propagate exceptions in development
CELERY_TASK_REMOTE_TRACEBACKS = True
CELERY_TASK_STORE_ERRORS_EVEN_IF_IGNORED = True

# Security Settings
CELERY_SECURITY_KEY = None
CELERY_SECURITY_CERTIFICATE = None
CELERY_SECURITY_CERT_STORE = None

# Task Priority Settings
CELERY_TASK_DEFAULT_PRIORITY = 5
CELERY_TASK_PRIORITY_MAPPING = {
    "high": 9,
    "normal": 5,
    "low": 1,
}

# Development Settings
if DEBUG:
    CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously in development
    CELERY_TASK_EAGER_PROPAGATES = True
    CELERY_REDIS_MAX_CONNECTIONS = 5


INFOBIP_API_KEY = env("INFOBIP_API_KEY")
INFOBIP_BASE_URL = env("INFOBIP_BASE_URL")
INFOBIP_SENDER_NAME = env("INFOBIP_SENDER_NAME", default="InfoSMS")
INFOBIP_SEND_SMS_ENDPOINT = env("INFOBIP_SEND_SMS_ENDPOINT")


EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = "smtp.yourmailserver.com"
EMAIL_PORT = 587
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = "your_email_password"
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = "Rezio <<EMAIL>>"


FCM_DJANGO_SETTINGS = {
    # an instance of firebase_admin.App to be used as default for all fcm-django requests
    # default: None (the default Firebase app)
    "DEFAULT_FIREBASE_APP": None,
    # default: _('FCM Django')
    "APP_VERBOSE_NAME": "Notification Service",
    # true if you want to have only one active device per registered user at a time
    # default: False
    "ONE_DEVICE_PER_USER": False,
    # devices to which notifications cannot be sent,
    # are deleted upon receiving error response from FCM
    # default: False
    "DELETE_INACTIVE_DEVICES": False,
}

FCM_DJANGO_FCMDEVICE_MODEL = "notifications.models.CustomFCMDevice"


BASIC_PLAN_PROPERTIES_COUNT = env("BASIC_PLAN_PROPERTIES_COUNT", default=20)

AI_USER_NAME = env("AI_USER_NAME")
AI_SECRET_KEY = env("AI_SECRET_KEY")
# AI_PUBLIC_KEY = env("AI_PUBLIC_KEY")
# PUBLIC_KEY = serialization.load_pem_public_key(AI_PUBLIC_KEY.encode("utf-8"))

# News Settings
NEWS_CACHE_TIMEOUT = env.int("NEWS_CACHE_TIMEOUT", default=3600)  # 1 hour
NEWS_MAX_AGE_DAYS = env.int("NEWS_MAX_AGE_DAYS", default=90)  # 90 days
NEWS_FETCH_HOUR = env.int("NEWS_FETCH_HOUR", default=0)  # Default midnight
NEWS_FETCH_MINUTE = env.int("NEWS_FETCH_MINUTE", default=1)  # Default 1 minutes

# Dictionary to define task and its schedule time
TASK_SCHEDULE_TIME = {
    "check_agent_license_expiry": crontab(hour=0, minute=1),
    "fetch_news_task": crontab(hour=NEWS_FETCH_HOUR, minute=NEWS_FETCH_MINUTE),
}

TRENDS_AND_TRANSACTIONS_DEFAULT_LOCATION = env(
    "TRENDS_AND_TRANSACTIONS_DEFAULT_LOCATION", default="Dubai Marina"
)

# BRN_DATA_CONSUMER_KEY = env("BRN_DATA_CONSUMER_KEY")

PROPERTY_SHARING_ICONS_URL = env(
    "PROPERTY_SHARING_ICONS_URL",
    default="https://rezio-static-files.s3.me-central-1.amazonaws.com/property_sharing_icons",
)

# OTP Settings
LOGIN_OTP_MAX_ATTEMPTS = env.int("LOGIN_OTP_MAX_ATTEMPTS", default=3)
LOGIN_OTP_COOLDOWN_MINUTES = env.int("LOGIN_OTP_COOLDOWN_MINUTES", default=10)
CHAT_OTP_MAX_ATTEMPTS = env.int("CHAT_OTP_MAX_ATTEMPTS", default=3)
CHAT_OTP_COOLDOWN_MINUTES = env.int("CHAT_OTP_COOLDOWN_MINUTES", default=30)

OPENAI_API_KEY = env("OPENAI_API_KEY", default="test")
TAVILY_API_KEY = env("TAVILY_API_KEY", default="test")

GOOGLE_API_KEY = env("GOOGLE_API_KEY", default="test")
GOOGLE_CSE_ID = env("GOOGLE_CSE_ID", default="test")

POSTGRES_HOST = env("POSTGRES_HOST")
POSTGRES_PORT = env("POSTGRES_PORT")
POSTGRES_DB = env("POSTGRES_DB")
POSTGRES_USER = env("POSTGRES_USER")
POSTGRES_PASSWORD = env("POSTGRES_PASSWORD")
DATABASE_URL = f"postgres://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

NON_EDITABLE_ATTRIBUTES = env(
    "NON_EDITABLE_ATTRIBUTES",
    default=[
        "annual_rent",
        "security_deposit",
        "preferred_payment_frequency",
        "available_from",
        "asking_price",
    ],
)


NON_SHAREABLE_DEFAULT = {"gains_component": ["valuation"]}

ENVIRONMENT = env("ENVIRONMENT", default="development")

REVENUECAT_WEBHOOK_KEY = env("REVENUECAT_WEBHOOK_KEY", default="test")

DO_MORE_WITH_PREMIUM = env(
    "DO_MORE_WITH_PREMIUM",
    default="https://rezio-static-files.s3.me-central-1.amazonaws.com/Subscription_Files/Frame+**********.svg",
)
UNLOCKED_BENIFITS = env(
    "UNLOCKED_BENIFITS",
    default="https://rezio-static-files.s3.me-central-1.amazonaws.com/Subscription_Files/unlocked_benifits.svg",
)

PREMIUM_CARD_VISIBILITY_AFTER_PROPERTY = env(
    "PREMIUM_CARD_VISIBILITY_AFTER_PROPERTY", default=2
)
GOOGLE_MAPS_API_KEY = env("GOOGLE_MAPS_API_KEY", default="test")
# Twilio Settings
TWILIO_ACCOUNT_SID = env("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = env("TWILIO_AUTH_TOKEN")
TWILIO_VERIFY_SERVICE_SID = env("TWILIO_VERIFY_SERVICE_SID")

# JWT Settings
SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(
        minutes=int(env("JWT_ACCESS_TOKEN_LIFETIME", default=30))
    ),
    "REFRESH_TOKEN_LIFETIME": timedelta(
        days=int(env("JWT_REFRESH_TOKEN_LIFETIME", default=7200))
    ),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": env("JWT_SECRET_KEY", default=SECRET_KEY),
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "JWK_URL": None,
    "LEEWAY": 0,
    "AUTH_HEADER_TYPES": (
        "",
    ),  # Empty string allows passing just the token without 'Bearer' prefix
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",  # Django converts 'Authorization' to 'HTTP_AUTHORIZATION'
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "TOKEN_USER_CLASS": "rest_framework_simplejwt.models.TokenUser",
    "JTI_CLAIM": "jti",
    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
    # Custom claims
    "TOKEN_OBTAIN_SERIALIZER": "rest_framework_simplejwt.serializers.TokenObtainPairSerializer",
    "TOKEN_REFRESH_SERIALIZER": "rest_framework_simplejwt.serializers.TokenRefreshSerializer",
    "TOKEN_VERIFY_SERIALIZER": "rest_framework_simplejwt.serializers.TokenVerifySerializer",
    "TOKEN_BLACKLIST_SERIALIZER": "rest_framework_simplejwt.serializers.TokenBlacklistSerializer",
}

# Phone country code to ISO country code mapping
PHONE_TO_ISO_COUNTRY_MAP = {
    "91": "IN",  # India
    "971": "UAE",  # UAE
}

# Property count limits by ISO country code
PROPERTY_COUNT_LIMITS = {
    "IN": env.int("INDIA_BASIC_PLAN_PROPERTIES_COUNT", default=3),  # India
    "UAE": env.int("UAE_BASIC_PLAN_PROPERTIES_COUNT", default=50),  # UAE
    "DEFAULT": env.int("BASIC_PLAN_PROPERTIES_COUNT", default=20),  # Other countries
}

# Keep the existing setting for backward compatibility
BASIC_PLAN_PROPERTIES_COUNT = PROPERTY_COUNT_LIMITS["DEFAULT"]
