"""
URL configuration for rezio project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path("admin/", admin.site.urls),
    path("users/", include("rezio.user.urls")),
    path("properties/", include("rezio.properties.urls")),
    path("web/users/", include("rezio.user.web_urls")),
    path("web/properties/", include("rezio.properties.web_urls")),
    path("notifications/", include("rezio.notifications.urls")),
    path("api/", include("rezio.menu_settings.urls")),
    path("news/", include("rezio.news.urls")),
    path("web/ai/", include("rezio.assistant.urls")),
    path("v2/web/ai/", include("rezio.ai.urls")),
    path("web/twilio/", include("rezio.twilio_integration.urls")),
]
