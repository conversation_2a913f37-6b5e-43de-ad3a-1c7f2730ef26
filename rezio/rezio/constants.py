from django.conf import settings
from rest_framework.throttling import AnonRateThrottle

MAXIMUM_EXPIRY_TIME = 1 * 24 * 60 * 60

DD_MMM_YYYY = "%d-%b-%Y-%H-%M-%S"

# * Presigned post structures

KEY = "key"
CONTENT_TYPE = "content-type"
REQUIRED = "required"
NAME = "name"
SIZE = "size"

PROPERTY_RENT_CONTRACT = "PROPERTY_RENT_CONTRACT"
PROPERTY_OWNERSHIP_PROOF = "PROPERTY_OWNERSHIP_PROOF"
PROFILE_PHOTO = "PROFILE_PHOTO"
COVER_PHOTO = "COVER_PHOTO"
EMIRATES_ID_IMAGE = "EMIRATES_ID_IMAGE"
PASSPORT_IMAGE = "PASSPORT_IMAGE"
PROPERTY_UNIT_SECTION_MEDIA = "PROPERTY_UNIT_SECTION_MEDIA"
PROPERTY_AGENT_CONTRACT = "AGENT_CONTRACT"
AGENT_LICENSE_FILE = "AGENT_LICENSE_FILE"
FLOOR_PLAN = "FLOOR_PLAN"
EXTERNAL_MEDIA = "EXTERNAL_MEDIA"

PRESIGNED_POST_STRUCTURES = {
    PROPERTY_RENT_CONTRACT: {
        KEY: "properties/{property_id}/rent_contract/{filename}",
        CONTENT_TYPE: ["application/pdf"],
        REQUIRED: ["property_id", "filename"],
    },
    PROPERTY_OWNERSHIP_PROOF: {
        KEY: "properties/{property_id}/ownership_proof/{filename}",
        CONTENT_TYPE: ["application/pdf"],
        REQUIRED: ["property_id", "filename"],
    },
    PROFILE_PHOTO: {
        KEY: "users/{user_id}/profile_photo/{role_name}/{filename}",
        CONTENT_TYPE: ["image/png", "image/jpeg"],
        REQUIRED: ["user_id", "role_name", "filename"],
    },
    COVER_PHOTO: {
        KEY: "users/{user_id}/cover_photo/{role_name}/{filename}",
        CONTENT_TYPE: ["image/png", "image/jpeg"],
        REQUIRED: ["user_id", "role_name", "filename"],
    },
    EMIRATES_ID_IMAGE: {
        KEY: "users/{user_id}/profile_documents/{filename}",
        CONTENT_TYPE: ["image/png", "image/jpeg", "application/pdf"],
        REQUIRED: ["user_id", "filename"],
    },
    PASSPORT_IMAGE: {
        KEY: "users/{user_id}/profile_documents/{filename}",
        CONTENT_TYPE: ["image/png", "image/jpeg", "application/pdf"],
        REQUIRED: ["user_id", "filename"],
    },
    PROPERTY_UNIT_SECTION_MEDIA: {
        KEY: "properties/{property_id}/unit_media/{user_id}/{user_role}/{filename}",
        CONTENT_TYPE: ["image/png", "image/jpeg", "application/pdf"],
        REQUIRED: ["user_id", "filename"],
    },
    PROPERTY_AGENT_CONTRACT: {
        KEY: "properties/{property_id}/agent_contract/{user_id}/{filename}",
        CONTENT_TYPE: ["application/pdf"],
        REQUIRED: ["property_id", "user_id", "filename"],
    },
    AGENT_LICENSE_FILE: {
        KEY: "users/{user_id}/agent_license/{filename}",
        CONTENT_TYPE: [
            "image/png",
            "image/jpeg",
            "image/jpg",
            "image/heic",
            "application/pdf",
        ],
        REQUIRED: ["user_id", "filename"],
    },
    FLOOR_PLAN: {
        KEY: "properties/{property_id}/{user_id}/{user_role}/{document_type}/{filename}",
        CONTENT_TYPE: [
            "image/png",
            "image/jpeg",
            "image/jpg",
            "image/heic",
            "application/pdf",
        ],
        REQUIRED: ["property_id", "document_type", "filename", "user_id", "user_role"],
    },
    EXTERNAL_MEDIA: {
        KEY: "properties/{property_id}/external_media/{section_id}/{document_type}/{filename}",
        CONTENT_TYPE: settings.ALLOWED_IMAGE_MIME_TYPES,
        REQUIRED: ["property_id", "section_id", "document_type", "filename"],
    },
}


class PropertyDetailsThrottle(AnonRateThrottle):
    """
    Throttle rate class for property details
    """

    scope = "property_details_rate"


class PropertyFinancialsThrottle(AnonRateThrottle):
    """
    Throttle rate class for property financials details
    """

    scope = "property_financials_rate"


class PropertyBasicDetailsThrottle(AnonRateThrottle):
    """
    Throttle rate class for property financials details
    """

    scope = "property_basic_details_rate"


class UserPortfolioThrottle(AnonRateThrottle):
    """
    Throttle rate class for property financials details
    """

    scope = "user_portfolio_rate"


class AgentProfileThrottle(AnonRateThrottle):
    """
    Throttle rate class for agent profile
    """

    scope = "agent_profile_rate"


class AgentPropertyListThrottle(AnonRateThrottle):
    """
    Throttle rate class for agent property list
    """

    scope = "agent_property_list_rate"


class InvestorProfileThrottle(AnonRateThrottle):
    """
    Throttle rate class for investor profile
    """

    scope = "investor_profile_rate"


class InvestorPropertyListThrottle(AnonRateThrottle):
    """
    Throttle rate class for investor property list
    """

    scope = "investor_property_list_rate"


class InvestorPreferencesThrottle(AnonRateThrottle):
    """
    Throttle rate class for investor preferences
    """

    scope = "investor_preferences_rate"
