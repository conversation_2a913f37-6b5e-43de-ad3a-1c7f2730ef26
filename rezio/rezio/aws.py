import io
import logging
import traceback
from datetime import datetime, timedelta
import json

import boto3
import requests
from botocore.client import Config
from django.conf import settings

from rezio.utils.constants import DJANGO_LOGGER_NAME

logger = logging.getLogger(DJANGO_LOGGER_NAME)

client = boto3.client(
    "s3",
    region_name=settings.AWS_S3_REGION_NAME,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    endpoint_url=f"https://s3.{settings.AWS_S3_REGION_NAME}.amazonaws.com",
    config=Config(signature_version="s3v4"),
)


# def rsa_signer(message):
#     # Load private key from environment
#     # private_key = dedent(settings.AWS_CLOUDFRONT_KEY).encode('utf-8')  # Convert to bytes
#     pem_content = base64.b64decode(settings.AWS_CLOUDFRONT_KEY).decode('utf-8')  # Convert to bytes
#     # private_key = rsa.PrivateKey.load_pkcs1(private_key)
#     private_key = rsa.PrivateKey.load_pkcs1(pem_content.encode("utf-8"))
#     print(private_key)
#     return rsa.sign(message, private_key, 'SHA-1')


# cloudfront_signer = CloudFrontSigner(settings.AWS_CLOUDFRONT_KEY_ID, rsa_signer)


class S3Client:
    def __init__(self, cache_timeout=60 * 60):
        self.client = client
        self.cache_timeout = cache_timeout

    def upload_file(self, file, key, content_type=None):
        if not content_type:
            content_type = file.content_type
        try:
            logger.info(
                f"this is content type for media {file} and type is {content_type}"
            )
            logger.info(f"Uploading file to s3 - filename - {key}, {file.content_type}")
            file.seek(0)
            return self.client.upload_fileobj(
                file,
                settings.AWS_STORAGE_BUCKET_NAME,
                key,
                ExtraArgs={
                    "ContentType": content_type,
                },
            )
        except Exception as error:
            logger.error(
                f"Failed to upload file to S3 - filename: {key}, Error: {str(error)}"
            )
            raise

    def delete_file(self, key):
        logger.info(f"Deleting file from s3 - filename - {key}")
        return self.client.delete_object(
            Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=key
        )

    def get_file(self, s3_key):
        try:
            url = f"{settings.AWS_CLOUDFRONT_DOMAIN}/{s3_key}"
            expires_at = (datetime.utcnow() + timedelta(days=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
            # expire_date = datetime(2025, 1, 1)
            # signed_url = cloudfront_signer.generate_presigned_url(
            #     url=url,
            #     date_less_than=expires_at
            # )

            return url

        except Exception as e:
            logger.error(f"Error in getting file from s3 - filename - {s3_key}")
            traceback.print_exc()

    def generate_presigned_url(self, key, content_type=None, expiry=3600):
        """
        Generate a presigned URL for S3 upload

        Args:
            key (str): S3 object key
            content_type (str): File content type
            expiry (int): URL expiry time in seconds

        Returns:
            str: Presigned URL for upload
        """
        try:
            response = self.client.generate_presigned_url(
                "put_object",
                Params={
                    "Bucket": settings.AWS_STORAGE_BUCKET_NAME,
                    "Key": key,
                    "ContentType": content_type,
                },
                ExpiresIn=expiry,
            )
            return response
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            traceback.print_exc()
            raise

    @staticmethod
    def fetch_image_from_s3(key):
        """
        Function to fetch image from S3

        :param key: Key of the file object
        :return image_buffer: Image buffer
        """
        try:
            response = requests.get(key, stream=True)
            if response.status_code == 200:
                image_buffer = io.BytesIO(response.content)
                image_buffer.seek(0)
                return image_buffer
            else:
                print(f"Failed to download image. Status code: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"Error while downloading image from S3: {e}")
            raise

    def bulk_delete_files(self, keys):
        """
        Delete multiple files from S3 bucket in a single request.

        Args:
            keys (list): List of S3 object keys to delete

        Returns:
            dict: Response from S3 containing deletion results
        """
        try:
            if not keys:
                logger.warning("No keys provided for bulk deletion")
                return

            # Format the keys into the required structure for S3
            objects_to_delete = [{"Key": key} for key in keys]

            logger.info(f"Bulk deleting {len(keys)} files from S3")
            response = self.client.delete_objects(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                Delete={
                    "Objects": objects_to_delete,
                    "Quiet": False,  # Set to True to suppress detailed deletion results
                },
            )

            # Log any errors that occurred during deletion
            if "Errors" in response and response["Errors"]:
                logger.error(
                    f"Errors occurred during bulk deletion: {response['Errors']}"
                )

            return response

        except Exception as error:
            logger.error(f"Failed to bulk delete files from S3. Error: {str(error)}")
            raise

    def copy_file(self, source_key, destination_key):
        """
        Copy a file from one S3 key to another within the same bucket.

        Args:
            source_key (str): Source S3 object key
            destination_key (str): Destination S3 object key

        Returns:
            dict: Response from S3 containing copy results
        """
        try:
            logger.info(f"Copying file from {source_key} to {destination_key}")
            response = self.client.copy_object(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                CopySource={
                    "Bucket": settings.AWS_STORAGE_BUCKET_NAME,
                    "Key": source_key,
                },
                Key=destination_key,
            )
            return response

        except Exception as error:
            logger.error(
                f"Failed to copy file in S3. Source: {source_key}, Destination: {destination_key}, Error: {str(error)}"
            )
            raise

    def download_and_upload_file(self, url, s3_key, content_type=None):
        """
        Download a file from a URL and upload it to S3.

        Args:
            url (str): URL of the file to download
            s3_key (str): S3 key where the file will be stored
            content_type (str, optional): Content type of the file. If not provided, will be determined from the URL.

        Returns:
            dict: Response from S3 containing upload results
        """
        try:
            # Download the file from URL
            response = requests.get(url, stream=True)
            response.raise_for_status()  # Raise an exception for bad status codes

            # Create a file-like object from the response content
            file_obj = io.BytesIO(response.content)
            file_obj.seek(0)

            # Determine content type if not provided
            if not content_type:
                content_type = response.headers.get("content-type")

            # Upload to S3
            logger.info(f"Uploading file from URL to S3 - URL: {url}, S3 key: {s3_key}")
            self.client.upload_fileobj(
                file_obj,
                settings.AWS_STORAGE_BUCKET_NAME,
                s3_key,
                ExtraArgs={
                    "ContentType": content_type,
                },
            )

            logger.info(f"Successfully uploaded file from URL to S3 - {s3_key}")
            return {
                "key": s3_key,
                "content_type": content_type,
                "size": len(response.content),
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to download file from URL: {url}. Error: {str(e)}")
            raise
        except Exception as error:
            logger.error(
                f"Failed to upload file to S3. URL: {url}, S3 key: {s3_key}, Error: {str(error)}"
            )
            raise

    def get_json_files_from_prefix(self, prefix):
        """
        Fetch and parse all JSON files from a given S3 prefix

        Args:
            prefix (str): S3 prefix to list objects from

        Returns:
            list: List of dictionaries containing parsed JSON data
        """
        try:
            # List all objects with the given prefix
            response = self.client.list_objects_v2(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                Prefix=prefix
            )

            if 'Contents' not in response:
                return []

            json_data = []
            for obj in response['Contents']:
                key = obj['Key']
                if not key.endswith('.json'):
                    continue

                # Get the object content
                response = self.client.get_object(
                    Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                    Key=key
                )
                
                # Parse JSON content
                content = response['Body'].read().decode('utf-8')
                try:
                    parsed_json = json.loads(content)
                    json_data.append({
                        'key': key,
                        'data': parsed_json,
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat()
                    })
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing JSON from {key}: {e}")
                    continue

            return json_data

        except Exception as e:
            logger.error(f"Error fetching JSON files from prefix {prefix}: {e}")
            traceback.print_exc()
            raise
