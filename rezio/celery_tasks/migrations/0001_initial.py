# Generated by Django 5.1.6 on 2025-04-14 09:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
    ]

    operations = [
        migrations.CreateModel(
            name="CeleryTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "task_id",
                    models.CharField(db_index=True, max_length=255, unique=True),
                ),
                ("task_name", models.CharField(db_index=True, max_length=255)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending"),
                            ("STARTED", "Started"),
                            ("RETRY", "Retry"),
                            ("FAILURE", "Failure"),
                            ("SUCCESS", "Success"),
                            ("REVOKED", "Revoked"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("HIGH", "High"),
                            ("NORMAL", "Normal"),
                            ("LOW", "Low"),
                        ],
                        default="NORMAL",
                        max_length=10,
                    ),
                ),
                ("queue", models.CharField(db_index=True, max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("eta", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("args", models.JSONField(blank=True, default=list)),
                ("kwargs", models.JSONField(blank=True, default=dict)),
                ("result", models.JSONField(blank=True, null=True)),
                ("error", models.TextField(blank=True, null=True)),
                ("traceback", models.TextField(blank=True, null=True)),
                ("retries", models.PositiveIntegerField(default=0)),
                ("max_retries", models.PositiveIntegerField(default=3)),
                ("retry_delay", models.PositiveIntegerField(blank=True, null=True)),
                ("worker", models.CharField(blank=True, max_length=255, null=True)),
                ("hostname", models.CharField(blank=True, max_length=255, null=True)),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="contenttypes.contenttype",
                    ),
                ),
            ],
            options={
                "verbose_name": "Celery Task",
                "verbose_name_plural": "Celery Tasks",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["task_id"], name="celery_task_task_id_750aba_idx"
                    ),
                    models.Index(
                        fields=["task_name"], name="celery_task_task_na_471a9d_idx"
                    ),
                    models.Index(
                        fields=["status"], name="celery_task_status_2e9e85_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="celery_task_created_bff8d5_idx"
                    ),
                    models.Index(fields=["queue"], name="celery_task_queue_484bb9_idx"),
                ],
            },
        ),
    ]
