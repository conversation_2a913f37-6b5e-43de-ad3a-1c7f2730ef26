from celery.signals import (
    task_prerun,
    task_success,
    task_failure,
    task_revoked,
    task_retry,
)
from django.dispatch import receiver
from django.utils import timezone
from rezio.celery_tasks.models import CeleryTask
import logging
from celery import current_app
from celery.schedules import crontab
import traceback

from rezio.utils.constants import DJANGO_LOGGER_NAME
from rezio.utils.decorators import close_db_connection

logger = logging.getLogger(DJANGO_LOGGER_NAME)


def get_task_info(sender=None, task=None, task_id=None, **kwds):
    """Extract task information from different sources.

    This helper function extracts task metadata like name, sender, and periodic status
    from various possible sources including the task object, sender, and keyword arguments.

    Args:
        sender: The sender of the task signal
        task: The task object or task name string
        task_id: The unique ID of the task
        **kwds: Additional keyword arguments that may contain task information

    Returns:
        dict: A dictionary containing:
            - task_name (str): Name of the task
            - sender_name (str): Name of the sender
            - is_periodic (bool): Whether this is a periodic task
            - periodic_task_name (str): Name of the periodic task if applicable
    """
    task_name = None
    sender_name = None
    is_periodic = False
    periodic_task_name = None

    # Try to get task name from different sources
    if isinstance(task, str):
        task_name = task
    elif hasattr(task, "name"):
        task_name = task.name
    elif sender and hasattr(sender, "name"):
        task_name = sender.name
    elif "task" in kwds and isinstance(kwds["task"], str):
        task_name = kwds["task"]

    # Check headers for periodic task information
    headers = kwds.get("headers", {})
    if headers and isinstance(headers, dict):
        if "periodic_task_name" in headers:
            is_periodic = True
            periodic_task_name = headers["periodic_task_name"]
            # Use the periodic task name if available
            if periodic_task_name:
                task_name = periodic_task_name
        elif (
            headers.get("source") == "beat"
            or headers.get("scheduler", "").lower() == "beat"
        ):
            is_periodic = True

    # Get sender name and check for periodic task indicators
    if sender:
        sender_name = sender.__class__.__name__
        # Check if this is a periodic task from beat
        if hasattr(sender, "app") and hasattr(sender.app, "beat") and sender.app.beat:
            is_periodic = True
            sender_name = "CeleryBeat"

        # Check request properties
        if hasattr(sender, "request"):
            # Check delivery info
            if (
                hasattr(sender.request, "delivery_info")
                and sender.request.delivery_info
            ):
                delivery_info = sender.request.delivery_info
                if isinstance(delivery_info, dict):
                    exchange = delivery_info.get("exchange", "")
                    routing_key = delivery_info.get("routing_key", "")
                    if (exchange and "beat" in exchange.lower()) or (
                        routing_key and "beat" in routing_key.lower()
                    ):
                        is_periodic = True
                        sender_name = "CeleryBeat"

            # Check properties
            if hasattr(sender.request, "properties"):
                properties = sender.request.properties or {}
                if (
                    properties.get("source") == "beat"
                    or properties.get("scheduler", "").lower() == "beat"
                ):
                    is_periodic = True
                    sender_name = "CeleryBeat"

            # Check task name pattern
            if hasattr(sender.request, "task_name"):
                task_name = sender.request.task_name
                if task_name and task_name.startswith("celery.beat:"):
                    is_periodic = True
                    sender_name = "CeleryBeat"
                    task_name = task_name.replace("celery.beat:", "")

    # Check for periodic task indicators in the task name
    if task_name:
        # Some periodic tasks have specific naming patterns
        if any(
            pattern in task_name.lower()
            for pattern in ["beat", "periodic", "scheduled", "interval", "crontab"]
        ):
            is_periodic = True
            sender_name = sender_name or "CeleryBeat"

    # Additional check for beat-specific metadata
    if kwds.get("eta") or kwds.get("expires"):
        is_periodic = True

    return {
        "task_name": task_name,
        "sender_name": sender_name,
        "is_periodic": is_periodic,
        "periodic_task_name": periodic_task_name,
    }


def get_schedule_info(schedule):
    """Extract schedule information in a readable format.

    Args:
        schedule: A Celery schedule object (crontab or interval)

    Returns:
        dict: Schedule information in a readable format containing either:
            - For crontab: type, minute, hour, day_of_week, day_of_month, month_of_year
            - For interval: type, seconds
            - For others: string representation
    """
    if isinstance(schedule, crontab):
        return {
            "type": "crontab",
            "minute": schedule._orig_minute,
            "hour": schedule._orig_hour,
            "day_of_week": schedule._orig_day_of_week,
            "day_of_month": schedule._orig_day_of_month,
            "month_of_year": schedule._orig_month_of_year,
        }
    elif hasattr(schedule, "run_every"):
        return {
            "type": "interval",
            "seconds": schedule.run_every.total_seconds(),
        }
    else:
        return str(schedule)


@receiver(task_prerun)
@close_db_connection
def task_prerun_handler(
    sender=None, task_id=None, task=None, args=None, kwargs=None, **kwds
):
    """Handle task prerun signal.

    This handler is triggered when a task is about to be executed. It ensures that
    all tasks are recorded in the database, even if they somehow bypassed the publish signals.
    This is especially important for Beat-generated tasks.

    Args:
        sender: The task sender
        task_id: The unique ID of the task
        task: The task object
        args: Task positional arguments
        kwargs: Task keyword arguments
        **kwds: Additional keyword arguments
    """
    try:
        task_info = get_task_info(sender, task, task_id, **kwds)
        logger.info(
            f"Task prerun signal received - task_id: {task_id}, task_name: {task_info['task_name']}, "
            f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
        )

        # Safely get worker and hostname information
        worker = None
        hostname = None
        queue = "default"  # Default queue name

        if sender:
            if hasattr(sender, "hostname"):
                worker = sender.hostname
                hostname = sender.hostname
            elif hasattr(sender, "request") and hasattr(sender.request, "hostname"):
                worker = sender.request.hostname
                hostname = sender.request.hostname
            elif hasattr(sender, "worker"):
                worker = sender.worker
                hostname = sender.worker

            # Get queue name if available
            if hasattr(sender, "request") and hasattr(sender.request, "delivery_info"):
                queue = sender.request.delivery_info.get("routing_key", "default")
                if queue is None:
                    queue = "default"

        if task_id:
            try:
                # Try to get existing task record
                celery_task = CeleryTask.objects.get(task_id=task_id)

                logger.info(
                    f"Updating task status to STARTED - task_id: {task_id}, task_name: {task_info['task_name']}, "
                    f"worker: {worker}, hostname: {hostname}, periodic: {task_info['is_periodic']}"
                )
                celery_task.update_status(
                    "STARTED",
                    worker=worker,
                    hostname=hostname,
                )
            except CeleryTask.DoesNotExist:
                # Task record doesn't exist yet, create it
                logger.warning(
                    f"Task not found in database, creating new record - task_id: {task_id}, "
                    f"task_name: {task_info['task_name']}, periodic: {task_info['is_periodic']}"
                )

                # Create metadata
                metadata = {
                    "is_periodic": task_info["is_periodic"],
                    "created_from": "task_prerun",
                    "worker": worker,
                    "hostname": hostname,
                    "sender": str(sender) if sender else None,
                    "queue": queue,
                }

                # For periodic tasks, try to find the template
                if task_info["is_periodic"] and task_info["task_name"]:
                    try:
                        template_task = CeleryTask.objects.filter(
                            task_name=task_info["task_name"], metadata__is_template=True
                        ).latest("created_at")
                        metadata["template_task_id"] = template_task.task_id
                        logger.info(
                            f"Found template task for periodic task {task_info['task_name']}: {template_task.task_id}"
                        )
                    except CeleryTask.DoesNotExist:
                        logger.warning(
                            f"No template found for periodic task {task_info['task_name']}"
                        )

                # Create the task record
                CeleryTask.objects.create(
                    task_id=task_id,
                    task_name=task_info["task_name"]
                    or (task.name if task else "Unknown"),
                    args=args or [],
                    kwargs=kwargs or {},
                    status="STARTED",
                    started_at=timezone.now(),
                    worker=worker,
                    hostname=hostname,
                    metadata=metadata,
                    queue=queue,
                )
                logger.info(f"Created new task record for {task_id} in prerun handler")
    except Exception as e:
        logger.error(
            f"Error in task_prerun_handler: {str(e)}\n{traceback.format_exc()}",
            exc_info=True,
        )


@receiver(task_success)
def task_success_handler(sender=None, result=None, **kwds):
    """Handle task success signal.

    This handler is triggered when a task completes successfully.
    It updates the task status and stores the result.

    Args:
        sender: The task sender
        result: The result of the task execution
        **kwds: Additional keyword arguments
    """
    try:
        task_id = None
        task_info = get_task_info(sender, None, None, **kwds)

        if sender:
            if hasattr(sender, "request"):
                task_id = sender.request.id
                if hasattr(sender.request, "task_name"):
                    task_info["task_name"] = sender.request.task_name
        elif "task_id" in kwds:
            task_id = kwds["task_id"]
            task_info["task_name"] = kwds.get("task_name", "Unknown")

        if task_id:
            logger.info(
                f"Task success signal received - task_id: {task_id}, task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
            celery_task = CeleryTask.objects.get(task_id=task_id)
            celery_task.update_status("SUCCESS", result=result)
        else:
            logger.warning(
                f"No task_id found in success signal - task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
    except CeleryTask.DoesNotExist:
        logger.warning(
            f"Task not found in database - task_id: {task_id}, task_name: {task_info['task_name']}, "
            f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
        )
    except Exception as e:
        logger.error(f"Error in task_success_handler: {str(e)}", exc_info=True)


@receiver(task_failure)
def task_failure_handler(
    sender=None,
    task_id=None,
    exception=None,
    args=None,
    kwargs=None,
    traceback=None,
    **kwds,
):
    """Handle task failure signal.

    This handler is triggered when a task fails during execution.
    It updates the task status and stores error information.

    Args:
        sender: The task sender
        task_id: The unique ID of the task
        exception: The exception that caused the failure
        args: Task positional arguments
        kwargs: Task keyword arguments
        traceback: The traceback of the failure
        **kwds: Additional keyword arguments
    """
    try:
        task_info = get_task_info(sender, None, task_id, **kwds)

        if sender and hasattr(sender, "request"):
            task_id = sender.request.id
            if hasattr(sender.request, "task_name"):
                task_info["task_name"] = sender.request.task_name

        if task_id:
            logger.info(
                f"Task failure signal received - task_id: {task_id}, task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
            celery_task = CeleryTask.objects.get(task_id=task_id)
            celery_task.update_status(
                "FAILURE", error=str(exception), traceback=traceback
            )
        else:
            logger.warning(
                f"No task_id found in failure signal - task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
    except CeleryTask.DoesNotExist:
        logger.warning(
            f"Task not found in database - task_id: {task_id}, task_name: {task_info['task_name']}, "
            f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
        )
    except Exception as e:
        logger.error(f"Error in task_failure_handler: {str(e)}", exc_info=True)


@receiver(task_retry)
def task_retry_handler(sender=None, task_id=None, reason=None, **kwds):
    """Handle task retry signal.

    This handler is triggered when a task is retried after a failure.
    It updates the task status and retry count.

    Args:
        sender: The task sender
        task_id: The unique ID of the task
        reason: The reason for the retry
        **kwds: Additional keyword arguments
    """
    try:
        task_info = get_task_info(sender, None, task_id, **kwds)

        if sender and hasattr(sender, "request"):
            task_id = sender.request.id
            if hasattr(sender.request, "task_name"):
                task_info["task_name"] = sender.request.task_name

        if task_id:
            logger.info(
                f"Task retry signal received - task_id: {task_id}, task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
            celery_task = CeleryTask.objects.get(task_id=task_id)
            celery_task.update_status(
                "RETRY", retries=celery_task.retries + 1, error=reason
            )
        else:
            logger.warning(
                f"No task_id found in retry signal - task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
    except CeleryTask.DoesNotExist:
        logger.warning(
            f"Task not found in database - task_id: {task_id}, task_name: {task_info['task_name']}, "
            f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
        )
    except Exception as e:
        logger.error(f"Error in task_retry_handler: {str(e)}", exc_info=True)


@receiver(task_revoked)
def task_revoked_handler(
    sender=None, task_id=None, terminated=None, signum=None, expired=None, **kwds
):
    """Handle task revoked signal.

    This handler is triggered when a task is revoked before completion.
    It updates the task status and stores revocation details.

    Args:
        sender: The task sender
        task_id: The unique ID of the task
        terminated: Whether the task was terminated
        signum: The signal number that caused the revocation
        expired: Whether the task expired
        **kwds: Additional keyword arguments
    """
    try:
        task_info = get_task_info(sender, None, task_id, **kwds)

        if sender and hasattr(sender, "request"):
            task_id = sender.request.id
            if hasattr(sender.request, "task_name"):
                task_info["task_name"] = sender.request.task_name

        if task_id:
            logger.info(
                f"Task revoked signal received - task_id: {task_id}, task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
            celery_task = CeleryTask.objects.get(task_id=task_id)
            celery_task.update_status(
                "REVOKED",
                metadata={
                    "terminated": terminated,
                    "signum": signum,
                    "expired": expired,
                },
            )
        else:
            logger.warning(
                f"No task_id found in revoked signal - task_name: {task_info['task_name']}, "
                f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
            )
    except CeleryTask.DoesNotExist:
        logger.warning(
            f"Task not found in database - task_id: {task_id}, task_name: {task_info['task_name']}, "
            f"sender: {task_info['sender_name']}, periodic: {task_info['is_periodic']}"
        )
    except Exception as e:
        logger.error(f"Error in task_revoked_handler: {str(e)}", exc_info=True)
