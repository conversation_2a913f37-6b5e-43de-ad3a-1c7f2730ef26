from django.contrib import admin
from django.utils.html import format_html
from .models import CeleryTask


@admin.register(CeleryTask)
class CeleryTaskAdmin(admin.ModelAdmin):
    list_display = (
        "task_id",
        "task_name",
        "status",
        "queue",
        "created_at",
        "duration",
        "retries",
        "worker",
    )
    list_filter = ("status", "queue", "priority", "created_at", "worker")
    search_fields = ("task_id", "task_name", "error", "worker", "hostname")
    readonly_fields = (
        "task_id",
        "task_name",
        "status",
        "created_at",
        "started_at",
        "completed_at",
        "duration",
        "retries",
        "worker",
        "hostname",
    )
    fieldsets = (
        (
            "Task Information",
            {"fields": ("task_id", "task_name", "status", "queue", "priority")},
        ),
        (
            "Timing Information",
            {
                "fields": (
                    "created_at",
                    "started_at",
                    "completed_at",
                    "duration",
                    "eta",
                    "expires_at",
                )
            },
        ),
        (
            "Task Details",
            {"fields": ("args", "kwargs", "result", "error", "traceback")},
        ),
        ("Retry Information", {"fields": ("retries", "max_retries", "retry_delay")}),
        ("Worker Information", {"fields": ("worker", "hostname")}),
        ("Related Object", {"fields": ("content_type", "object_id", "related_object")}),
        ("Metadata", {"fields": ("metadata",)}),
    )

    def duration(self, obj):
        """Display task duration in a human-readable format."""
        if obj.duration is not None:
            return f"{obj.duration:.2f} seconds"
        return "-"

    duration.short_description = "Duration"

    def has_add_permission(self, request):
        """Prevent manual creation of CeleryTask instances."""
        return False

    def has_change_permission(self, request, obj=None):
        """Prevent modification of CeleryTask instances."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Allow deletion of CeleryTask instances."""
        return True
