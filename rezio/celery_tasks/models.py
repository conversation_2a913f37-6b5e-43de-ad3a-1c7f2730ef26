from django.db import models
from django.utils import timezone
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType


class CeleryTask(models.Model):
    """
    Model to track and store information about Celery tasks.
    This helps in monitoring, debugging, and analyzing task execution.
    """

    # Task Status Choices
    STATUS_CHOICES = (
        ("PENDING", "Pending"),
        ("STARTED", "Started"),
        ("RETRY", "Retry"),
        ("FAILURE", "Failure"),
        ("SUCCESS", "Success"),
        ("REVOKED", "Revoked"),
    )

    # Priority Choices
    PRIORITY_CHOICES = (
        ("HIGH", "High"),
        ("NORMAL", "Normal"),
        ("LOW", "Low"),
    )

    # Core Task Information
    task_id = models.CharField(max_length=255, unique=True, db_index=True)
    task_name = models.Char<PERSON>ield(max_length=255, db_index=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="PENDING")
    priority = models.CharField(
        max_length=10, choices=PRIORITY_CHOICES, default="NORMAL"
    )
    queue = models.CharField(max_length=255, db_index=True)

    # Timing Information
    created_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    eta = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Task Arguments and Results
    args = models.JSONField(default=list, blank=True)
    kwargs = models.JSONField(default=dict, blank=True)
    result = models.JSONField(null=True, blank=True)
    error = models.TextField(null=True, blank=True)
    traceback = models.TextField(null=True, blank=True)

    # Retry Information
    retries = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    retry_delay = models.PositiveIntegerField(null=True, blank=True)  # in seconds

    # Worker Information
    worker = models.CharField(max_length=255, null=True, blank=True)
    hostname = models.CharField(max_length=255, null=True, blank=True)

    # Related Object (Generic Foreign Key)
    content_type = models.ForeignKey(
        ContentType, on_delete=models.SET_NULL, null=True, blank=True
    )
    object_id = models.PositiveIntegerField(null=True, blank=True)
    related_object = GenericForeignKey("content_type", "object_id")

    # Additional Metadata
    metadata = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["task_id"]),
            models.Index(fields=["task_name"]),
            models.Index(fields=["status"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["queue"]),
        ]
        verbose_name = "Celery Task"
        verbose_name_plural = "Celery Tasks"

    def __str__(self):
        return f"{self.task_name} ({self.task_id}) - {self.status}"

    @property
    def duration(self):
        """Calculate task duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None

    @property
    def is_expired(self):
        """Check if task has expired."""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def is_retryable(self):
        """Check if task can be retried."""
        return self.retries < self.max_retries

    def update_status(self, status, **kwargs):
        """Update task status with additional information."""
        self.status = status
        if status == "STARTED":
            self.started_at = timezone.now()
        elif status in ["SUCCESS", "FAILURE"]:
            self.completed_at = timezone.now()

        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

        self.save(
            update_fields=["status", "started_at", "completed_at"] + list(kwargs.keys())
        )
