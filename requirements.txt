asgiref==3.6.0
backports.zoneinfo;python_version<"3.9"
cachecontrol==0.14.0
cachetools==5.3.3
certifi==2024.6.2
celery==5.2.7
cffi==1.16.0
charset-normalizer==3.3.2
cryptography==42.0.8
Django==4.1.7
django-cors-headers==4.5.0
django-environ==0.10.0
django-phonenumber-field==7.3.0
djangorestframework==3.14.0
django-filter==23.2
firebase-admin==6.5.0
google-api-core==2.19.0
google-api-python-client==2.133.0
google-auth==2.30.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-firestore==2.16.0
google-cloud-storage==2.17.0
google-crc32c==1.5.0
google-resumable-media==2.7.1
googleapis-common-protos==1.63.1
grpcio==1.64.1
grpcio-status==1.60.0
httplib2==0.22.0
idna==3.7
msgpack==1.0.8
phonenumbers==8.13.39
proto-plus==1.23.0
protobuf==4.25.3
psycopg2==2.9.5
pyasn1==0.6.0
pyasn1-modules==0.4.0
pycparser==2.22
PyJWT==2.8.0
djangorestframework-simplejwt==5.3.1
pyparsing==3.1.2
pytz==2022.7.1
requests==2.32.3
rsa==4.9
sqlparse==0.4.3
uritemplate==4.1.1
urllib3==2.2.2
gunicorn==22.0.0
pandas==2.2.3
openpyxl==3.1.5
boto3==1.35.44
PyMuPDF==1.24.11
pillow==11.0.0
pycountry==24.6.1
countryinfo==0.1.2
redis==4.5.4
factory_boy>=3.0,<4.0
fcm-django==2.2.1
django-redis==5.4.0
sentry-sdk==2.20.0
newrelic==10.5.0
feedparser==6.0.10
beautifulsoup4>=4.12.0
html5lib>=1.1
django-constance[redis]==3.1.0
channels-redis
channels
langfuse==2.59.3
rich==13.7.1
daphne
django-celery-beat==2.7.0
pubnub==10.2.0
twilio>=8.10.0
googlemaps==4.10.0
openai==1.70.0
langchain-openai==0.3.12
langchain-core==0.3.49
langgraph==0.3.25
langchain-community==0.3.18
langchain-google-community==2.0.3
playwright==1.50.0
html2text==2024.2.26
langgraph-checkpoint-postgres==2.0.17
streamlit==1.44.1
langchain-tavily==0.1.5
better-profanity==0.7.0
indian-cities