version: "3"

volumes:
  rezio_production_redis_data: { }

services:
  django:
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile
    container_name: django_container
    image: rezio_production_django:latest
    entrypoint: /entrypoint
    volumes:
      - .:/app:z
      - /var/log/rezio:/app/rezio/logs
      - ./static:/app/rezio/static
    ports:
      - "8000:8000"
    networks:
      - app-net
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start

  redis:
    image: redis:6.2-alpine
    container_name: rezio_production_redis
    ports:
      - "6379:6379"
    volumes:
      - rezio_production_redis_data:/data
    networks:
      - app-net
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5

  celery_worker:
    image: rezio_production_django:latest
    container_name: celery_worker_container
    depends_on:
      - redis
    networks:
      - app-net
    volumes:
      - .:/app:z
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start-celeryworker

  celery_beat:
    image: rezio_production_django:latest
    container_name: celery_beat_container
    depends_on:
      - redis
    networks:
      - app-net
    volumes:
      - .:/app:z
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start-celerybeat

  flower:
    image: mher/flower:latest
    container_name: rezio_production_flower
    command: celery --broker=redis://rezio_production_redis:6379/0 flower --persistent=True --port=5555 --basic_auth=rezio:rezio  --timezone=Asia/Kolkata
    networks:
      - app-net
    ports:
      - "5555:5555"
    volumes:
      - .:/app:z
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    depends_on:
      - redis
    restart: always

networks:
  app-net:
    driver: bridge

