#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

# Load environment variables explicitly
if [ -f /app/.envs/.production/.django ]; then
    export $(grep -v '^#' /app/.envs/.production/.django | xargs)
fi

# Activate virtual environment
source /app/.venv/bin/activate

# Run Django commands
python -m rezio.manage collectstatic --noinput
python -m rezio.manage migrate

# Run gunicorn from the virtual environment
exec gunicorn rezio.rezio.wsgi --bind 0.0.0.0:8000 --chdir=/app
