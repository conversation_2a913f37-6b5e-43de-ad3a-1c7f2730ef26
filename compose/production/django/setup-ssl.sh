#!/bin/bash

# Replace these variables with your actual domain and email
DOMAIN="dev-api.rezio.ai"
EMAIL="<EMAIL>"

# Wait for nginx to start
sleep 5

# Check if certificates already exist
if [ ! -d "/etc/nginx/ssl/live/$DOMAIN" ]; then
    # Get SSL certificate
    certbot --nginx \
        --non-interactive \
        --agree-tos \
        --email $EMAIL \
        --domains $DOMAIN,www.$DOMAIN \
        --redirect

    # Create renewal cron job
    echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
fi

# Keep the container running
nginx -g "daemon off;"