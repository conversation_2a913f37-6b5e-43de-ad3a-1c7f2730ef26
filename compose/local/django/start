#!/bin/bash

set -o errexit
set -o pipefail
set -o nounset

echo "Starting Django server..."
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Pip version: $(pip --version)"
echo "Checking for psycopg2:"
python -c "import psycopg2; print(f'psycopg2 version: {psycopg2.__version__}')"

echo "Starting Django Migration..."
python -m rezio.manage migrate --traceback

echo "Django server is starting..."
python -m rezio.manage runserver 0.0.0.0:8000 